# Server definitions
app01_server: "stgcpsapp01.int.cbdtp.net"
app02_server: "stgcpsapp02.int.cbdtp.net"
app03_server: "n.a"
web01_server: "stgcpsweb01.int.cbdtp.net"
web02_server: "stgcpsweb02.int.cbdtp.net"
ext01_server: "stgcpsext01.int.cbdtp.net"
transportal_db_server: "stgtxp01.int.cbdtp.net"
transportal_db_listener: "stgtxp01.int.cbdtp.net"
moms_app_server: "stginstapp01.int.cbdtp.net"
insight_mobile_server: "stgcpsext01.int.cbdtp.net"

# Load Balancing/Cluster
cluster_feature: true  # ClusterFeature

# Is Project using TransPortal?
transportal_feature: true  # TransPortalFeature

# Define the Deployment Project Environment

# Project Acronym ex. VTA2/NCTA/ etc. - matches acronym in SVN and in Build # Scripts \ Projects \ director
deployment_environment: "CBDTP"

# Build Type:  DEV, QA, PROD... should match the name of this file ENVIRONMENT_DEV.include or ENVIRONMENT_QA.include etc -->
deployment_type: "STAGE"


# Default is always D unless the project has an E drive, which is rare
deployment_drive: "D"

# Set features for CBDTP
dna_match_feature: true  # DNAMatchFeature
vrg_feature: true  # VRGFeature
aar_feature: false  # AARFeature
trans_com_service_feature: false  # TransComServiceFeature
dmv_worker_feature: true  # DMVWorkerFeature
dmv_feature: true  # DMVFeature

# Is Project using ZPIProcess
zpi_process_task: true  # ZPIProcessTask

# Is Project using ADPollingTask
ad_polling_task: false  # ADPollingTask

# Build the simluators? (DEV/QA only)
simulator_feature: false  # SimulatorFeature

# Used by the build, do not modify the below lines
cps:
    dated_deployment_dir: "2025-04-29"
    assembly_version: "4.20.0-rc2"

mit:
    dated_deployment_dir: "2024-12-05"
    release_version: "23.2.0-rc1"

insight:
    dated_deployment_dir: "2024-11-06"
    release_version: "20230217-INS-REL0004-P8-final"

# If TransPortal Feature = true, need the following DB information to update TransPortal DB
db_user: "{{ db_user | default('') }}"
db_password: "{{ db_password | default('') }}"

# MIT

# Features
finger_print_feature: false # FingerPrintFeature
hocp_feature: false # HOCPFeature
ifx_feature: false #IFXFeature
irr_feature: false # IRRFeature
image_client_service_feature: false # ImageClientServiceFeature
vott_queue_task_feature: true # VOTTQueueTaskFeature
mir_email_notification_task_feature: true # MIREmailNotificationTaskFeature
vehicle_detection_task_feature: false # VehicleDetectionTaskFeature
human_readability_feature: false # HumanReadabilityFeature
manual_results_feature: false # ManualResultsFeature
pre_hocp_feature: false # PreHOCPFeature
trans_api_and_mir_int_service_feature: true # TransAPIandMIRIntServiceFeature
angular9_website_feature: true # Angular9WebSiteFeature

#Image server "deploy" server because BAIFA (and maybe others can't use the FQDN
install_imagereview_fingerprint_server: "stgmirapp01.int.cbdtp.net"
install_imagereview_fingerprint02_server: "stgmirapp02.int.cbdtp.net"
install_imagereview_app_server: "stgmirapp01.int.cbdtp.net"
install_imagereview_app02_server: "stgmirapp02.int.cbdtp.net"
install_image_review_web_server: "stgmirweb01.int.cbdtp.net"
install_image_review_web02_server: "stgmirweb02.int.cbdtp.net"
install_ifx_app01_server: "n.a"
install_ifx_app02_server: "n.a"
install_imagereview_trans_server: "stgmirapp01.int.cbdtp.net"
install_imagereview_trans02_server: "stgmirapp02.int.cbdtp.net"
install_imagereview_humanread_server: "n.a"

# Image Review servers
imagereview_fingerprint_server: "stgmirapp01.int.cbdtp.net"
imagereview_trans_server: "stgmirapp01.int.cbdtp.net"
imagereview_app_server: "stgmirapp01.int.cbdtp.net"
imagereview_humanread_server: "?????"

# Image review web url alias - if no alias than this should be the web servername
imagereview_fingerprint02_server: "stgmirapp02.int.cbdtp.net"
imagereview_trans02_server: "stgmirapp02.int.cbdtp.net"
imagereview_app02_server: "stgmirapp02.int.cbdtp.net"

# new IRR and HOCP servers
imagereview_hocp01_server: "stgmirapp01.int.cbdtp.net"
imagereview_hocp02_server: "stgmirapp02.int.cbdtp.net"
install_imagereview_hocp01_server: "stgmirapp01.int.cbdtp.net"
install_imagereview_hocp02_server: "stgmirapp02.int.cbdtp.net"
install_imagereview_irr01_server: "stgmirapp01.int.cbdtp.net"
install_imagereview_irr02_server: "stgmirapp02.int.cbdtp.net"
imagereview_irr01_server: "stgmirapp01.int.cbdtp.net"
imagereview_irr02_server: "stgmirapp02.int.cbdtp.net"

# PreHOCP Servers and aliases
imagereview_prehocp01_server: "n.a"
imagereview_prehocp02_server: "n.a"

# Insight

integrity_event_loader: false # IntegrityEventLoader
integrity_event_loader_exe_server: "stginstapp01.int.cbdtp.net"

# Insight mobile
insight_mobile_feature: true # InsightMobileFeature
work_order_messaging_feature: true # WorkOrderMessagingFeature
active_directory_feature: true # ActiveDirectoryFeature
transportal_web_alias: "stgtxp.cbdtp.net"
mobile_feature: false # MobileFeature
trans_suite_feature: false # TransSuiteFeature
infinity_feature: true # InfinityFeature
integrity_feature: false # IntegrityFeature
gps_feature: false # GPSFeature