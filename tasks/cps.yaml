---
####################
# Preinstall Sleep #
####################
- name: Stop services on app servers
  ansible.windows.win_service:
    name: "{{ item.service }}"
    state: stopped
  when: item.condition
  delegate_to: "{{ item.server }}"
  async: 160
  poll: 5
  loop:
    - { service: "DNAMatchService", server: "{{ app01_server }}", condition: dna_match_feature }
    - { service: "DNAMatchService", server: "{{ app02_server }}", condition: dna_match_feature and cluster_feature }
    - { service: "VRGService", server: "{{ app01_server }}", condition: vrg_feature }
    - { service: "VRGService", server: "{{ app02_server }}", condition: vrg_feature and cluster_feature }
    - { service: "VRGService", server: "{{ app03_server }}", condition: vrg_feature and deployment_type == 'PROD' }
  tags:
    - cps

- name: Stop IMS
  ansible.windows.win_shell: |
    C:\Windows\System32\inetsrv\appcmd stop apppool /apppool.name:"IMS{{ item.split(';')[2] if item.split(';')[2] != 'none' else '' }}Api"
    {% if item.split(';')[0] not in ['stgcpsext01.int.cbdtp.net', 'prdcpsext01.int.cbdtp.net'] %}
    C:\Windows\System32\inetsrv\appcmd stop site /site.name:"
    IMS{{ item.split(';')[2] if item.split(';')[2] != 'none' else ''}}Api"
    {% endif %}
  delegate_to: "{{ item.split(';')[0] }}"
  loop: "{{ lookup('file', '../files/MultiServerList_CPS{{ deployment_type }}.txt').splitlines() }}"
  loop_control:
    label: "{{ item.split(';')[0] }} - Port {{ item.split(';')[2] }}"
  tags:
    - cps

- name: Disable CPS Tasks
  ansible.windows.win_shell: |
      $taskName = "{{ item.name }}"
      $taskPath = "\\"
      $task = Get-ScheduledTask -TaskName $taskName -TaskPath $taskPath -ErrorAction SilentlyContinue
      if ($task) {
        Stop-ScheduledTask -TaskName $taskName -TaskPath $taskPath
        Disable-ScheduledTask -TaskName $taskName -TaskPath $taskPath
      }
  when: item.condition
  delegate_to: "{{ app01_server }}"
  loop:
    - { name: "ActiveDirectoryPollingTask", condition: ad_polling_task }
    - { name: "ImageFilterSimulator", condition: simulator }
  loop_control:
    label: "{{ item.name }}"
  tags:
    - cps

- name: Stop and disable a scheduled task
  ansible.windows.win_shell: |
    $taskName = "{{ item }}"
    $taskPath = "\\"
    $markerPath = "\\{{ app01_server }}\C$\\CPS\\ScheduledTasks\\$taskName"

    if (Test-Path $markerPath) {
      $task = Get-ScheduledTask -TaskName $taskName -TaskPath $taskPath -ErrorAction SilentlyContinue
      if ($task) {
        Stop-ScheduledTask -TaskName $taskName -TaskPath $taskPath -ErrorAction SilentlyContinue
        Disable-ScheduledTask -TaskName $taskName -TaskPath $taskPath -ErrorAction SilentlyContinue
      }
    }
  delegate_to: "{{ app01_server }}"
  loop:
    - CPSEmailNotification
    - AVIPromotion
    - DigitalPromotion
    - TableauImagesPollingTask
  loop_control:
    label: "{{ item }}"
  tags:
    - cps

- name: Stop IIS
  ansible.windows.win_shell: iisreset
  delegate_to: "{{ item.server }}"
  when: item.condition
  loop:
    - { server: "app01_server", condition: true }
    - { server: "app02_server", condition: cluster_feature }
  loop_control:
    label: "{{ item.server }}"
  tags:
    - cps

- name: Stop App pools and Sites
  ansible.windows.win_shell: |
    C:\Windows\System32\inetsrv\appcmd stop apppool /apppool.name:"{{ item.name }}"
    C:\Windows\System32\inetsrv\appcmd stop site /site.name:"{{ item.name }}"
  delegate_to: "{{ item.server }}"
  when: item.condition
  loop:
    - { server: "app01_server", name: "DMAPI", condition: true }
    - { server: "app02_server", name: "DMAPI",condition: cluster_feature }
    - { server: "app01_server", name: "CFTSAPI", condition: true }
    - { server: "app02_server", name: "CFTSAPI",condition: cluster_feature }
    - { server: "app01_server", name: "CPSWebAPI", condition: true }
    - { server: "app02_server", name: "CPSWebAPI",condition: cluster_feature }
  loop_control:
    label: "{{ item.server }} @ {{ item.name }}"
  tags:
    - cps

- name: Stop Web Services
  ansible.windows.win_service:
    name: "{{ item.service }}"
    state: stopped
  when: item.condition
  delegate_to: "{{ item.server }}"
  async: 160
  poll: 5
  loop:
    - { service: "World Wide Web Publishing Service", server: "{{ app01_server }}", condition: true }
    - { service: "World Wide Web Publishing Service", server: "app02_server", condition: cluster_feature }
    - { service: "Windows Process Activation Service", server: "{{ app01_server }}", condition: true }
    - { service: "Windows Process Activation Service", server: "{{ app02_server }}", condition: cluster_feature }
  loop_control:
    label: "{{ item.service }} @ {{ item.server }}"
  tags:
    - cps

- name: Stop ext services
  ansible.windows.win_shell: |
      $taskName = "{{ item.name }}"
      $taskPath = "\\"
      $task = Get-ScheduledTask -TaskName $taskName -TaskPath $taskPath -ErrorAction SilentlyContinue
      if ($task) {
        Stop-ScheduledTask -TaskName $taskName -TaskPath $taskPath
        Disable-ScheduledTask -TaskName $taskName -TaskPath $taskPath
      }
  delegate_to: "{{ ext01_server }}"
  when: item.condition
  loop:
    - { name: "ZPIProcessTask", condition: zpi_process_task }
    - { name: "DMVLookupMD", condition: dmv_feature }
    - { name: "MVLookupXP", condition: dmv_feature }
    - { name: "DMVLookupNJ", condition: dmv_feature }
  loop_control:
    label: "{{ item.name }}"
  tags:
    - cps

- name: Stop external App pools and Sites
  ansible.windows.win_shell: |
    C:\Windows\System32\inetsrv\appcmd stop apppool /apppool.name:"{{ item }}"
    C:\Windows\System32\inetsrv\appcmd stop site /site.name:"{{ item }}"
  delegate_to: "{{ ext01_server }}"
  loop:
    - WorkflowAPI
    - AAR
    - IAS
    - RAAS
    - TPI
    - TPI02
    - DMVAPI
  loop_control:
    label: "{{ item }}"
  tags:
    - cps

- name: Stop special App pools and Sites
  ansible.windows.win_shell: |
    C:\Windows\System32\inetsrv\appcmd stop apppool /apppool.name:"{{ item.name }}"
    C:\Windows\System32\inetsrv\appcmd stop site /site.name:"{{ item.name }}"
  delegate_to: "{{ item.server }}"
  when: item.condition
  loop:
    - { name: "NYCDOT", server: "{{ ext01_server }}", condition: deployment_type != 'STAGE' }
    - { name: "DMAPI", server: "{{ app03_server }}", condition: deployment_type == 'STAGE' }
  loop_control:
    label: "{{ item.name }}"
  tags:
    - cps

#TODO Rabbit
- name: Stop services on app servers
  ansible.windows.win_service:
    name: "{{ item.service }}"
    state: stopped
  when: item.condition
  delegate_to: "{{ item.server }}"
  async: 160
  poll: 5
  loop:
    - { service: "AARProcessingService", server: "{{ app01_server }}", condition: aar_feature }
    - { service: "AARProcessingService", server: "{{ app02_server }}", condition: aar_feature and cluster_feature }
    - { service: "TransComService", server: "{{ app01_server }}", condition: trans_com_service_feature }
    - { service: "TransComService", server: "{{ app02_server }}", condition: trans_com_service_feature and cluster_feature }
    - { service: "DMVService", server: "{{ ext01_server }}", condition: dmv_worker_feature }
  loop_control:
    label: "{{ item.server }} @ {{ item.service }}"
  tags:
    - cps

# TODO Needs list of servers for reboot
- name: Reboot the Windows machines
  ansible.windows.win_reboot:
    reboot_timeout: 600      # Wait up to 10 minutes
    test_command: 'whoami'   # Ensures WinRM is fully responsive before continuing
  delegate_to: "{{ item.server }}"
  when: item.condition
  loop:
    - { server: "{{ app01_server }}", condition: true }
    - { server: "{{ app02_server }}", condition: cluster_feature }
  loop_control:
    label: "{{ item.server }}"
  tags:
    - cps

#######################
# Postinstall Startup #
#######################
#TODORabbit run ??
- name: Start services on app servers
  ansible.windows.win_service:
    name: "{{ item.service }}"
    state: started
  when: item.condition
  delegate_to: "{{ item.server }}"
  async: 160
  poll: 5
  loop:
    - { service: "AARProcessingService", server: "{{ app01_server }}", condition: aar_feature }
    - { service: "AARProcessingService", server: "{{ app02_server }}", condition: aar_feature and cluster_feature }
    - { service: "TransComService", server: "{{ app01_server }}", condition: trans_com_service_feature }
    - { service: "TransComService", server: "{{ app02_server }}", condition: trans_com_service_feature and cluster_feature }
    - { service: "DMVService", server: "{{ ext01_server }}", condition: dmv_worker_feature }
  loop_control:
    label: "{{ item.server }} @ {{ item.service }}"
  tags:
    - cps

#TODO
- name: Check if vcTagVer column exists and update stbApp table
  ansible.windows.win_shell: |
    sqlcmd -b -U "{{ db_user }}" -P "{{ db_password }}" -Q
    "IF EXISTS (SELECT 1 FROM sys.columns WHERE Name=N'vcTagVer')
    BEGIN EXEC sp_executesql N'update stbApp set vcTagVer=''{{ cps.assembly_version }}'',
    dtReleaseDate=''{{ cps.dated_deployment_dir }}'' where iAppID=859' END" -S
    "{{ transportal_db_listener }}" -d SSO
  delegate_to: "{{ transportal_db_server }}"
  when: transportal_feature
  tags:
    - cps

- name: Start CPS Services
  ansible.windows.win_service:
    name: "{{ item.service }}"
    state: started
  delegate_to: "{{ item.server }}"
  loop:
    - { service: "Windows Process Activation Service", server: "{{ app01_server }}", condition: true }
    - { service: "World Wide Web Publishing Service", server: "{{ app01_server }}", condition: true }
    - { service: "Windows Process Activation Service", server: "{{ app02_server }}", condition: cluster_feature }
    - { service: "World Wide Web Publishing Service", server: "{{ app02_server }}", condition: cluster_feature }
    - { service: "Windows Process Activation Service", server: "{{ web01_server }}", condition: true }
    - { service: "World Wide Web Publishing Service", server: "{{ web01_server }}", condition: true }
    - { service: "Windows Process Activation Service", server: "{{ web02_server }}", condition: cluster_feature }
    - { service: "World Wide Web Publishing Service", server: "{{ web02_server }}", condition: cluster_feature }
  loop_control:
    label: "{{ item.server }} @ {{ item.service }}"
  tags:
    - cps

- name: Start App pools and Sites
  ansible.windows.win_shell: |
    C:\Windows\System32\inetsrv\appcmd start apppool /apppool.name:"{{ item.name }}"
    C:\Windows\System32\inetsrv\appcmd start site /site.name:"{{ item.name }}"
  delegate_to: "{{ item.server }}"
  when: item.condition
  loop:
    - { server: "app01_server", name: "DMAPI", condition: true }
    - { server: "app02_server", name: "DMAPI",condition: cluster_feature }
    - { server: "app01_server", name: "CFTSAPI", condition: true }
    - { server: "app02_server", name: "CFTSAPI",condition: cluster_feature }
    - { server: "app01_server", name: "CPSWebAPI", condition: true }
    - { server: "app02_server", name: "CPSWebAPI",condition: cluster_feature }
  loop_control:
    label: "{{ item.server }} @ {{ item.name }}"
  tags:
    - cps

- name: Enable EXT IIS
  ansible.windows.win_shell: |
    C:\Windows\System32\inetsrv\appcmd start apppool /apppool.name:"{{ item }}"
    C:\Windows\System32\inetsrv\appcmd start site /site.name:"{{ item }}"
  delegate_to: "{{ ext01_server }}"
  when: cluster_value != '02'
  loop:
    - DMVAPI
    - IAS
    - RAAS
    - TPI
  loop_control:
    label: "{{ item }}"
  tags:
    - cps

- name: Start special App pools and Sites
  ansible.windows.win_shell: |
    C:\Windows\System32\inetsrv\appcmd start apppool /apppool.name:"{{ item.name }}"
    C:\Windows\System32\inetsrv\appcmd start site /site.name:"{{ item.name }}"
  delegate_to: "{{ item.server }}"
  when: item.condition
  loop:
    - { name: "NYCDOT", server: "{{ ext01_server }}", condition: deployment_type != 'STAGE' }
    - { name: "DMAPI", server: "{{ app03_server }}", condition: deployment_type == 'STAGE' }
  loop_control:
    label: "{{ item.name }} @ {{ item.server }}"
  tags:
    - cps

- name: Start IIS Site DMAPI using PowerShell
  win_powershell:
    script: |
      Start-IISSite -Name "DMAPI"
  delegate_to: "{{ app03_server }}"
  when: deployment_type == 'STAGE'
  tags:
    - cps

- name: Enable CPS Tasks
  ansible.windows.win_shell: |
      $taskName = "{{ item.name }}"
      $taskPath = "\\"
      $task = Get-ScheduledTask -TaskName $taskName -TaskPath $taskPath -ErrorAction SilentlyContinue
      if ($task) {
        Enable-ScheduledTask -TaskName $taskName -TaskPath $taskPath
      }
  when: item.condition
  delegate_to: "{{ app01_server }}"
  loop:
    - { name: "ActiveDirectoryPollingTask", condition: ad_polling_task }
    - { name: "ImageFilterSimulator", condition: simulator }
  loop_control:
    label: "{{ item.name }}"
  tags:
    - cps

- name: Enable a scheduled task {{ item }}
  ansible.windows.win_shell: |
    $taskName = "{{ item }}"
    $taskPath = "\\"
    $markerPath = "\\{{ app01_server }\C$\\CPS\\ScheduledTasks\\$taskName"

    if (Test-Path $markerPath) {
      $task = Get-ScheduledTask -TaskName $taskName -TaskPath $taskPath -ErrorAction SilentlyContinue
      if ($task) {
        Enable-ScheduledTask -TaskName $taskName -TaskPath $taskPath -ErrorAction SilentlyContinue
      }
    }
  delegate_to: "{{ app01_server }}"
  loop:
    - CPSEmailNotification
    - AVIPromotion
    - DigitalPromotion
    - TableauImagesPollingTask
  loop_control:
    label: "{{ item }}"
  tags:
    - cps

- name: Enable EXT Scheduled Tasks
  ansible.windows.win_shell: |
      $taskName = "{{ item.name }}"
      $taskPath = "\\"
      $task = Get-ScheduledTask -TaskName $taskName -TaskPath $taskPath -ErrorAction SilentlyContinue
      if ($task) {
        Enable-ScheduledTask -TaskName $taskName -TaskPath $taskPath
      }
  delegate_to: "{{ item.server }}"
  when: item.condition
  loop:
    - { name: "ZPIProcessTask", server: "{{ ext01_server }}", condition: zpi_process_task and env_type != 'PROD' and env_type != 'STAGE' }
    - { name: "DMVLookupMD", server: "{{ ext01_server }}", condition: dmv_feature }
    - { name: "MVLookupXP", server: "{{ ext01_server }}", condition: dmv_feature }
    - { name: "DMVLookupNJ", server: "{{ ext01_server }}", condition: dmv_feature }
  loop_control:
    label: "{{ item.name }} @ {{ item.server }}"
  tags:
    - cps

- name: Forcibly run Active Directory Tasks
  ansible.windows.win_shell: |
      $taskName = "ActiveDirectoryPollingTask"
      $taskPath = "\\"
      $task = Get-ScheduledTask -TaskName $taskName -TaskPath $taskPath -ErrorAction SilentlyContinue
      if ($task) {
        Start-ScheduledTask -TaskName $taskName -TaskPath $taskPath
      }
  when: ad_polling_task
  delegate_to: "{{ app01_server }}"
  tags:
    - cps

- name: Start IMS
  ansible.windows.win_shell: |
    powershell -Command "& {Start-IISSite -Name "IMS{{ item.split(';')[2] if item.split(';')[2] != 'none' else '' }}Api"}"
    C:\Windows\System32\inetsrv\appcmd start apppool /apppool.name:"IMS{{ item.split(';')[2] if item.split(';')[2] != 'none' else '' }}Api"
  delegate_to: "{{ item.split(';')[0] }}"
  loop: "{{ lookup('file', '../files/MultiServerList_CPS_{{ deployment_type }}.txt').splitlines() }}"
  loop_control:
    label: "{{ item.split(';')[0] }} - Port {{ item.split(';')[2] }}"
  tags:
    - cps

- name: Start VRG and DNA
  ansible.windows.win_service:
    name: "{{ item.service }}"
    state: started
  delegate_to: "{{ item.server }}"
  when: item.condition
  async: 160
  poll: 5
  loop:
    - { service: "VRGService", server: "{{ app01_server }}", condition: vrg_feature }
    - { service: "VRGService", server: "{{ app02_server }}", condition: vrg_feature and cluster_feature }
    - { service: "VRGService", server: "{{ app03_server }}", condition: vrg_feature and deployment_type == 'PROD' }
    - { service: "DNAMatchService", server: "{{ app01_server }}", condition: dna_match_feature }
    - { service: "DNAMatchService", server: "{{ app02_server }}", condition: dna_match_feature and cluster_feature }
  tags:
    - cps
