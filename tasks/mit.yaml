---
####################
# Preinstall Sleep #
####################
- name: Disable IFX tasks
  ansible.windows.win_shell: |
    $taskName = "{{ item.name }}"
    $taskPath = "\\"
    $markerPath = "\\{{ install_ifx_app01_server }}\C$\{{ item.path }}\$taskName"

    if (Test-Path $markerPath) {
      $task = Get-ScheduledTask -TaskName $taskName -TaskPath $taskPath -ErrorAction SilentlyContinue
      if ($task) {
        Stop-ScheduledTask -TaskName $taskName -TaskPath $taskPath -ErrorAction SilentlyContinue
        Disable-ScheduledTask -TaskName $taskName -TaskPath $taskPath -ErrorAction SilentlyContinue
      }
    }
  delegate_to: "{{ install_ifx_app01_server }}"
  when:
    # - not cluster_feature
    - ifx_feature
    - not human_readability_feature
  loop:
    - { path: "IFX\\ScheduledTasks", name: "IFXImageReviewSendTrans" }
    - { path: "IFX\\ScheduledTasks", name: "IFXImageReviewSendTransRetries" }
    - { path: "CAMS\\ScheduledTasks", name: "IFXImageReviewSendTrans" }
    - { path: "CAMS\\ScheduledTasks", name: "IFXImageReviewSendTrans2" }
    - { path: "CAMS\\ScheduledTasks", name: "IFXImageReviewSendTrans3" }
    - { path: "CAMS\\ScheduledTasks", name: "IFXImageReviewSendTrans4" }
    - { path: "IFX\\ScheduledTasks", name: "IFXImageReviewSendTrans-1100" }
    - { path: "IFX\\ScheduledTasks", name: "IFXImageReviewSendTrans-1200" }
    - { path: "IFX\\ScheduledTasks", name: "IFXImageReviewSendTrans-1300" }
    - { path: "IFX\\ScheduledTasks", name: "IFXImageReviewSendTrans-1400" }
  loop_control:
    label: "{{ item.name }}"
  tags:
    - mit

- name: Stop Services
  ansible.windows.win_service:
    name: "{{ item.service }}"
    state: stopped
  delegate_to: "{{ item.server }}"
  async: 200
  poll: 10
  when:
    - item.condition
    - not human_readability_feature
  loop:
    - { service: "World Wide Web Publishing Service", server: "{{ install_image_review_web_server }}", condition: true }
    - { service: "Net.Tcp Listener Adapter", server: "{{ install_image_review_web_server }}", condition: true }
    - { service: "Windows Process Activation Service", server: "{{ install_image_review_web_server }}", condition: true }
    - { service: "World Wide Web Publishing Service", server: "{{ install_image_review_web02_server }}", condition: cluster_feature }
    - { service: "Net.Tcp Listener Adapter", server: "{{ install_image_review_web02_server }}", condition: cluster_feature }
    - { service: "Windows Process Activation Service", server: "{{ install_image_review_web02_server }}", condition: cluster_feature }
    - { service: "World Wide Web Publishing Service", server: "{{ install_imagereview_app_server }}", condition: true }
    - { service: "Net.Tcp Listener Adapter", server: "{{ install_imagereview_app_server }}", condition: true }
    - { service: "Windows Process Activation Service", server: "{{ install_imagereview_app_server }}", condition: true }
    - { service: "World Wide Web Publishing Service", server: "{{ install_imagereview_app02_server }}", condition: cluster_feature }
    - { service: "Net.Tcp Listener Adapter", server: "{{ install_imagereview_app02_server }}", condition: cluster_feature }
    - { service: "Windows Process Activation Service", server: "{{ install_imagereview_app02_server }}", condition: cluster_feature }
    - { service: "World Wide Web Publishing Service", server: "{{ install_imagereview_trans_server }}", condition: "{{ install_imagereview_app_server }} != {{ install_imagereview_trans_server }}" }
    - { service: "Net.Tcp Listener Adapter", server: "{{ install_imagereview_trans_server }}", condition: "{{ install_imagereview_app_server }} != {{ install_imagereview_trans_server }}" }
    - { service: "Windows Process Activation Service", server: "{{ install_imagereview_trans_server }}", condition: "{{ install_imagereview_app_server }} != {{ install_imagereview_trans_server }}" }
    - { service: "World Wide Web Publishing Service", server: "{{ install_imagereview_trans02_server }}", condition: "{{ install_imagereview_app02_server }} != {{ install_imagereview_trans02_server }} and cluster_feature" }
    - { service: "Net.Tcp Listener Adapter", server: "{{ install_imagereview_trans02_server }}", condition: "{{ install_imagereview_app02_server }} != {{ install_imagereview_trans02_server }} and cluster_feature" }
    - { service: "Windows Process Activation Service", server: "{{ install_imagereview_trans02_server }}", condition: "{{ install_imagereview_app02_server }} != {{ install_imagereview_trans02_server }} and cluster_feature" }
    - { service: "World Wide Web Publishing Service", server: "{{ install_imagereview_fingerprint_server }}", condition: finger_print_feature" }
    - { service: "Net.Tcp Listener Adapter", server: "{{ install_imagereview_fingerprint_server }}", condition: finger_print_feature }
    - { service: "Windows Process Activation Service", server: "{{ install_imagereview_fingerprint_server }}", condition: finger_print_feature }
    - { service: "World Wide Web Publishing Service", server: "{{ install_imagereview_fingerprint02_server }}", condition: finger_print_feature and cluster_feature }
    - { service: "Net.Tcp Listener Adapter", server: "{{ install_imagereview_fingerprint02_server }}", condition: finger_print_feature and cluster_feature }
    - { service: "Windows Process Activation Service", server: "{{ install_imagereview_fingerprint02_server }}", condition: finger_print_feature and cluster_feature }
    - { service: "World Wide Web Publishing Service", server: "{{ install_imagereview_trans_server }}", condition: "{{ install_imagereview_app_server }} != {{ install_imagereview_hocp01_server }}" }
    - { service: "Net.Tcp Listener Adapter", server: "{{ install_imagereview_trans_server }}", condition: "{{ install_imagereview_app_server }} != {{ install_imagereview_hocp01_server }}" }
    - { service: "Windows Process Activation Service", server: "{{ install_imagereview_trans_server }}", condition: "{{ install_imagereview_app_server }} != {{ install_imagereview_hocp01_server }}" }
    - { service: "World Wide Web Publishing Service", server: "{{ install_imagereview_trans02_server }}", condition: "{{ install_imagereview_app02_server }} != {{ install_imagereview_hocp02_server }} and cluster_feature" }
    - { service: "Net.Tcp Listener Adapter", server: "{{ install_imagereview_trans02_server }}", condition: "{{ install_imagereview_app02_server }} != {{ install_imagereview_hocp02_server }} and cluster_feature" }
    - { service: "Windows Process Activation Service", server: "{{ install_imagereview_trans02_server }}", condition: "{{ install_imagereview_app02_server }} != {{ install_imagereview_hocp02_server }} and cluster_feature" }
    - { service: "World Wide Web Publishing Service", server: "{{ install_imagereview_irr01_server }}", condition: true }
    - { service: "Net.Tcp Listener Adapter", server: "{{ install_imagereview_irr01_server }}", condition: true }
    - { service: "Windows Process Activation Service", server: "{{ install_imagereview_irr01_server }}", condition: true }
    - { service: "World Wide Web Publishing Service", server: "{{ install_imagereview_irr02_server }}", condition: cluster_feature }
    - { service: "Net.Tcp Listener Adapter", server: "{{ install_imagereview_irr02_server }}", condition: cluster_feature }
    - { service: "Windows Process Activation Service", server: "{{ install_imagereview_irr02_server }}", condition: cluster_feature }
    - { service: "World Wide Web Publishing Service", server: "{{ install_imagereview_hocp01_server }}", condition: true }
    - { service: "Net.Tcp Listener Adapter", server: "{{ install_imagereview_hocp01_server }}", condition: true }
    - { service: "Windows Process Activation Service", server: "{{ install_imagereview_hocp01_server }}", condition: true }
    - { service: "World Wide Web Publishing Service", server: "{{ install_imagereview_hocp02_server }}", condition: cluster_feature }
    - { service: "Net.Tcp Listener Adapter", server: "{{ install_imagereview_hocp02_server }}", condition: cluster_feature }
    - { service: "Windows Process Activation Service", server: "{{ install_imagereview_hocp02_server }}", condition: cluster_feature }
  loop_control:
    label: "{{ item.service }} @ {{ item.server }}"
  tags:
    - mit

- name: Stop Services
  ansible.windows.win_service:
    name: "{{ item }}"
    state: stopped
  when: human_readability_feature
  delegate_to: "{{ install_imagereview_humanread_server }}"
  async: 200
  poll: 10
  loop:
    - "World Wide Web Publishing Service"
    - "Net.Tcp Listener Adapter"
    - "Windows Process Activation Service"
  loop_control:
    label: "{{ item }}"
  tags:
    - mit

- name: Stop Fingerprint ServiceHost
  ansible.windows.win_service:
    name: "{{ item.service }}"
    state: stopped
  delegate_to: "{{ item.server }}"
  async: 200
  poll: 10
  when:
    - item.condition
    - finger_print_feature
  loop:
    - { service: "FPMInternalServiceHost", server: "{{ imagereview_fingerprint_server }}", condition: true }
    - { service: "FPPInternalServiceHost", server: "{{ imagereview_fingerprint_server }}", condition: true }
    - { service: "FPPInternalServiceClient", server: "{{ imagereview_fingerprint_server }}", condition: true }
    - { service: "FPMInternalServiceHost", server: "{{ imagereview_fingerprint02_server }}", condition: cluster_feature }
    - { service: "FPPInternalServiceHost", server: "{{ imagereview_fingerprint02_server }}", condition: cluster_feature }
    - { service: "FPPInternalServiceClient", server: "{{ imagereview_fingerprint02_server }}", condition: cluster_feature }
  loop_control:
    label: "{{ item.service }} @ {{ item.server }}"
  tags:
    - mit

- name: Stop HOCP ServiceHost
  ansible.windows.win_service:
    name: "{{ item.service }}"
    state: stopped
  delegate_to: "{{ item.server }}"
  async: 200
  poll: 10
  when:
    - item.condition
    - hocp_feature
  loop:
    - { service: "HOCPInternalServiceHost", server: "{{ imagereview_hocp01_server }}", condition: true }
    - { service: "HOCPInternalServiceHost", server: "{{ imagereview_hocp02_server }}", condition: cluster_feature }
  loop_control:
    label: "{{ item.service }} @ {{ item.server }}"
  tags:
    - mit

- name: Stop PREHOCP ServiceHost
  ansible.windows.win_service:
    name: "{{ item.service }}"
    state: stopped
  delegate_to: "{{ item.server }}"
  async: 200
  poll: 10
  when:
    - item.condition
    - pre_hocp_feature
  loop:
    - { service: "PREHOCPInternalServiceHost", server: "{{ imagereview_prehocp01_server }}", condition: true }
    - { service: "PREHOCPInternalServiceHost", server: "{{ imagereview_prehocp02_server }}", condition: cluster_feature }
  loop_control:
    label: "{{ item.service }} @ {{ item.server }}"
  tags:
    - mit

- name: Stop Required ServiceHost
  ansible.windows.win_service:
    name: "{{ item.service }}"
    state: stopped
  delegate_to: "{{ item.server }}"
  async: 200
  poll: 10
  when:
    - item.condition
    - not human_readability_feature
  loop:
    - { service: "MIRInternalServiceHost", server: "{{ imagereview_trans_server }}", condition: trans_api_and_mir_int_service_feature }
    - { service: "MIRInternalServiceHost", server: "{{ imagereview_trans02_server }}", condition: trans_api_and_mir_int_service_feature and cluster_feature }
    - { service: "IRRInternalServiceHost", server: "{{ imagereview_irr01_server }}", condition: irr_feature }
    - { service: "IRRInternalServiceHost", server: "{{ imagereview_irr02_server }}", condition: irr_feature and cluster_feature }
    - { service: "MIRImageReviewServiceHost", server: "{{ imagereview_app_server }}", condition: true }
    - { service: "MIRImageReviewServiceHost", server: "{{ imagereview_app02_server }}", condition: cluster_feature }
    - { service: "tcore.ImageReview.ImageTransactionQueueingService", server: "{{ imagereview_app_server }}", condition: true }
    - { service: "tcore.ImageReview.ImageTransactionQueueingService", server: "{{ imagereview_app02_server }}", condition: cluster_feature }
    - { service: "MIR Transaction Queue Service", server: "{{ imagereview_app_server }}", condition: true }
    - { service: "MIR Transaction Queue Service", server: "{{ imagereview_app02_server }}", condition: cluster_feature }
  loop_control:
    label: "{{ item.service }} @ {{ item.server }}"
  tags:
    - mit

- name: Stop HR ServiceHost
  ansible.windows.win_service:
    name: "{{ item }}"
    state: stopped
  when: human_readability_feature
  delegate_to: "{{ imagereview_humanread_server }}"
  async: 200
  poll: 10
  loop:
    - "MIRInternalServiceHost"
    - "MIRImageReviewServiceHost"
  loop_control:
    label: "{{ item }}"
  tags:
    - mit

- name: ImageReview  - Pre-Install FPClient Sleep
  ansible.windows.win_service:
    name: "FPPInternalServiceClient"
    state: stopped
  delegate_to: "{{ item.split(';')[0] }}"
  async: 200
  poll: 10
  when: finger_print_feature
  loop: "{{ lookup('file', '../files/fppclientlist_mir_{{ deployment.type }}.txt').splitlines() }}"
  loop_control:
    label: "{{ item.split(';')[0] }} - type {{ item.split(';')[1] }}"
  tags:
    - mit

- name: ImageReview  - Pre-Install MultiServer Sleep
  ansible.windows.win_shell: |
    {% if item.split(';')[2] == "none" %}
    $taskName = "{{item.split(';')[1] }}"
    {% else %}
    $taskName = "{{ item.split(';')[1] }}-{{ item.split(';')[2] }}"
    {% endif %}
    $taskPath = "\\"
    $task = Get-ScheduledTask -TaskName $taskName -TaskPath $taskPath -ErrorAction SilentlyContinue
    if ($task) {
      Stop-ScheduledTask -TaskName $taskName -TaskPath $taskPath -ErrorAction SilentlyContinue
      Disable-ScheduledTask -TaskName $taskName -TaskPath $taskPath -ErrorAction SilentlyContinue
    }
  delegate_to: "{{ item.split(';')[0] }}"
  loop: "{{ lookup('file', '../files/MultiServerList_MIR{{ deployment_type }}.txt').splitlines() }}"
  loop_control:
    label: "{{ item.split(';')[0] }} - type {{ item.split(';')[1] }}"
  tags:
    - mit

# TODO Needs list of servers for reboot
- name: Reboot the Windows machines
  ansible.windows.win_reboot:
    reboot_timeout: 600      # Wait up to 10 minutes
    test_command: 'whoami'   # Ensures WinRM is fully responsive before continuing
  delegate_to: "{{ item.server }}"
  when: item.condition
  loop:
    - { server: "{{ install_imagereview_app01_server }}", condition: true }
    - { server: "{{ install_imagereview_app02_server }}", condition: cluster_feature }
  loop_control:
    label: "{{ item.server }}"
  tags:
    - mit

######################
# Preinstall Startup #
######################
- name: StartFingerprint ServiceHost
  ansible.windows.win_service:
    name: "{{ item.service }}"
    state: started
  delegate_to: "{{ item.server }}"
  async: 200
  poll: 10
  when:
    - item.condition
    - finger_print_feature
  loop:
    - { service: "FPMInternalServiceHost", server: "{{ imagereview_fingerprint_server }}", condition: true }
    - { service: "FPPInternalServiceHost", server: "{{ imagereview_fingerprint_server }}", condition: true }
    - { service: "FPPInternalServiceClient", server: "{{ imagereview_fingerprint_server }}", condition: true }
    - { service: "FPMInternalServiceHost", server: "{{ imagereview_fingerprint02_server }}", condition: cluster_feature }
    - { service: "FPPInternalServiceHost", server: "{{ imagereview_fingerprint02_server }}", condition: cluster_feature }
    - { service: "FPPInternalServiceClient", server: "{{ imagereview_fingerprint02_server }}", condition: cluster_feature }
  loop_control:
    label: "{{ item.service }} @ {{ item.server }}"
  tags:
    - mit

- name: Start HOCP ServiceHost
  ansible.windows.win_service:
    name: "{{ item.service }}"
    state: started
  delegate_to: "{{ item.server }}"
  async: 200
  poll: 10
  when:
    - item.condition
    - hocp_feature
  loop:
    - { service: "HOCPInternalServiceHost", server: "{{ imagereview_hocp01_server }}", condition: true }
    - { service: "HOCPInternalServiceHost", server: "{{ imagereview_hocp02_server }}", condition: cluster_feature }
  loop_control:
    label: "{{ item.service }} @ {{ item.server }}"
  tags:
    - mit

- name: Start PREHOCP ServiceHost
  ansible.windows.win_service:
    name: "{{ item.service }}"
    state: started
  delegate_to: "{{ item.server }}"
  async: 200
  poll: 10
  when:
    - item.condition
    - pre_hocp_feature
  loop:
    - { service: "PREHOCPInternalServiceHost", server: "{{ imagereview_prehocp01_server }}", condition: true }
    - { service: "PREHOCPInternalServiceHost", server: "{{ imagereview_prehocp02_server }}", condition: cluster_feature }
  loop_control:
    label: "{{ item.service }} @ {{ item.server }}"
  tags:
    - mit

- name: Start Required ServiceHost
  ansible.windows.win_service:
    name: "{{ item.service }}"
    state: started
  delegate_to: "{{ item.server }}"
  async: 200
  poll: 10
  when:
    - item.condition
    - not human_readability_feature
  loop:
    - { service: "MIRInternalServiceHost", server: "{{ imagereview_trans_server }}", condition: trans_api_and_mir_int_service_feature }
    - { service: "MIRInternalServiceHost", server: "{{ imagereview_trans02_server }}", condition: trans_api_and_mir_int_service_feature and cluster_feature }
    - { service: "IRRInternalServiceHost", server: "{{ imagereview_irr01_server }}", condition: irr_feature }
    - { service: "IRRInternalServiceHost", server: "{{ imagereview_irr02_server }}", condition: irr_feature and cluster_feature }
    - { service: "MIRImageReviewServiceHost", server: "{{ imagereview_app_server }}", condition: true }
    - { service: "MIRImageReviewServiceHost", server: "{{ imagereview_app02_server }}", condition: cluster_feature }
    - { service: "tcore.ImageReview.ImageTransactionQueueingService", server: "{{ imagereview_app_server }}", condition: true }
    - { service: "tcore.ImageReview.ImageTransactionQueueingService", server: "{{ imagereview_app02_server }}", condition: cluster_feature }
    - { service: "MIR Transaction Queue Service", server: "{{ imagereview_app_server }}", condition: true }
    - { service: "MIR Transaction Queue Service", server: "{{ imagereview_app02_server }}", condition: cluster_feature }
  loop_control:
    label: "{{ item.service }} @ {{ item.server }}"
  tags:
    - mit

#TODO
- name:  Update the Release version and date in the TransPortal DB
  ansible.windows.win_shell: |
    sqlcmd -b -U "{{ db_user }}" -P "{{ db_password }}" -Q
    "IF EXISTS (SELECT 1 FROM sys.columns WHERE Name=N'vcTagVer')
    BEGIN EXEC sp_executesql N'update stbApp set vcTagVer=''{{ mit.release_version }}'',
    dtReleaseDate=''{{ mit.dated_deployment_dir }}'' where iAppID=853' END" -S
    "{{ transportal_db_listener }}" -d SSO
  delegate_to: "{{ transportal_db_server }}"
  when: transportal_feature
  tags:
    - mit

- name: Start Services
  ansible.windows.win_service:
    name: "{{ item.service }}"
    state: started
  delegate_to: "{{ item.server }}"
  async: 200
  poll: 10
  when:
    - item.condition
    - not human_readability_feature
  loop:
    - { service: "World Wide Web Publishing Service", server: "{{ install_image_review_web_server }}", condition: true }
    - { service: "Net.Tcp Listener Adapter", server: "{{ install_image_review_web_server }}", condition: true }
    - { service: "Windows Process Activation Service", server: "{{ install_image_review_web_server }}", condition: true }
    - { service: "World Wide Web Publishing Service", server: "{{ install_image_review_web02_server }}", condition: cluster_feature }
    - { service: "Net.Tcp Listener Adapter", server: "{{ install_image_review_web02_server }}", condition: cluster_feature }
    - { service: "Windows Process Activation Service", server: "{{ install_image_review_web02_server }}", condition: cluster_feature }
    - { service: "World Wide Web Publishing Service", server: "{{ install_imagereview_app_server }}", condition: true }
    - { service: "Net.Tcp Listener Adapter", server: "{{ install_imagereview_app_server }}", condition: true }
    - { service: "Windows Process Activation Service", server: "{{ install_imagereview_app_server }}", condition: true }
    - { service: "World Wide Web Publishing Service", server: "{{ install_imagereview_app02_server }}", condition: cluster_feature }
    - { service: "Net.Tcp Listener Adapter", server: "{{ install_imagereview_app02_server }}", condition: cluster_feature }
    - { service: "Windows Process Activation Service", server: "{{ install_imagereview_app02_server }}", condition: cluster_feature }
    - { service: "World Wide Web Publishing Service", server: "{{ install_imagereview_trans_server }}", condition: "{{ install_imagereview_app_server }} != {{ install_imagereview_trans_server }}" }
    - { service: "Net.Tcp Listener Adapter", server: "{{ install_imagereview_trans_server }}", condition: "{{ install_imagereview_app_server }} != {{ install_imagereview_trans_server }}" }
    - { service: "Windows Process Activation Service", server: "{{ install_imagereview_trans_server }}", condition: "{{ install_imagereview_app_server }} != {{ install_imagereview_trans_server }}" }
    - { service: "World Wide Web Publishing Service", server: "{{ install_imagereview_trans02_server }}", condition: "{{ install_imagereview_app02_server }} != {{ install_imagereview_trans02_server }} and cluster_feature" }
    - { service: "Net.Tcp Listener Adapter", server: "{{ install_imagereview_trans02_server }}", condition: "{{ install_imagereview_app02_server }} != {{ install_imagereview_trans02_server }} and cluster_feature" }
    - { service: "Windows Process Activation Service", server: "{{ install_imagereview_trans02_server }}", condition: "{{ install_imagereview_app02_server }} != {{ install_imagereview_trans02_server }} and cluster_feature" }
    - { service: "World Wide Web Publishing Service", server: "{{ install_imagereview_fingerprint_server }}", condition: finger_print_feature" }
    - { service: "Net.Tcp Listener Adapter", server: "{{ install_imagereview_fingerprint_server }}", condition: finger_print_feature }
    - { service: "Windows Process Activation Service", server: "{{ install_imagereview_fingerprint_server }}", condition: finger_print_feature }
    - { service: "World Wide Web Publishing Service", server: "{{ install_imagereview_fingerprint02_server }}", condition: finger_print_feature and cluster_feature }
    - { service: "Net.Tcp Listener Adapter", server: "{{ install_imagereview_fingerprint02_server }}", condition: finger_print_feature and cluster_feature }
    - { service: "Windows Process Activation Service", server: "{{ install_imagereview_fingerprint02_server }}", condition: finger_print_feature and cluster_feature }
    - { service: "World Wide Web Publishing Service", server: "{{ install_imagereview_trans_server }}", condition: "{{ install_imagereview_app_server }} != {{ install_imagereview_hocp01_server }}" }
    - { service: "Net.Tcp Listener Adapter", server: "{{ install_imagereview_trans_server }}", condition: "{{ install_imagereview_app_server }} != {{ install_imagereview_hocp01_server }}" }
    - { service: "Windows Process Activation Service", server: "{{ install_imagereview_trans_server }}", condition: "{{ install_imagereview_app_server }} != {{ install_imagereview_hocp01_server }}" }
    - { service: "World Wide Web Publishing Service", server: "{{ install_imagereview_trans02_server }}", condition: "{{ install_imagereview_app02_server }} != {{ install_imagereview_hocp02_server }} and cluster_feature" }
    - { service: "Net.Tcp Listener Adapter", server: "{{ install_imagereview_trans02_server }}", condition: "{{ install_imagereview_app02_server }} != {{ install_imagereview_hocp02_server }} and cluster_feature" }
    - { service: "Windows Process Activation Service", server: "{{ install_imagereview_trans02_server }}", condition: "{{ install_imagereview_app02_server }} != {{ install_imagereview_hocp02_server }} and cluster_feature" }
    - { service: "World Wide Web Publishing Service", server: "{{ install_imagereview_irr01_server }}", condition: true }
    - { service: "Net.Tcp Listener Adapter", server: "{{ install_imagereview_irr01_server }}", condition: true }
    - { service: "Windows Process Activation Service", server: "{{ install_imagereview_irr01_server }}", condition: true }
    - { service: "World Wide Web Publishing Service", server: "{{ install_imagereview_irr02_server }}", condition: cluster_feature }
    - { service: "Net.Tcp Listener Adapter", server: "{{ install_imagereview_irr02_server }}", condition: cluster_feature }
    - { service: "Windows Process Activation Service", server: "{{ install_imagereview_irr02_server }}", condition: cluster_feature }
    - { service: "World Wide Web Publishing Service", server: "{{ install_imagereview_hocp01_server }}", condition: true }
    - { service: "Net.Tcp Listener Adapter", server: "{{ install_imagereview_hocp01_server }}", condition: true }
    - { service: "Windows Process Activation Service", server: "{{ install_imagereview_hocp01_server }}", condition: true }
    - { service: "World Wide Web Publishing Service", server: "{{ install_imagereview_hocp02_server }}", condition: cluster_feature }
    - { service: "Net.Tcp Listener Adapter", server: "{{ install_imagereview_hocp02_server }}", condition: cluster_feature }
    - { service: "Windows Process Activation Service", server: "{{ install_imagereview_hocp02_server }}", condition: cluster_feature }
  loop_control:
    label: "{{ item.service }} @ {{ item.server }}"
  tags:
    - mit

- name: Start Services
  ansible.windows.win_service:
    name: "{{ item }}"
    state: started
  when: human_readability_feature
  delegate_to: "{{ install_imagereview_humanread_server }}"
  async: 200
  poll: 10
  loop:
    - "World Wide Web Publishing Service"
    - "Net.Tcp Listener Adapter"
    - "Windows Process Activation Service"
  loop_control:
    label: "{{ item }}"
  tags:
    - mit

- name: Enable IFX tasks
  ansible.windows.win_shell: |
    $taskName = "{{ item.name }}"
    $taskPath = "\\"
    $markerPath = "\\{{ install_ifx_app01_server }}\C$\{{ item.path }}\$taskName"

    if (Test-Path $markerPath) {
      $task = Get-ScheduledTask -TaskName $taskName -TaskPath $taskPath -ErrorAction SilentlyContinue
      if ($task) {
        Enable-ScheduledTask -TaskName $taskName -TaskPath $taskPath -ErrorAction SilentlyContinue
      }
    }
  delegate_to: "{{ install_ifx_app01_server }}"
  when:
    # - not cluster_feature
    - ifx_feature
    - not human_readability_feature
    - not angular9_website_feature
  loop:
    - { path: "IFX\\ScheduledTasks", name: "IFXImageReviewSendTrans" }
    - { path: "IFX\\ScheduledTasks", name: "IFXImageReviewSendTransRetries" }
    - { path: "CAMS\\ScheduledTasks", name: "IFXImageReviewSendTrans" }
    - { path: "CAMS\\ScheduledTasks", name: "IFXImageReviewSendTrans2" }
    - { path: "CAMS\\ScheduledTasks", name: "IFXImageReviewSendTrans3" }
    - { path: "CAMS\\ScheduledTasks", name: "IFXImageReviewSendTrans4" }
    - { path: "IFX\\ScheduledTasks", name: "IFXImageReviewSendTrans-1100" }
    - { path: "IFX\\ScheduledTasks", name: "IFXImageReviewSendTrans-1200" }
    - { path: "IFX\\ScheduledTasks", name: "IFXImageReviewSendTrans-1300" }
    - { path: "IFX\\ScheduledTasks", name: "IFXImageReviewSendTrans-1400" }
  loop_control:
    label: "{{ item.name }}"
  tags:
    - mit

- name: ImageReview - Post-Install FPClient Wake
  ansible.windows.win_service:
    name: "FPPInternalServiceClient"
    state: started
  delegate_to: "{{ item.split(';')[0] }}"
  async: 200
  poll: 10
  when: finger_print_feature
  loop: "{{ lookup('file', '../files/fppclientlist_mir_{{ deployment.type }}.txt').splitlines() }}"
  loop_control:
    label: "{{ item.split(';')[0] }} - type {{ item.split(';')[1] }}"
  tags:
    - mit

- name: ImageReview  - Post-Install FXTxnMultiServer Wake
  ansible.windows.win_shell: |
    {% if item.split(';')[2] == "none" %}
    $taskName = "{{item.split(';')[1] }}"
    {% else %}
    $taskName = "{{ item.split(';')[1] }}-{{ item.split(';')[2] }}"
    {% endif %}
    $taskPath = "\\"
    $task = Get-ScheduledTask -TaskName $taskName -TaskPath $taskPath -ErrorAction SilentlyContinue
    if ($task) {
      Enable-ScheduledTask -TaskName $taskName -TaskPath $taskPath -ErrorAction SilentlyContinue
    }
  delegate_to: "{{ item.split(';')[0] }}"
  loop: "{{ lookup('file', '../files/MultiServerList_MIR{{ deployment_type }}.txt').splitlines() }}"
  loop_control:
    label: "{{ item.split(';')[0] }} - type {{ item.split(';')[1] }}"
  tags:
    - mit
