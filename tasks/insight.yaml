---
# Preinstall Sleep
- name: Stop and disable MOMS tasks
  ansible.windows.win_shell: |
    $taskName = "{{ item.name }}"
    $taskPath = "\\"
    $markerPath = "\\{{ moms_app_server }}\C$\\MOMS\\ScheduledTasks\\$taskName"

    if (Test-Path $markerPath) {
      $task = Get-ScheduledTask -TaskName $taskName -TaskPath $taskPath -ErrorAction SilentlyContinue
      if ($task) {
        Stop-ScheduledTask -TaskName $taskName -TaskPath $taskPath -ErrorAction SilentlyContinue
        Disable-ScheduledTask -TaskName $taskName -TaskPath $taskPath -ErrorAction SilentlyContinue
      }
    }
  when: item.condition
  delegate_to: "{{ moms_app_server }}"
  loop:
    - name: "MOMSWorkOrderMessagingTask"
      condition: work_order_messaging_feature
    - name: "MOMSFailureAnalysisTaskConsole"
      condition: true
    - name: "MOMSInventoryLevelNotification"
      condition: true
    - name: "MOMSNotificationEscalationTask"
      condition: true
    - name: "MOMSPredictiveMaintenanceNotificationTaskConsole"
      condition: true
    - name: "MOMSPreventiveMaintenanceTask"
      condition: true
    - name: "TransPortal_MOMSEventLoader"
      condition: true
    - name: "CPS_MOMSEventLoader"
      condition: true
    - name: "VOTT_MOMSEventLoader"
      condition: true
    - name: "SolarWinds_MOMSEventLoader"
      condition: true
    - name: "WhatsUp_MOMSEventLoader"
      condition: true
    - name: "MOMSEventLoader"
      condition: true
    - name: "ImageReview_MOMSEventLoader"
      condition: true
    - name: "MOMSExternalNotifier"
      condition: true
    - name: "MOMSTrafficDataLoader"
      condition: true
  loop_control:
    label: "{{ item.name }}"
  tags:
    - insight

- name: Disable MOMS Tasks
  ansible.windows.win_shell: |
    $taskName = "{{ item.name }}"
    $taskPath = "\\"
    $task = Get-ScheduledTask -TaskName $taskName -TaskPath $taskPath -ErrorAction SilentlyContinue
    if ($task) {
      Stop-ScheduledTask -TaskName $taskName -TaskPath $taskPath
      Disable-ScheduledTask -TaskName $taskName -TaskPath $taskPath
    }
  when: item.condition
  delegate_to: "{{ item.server }}"
  loop:
    - name: "MOMSActiveDirectoryPolling"
      server: "{{ moms_app_server }}"
      condition: ad_polling_task
    - name: "TransPortalActiveDirectoryPollingTask"
      server: "{{ transportal_web_alias }}"
      condition: "not {{ active_directory_feature }} and {{ deployment_type }} == 'PRODUCT'"
    - name: "Integrity_MOMSEventLoader"
      server: "{{ integrity_event_loader_exe_server }}"
      condition: integrity_event_loader
  loop_control:
    label: "{{ item.name }} @ {{ item.server }}"
  tags:
    - insight

- name: Stop MOMS Services
  ansible.windows.win_service:
    name: "{{ item.service }}"
    state: stopped
  when: item.condition
  delegate_to: "{{ moms_app_server }}"
  async: 1000
  poll: 5
  loop:
    - service: "MOMSMobileService"
      condition: mobile_feature
    - service: "MOMSService"
      condition: true
    - service: "TransSuiteMOMSService"
      condition: trans_suite_feature
    - service: "InfinityMOMSService"
      condition: infinity_feature
    - service: "IntegrityMOMSService"
      condition: integrity_feature
    - service: "MOMSGPSLocatorService"
      condition: gps_feature
  loop_control:
    label: "{{ item.name }}"
  tags:
    - insight

- name: Stop IIS Web
  ansible.windows.win_service:
    name: "{{ item.service }}"
    state: stopped
  when: item.condition
  delegate_to: "{{ item.server }}"
  async: 1000
  poll: 5
  loop:
    - service: "World Wide Web Publishing Service"
      server: "{{ moms_app_server }}"
      condition: true
    - service: "Net.Tcp Listener Adapter"
      server: "{{ moms_app_server }}"
      condition: true
    - service: "NetTcpActivator"
      server: "{{ moms_app_server }}"
      condition: true
    - service: "Windows Process Activation Service"
      server: "{{ moms_app_server }}"
      condition: true
    - service: "World Wide Web Publishing Service"
      server: "{{ insight_mobile_server }}"
      condition: insight_mobile_feature
    - service: "Net.Tcp Listener Adapter"
      server: "{{ insight_mobile_server }}"
      condition: insight_mobile_feature
    - service: "NetTcpActivator"
      server: "{{ insight_mobile_server }}"
      condition: insight_mobile_feature
    - service: "Windows Process Activation Service"
      server: "{{ insight_mobile_server }}"
      condition: insight_mobile_feature
  loop_control:
    label: "{{ item.service }} @ {{ item.server }}"
  tags:
    - insight

# TODO
- name: Reboot the Windows machines
  ansible.windows.win_reboot:
    reboot_timeout: 600 # Wait up to 10 minutes
    test_command: "whoami" # Ensures WinRM is fully responsive before continuing
  delegate_to: "{{ item }}"
  when: item.condition
  loop:
    - moms_app_server
    - insight_mobile_server
  loop_control:
    label: "{{ item }}"
  tags:
    - insight

# Postinstall Startup
- name: Insert data for Knowledge Base
  debug:
    msg: "TODO: Insert data for Knowledge Base"

# TODO
- name: Update the Release version and date in the TransPortal DB
  ansible.windows.win_shell: |
    sqlcmd -b -U "{{ db_user }}" -P "{{ db_password }}" -Q
    "IF EXISTS (SELECT 1 FROM sys.columns WHERE Name=N'vcTagVer')
    BEGIN EXEC sp_executesql N'update stbApp set vcTagVer=''{{ insight.release_version }}'',
    dtReleaseDate=''{{ insight.dated_deployment_dir }}'' where iAppID=852' END" -S
    "{{ transportal_db_listener }}" -d SSO
  delegate_to: "{{ transportal_db_server }}"
  when: transportal_feature
  tags:
    - insight

- name: Start MOMS Services
  ansible.windows.win_service:
    name: "{{ item.service }}"
    state: started
  when: item.condition
  delegate_to: "{{ moms_app_server }}"
  async: 1000
  poll: 5
  loop:
    - service: "MOMSMobileService"
      condition: mobile_feature
    - service: "MOMSService"
      condition: true
    - service: "TransSuiteMOMSService"
      condition: trans_suite_feature
    - service: "InfinityMOMSService"
      condition: infinity_feature
    - service: "IntegrityMOMSService"
      condition: integrity_feature
    - service: "MOMSGPSLocatorService"
      condition: gps_feature
    - service: "ASP.NET State Service"
      condition: true
  loop_control:
    label: "{{ item.name }}"
  tags:
    - insight

- name: Start IIS Web
  ansible.windows.win_service:
    name: "{{ item.service }}"
    state: started
  when: item.condition
  delegate_to: "{{ item.server }}"
  async: 1000
  poll: 5
  loop:
    - service: "World Wide Web Publishing Service"
      server: "{{ moms_app_server }}"
      condition: true
    - service: "Net.Tcp Listener Adapter"
      server: "{{ moms_app_server }}"
      condition: true
    - service: "NetTcpActivator"
      server: "{{ moms_app_server }}"
      condition: true
    - service: "Windows Process Activation Service"
      server: "{{ moms_app_server }}"
      condition: true
    - service: "World Wide Web Publishing Service"
      server: "{{ insight_mobile_server }}"
      condition: insight_mobile_feature
    - service: "Net.Tcp Listener Adapter"
      server: "{{ insight_mobile_server }}"
      condition: insight_mobile_feature
    - service: "NetTcpActivator"
      server: "{{ insight_mobile_server }}"
      condition: insight_mobile_feature
    - service: "Windows Process Activation Service"
      server: "{{ insight_mobile_server }}"
      condition: insight_mobile_feature
  loop_control:
    label: "{{ item.service }} @ {{ item.server }}"
  tags:
    - insight

- name: Enable MOMS tasks
  ansible.windows.win_shell: |
    $taskName = "{{ item.name }}"
    $taskPath = "\\"
    $markerPath = "\\{{ moms_app_server }}\C$\\MOMS\\ScheduledTasks\\$taskName"

    if (Test-Path $markerPath) {
      $task = Get-ScheduledTask -TaskName $taskName -TaskPath $taskPath -ErrorAction SilentlyContinue
      if ($task) {
        Enable-ScheduledTask -TaskName $taskName -TaskPath $taskPath -ErrorAction SilentlyContinue
      }
    }
  when: "{{ item.condition }} and {{ deployment_type }} not in ('DEV', 'QA')"
  delegate_to: "{{ item.server }}"
  loop:
    - name: "MOMSWorkOrderMessagingTask"
      server: "{{ moms_app_server }}"
      condition: work_order_messaging_feature
    - name: "MOMSFailureAnalysisTaskConsole"
      server: "{{ moms_app_server }}"
      condition: true
    - name: "MOMSInventoryLevelNotification"
      server: "{{ moms_app_server }}"
      condition: true
    - name: "MOMSNotificationEscalationTask"
      server: "{{ moms_app_server }}"
      condition: "and {{ deployment_environment }} != 'PRODUCT'"
    - name: "MOMSPredictiveMaintenanceNotificationTaskConsole"
      server: "{{ moms_app_server }}"
      condition: true
    - name: "MOMSPreventiveMaintenanceTask"
      server: "{{ moms_app_server }}"
      condition: true
    - name: "TransPortal_MOMSEventLoader"
      server: "{{ moms_app_server }}"
      condition: true
    - name: "CPS_MOMSEventLoader"
      server: "{{ moms_app_server }}"
      condition: true
    - name: "VOTT_MOMSEventLoader"
      server: "{{ moms_app_server }}"
      condition: true
    - name: "SolarWinds_MOMSEventLoader"
      server: "{{ moms_app_server }}"
      condition: true
    - name: "WhatsUp_MOMSEventLoader"
      server: "{{ moms_app_server }}"
      condition: true
    - name: "MOMSEventLoader"
      server: "{{ moms_app_server }}"
      condition: true
    - name: "ImageReview_MOMSEventLoader"
      server: "{{ moms_app_server }}"
      condition: true
    - name: "MOMSExternalNotifier"
      server: "{{ moms_app_server }}"
      condition: true
    - name: "MOMSTrafficDataLoader"
      server: "{{ moms_app_server }}"
      condition: true
  loop_control:
    label: "{{ item.name }}"
  tags:
    - insight

- name: Enable MOMS Tasks
  ansible.windows.win_shell: |
    $taskName = "{{ item.name }}"
    $taskPath = "\\"
    $task = Get-ScheduledTask -TaskName $taskName -TaskPath $taskPath -ErrorAction SilentlyContinue
    if ($task) {
      Enable-ScheduledTask -TaskName $taskName -TaskPath $taskPath
    }
  when: "{{ item.condition }} and {{ deployment_type }} not  in ('DEV', 'QA')"
  delegate_to: "{{ item.server }}"
  loop:
    - name: "Integrity_MOMSEventLoader"
      server: "{{ integrity_event_loader_exe_server }}"
      condition: integrity_event_loader
  loop_control:
    label: "{{ item.name }} @ {{ item.server }}"
  tags:
    - insight

- name: Enable MOMS Tasks
  ansible.windows.win_shell: |
    $taskName = "{{ item.name }}"
    $taskPath = "\\"
    $task = Get-ScheduledTask -TaskName $taskName -TaskPath $taskPath -ErrorAction SilentlyContinue
    if ($task) {
      Start-ScheduledTask -TaskName $taskName -TaskPath $taskPath
      Enable-ScheduledTask -TaskName $taskName -TaskPath $taskPath
    }
  when: item.condition
  delegate_to: "{{ item.server }}"
  loop:
    - name: "MOMSActiveDirectoryPolling"
      server: "{{ moms_app_server }}"
      condition: ad_polling_task
  loop_control:
    label: "{{ item.name }} @ {{ item.server }}"
  tags:
    - insight
