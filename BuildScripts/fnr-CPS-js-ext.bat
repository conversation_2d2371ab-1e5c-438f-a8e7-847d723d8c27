@echo off
rem set variables
Set CURRENTDIR=%CD%
Set SOURCE=%1
Set CURRENTSTRING=%2
Set TARGETSTRING=%3

echo %SOURCE%


rem "%CURRENTDIR%\fnr.exe" --cl --dir "%SOURCE%" --fileMask "main*.js" --includeSubDirectories  --alwaysUseEncoding "utf-16" --find "%CURRENTSTRING%/index.html?LandingPage=1&Token=" --replace "%TARGETSTRING%/#/audits/bos-trip-review"

rem "%CURRENTDIR%\PowerShell\Replace-FileString.ps1 '%CURRENTSTRING%/index.html?LandingPage=1&Token=' '%TARGETSTRING%/#/audits/bos-trip-review' main*.js -Overwrite
echo change the landing page!!!!!!
call "%CURRENTDIR%\ReplaceUtil.exe" %SOURCE% js "%CURRENTSTRING%/index.html?LandingPage=1&Token=" "%TARGETSTRING%/#/audits/bos-trip-review" ALL