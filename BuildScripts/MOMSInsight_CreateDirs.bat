@ECHO OFF
set /P drive=Enter the desired Insight Drive (ex. D or F):=
set /P DBdrive=Enter the desired Database Backups Drive (ex. F)=
set /P user=Enter the SSPI user (ex. int\srv_sspiinternaluser)=
mkdir C:\Insight
cacls C:\Insight /t /e /g Users:f
mkdir C:\Insight_bu
cacls C:\Insight_bu /t /e /g Users:f
mkdir C:\MOMS_bu
cacls C:\MOMS_bu /t /e /g Users:f
mkdir C:\MOMS
cacls C:\MOMS /t /e /g Users:f
echo create staging dir
mkdir %drive%:\Staging-Insight
cacls %drive%:\Staging-Insight /t /e /g Users:f
echo create deployment dir
mkdir %drive%:\Deployment
cacls %drive%:\Deployment /t /e /g Users:f
echo create log dir
mkdir %drive%:\MOMSLogs
cacls %drive%:\MOMSLogs /t /e /g Users:f
echo create dbbackup dir
mkdir %DBdrive%:\DBBackups
cacls %DBdrive%:\DBBackups /t /e /g Users:f
mkdir %drive%:\MOMSAttachment
cacls %drive%:\MOMSAttachment /t /e /g Users:f
mkdir %drive%:\MOMSAttachment\WorkOrderDocument
mkdir  %drive%:\MOMSAttachment\PartDocument
mkdir %drive%:\MOMSAttachment\WikiPageDocument
mkdir %drive%:\MOMSAttachment\PartReorderDocument
net share WorkOrderDocument=%drive%:\MOMSAttachment\WorkOrderDocument /GRANT:%user%,FULL
net share PartDocument=%drive%:\MOMSAttachment\PartDocument /GRANT:%user%,FULL
net share WikiPageDocument=%drive%:\MOMSAttachment\WikiPageDocument /GRANT:%user%,FULL
net share PartReorderDocument=%drive%:\MOMSAttachment\PartReorderDocument /GRANT:%user%,FULL
echo done
goto :EOF