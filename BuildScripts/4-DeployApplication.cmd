@ECHO OFF
rem SET /P userInput=This action will deploy (copy) MOMS to the servers, do you wish to continue? (Y/N): 
set NOW=%date:~10,4%-%date:~4,2%-%date:~7,2%
rem IF "%userInput%"=="Y" GOTO DEPLOY
rem IF "%userInput%"=="y" GOTO DEPLOY
set /P createDB= Will you be creating a new DB (Y/N)=
for /f "usebackq delims=" %%I in (`powershell "\"%createDB%\".toUpper()"`) do set "createDB=%%~I"
set /P user=[Enter Project Deployment User i.e. ncta\kelleym]:=
set "psCommand=powershell -Command "$pword = read-host 'Enter Password' -AsSecureString ; ^
    $BSTR=[System.Runtime.InteropServices.Marshal]::SecureStringToBSTR($pword); ^
        [System.Runtime.InteropServices.Marshal]::PtrToStringAuto($BSTR)""
for /f "usebackq delims=" %%p in (`%psCommand%`) do set pw=%%p


:set-create-db
rem let's set the build id so that we can see its output during the build
call fnr.exe --cl --dir "%CURRENTDIR%" --fileMask "ENVIRONMENT.include" --find "CREATEDB" --replace "%createDB%"
@echo Set create DB to %createDB% complete.

:DEPLOY
NAnt.exe -buildfile:MOMS_build.build -logger:NAnt.Core.MailLogger -logfile:AppLogs\4-Deploy.log deploy-MOMS -D:deployment.user=%user% -D:deployment.password=%pw%
rem svn revert -R --non-interactive --trust-server-cert BUILD.include
if %ERRORLEVEL% NEQ 0 GOTO :error 
GOTO END



:error
@echo -----------------------------------------------------------
@echo -----------------------------------------------------------
@echo -e-:%0:**** ERROR DURING DEPLOYMENT
@echo -----------------------------------------------------------						
GOTO END


:END

pause