@echo off
rem set variables
Set CURRENTDIR=%CD%

echo ------- Your current directory is:  %CURRENTDIR% ---------

set NOW=%date:~10,4%-%date:~4,2%-%date:~7,2%
echo Deployment beginning %NOW%
echo -----------------------------------
echo Connect to Server
echo -----------------------------------
verify > nul
SET SERVER=%2
SET USER=%3
SET PASSWORD=%4
SET DEPLOYMENTDRIVE=%5
SET DEPLOYMENTDIR=%6
SET JUMPDRIVE=%7
SET TYPE=%8


ECHO %SERVER%
SET BASEDIR=%JUMPDRIVE%:\Staging-ImageReview\%DEPLOYMENTDIR%
net use m: /delete

echo ------- map drive ----------
net use m: \\%SERVER%\%DEPLOYMENTDRIVE%$ %PASSWORD% /user:%USER%

echo Server: %SERVER% Type: %TYPE%

if %ERRORLEVEL% NEQ 0 GOTO :error 
echo ------- destination cleanup -----------
if exist m:\Deployment-ImageReview\%DEPLOYMENTDIR%\%TYPE%.zip (del /s /q m:\Deployment-ImageReview\%DEPLOYMENTDIR%\%TYPE%.zip)
if %ERRORLEVEL% NEQ 0 GOTO :error 
if exist m:\Deployment-ImageReview\%DEPLOYMENTDIR%\%TYPE% (rmdir /s /q m:\Deployment-ImageReview\%DEPLOYMENTDIR%\%TYPE%)
if %ERRORLEVEL% NEQ 0 GOTO :error 
if exist m:\Deployment-ImageReview\%DEPLOYMENTDIR%\AES (rmdir /s /q m:\Deployment-ImageReview\%DEPLOYMENTDIR%\AES)
if %ERRORLEVEL% NEQ 0 GOTO :error 
if NOT exist m:\Deployment-ImageReview\%DEPLOYMENTDIR% (mkdir m:\Deployment-ImageReview\%DEPLOYMENTDIR%)
if %ERRORLEVEL% NEQ 0 GOTO :error 


echo Error Level is %ERRORLEVEL%
:deploy
echo ------------------------------------
echo Copy NODES....to %SERVER% %TYPE%
echo ------------------------------------
set STEP="Deploy Zips to %SERVER% %TYPE%"
for /F %%i in ('dir /b "%BASEDIR%\%TYPE%.zip"') do (
robocopy /R:3 %BASEDIR% m:\Deployment-ImageReview\%DEPLOYMENTDIR% %TYPE%.zip
)
echo error level %ERRORLEVEL% 
if %ERRORLEVEL% GEQ 8 GOTO :error 
verify > nul
if not exist m:\Deployment-ImageReview\%DEPLOYMENTDIR%\AES.zip (xcopy /R /Y %BASEDIR%\AES.zip m:\Deployment-ImageReview\%DEPLOYMENTDIR%
)
if %ERRORLEVEL% NEQ 0 GOTO :error 

echo DEPLOYMENT COMPLETE
net use m: /delete


echo Nodes Unzip is starting at %fullstamp% 
echo ---------------------------------- 

:Node_Delete_Current_unzips
SET STEP="Remove all unzipped files from %SERVER% %TYPE%"
echo Step %STEP% commencing...  --------- 
ECHO "if exist remove directory for %TYPE%"
rem psexec -accepteula \\%SERVER% cmd /c if exist %DEPLOYMENTDRIVE%:\Deployment-ImageReview\%DEPLOYMENTDIR%\%TYPE% (rmdir /s /q %DEPLOYMENTDRIVE%:\Deployment-ImageReview\%DEPLOYMENTDIR%\%TYPE%)
if %ERRORLEVEL% NEQ 0 GOTO :error
ECHO "if exist remove directory for %TYPE% AES"
rem psexec -accepteula \\%SERVER% cmd /c if exist %DEPLOYMENTDRIVE%:\Deployment-ImageReview\%DEPLOYMENTDIR%\AES (rmdir /s /q %DEPLOYMENTDRIVE%:\Deployment-ImageReview\%DEPLOYMENTDIR%\AES)
if %ERRORLEVEL% NEQ 0 GOTO :error
echo Step %STEP% completed...  --------- 


:UnzipNode
SET STEP="Unzip Nodes %SERVER% %TYPE%"
echo Step %STEP% commencing...  --------- 
call Autobuild\do_unzip_locrem1.cmd %TYPE%.zip %DEPLOYMENTDRIVE%:\Deployment-ImageReview\%DEPLOYMENTDIR%\ -qo %SERVER%
if %ERRORLEVEL% NEQ 0 GOTO :error
call Autobuild\do_unzip_locrem1.cmd AES.zip %DEPLOYMENTDRIVE%:\Deployment-ImageReview\%DEPLOYMENTDIR%\ -qo %SERVER%
if %ERRORLEVEL% NEQ 0 GOTO :error
echo Step %STEP% completed...  --------- 

if %ERRORLEVEL% NEQ 0 GOTO :error
GOTO END


:error
echo ------- AN ERROR OCCURED DURING THIS STEP: %STEP% %SERVER% %TYPE% ------
echo ------- AN ERROR OCCURED DURING THIS STEP: %STEP% %SERVER% %TYPE%------ 
rem NAnt.exe -buildfile:FPClient_build.build -logger:NAnt.Core.MailLogger -logfile:AppLogs\build.log autobuild-error
exit /b 1

:END
echo -- Unzip Packages of Nodes %SERVER% %TYPE% is complete
echo -- Unzip Packages of Nodes %SERVER% %TYPE% is complete 
rem NAnt.exe -buildfile:FPClient_build.build -logger:NAnt.Core.MailLogger -logfile:AppLogs\build.log autobuild-complete
exit /b 0




:error
echo ------- AN ERROR OCCURED DURING THIS STEP: %STEP% ------
exit /b 1

:END