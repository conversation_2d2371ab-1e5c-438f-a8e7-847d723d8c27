﻿<?xml version="1.0" encoding="UTF-8"?>
<project name="CPS" default="build" basedir=".">
  <include buildfile="UserInput.include"/>
  <include buildfile="BUILD.include"/>
  <include buildfile="ENVIRONMENT.include"/>
  <property name="env" value="${deployment.environment}"/>

	<!-- Request User to Confirm Environment as defined in ENVIRONMENT.include"
		<property name="isConfirmEnvironment" value="${userInput::confirmTargetEnvironment(env)}"/>
		<if test="${isConfirmEnvironment != 'true'}">
			<property name="MailLogger.failure.subject" value="CPS- ${env} Environment not confirmed" />
			<fail>Environment not confirmed.</fail>
		</if> -->


	<!-- Echo Environment Target -->
	<echo message="****** Deployment Target is ${env} *******" />

	<!-- Remove all debris from any prior builds -->
	<target name="clean" description="Remove temporary folders">
		<property name="MailLogger.failure.subject" value="CPS - SVN cleanup step failed." />
		<!--<attrib readonly="false">
			<fileset>
				<include name="${build.dir}/**/*.*" />
				<include name="${base.dir}/*.*" />
				<include name="${web01.dist.dir}/**/*.*"/>
				<include name="${web02.dist.dir}/**/*.*"/>
				<include name="${app01.dist.dir}/**/*.*"/>
				<include name="${app02.dist.dir}/**/*.*"/>
				<include name="${rpt01.dist.dir}/**/*.*"/>
			</fileset>
			</attrib>-->

		<!--<delete dir="${base.dir}/BuildScripts" failonerror="false" />-->
		<delete dir="${web01.dist.dir}" failonerror="false" />
		<delete dir="${web02.dist.dir}" failonerror="false" />
		<delete dir="${app01.dist.dir}" failonerror="false" />
		<delete dir="${app02.dist.dir}" failonerror="false" />
		<delete dir="${rpt01.dist.dir}" failonerror="false" />
		<delete dir="${ims.dist.dir}" failonerror="false" />
		<delete dir="${ext01.dist.dir}" failonerror="false" />
		<delete dir="${tableau.dist.dir}" failonerror="false" />
		<delete dir="${build.dir}" failonerror="true" />
		<delete dir="${base.dir}\InfinityCommon-Temp" failonerror="false" />

		<delete>
			<fileset>
			<include name="${base.dir}/*.*"/>
			</fileset>
		</delete>

	</target>

	<!-- Create all default directories -->
	<target name="init" description="create build folders">
			<!-- remove old folders -->
		<call target="clean" />
			<!--<mkdir dir="${build.dir}" />
			<mkdir dir="${web01.dist.dir}" />
			<mkdir dir="${app01.dist.dir}" />-->
			<!--<mkdir dir="${rpt01.dist.dir}" />-->

	</target>

	<target name="checkout" description="Get current version of source">
			<exec program="devopsfetch.bat" failonerror="true">
			<arg value="${devops.dir}" />
			<arg value="${base.dir}" />
			<arg value="${build_id}" />
			<arg value="${devops.repo}" />
			<arg value="${devops.repo.common}" />
			<arg value="${devops.repo.tools}" />
		</exec>
	</target>


	<!-- Build the solutions -->
	<target name="build" description="Build solution">
		<property name="MailLogger.success.subject" value="CPS ${deployment.environment} ${deployment.type}  - Step 1 Build Successfully Completed." />
		<property name="MailLogger.failure.subject" value="CPS ${deployment.environment} ${deployment.type}  - Build step failed" />
				<exec program="fnr-CPS-ts.bat" failonerror="true" >
					<arg value="${build.dir}\CPS\WebUI\ClientApp\src\environments" />
					<arg value="${timeout.seconds}" />
					<arg value="${appTabLabel}" />
					<arg value="${showLandingPage}" />
				</exec>
			<if test="${SSLFeature=='true'}">
				<exec program="ReplaceUtil.exe" failonerror="true">
				<arg value="${build.dir}\CPS\WebUI\ClientApp\src" />
				<arg value="ts"/>
				<arg value="useHttps: false" />
				<arg value="useHttps: true" />
				<arg value="ALL"/>
				</exec>
				<exec program="ReplaceUtil.exe" failonerror="true">
				<arg value="${build.dir}\CPS\WebUI\ClientApp\src" />
				<arg value="ts"/>
				<arg value="http://localhost:4000" />
				<arg value="https://${app.server.cluster}:4443" />
				<arg value="ALL"/>
				</exec>
				<exec program="ReplaceUtil.exe" failonerror="true">
				<arg value="${build.dir}\CPS\WebUI\ClientApp\src" />
				<arg value="ts"/>
				<arg value="http://txp-api:7006/api" />
				<arg value="https://${transportal.web.alias}:4436/api" />
				<arg value="ALL"/>
				</exec>
				<exec program="ReplaceUtil.exe" failonerror="true">
				<arg value="${build.dir}\CPS\WebUI\ClientApp\src" />
				<arg value="ts"/>
				<arg value="http://txp-servername" />
				<arg value="https://${transportal.web.alias}" />
				<arg value="ALL"/>
				</exec>
				<exec program="ReplaceUtil.exe" failonerror="true">
				<arg value="${build.dir}\CPS\WebUI\ClientApp\src" />
				<arg value="ts"/>
				<arg value="http://ims-api:4500" />
				<arg value="https://${ims.cluster.alias}:5443" />
				<arg value="ALL"/>
				</exec>
				<!-- Will change once we get the right values -->
				<exec program="ReplaceUtil.exe" failonerror="true">
				<arg value="${build.dir}\CPS\WebUI\ClientApp\src" />
				<arg value="ts"/>
				<arg value="http://dvas-api" />
				<arg value="https://${dvas.api.alias}" />
				<arg value="ALL"/>
				</exec>
				<exec program="ReplaceUtil.exe" failonerror="true">
				<arg value="${build.dir}\CPS\WebUI\ClientApp\src" />
				<arg value="ts"/>
				<arg value="http://insight-servername" />
				<arg value="https://${insight.web.alias}" />
				<arg value="ALL"/>
				</exec>
				<exec program="ReplaceUtil.exe" failonerror="true">
				<arg value="${build.dir}\CPS\WebUI\ClientApp\src" />
				<arg value="ts"/>
				<arg value="https://localhost:44384/report-viewer" />
				<arg value="https://${web.cluster.alias}/CPSReports/report-viewer" />
				<arg value="ALL"/>
				</exec>
				<exec program="ReplaceUtil.exe" failonerror="true">
				<arg value="${build.dir}\CPS\WebUI\ClientApp\src" />
				<arg value="ts"/>
				<arg value="http://localhost/CPSReports/report-viewer" />
				<arg value="https://${web.cluster.alias}/CPSReports/report-viewer" />
				<arg value="ALL"/>
				</exec>
				<exec program="ReplaceUtil.exe" failonerror="true">
				<arg value="${build.dir}\CPS\WebUI\ClientApp\src" />
				<arg value="ts"/>
				<arg value="ws://app-servername" />
				<arg value="wss://${rabbitmq.cluster}" />
				<arg value="ALL"/>
				</exec>
				<exec program="ReplaceUtil.exe" failonerror="true">
				<arg value="${build.dir}\CPS\WebUI\ClientApp\src" />
				<arg value="ts"/>
				<arg value="http://txp-servername" />
				<arg value="https://${transportal.web.alias}" />
				<arg value="ALL"/>
				</exec>
			</if>
			<if test="${SSLFeature=='false'}">
				<exec program="ReplaceUtil.exe" failonerror="true">
				<arg value="${build.dir}\CPS\WebUI\ClientApp\src" />
				<arg value="ts"/>
				<arg value="http://localhost:4000" />
				<arg value="http://${app.server.cluster}:4000" />
				<arg value="ALL"/>
				</exec>
				<exec program="ReplaceUtil.exe" failonerror="true">
				<arg value="${build.dir}\CPS\WebUI\ClientApp\src" />
				<arg value="ts"/>
				<arg value="http://txp-api:7006/api" />
				<arg value="http://${transportal.web.alias}:7006/api" />
				<arg value="ALL"/>
				</exec>
				<exec program="ReplaceUtil.exe" failonerror="true">
				<arg value="${build.dir}\CPS\WebUI\ClientApp\src" />
				<arg value="ts"/>
				<arg value="http://txp-servername" />
				<arg value="http://${transportal.web.alias}" />
				<arg value="ALL"/>
				</exec>
				<exec program="ReplaceUtil.exe" failonerror="true">
				<arg value="${build.dir}\CPS\WebUI\ClientApp\src" />
				<arg value="ts"/>
				<arg value="http://ims-api:4500" />
				<arg value="http://${ims.cluster.alias}:4500" />
				<arg value="ALL"/>
				</exec>
				<!-- Will change once we get the right values -->
				<exec program="ReplaceUtil.exe" failonerror="true">
				<arg value="${build.dir}\CPS\WebUI\ClientApp\src" />
				<arg value="ts"/>
				<arg value="http://dvas-api" />
				<arg value="http://${dvas.api.alias}" />
				<arg value="ALL"/>
				</exec>
				<exec program="ReplaceUtil.exe" failonerror="true">
				<arg value="${build.dir}\CPS\WebUI\ClientApp\src" />
				<arg value="ts"/>
				<arg value="http://insight-servername" />
				<arg value="http://${insight.web.alias}" />
				<arg value="ALL"/>
				</exec>
				<exec program="ReplaceUtil.exe" failonerror="true">
				<arg value="${build.dir}\CPS\WebUI\ClientApp\src" />
				<arg value="ts"/>
				<arg value="https://localhost:44384/report-viewer" />
				<arg value="http://${web.cluster.alias}/CPSReports/report-viewer" />
				<arg value="ALL"/>
				</exec>
				<exec program="ReplaceUtil.exe" failonerror="true">
				<arg value="${build.dir}\CPS\WebUI\ClientApp\src" />
				<arg value="ts"/>
				<arg value="http://localhost/CPSReports/report-viewer" />
				<arg value="http://${web.cluster.alias}/CPSReports/report-viewer" />
				<arg value="ALL"/>
				</exec>
				<exec program="ReplaceUtil.exe" failonerror="true">
				<arg value="${build.dir}\CPS\WebUI\ClientApp\src" />
				<arg value="ts"/>
				<arg value="ws://app-servername" />
				<arg value="ws://${rabbitmq.cluster}" />
				<arg value="ALL"/>
				</exec>
				<exec program="ReplaceUtil.exe" failonerror="true">
				<arg value="${build.dir}\CPS\WebUI\ClientApp\src" />
				<arg value="ts"/>
				<arg value="http://txp-servername" />
				<arg value="http://${transportal.web.alias}" />
				<arg value="ALL"/>
				</exec>
			</if>
			<exec program="ReplaceUtil.exe" failonerror="true">
				<arg value="${build.dir}\CPS\WebUI\ClientApp\src" />
				<arg value="ts"/>
				<arg value="webLog-User" />
				<arg value="${webLog-User}" />
				<arg value="ALL"/>
			</exec>
			<exec program="ReplaceUtil.exe" failonerror="true">
				<arg value="${build.dir}\CPS\WebUI\ClientApp\src" />
				<arg value="ts"/>
				<arg value="webLog-pwd" />
				<arg value="${webLog-pwd}" />
				<arg value="ALL"/>
			</exec>
			<exec program="ReplaceUtil.exe" failonerror="true">
				<arg value="${build.dir}\CPS\WebUI\ClientApp\src\app\shared\constant" />
				<arg value="ts"/>
				<arg value="1.0.0" />
				<arg value="${release.version}" />
				<arg value="ALL"/>
			</exec>
			<exec program="ReplaceUtil.exe" failonerror="true">
				<arg value="${build.dir}\CPS\WebUI\ClientApp\src\app\shared\constant" />
				<arg value="ts"/>
				<arg value="11/08/2019" />
				<arg value="${dated.deployment.dir}" />
				<arg value="ALL"/>
			</exec>
			<exec program="ReplaceUtil.exe" failonerror="true">
				<arg value="${build.dir}\CPS\WebUI\ClientApp\src" />
				<arg value="ts"/>
				<arg value="enableHub: true" />
				<arg value="enableHub: ${enableSSOHub}" />
				<arg value="ALL"/>
			</exec>

			<if test="${TransPortalFeature=='true'}">
			<exec program="ReplaceUtil.exe" failonerror="true">
				<arg value="${build.dir}\CPS\WebUI\ClientApp\src\app\shared\constant" />
				<arg value="ts"/>
				<arg value="isSSO:true" />
				<arg value="isSSO:true" />
				<arg value="ALL"/>
			</exec>
			</if>
			<if test="${TransPortalFeature=='false'}">
			<exec program="ReplaceUtil.exe" failonerror="true">
				<arg value="${build.dir}\CPS\WebUI\ClientApp\src\app\shared\constant" />
				<arg value="ts"/>
				<arg value="isSSO:true" />
				<arg value="isSSO:false" />
				<arg value="ALL"/>
			</exec>
			</if>
			<exec program="ReplaceUtil.exe" failonerror="true">
				<arg value="${build.dir}\CPS\WebUI\ClientApp\src" />
				<arg value="ts"/>
				<arg value="CONGESTION PRICING SYSTEM" />
				<arg value="${appTitle}" />
				<arg value="ALL"/>
			</exec>

		<call target="build-CPS-dotnet" failonerror="true" />
		<call target="build-CPS-webreportviewer" failonerror="true" />

		<if test="${TPIFeature=='true'}">
			<call target="build-CPS-tpi" failonerror="true" />
		</if>

		<if test="${DATAANALYTICFeature=='true'}">
			<call target="build-CPS-python" failonerror="true" />
		</if>

		<if test="${AARFeature=='true'}">
			<call target="build-CPS-aar" failonerror="true" />
		</if>
		<if test="${RaasAcknowledgementFeature=='true'}">
			<call target="build-CPS-raasack" failonerror="true" />
		</if>
		<if test="${IASFeature=='true'}">
			<call target="build-CPS-ias" failonerror="true" />
		</if>
		<if test="${DMVFeature=='true'}">
			<call target="build-CPS-dmv" failonerror="true" />
		</if>

		<if test="${NYCDOTFeature=='true'}">
			<call target="build-CPS-nycdot" failonerror="true" />
		</if>

		<if test="${VRGFeature=='true'}">
			<call target="build-CPS-vrg" failonerror="true" />
		</if>

		<call target="build-CPS-ims" failonerror="true" />
		<call target="build-CPS-dm" failonerror="true" />
		<call target="build-CPS-cfts" failonerror="true" />

		<if test="${TransComServiceFeature=='true'}">
			<call target="build-CPS-transcom" failonerror="true" />
		</if>

		<call target="build-CPS-webui" failonerror="true" />
		<if test="${BOSExternalWebFeature=='true'}">
		<!-- building twice
		<copy todir="${build.dir}\CPS\ExtWebUI">
				<fileset basedir="${build.dir}\CPS\WebUI">
					<include name="**/*" />
				</fileset>
		</copy>
			<if test="${SSLFeature=='true'}">
				<exec program="ReplaceUtil.exe" failonerror="true">
				<arg value="${build.dir}\CPS\ExtWebUI\ClientApp\src" />
				<arg value="js"/>
				<arg value="https://${app.server.cluster}:4443" />
				<arg value="https://${ext.cps.api.alias}" />
				<arg value="ALL"/>
				</exec>
				<exec program="ReplaceUtil.exe" failonerror="true">
				<arg value="${build.dir}\CPS\ExtWebUI\ClientApp\src" />
				<arg value="js"/>
				<arg value="https://${ims.cluster.alias}:5443" />
				<arg value="https://${ext.ims.api.alias}" />
				<arg value="ALL"/>
				</exec>
				<exec program="ReplaceUtil.exe" failonerror="true">
				<arg value="${build.dir}\CPS\ExtWebUI\ClientApp\src" />
				<arg value="js"/>
				<arg value="https://${dvas.api.alias}" />
				<arg value="https://${ext.dvas.api.alias}" />
				<arg value="ALL"/>
				</exec>
				<exec program="fnr-CPS-js-ext.bat" failonerror="true" >
					<arg value="${build.dir}\CPS\ExtWebUI\ClientApp\src" />
					<arg value="https://${insight.web.alias}" />
					<arg value="https://${ext.cps.web.alias}" />
				</exec>
			</if>
			<if test="${SSLFeature=='false'}">
				<exec program="ReplaceUtil.exe" failonerror="true">
				<arg value="${build.dir}\CPS\ExtWebUI\ClientApp\src" />
				<arg value="js"/>
				<arg value="http://${app.server.cluster}:4000" />
				<arg value="http://${ext.cps.api.alias}" />
				<arg value="ALL"/>
				</exec>
				<exec program="ReplaceUtil.exe" failonerror="true">
				<arg value="${build.dir}\CPS\ExtWebUI\ClientApp\src" />
				<arg value="js"/>
				<arg value="http://${ims.cluster.alias}:4500" />
				<arg value="http://${ext.ims.api.alias}" />
				<arg value="ALL"/>
				</exec>
				<exec program="ReplaceUtil.exe" failonerror="true">
				<arg value="${build.dir}\CPS\ExtWebUI\ClientApp\src" />
				<arg value="js"/>
				<arg value="http://${dvas.api.alias}" />
				<arg value="http://${ext.dvas.api.alias}" />
				<arg value="ALL"/>
				</exec>
				<exec program="fnr-CPS-js-ext.bat" failonerror="true" >
					<arg value="${build.dir}\CPS\ExtWebUI\ClientApp\src" />
					<arg value="http://${insight.web.alias}" />
					<arg value="http://${ext.cps.web.alias}" />
				</exec>
			</if>
				<call target="build-CPS-extwebui" failonerror="true" />-->
			</if>

		<if test="${SimulatorFeature=='true'}">
			<call target="build-CPS-simulators" failonerror="true" />
		</if>
	</target>

	<!-- Build AAR  -->
	<target name="build-CPS-aar" description="Build AAR Website Solution">
		<property name="MailLogger.failure.subject" value="CPS Build AAR Website Solution  failed" />
		<!--<exec program="nuget" failonerror="true">
			<arg value="restore" />
			<arg value="${build.dir}\Services\AAR\AAR.sln" />
		</exec>		-->
		<exec program="C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\MSBuild\Current\Bin\msbuild.exe" failonerror="true">
			<arg value="${build.dir}\Services\AAR\AAR.sln" />
			<arg value="/p:Configuration=Release" />
			<arg value="/p:Version=${assembly.version}" />
		</exec>
	</target>

	<!-- Build VRG  -->
	<target name="build-CPS-vrg" description="Build VRG Solution">
		<property name="MailLogger.failure.subject" value="CPS Build VRG Solution  failed" />
		<!--<exec program="nuget" failonerror="true">
			<arg value="restore" />
			<arg value="${build.dir}\Services\VRG\VRG.sln" />
		</exec>	-->
		<exec program="C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\MSBuild\Current\Bin\msbuild.exe" failonerror="true">
			<arg value="${build.dir}\Services\VRG\VRG.sln" />
			<arg value="/p:Configuration=Release" />
			<arg value="/p:Version=${assembly.version}" />
		</exec>
	</target>

	<!-- Build IMS  -->
	<target name="build-CPS-ims" description="Build IMS Solution">
		<property name="MailLogger.failure.subject" value="CPS Build IMS Solution  failed" />
		<!--<exec program="nuget" failonerror="true">
			<arg value="restore" />
			<arg value="${build.dir}\Services\IMS\IMS.sln" />
		</exec>		-->
		<exec program="C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\MSBuild\Current\Bin\msbuild.exe" failonerror="true">
			<arg value="${build.dir}\Services\IMS\IMS.sln" />
			<arg value="/p:Configuration=Release" />
			<arg value="/p:Version=${assembly.version}" />
		</exec>
	</target>

	<!-- Build DM  -->
	<target name="build-CPS-dm" description="Build DM Solution">
		<property name="MailLogger.failure.subject" value="CPS Build DM Solution  failed" />
		<!--<exec program="nuget" failonerror="true">
			<arg value="restore" />
			<arg value="${build.dir}\Services\DM\DM.sln" />
			<arg value="/p:Version=${assembly.version}" />
		</exec>		-->
		<exec program="C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\MSBuild\Current\Bin\msbuild.exe" failonerror="true">
			<arg value="${build.dir}\Services\DM\DM.sln" />
			<arg value="/p:Configuration=Release" />
			<arg value="/p:Version=${assembly.version}" />
		</exec>
	</target>

	<!-- Build CFTS  -->
	<target name="build-CPS-cfts" description="Build CFTS Solution">
		<property name="MailLogger.failure.subject" value="CPS Build CFTS Solution  failed" />
		<!--<exec program="nuget" failonerror="true">
			<arg value="restore" />
			<arg value="${build.dir}\Services\ConfigTrackingService\ConfigTrackingService.sln" />
		</exec>			-->
		<exec program="C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\MSBuild\Current\Bin\msbuild.exe" failonerror="true">
			<arg value="${build.dir}\Services\ConfigTrackingService\ConfigTrackingService.sln" />
			<arg value="/p:Configuration=Release" />
			<arg value="/p:Version=${assembly.version}" />
		</exec>
	</target>

	<!-- Build TransComService  -->
	<target name="build-CPS-transcom" description="Build TransComService Solution">
		<property name="MailLogger.failure.subject" value="CPS Build TransComService Solution  failed" />
		<!--<exec program="nuget" failonerror="true">
			<arg value="restore" />
			<arg value="${build.dir}\Services\TransComService\TransComService.sln" />
		</exec>	-->
		<exec program="C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\MSBuild\Current\Bin\msbuild.exe" failonerror="true">
			<arg value="${build.dir}\Services\TransComService\TransComService.sln" />
			<arg value="/p:Configuration=Release" />
			<arg value="/p:Version=${assembly.version}" />
		</exec>
	</target>

	<!-- Build TPI (Third Party Interface) -->
	<target name="build-CPS-tpi" description="Build TPI Website Solution">
		<property name="MailLogger.failure.subject" value="CPS Build TPI API Solution  failed" />
		<exec program="nuget" failonerror="true">
			<arg value="restore" />
			<arg value="${build.dir}\Services\TPI\Tcore.CPS.TPI.sln" />
		</exec>
		<exec program="C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\MSBuild\Current\Bin\msbuild.exe" failonerror="true">
			<arg value="${build.dir}\Services\TPI\Tcore.CPS.TPI.sln" />
			<arg value="/p:Configuration=Release" />
			<arg value="/p:Version=${assembly.version}" />
		</exec>
	</target>

	<!-- Build RAAS Acknowledge -->
	<target name="build-CPS-raasack" description="Build RaasAcknowledge Website Solution">
		<property name="MailLogger.failure.subject" value="CPS Build RaasAcknowledge Website Solution  failed" />
		<!--<exec program="nuget" failonerror="true">
			<arg value="restore" />
			<arg value="${build.dir}\Services\RaasAcknowledge\RaasAcknowledge.sln" />
		</exec>		-->
		<exec program="C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\MSBuild\Current\Bin\msbuild.exe" failonerror="true">
			<arg value="${build.dir}\Services\RaasAcknowledge\RaasAcknowledge.sln" />
			<arg value="/p:Configuration=Release" />
			<arg value="/p:Version=${assembly.version}" />
		</exec>
	</target>

	<!-- Build IAS -->
	<target name="build-CPS-ias" description="Build IAS WebAPI Solution">
		<property name="MailLogger.failure.subject" value="CPS Build IAS WebAPISolution  failed" />
		<!--<exec program="nuget" failonerror="true">
			<arg value="restore" />
			<arg value="${build.dir}\Services\IAS\IAS.sln" />
		</exec>			-->
		<exec program="C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\MSBuild\Current\Bin\msbuild.exe" failonerror="true">
			<arg value="${build.dir}\Services\IAS\IAS.sln" />
			<arg value="/p:Configuration=Release" />
			<arg value="/p:Version=${assembly.version}" />
		</exec>
	</target>

	<!-- Build DMV -->
	<target name="build-CPS-dmv" description="Build DMV Solution">
		<property name="MailLogger.failure.subject" value="CPS Build DMV failed" />
		<!--<exec program="nuget" failonerror="true">
			<arg value="restore" />
			<arg value="${build.dir}\Services\DMV\DMV.sln" />
		</exec>		-->
		<exec program="nuget" failonerror="true">
			<arg value="restore" />
			<arg value="${build.dir}\Common\Core\Core.sln" />
		</exec>
		<exec program="C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\MSBuild\Current\Bin\msbuild.exe" failonerror="true">
			<arg value="${build.dir}\Common\Core\Core.sln" />
			<arg value="/p:Configuration=Release" />
		</exec>
		<exec program="C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\MSBuild\Current\Bin\msbuild.exe" failonerror="true">
			<arg value="${build.dir}\Services\DMV\DMV.sln" />
			<arg value="/p:Configuration=Release" />
			<!--<arg value="/p:Version=${assembly.version}" />-->
		</exec>
	</target>

	<!-- Build NYCDOT -->
	<target name="build-CPS-nycdot" description="Build NYCDOT Solution">
		<property name="MailLogger.failure.subject" value="CPS Build NYCDOT failed" />
		<!--<exec program="nuget" failonerror="true">
			<arg value="restore" />
			<arg value="${build.dir}\Services\NYCDOT\NYCDOT.sln" />
		</exec>-->
		<exec program="C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\MSBuild\Current\Bin\msbuild.exe" failonerror="true">
			<arg value="${build.dir}\Services\NYCDOT\NYCDOT.sln" />
			<arg value="/p:Configuration=Release" />
			<arg value="/p:Version=${assembly.version}" />
		</exec>
	</target>

	<!-- Build Web Report Viewer -->
	<target name="build-CPS-webreportviewer" description="Build Web Report Viewer project">
		<property name="MailLogger.failure.subject" value="CPS Build Web Report Viewer project failed" />
		<exec program="nuget" failonerror="true">
			<arg value="restore" />
			<arg value="${build.dir}\CPS\CPS.sln" />
		</exec>
		<exec program="C:\Program Files (x86)\Microsoft Visual Studio\2022\BuildTools\MSBuild\Current\Bin\msbuild.exe" failonerror="true">
			<arg value="${build.dir}\CPS\WebReportViewer\WebReportViewer.csproj" />
			<arg value="/p:Configuration=Release" />
			<arg value="/p:Version=${assembly.version}" />
		</exec>
	</target>

	<!-- Build CPS dotnet-->
	<target name="build-CPS-dotnet" description="Build CPS dotnet">
		<property name="MailLogger.failure.subject" value="CPS ${deployment.environment} ${deployment.type}  - dotnet build failed" />
		<exec program="compiledotnet.bat" failonerror="true">
			<arg value="${build.dir}" />
			<arg value="${base.dir}" />
			<arg value="${ZPIProcessTask}" />
			<arg value="${AARFeature}" />
			<arg value="${DMVFeature}" />
			<arg value="${IASFeature}" />
			<arg value="${NYCDOTFeature}" />
			<arg value="${RaasAcknowledgementFeature}" />
			<arg value="${TPIFeature}" />
			<arg value="${WorkflowFeature}" />
			<arg value="${VRGFeature}" />
			<arg value="${TableauFeature}" />
			<arg value="${DNAMatchFeature}" />
			<arg value="${TransComServiceFeature}" />
			<arg value="${DNAMatchCPSVersion}" />
			<arg value="${DNAMatchCommonVersion}" />
			<arg value="${assembly.version}" />
		</exec>
	</target>

	<target name="build-CPS-python" description="Build CPS python">
		<property name="MailLogger.failure.subject" value="CPS ${deployment.environment} ${deployment.type}  - python build failed" />
		<exec program="compilepython.bat" failonerror="true">
		<arg value="${build.dir}" />
		<arg value="${base.dir}" />
		</exec>
	</target>

	<!-- Build CPS webui-->
	<target name="build-CPS-webui" description="Build CPS angular webui">
		<property name="MailLogger.failure.subject" value="CPS ${deployment.environment} ${deployment.type} - webui build  failed" />
		<exec program="compilewebui.bat" failonerror="true">
		<arg value="${build.dir}" />
		<arg value="${base.dir}" />
		<arg value="${deployment.type}" />
		<arg value="WebUI" />
		</exec>
	</target>

	<!-- Build CPS extwebui-->
	<target name="build-CPS-extwebui" description="Build CPS angular webui">
		<property name="MailLogger.failure.subject" value="CPS ${deployment.environment} ${deployment.type} - extwebui build  failed" />
		<exec program="compilewebui.bat" failonerror="true">
		<arg value="${build.dir}" />
		<arg value="${base.dir}" />
		<arg value="${deployment.type}" />
		<arg value="ExtWebUI" />
		</exec>
	</target>

	<!-- Build CPS simulators-->
	<target name="build-CPS-simulators" description="Build CPS simulators">
		<property name="MailLogger.failure.subject" value="CPS ${deployment.environment} ${deployment.type} - simulator build failed" />
		<exec program="compilesimulators.bat" failonerror="true">
		<arg value="${build.dir}" />
		<arg value="${base.dir}" />
		</exec>
	</target>

	<!-- Ready the solution for deployment -->
	<target name="distribute" description="Distribute the files to their distribution folders">
		<property name="MailLogger.failure.subject" value="CPS ${deployment.environment} ${deployment.type} - Distribution step failed" />
		<property name="MailLogger.success.subject" value="CPS ${deployment.environment} ${deployment.type} - Step 2 Distribution Successfully Completed." />
		<call target="distribute-web" failonerror="true"/>

		<if test="${TableauFeature=='true'}">
			<call target="distribute-tableau" failonerror="true"/>
		</if>
		<if test="${AARFeature=='true'}">
			<call target="distribute-aar" failonerror="true"/>
		</if>

		<if test="${VRGFeature=='true'}">
			<call target="distribute-vrg" failonerror="true"/>
		</if>

		<call target="distribute-ims" failonerror="true"/>

		<call target="distribute-dm" failonerror="true"/>

		<call target="distribute-cfts" failonerror="true"/>

		<if test="${TPIFeature=='true'}">
			<call target="distribute-tpi" failonerror="true"/>
		</if>
		<!--<call target="distribute-tpi-webhookservice" failonerror="true"/>-->

		<if test="${RaasAcknowledgementFeature=='true'}">
			<call target="distribute-raasack" failonerror="true"/>
		</if>
		<if test="${IASFeature=='true'}">
			<call target="distribute-ias" failonerror="true"/>
		</if>
		<if test="${DMVFeature=='true'}">
			<call target="distribute-dmv" failonerror="true"/>
		</if>
		<if test="${NYCDOTFeature=='true'}">
			<call target="distribute-nycdot" failonerror="true"/>
		</if>
		<if test="${DATAANALYTICFeature=='true'}">
			<call target="distribute-dataanalytics" failonerror="true"/>
		</if>
		<if test="${TransComServiceFeature=='true'}">
			<call target="distribute-transcom" failonerror="true"/>
		</if>
	</target>


	<!-- Distribute the web folders -->
	<target name="distribute-web" description="Ready the solution for deployment of CPS Web">
		<property name="MailLogger.failure.subject" value="CPS ${deployment.environment} ${deployment.type} - Distribution of web step failed" />
		<copy todir="${web01.dist.dir}\CPS\WebUI">
				<fileset basedir="${build.dir}\CPS\WebUI\ClientApp\dist">
					<include name="**/*" />
				</fileset>
		</copy>
		<copy todir="${web01.dist.dir}\CPS\WebUI">
				<fileset basedir="${build.dir}\CPS\WebUI">
					<include name="web.config" />
				</fileset>
		</copy>
		<copy todir="${web01.dist.dir}\CPS\WebReportViewer">
			<fileset basedir="${build.dir}\CPS\WebReportViewer">
				<include name="**/*" />
				<exclude name="aspnet_client" />
				<exclude name="**/bin/*.cs" />
				<exclude name="**/.svn/*.*" />
				<exclude name="**/**/.svn/*.*" />
				<exclude name="**/**/**/.svn/*.*" />
				<exclude name="**/*.cs" />
			</fileset>
			</copy>
		<if test="${BOSExternalWebFeature=='true'}">
		<!-- for building twice
		<copy todir="${web01.dist.dir}\CPS\ExtWebUI">
				<fileset basedir="${build.dir}\CPS\ExtWebUI\ClientApp\dist">
					<include name="**/*" />
				</fileset>
		</copy>
		<copy todir="${web01.dist.dir}\CPS\ExtWebUI">
				<fileset basedir="${build.dir}\CPS\ExtWebUI">
					<include name="web.config" />
				</fileset>
		</copy>
		-->
		<copy todir="${web01.dist.dir}\CPS\ExtWebUI">
				<fileset basedir="${build.dir}\CPS\WebUI\ClientApp\dist">
					<include name="**/*" />
				</fileset>
		</copy>
		<copy todir="${web01.dist.dir}\CPS\ExtWebUI">
				<fileset basedir="${build.dir}\CPS\WebUI">
					<include name="web.config" />
				</fileset>
		</copy>

		<!--<copy todir="${build.dir}\CPS\ExtWebUI">
				<fileset basedir="${build.dir}\CPS\WebUI">
					<include name="**/*" />
				</fileset>
		</copy> -->
			<if test="${SSLFeature=='true'}">
				<exec program="ReplaceUtil.exe" failonerror="true">
				<arg value="${web01.dist.dir}\CPS\ExtWebUI" />
				<arg value="js"/>
				<arg value="https://${app.server.cluster}:4443" />
				<arg value="https://${ext.cps.api.alias}" />
				<arg value="ALL"/>
				</exec>
				<exec program="ReplaceUtil.exe" failonerror="true">
				<arg value="${web01.dist.dir}\CPS\ExtWebUI" />
				<arg value="js"/>
				<arg value="https://${ims.cluster.alias}:5443" />
				<arg value="https://${ext.ims.api.alias}" />
				<arg value="ALL"/>
				</exec>
				<exec program="ReplaceUtil.exe" failonerror="true">
				<arg value="${web01.dist.dir}\CPS\ExtWebUI" />
				<arg value="js"/>
				<arg value="https://${dvas.api.alias}" />
				<arg value="https://${ext.dvas.api.alias}" />
				<arg value="ALL"/>
				</exec>
				<exec program="ReplaceUtil.exe" failonerror="true">
				<arg value="${web01.dist.dir}\CPS\ExtWebUI" />
				<arg value="js"/>
				<arg value="https://${transportal.web.alias}:4436/api" />
				<arg value="https://${ext.txp.api.alias}/api" />
				<arg value="ALL"/>
				</exec>
				<exec program="fnr-CPS-js-ext.bat" failonerror="true" >
					<arg value="${web01.dist.dir}\CPS\ExtWebUI" />
					<arg value="https://${insight.web.alias}" />
					<arg value="https://${ext.cps.web.alias}" />
				</exec>

			</if>
			<if test="${SSLFeature=='false'}">
				<exec program="ReplaceUtil.exe" failonerror="true">
				<arg value="${web01.dist.dir}\CPS\ExtWebUI" />
				<arg value="js"/>
				<arg value="http://${app.server.cluster}:4000" />
				<arg value="https://${ext.cps.api.alias}" />
				<arg value="ALL"/>
				</exec>
				<exec program="ReplaceUtil.exe" failonerror="true">
				<arg value="${web01.dist.dir}\CPS\ExtWebUI" />
				<arg value="js"/>
				<arg value="http://${ims.cluster.alias}:4500" />
				<arg value="https://${ext.ims.api.alias}" />
				<arg value="ALL"/>
				</exec>
				<exec program="ReplaceUtil.exe" failonerror="true">
				<arg value="${web01.dist.dir}\CPS\ExtWebUI" />
				<arg value="js"/>
				<arg value="http://${dvas.api.alias}" />
				<arg value="https://${ext.dvas.api.alias}" />
				<arg value="ALL"/>
				</exec>
				<exec program="ReplaceUtil.exe" failonerror="true">
				<arg value="${web01.dist.dir}\CPS\ExtWebUI" />
				<arg value="js"/>
				<arg value="http://${transportal.web.alias}:7006/api" />
				<arg value="https://${ext.txp.api.alias}/api" />
				<arg value="ALL"/>
				</exec>
				<exec program="fnr-CPS-js-ext.bat" failonerror="true" >
					<arg value="${web01.dist.dir}\CPS\ExtWebUI" />
					<arg value="http://${insight.web.alias}" />
					<arg value="https://${ext.cps.web.alias}" />
				</exec>
			</if>
		</if>

	</target>

	<!-- Distribute the AAR folders -->
	<target name="distribute-aar" description="Ready the solution for deployment of CPS AAR">
		<property name="MailLogger.failure.subject" value="CPS ${deployment.environment} ${deployment.type} - Distribution of AAR step failed" />

		<copy todir="${ext01.dist.dir}\CPS\AAR">
			<fileset basedir="${build.dir}\Services\AAR\AAR.Service\bin\Release">
				<include name="**/*" />
			</fileset>
		</copy>
		<copy todir="${ext01.dist.dir}\CPS\AAR">
			<fileset basedir="${build.dir}\Services\AAR\AAR.Service">
				<include name="AAR.Service.xml" />
			</fileset>
		</copy>
	</target>

	<!-- Distribute the VRG folders -->
	<target name="distribute-vrg" description="Ready the solution for deployment of CPS VRG">
		<property name="MailLogger.failure.subject" value="CPS ${deployment.environment} ${deployment.type} - Distribution of VRG step failed" />

		<copy todir="${app01.dist.dir}\VRG\VRGService">
			<fileset basedir="${build.dir}\Services\VRG\VRG.Worker\bin\Release">
				<include name="**/*" />
			</fileset>
		</copy>
	</target>

	<!-- Distribute the IMS folders -->
	<target name="distribute-ims" description="Ready the solution for deployment of CPS IMS">
		<property name="MailLogger.failure.subject" value="CPS ${deployment.environment} ${deployment.type} - Distribution of IMS step failed" />

		<copy todir="${dist.dir}\IMS\API">
			<fileset basedir="${build.dir}\Services\IMS\IMS.API\bin\Release">
				<include name="**/*" />
			</fileset>
		</copy>
		<copy todir="${dist.dir}\IMS\API">
			<fileset basedir="${build.dir}\Services\IMS\IMS.API">
				<include name="IMS.API.xml" />
			</fileset>
		</copy>
		<copy todir="${dist.dir}\IMS\API">
			<fileset basedir="${build.dir}\Services\IMS\IMS.API">
				<include name="web.config" />
			</fileset>
		</copy>
	</target>

	<!-- Distribute the DM folders -->
	<target name="distribute-dm" description="Ready the solution for deployment of CPS DM">
		<property name="MailLogger.failure.subject" value="CPS ${deployment.environment} ${deployment.type} - Distribution of DM step failed" />

		<copy todir="${app01.dist.dir}\DM\API">
			<fileset basedir="${build.dir}\Services\DM\DM.API\bin\Release">
				<include name="**/*" />
			</fileset>
		</copy>
		<copy todir="${app01.dist.dir}\DM\API">
			<fileset basedir="${build.dir}\Services\DM\DM.API">
				<include name="DM.API.xml" />
			</fileset>
		</copy>
		<copy todir="${app01.dist.dir}\DM\API">
			<fileset basedir="${build.dir}\Services\DM\DM.API">
				<include name="web.config" />
			</fileset>
		</copy>
	</target>

	<!-- Distribute the CFTS folders -->
	<target name="distribute-cfts" description="Ready the solution for deployment of CPS CFTS">
		<property name="MailLogger.failure.subject" value="CPS ${deployment.environment} ${deployment.type} - Distribution of CFTS step failed" />

		<copy todir="${app01.dist.dir}\CFTS\ConfigTrackingService">
			<fileset basedir="${build.dir}\Services\ConfigTrackingService\ConfigTrackingService\bin\x64\Release">
				<include name="**/*" />
			</fileset>
		</copy>
		<copy todir="${app01.dist.dir}\CFTS\ConfigTrackingService">
			<fileset basedir="${build.dir}\Services\ConfigTrackingService\ConfigTrackingService">
				<include name="web.config" />
			</fileset>
		</copy>
	</target>

	<!-- Distribute the TRANSCOM folders -->
	<target name="distribute-transcom" description="Ready the solution for deployment of CPS TransCom">
		<property name="MailLogger.failure.subject" value="CPS ${deployment.environment} ${deployment.type} - Distribution of TransCom step failed" />

		<copy todir="${app01.dist.dir}\TransComService">
			<fileset basedir="${build.dir}\Services\TransComService\TransComServiceMain\bin\x64\Release">
				<include name="**/*" />
			</fileset>
		</copy>
	</target>

	<!-- Distribute the TPI folders -->
	<target name="distribute-tpi" description="Ready the solution for deployment of CPS TPI">
		<property name="MailLogger.failure.subject" value="CPS ${deployment.environment} ${deployment.type} - Distribution of TPI step failed" />
		<if test="${directory::exists(build.dir + '\Services\TPI\Tcore.CPS.TPI\bin\Release\net5.0')}" >
		<copy todir="${ext01.dist.dir}\CPS\TPI">
			<fileset basedir="${build.dir}\Services\TPI\Tcore.CPS.TPI\bin\Release\net5.0">
				<include name="**/*" />
			</fileset>
		</copy>
		</if>
		<if test="${not directory::exists(build.dir + '\Services\TPI\Tcore.CPS.TPI\bin\Release\net5.0')}" >
		<copy todir="${ext01.dist.dir}\CPS\TPI">
			<fileset basedir="${build.dir}\Services\TPI\Tcore.CPS.TPI\bin\Release">
				<include name="**/*" />
			</fileset>
		</copy>
		</if>

		<copy todir="${ext01.dist.dir}\CPS\TPI">
			<fileset basedir="${build.dir}\Services\TPI\Tcore.CPS.TPI">
				<include name="Tcore.CPS.TPI.XML" />
			</fileset>
		</copy>
		<if test="${directory::exists(build.dir + '\Services\TPI\Tcore.CPS.TPI.WebhookService\bin\Release\net5.0')}" >
		<copy todir="${ext01.dist.dir}\CPS\TPI">
			<fileset basedir="${build.dir}\Services\TPI\Tcore.CPS.TPI.WebhookService\bin\Release\net5.0">
				<include name="Tcore.CPS.TPI.WebhookService_Test.dll" />
			</fileset>
		</copy>
		</if>
		<if test="${not directory::exists(build.dir + '\Services\TPI\Tcore.CPS.TPI.WebhookService\bin\Release\net5.0')}" >
		<copy todir="${ext01.dist.dir}\CPS\TPI">
			<fileset basedir="${build.dir}\Services\TPI\Tcore.CPS.TPI.WebhookService\bin\Release">
				<include name="Tcore.CPS.TPI.WebhookService_Test.dll" />
			</fileset>
		</copy>
		</if>
		<if test="${directory::exists(build.dir + '\Services\TPI\Tcore.CPS.TPI.SharedBusiness\bin\Release\net5.0')}" >
		<copy todir="${ext01.dist.dir}\CPS\TPI">
			<fileset basedir="${build.dir}\Services\TPI\Tcore.CPS.TPI.SharedBusiness\bin\Release\net5.0">
				<include name="Tcore.CPS.TPI.SharedBusiness_Test.dll" />
			</fileset>
		</copy>
		</if>
		<if test="${not directory::exists(build.dir + '\Services\TPI\Tcore.CPS.TPI.SharedBusiness\bin\Release\net5.0')}" >
		<copy todir="${ext01.dist.dir}\CPS\TPI">
			<fileset basedir="${build.dir}\Services\TPI\Tcore.CPS.TPI.SharedBusiness\bin\Release">
				<include name="Tcore.CPS.TPI.SharedBusiness_Test.dll" />
			</fileset>
		</copy>
		</if>
		<!-- Deploy second TPIAPI -->
		<if test="${directory::exists(build.dir + '\Services\TPI\Tcore.CPS.TPI\bin\Release\net5.0')}" >
		<copy todir="${ext01.dist.dir}\CPS\TPI">
			<fileset basedir="${build.dir}\Services\TPI\Tcore.CPS.TPI\bin\Release\net5.0">
				<include name="**/*" />
			</fileset>
		</copy>
		</if>
		<if test="${not directory::exists(build.dir + '\Services\TPI\Tcore.CPS.TPI\bin\Release\net5.0')}" >
		<copy todir="${ext01.dist.dir}\CPS\TPI">
			<fileset basedir="${build.dir}\Services\TPI\Tcore.CPS.TPI\bin\Release">
				<include name="**/*" />
			</fileset>
		</copy>
		</if>

		<copy todir="${ext01.dist.dir}\CPS\TPI02">
			<fileset basedir="${build.dir}\Services\TPI\Tcore.CPS.TPI">
				<include name="Tcore.CPS.TPI.XML" />
			</fileset>
		</copy>
		<if test="${directory::exists(build.dir + '\Services\TPI\Tcore.CPS.TPI.WebhookService\bin\Release\net5.0')}" >
		<copy todir="${ext01.dist.dir}\CPS\TPI02">
			<fileset basedir="${build.dir}\Services\TPI\Tcore.CPS.TPI.WebhookService\bin\Release\net5.0">
				<include name="Tcore.CPS.TPI.WebhookService_Test.dll" />
			</fileset>
		</copy>
		</if>
		<if test="${not directory::exists(build.dir + '\Services\TPI\Tcore.CPS.TPI.WebhookService\bin\Release\net5.0')}" >
		<copy todir="${ext01.dist.dir}\CPS\TPI02">
			<fileset basedir="${build.dir}\Services\TPI\Tcore.CPS.TPI.WebhookService\bin\Release">
				<include name="Tcore.CPS.TPI.WebhookService_Test.dll" />
			</fileset>
		</copy>
		</if>
		<if test="${directory::exists(build.dir + '\Services\TPI\Tcore.CPS.TPI.SharedBusiness\bin\Release\net5.0')}" >
		<copy todir="${ext01.dist.dir}\CPS\TPI02">
			<fileset basedir="${build.dir}\Services\TPI\Tcore.CPS.TPI.SharedBusiness\bin\Release\net5.0">
				<include name="Tcore.CPS.TPI.SharedBusiness_Test.dll" />
			</fileset>
		</copy>
		</if>
		<if test="${not directory::exists(build.dir + '\Services\TPI\Tcore.CPS.TPI.SharedBusiness\bin\Release\net5.0')}" >
		<copy todir="${ext01.dist.dir}\CPS\TPI02">
			<fileset basedir="${build.dir}\Services\TPI\Tcore.CPS.TPI.SharedBusiness\bin\Release">
				<include name="Tcore.CPS.TPI.SharedBusiness_Test.dll" />
			</fileset>
		</copy>
		</if>
	</target>

	<!-- Distribute the TPIWebhookService folders -->
<!--	<target name="distribute-tpi-webhookservice" description="Ready the solution for deployment of CPS TPIWebhookService">
		<property name="MailLogger.failure.subject" value="CPS ${deployment.environment} ${deployment.type} - Distribution of TPIWebhookService step failed" />

		<copy todir="${ext01.dist.dir}\CPS\TPIWebhookService">
			<fileset basedir="${build.dir}\Services\TPI\Tcore.CPS.TPI.WebhookService\bin\Release\net5.0">
				<include name="**/*" />
			</fileset>
		</copy>
	</target>-->

	<!-- Distribute the dataanalytics folders -->
	<target name="distribute-dataanalytics" description="Ready the solution for deployment of CPS dataanalytics">
		<property name="MailLogger.failure.subject" value="CPS ${deployment.environment} ${deployment.type} - Distribution of dataanalytics step failed" />

		<copy todir="${app01.dist.dir}\CPS\ScheduledTasks\AVIPromotion">
			<fileset basedir="${build.dir}\DataAnalytics\AVIPromotion\dist">
				<include name="*.exe" />
			</fileset>
		</copy>
		<copy todir="${app01.dist.dir}\CPS\ScheduledTasks\AVIPromotion">
			<fileset basedir="${build.dir}\DataAnalytics\AVIPromotion">
				<include name="settings.txt" />
			</fileset>
		</copy>
		<copy todir="${app01.dist.dir}\CPS\ScheduledTasks\DigitalPromotion">
			<fileset basedir="${build.dir}\DataAnalytics\DigitalPromotion\dist">
				<include name="*.exe" />
			</fileset>
		</copy>
		<copy todir="${app01.dist.dir}\CPS\ScheduledTasks\DigitalPromotion">
			<fileset basedir="${build.dir}\DataAnalytics\DigitalPromotion">
				<include name="settings.txt" />
			</fileset>
		</copy>
	</target>


	<!-- Distribute the RaasAcknowledge folders -->
	<target name="distribute-raasack" description="Ready the solution for deployment of CPS RaasAcknowledge">
		<property name="MailLogger.failure.subject" value="CPS ${deployment.environment} ${deployment.type} - Distribution of RaasAcknowledge step failed" />
		<if test="${directory::exists(build.dir + '\Services\RaasAcknowledge\bin\Release\net5.0')}" >
		<copy todir="${ext01.dist.dir}\CPS\RaasAcknowledge">
			<fileset basedir="${build.dir}\Services\RaasAcknowledge\bin\Release\net5.0">
				<include name="**/*" />
			</fileset>
		</copy>
		</if>

		<if test="${not directory::exists(build.dir + '\Services\RaasAcknowledge\bin\Release\net5.0')}" >
		<copy todir="${ext01.dist.dir}\CPS\RaasAcknowledge">
			<fileset basedir="${build.dir}\Services\RaasAcknowledge\bin\Release">
				<include name="**/*" />
			</fileset>
		</copy>
		</if>

		<copy todir="${ext01.dist.dir}\CPS\RaasAcknowledge">
			<fileset basedir="${build.dir}\Services\RaasAcknowledge">
				<include name="RaasAcknowledge.xml" />
			</fileset>
		</copy>
	</target>

		<!-- Distribute the IAS folders -->
	<target name="distribute-ias" description="Ready the solution for deployment of CPS IAS">
		<property name="MailLogger.failure.subject" value="CPS ${deployment.environment} ${deployment.type} - Distribution of IAS step failed" />

		<copy todir="${ext01.dist.dir}\CPS\IAS">
			<fileset basedir="${build.dir}\Services\IAS\IAS.WebAPI\bin\Release\net5.0">
				<include name="**/*" />
			</fileset>
		</copy>
		<copy todir="${ext01.dist.dir}\CPS\IAS">
			<fileset basedir="${build.dir}\Services\IAS\IAS.WebAPI">
				<include name="Tcore.CPS.IAS.WebAPI.xml" />
			</fileset>
		</copy>
	</target>

		<!-- Distribute the DMV folders -->
	<target name="distribute-dmv" description="Ready the solution for deployment of CPS DMV">
		<property name="MailLogger.failure.subject" value="CPS ${deployment.environment} ${deployment.type} - Distribution of DMV step failed" />
		<if test="${directory::exists(build.dir + '\Services\DMV\DMV.WebAPI\bin\Release\net5.0')}" >
		<copy todir="${ext01.dist.dir}\CPS\DMV\WebAPI">
			<fileset basedir="${build.dir}\Services\DMV\DMV.WebAPI\bin\Release\net5.0">
				<include name="**/*" />
			</fileset>
		</copy>
		</if>
		<if test="${not directory::exists(build.dir + '\Services\DMV\DMV.WebAPI\bin\Release\net5.0')}" >
		<copy todir="${ext01.dist.dir}\CPS\DMV\WebAPI">
			<fileset basedir="${build.dir}\Services\DMV\DMV.WebAPI\bin\Release">
				<include name="**/*" />
			</fileset>
		</copy>
		</if>
		<copy todir="${ext01.dist.dir}\CPS\DMV\WebAPI">
			<fileset basedir="${build.dir}\Services\DMV\DMV.WebAPI">
				<include name="Tcore.CPS.DMV.WebAPI.xml" />
			</fileset>
		</copy>
		<if test="${DMVWorkerFeature=='true'}">
		<copy todir="${ext01.dist.dir}\CPS\DMV\DMV.Worker">
			<fileset basedir="${build.dir}\Services\DMV\DMV.Worker\bin\Release">
				<include name="**/*" />
			</fileset>
		</copy>
		</if>
	</target>

	<!-- Distribute the NYCDOT folders -->
	<target name="distribute-nycdot" description="Ready the solution for deployment of CPS NYCDOT">
		<property name="MailLogger.failure.subject" value="CPS ${deployment.environment} ${deployment.type} - Distribution of NYCDOT step failed" />
		<if test="${directory::exists(build.dir + '\Services\NYCDOT\NYCDOT.Api\bin\Release\net5.0')}" >
		<copy todir="${ext01.dist.dir}\CPS\NYCDOT">
			<fileset basedir="${build.dir}\Services\NYCDOT\NYCDOT.Api\bin\Release\net5.0">
				<include name="**/*" />
			</fileset>
		</copy>
		</if>
		<if test="${not directory::exists(build.dir + '\Services\NYCDOT\NYCDOT.Api\bin\Release\net5.0')}" >
		<copy todir="${ext01.dist.dir}\CPS\NYCDOT">
			<fileset basedir="${build.dir}\Services\NYCDOT\NYCDOT.Api\bin\Release">
				<include name="**/*" />
			</fileset>
		</copy>
		</if>
		<copy todir="${ext01.dist.dir}\CPS\NYCDOT">
			<fileset basedir="${build.dir}\Services\NYCDOT\NYCDOT.Api">
				<include name="NYCDOT.Api.xml" />
				<include name="web.config" />
			</fileset>
		</copy>
	</target>

	<!-- Distribute the web folders -->
	<target name="distribute-tableau" description="Ready the solution for deployment of CPS Tableau">
		<property name="MailLogger.failure.subject" value="CPS ${deployment.environment} ${deployment.type} - Distribution of Tableau step failed" />
		<copy todir="${tableau.dist.dir}\Tableau">
				<fileset basedir="${build.dir}\Tableau">
					<include name="**/*" />
				</fileset>
		</copy>
		<copy todir="${web01.dist.dir}\CPS\WebUI">
				<fileset basedir="${build.dir}\CPS\WebUI">
					<include name="web.config" />
				</fileset>
		</copy>
	</target>


	<!-- Distribute the reports resources
	<target name="distribute-reports-resources" description="Copy the resource files to the SQL server bin directory">
		<property name="MailLogger.failure.subject" value="MOMS - Reports resources deployment failed" />

		<copy todir="${moms.resources.dir}">
			<fileset basedir="${moms.release.directory}\CommonLayer\{moms.release.subdirectory}">
				<include name="**/*tcore.MOMSResources.resources*" />
				<exclude name="**/*.pdb" />
			</fileset>
		</copy>

	</target>-->
	<target name="prepare-buildscripts" description="Prepare build scripts for zip and deploy">
		<property name="MailLogger.failure.subject" value="CPS - Prepare build scripts for deployment ${deployment.environment} ${deployment.type}step failed" />
		<exec program="PrepareBuildScripts.bat" failonerror="true" >
		<arg value="${base.dir}" />
		<arg value="${deployment.server}" />
		<arg value="${deployment.user}" />
		<arg value="${deployment.password}" />
		<arg value="${jump.drive}" />
		<arg value="${dated.deployment.dir}" />
		<arg value="${ClusterFeature}" />
		</exec>
		<exec program="fnr-TableauTask-json.bat" failonerror="true">
			<arg value="${base.dir}\TableauDeployTool" />
			<arg value="${tableau.tool.parentfolder}" />
			<arg value="${tableau.content.url}" />
		</exec>
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${base.dir}\TableauDeployTool" />
			<arg value="json"/>
			<arg value="http://tableau-tool-server:8000/api/3.8/" />
			<arg value="${tableau.tool.apiUrl}" />
			<arg value="ALL"/>
		</exec>
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${base.dir}\TableauDeployTool" />
			<arg value="json"/>
			<arg value="http://tableau-tool-server:8000/" />
			<arg value="${tableau.server.url}" />
			<arg value="ALL"/>
		</exec>
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${base.dir}\TableauDeployTool" />
			<arg value="json"/>
			<arg value="SD-IFXTestUserValue" />
			<arg value="${tableau.tool.userName}" />
			<arg value="ALL"/>
		</exec>
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${base.dir}\TableauDeployTool" />
			<arg value="json"/>
			<arg value="ProjectNameValue" />
			<arg value="${tableau.ProjectName}" />
			<arg value="ALL"/>
		</exec>

	</target>


	<!-- Deploy/Copy files and directories to the destination machines -->
	<target name="deploy" description="">
		<property name="MailLogger.failure.subject" value="CPS - Deploy files to the ${deployment.environment} ${deployment.type} servers step failed" />
		<property name="MailLogger.success.subject" value="CPS ${deployment.environment} ${deployment.type}- Step 4 Deploy Successfully Completed." />
		<call target="prepare-buildscripts" failonerror="true" />
		<exec program="Deploy.bat" failonerror="true" >
		<arg value="${base.dir}" />
		<arg value="${deployment.server}" />
		<arg value="${deployment.user}" />
		<arg value="${deployment.password}" />
		<arg value="${jump.drive}" />
		<arg value="${dated.deployment.dir}" />
		<arg value="${ClusterFeature}" />
		</exec>
	</target>


	<target name="deploy-jump" description="">
		<property name="MailLogger.failure.subject" value="CPS - Deploy from Jump files to the ${env} servers step failed" />
		<property name="MailLogger.success.subject" value="CPS ${deployment.environment} - Step 4.5 Jump Deploy Successfully Completed." />

		<exec program="DeployJump.bat" failonerror="true" >
		<arg value="${base.dir}" />
		<arg value="${app01.server}" />
		<arg value="${deployment.user}" />
		<arg value="${deployment.password}" />
		<arg value="${deployment.drive}" />
		<arg value="${dated.deployment.dir}" />
		<arg value="${ClusterFeature}" />
		<arg value="${app02.server}" />
		<arg value="${web01.server}" />
		<arg value="${web02.server}" />
		<arg value="${jump.drive}" />
		<arg value="${ext01.server}" />
		<arg value="${app03.server}" />
		<arg value="${deployment.type}" />
		</exec>

		<foreach item="Line" in="MultiServerList_${deployment.type}.txt" delim=";" property="_server,_type,_port,_status">
			<exec program="DisperseNodes.bat" failonerror="true" >
			<arg value="${base.dir}" />
			<arg value="${_server}" />
			<arg value="${deployment.user}" />
			<arg value="${deployment.password}" />
			<arg value="${deployment.drive}" />
			<arg value="${dated.deployment.dir}" />
			<arg value="${jump.drive}" />
			<arg value="${_type}" />
			<arg value="${_port}" />
			<arg value="${log.dir}" />
			<arg value="${service-base-path-cps-hostname}" />
			</exec>
		</foreach>
	</target>


	<target name="unzip-packages" description="Unzip packages for official Insight builds">
	<property name="MailLogger.failure.subject" value="ERROR: ${env} ${deployment.type} CPS - Unzip failed" />
	<property name="MailLogger.success.subject" value="SUCCESS: ${env} ${deployment.type} CPS - Unzip Succeeded" />
	<exec program="UnzipPackages.bat" failonerror="true">
	<arg value="${app01.server}" />
	<arg value="${env}" />
	<arg value="${deployment.type}" />
	<arg value="${deployment.drive}" />
	<arg value="${dated.deployment.dir}" />
	<arg value="${ClusterFeature}" />
	<arg value="${app02.server}" />
	<arg value="${web01.server}" />
	<arg value="${web02.server}" />
	<arg value="${ext01.server}" />
	<arg value="${deployment.server}" />
	<arg value="${TableauFeature}" />
	<arg value="${app03.server}" />
	</exec>
	</target>

	<target name="sleep-cluster" description="Sleep Cluster Test onwards">
		<call target="pre-install-sleep" failonerror="true" />
	</target>

	<target name="pre-install-sleep" description="Put the application to sleep (stop tasks/services)">
	<property name="MailLogger.failure.subject" value="ERROR: ${env} ${deployment.type}  CPS  - Pre-Install Sleep failed" />
	<property name="MailLogger.success.subject" value="SUCCESS: ${env} ${deployment.type}  CPS - Pre-Install Sleep Succeeded" />
			<!-- Stop DNA-->
			<if test="${DNAMatchFeature=='true'}">
				<servicecontroller action="Stop" machine="${app01.server}" service="DNAMatchService" failonerror="${ratout.service.errors}"  timeout="160000"/>
				<if test="${ClusterFeature=='true'}">
					<if test="${DNAMatchFeature=='true'}">
						<servicecontroller action="Stop" machine="${app02.server}" service="DNAMatchService" failonerror="${ratout.service.errors}"  timeout="160000"/>
					</if>
				</if>

			</if>
			<!-- Stop VRG -->
			<if test="${VRGFeature=='true'}">
				<servicecontroller action="Stop" machine="${app01.server}" service="VRGService" failonerror="${ratout.service.errors}"  timeout="160000"/>
				<if test="${ClusterFeature=='true'}">
					<servicecontroller action="Stop" machine="${app02.server}" service="VRGService" failonerror="${ratout.service.errors}"  timeout="160000"/>
				</if>
				<if test="${deployment.type == 'PROD'}">
					<servicecontroller action="Stop" machine="${app03.server}" service="VRGService" failonerror="${ratout.service.errors}"  timeout="160000"/>
				</if>
			</if>
			<!-- Stop IMS -->
			<foreach item="Line" in="MultiServerList_${deployment.type}.txt" delim=";" property="_server,_type,_port,_status">
				<exec program="StopNodes.bat" failonerror="true">
					<arg value="${_server}" />
					<arg value="${_type}" />
					<arg value="${_port}" />
				</exec>
			</foreach>
			<!-- Stop CPS Apis and scheduled tasks -->
			<exec program="PreInstallSleep.bat" failonerror="true">
				<arg value="${app01.server}" />
				<arg value="${env}" />
				<arg value="${deployment.type}" />
				<arg value="${web01.server}" />
				<arg value="${SimulatorFeature}" />
				<arg value="01" />
				<arg value="${ADPollingTask}" />
				<arg value="${ext01.server}" />
				<arg value="${ZPIProcessTask}" />
				<arg value="${app03.server}" />
				<arg value="${DMVFeature}" />
			</exec>
			<if test="${ClusterFeature=='true'}">
				<exec program="PreInstallSleep.bat" failonerror="true">
					<arg value="${app02.server}" />
					<arg value="${env}" />
					<arg value="${deployment.type}" />
					<arg value="${web02.server}" />
					<arg value="${SimulatorFeature}" />
					<arg value="02" />
					<arg value="${ADPollingTask}" />
					<arg value="${ext01.server}" />
					<arg value="${ZPIProcessTask}" />
					<arg value="${app03.server}" />
					<arg value="${DMVFeature}" />
				</exec>
			</if>
			<!-- Stop AAR -->
			<if test="${AARFeature=='true'}">
				<servicecontroller action="Stop" machine="${app01.server}" service="AARProcessingService" failonerror="${ratout.service.errors}"  timeout="160000"/>
				<if test="${ClusterFeature=='true'}">
					<servicecontroller action="Stop" machine="${app02.server}" service="AARProcessingService" failonerror="${ratout.service.errors}"  timeout="160000"/>
				</if>
			</if>
			<!-- Stop Transcom -->
			<if test="${TransComServiceFeature=='true'}">
				<servicecontroller action="Stop" machine="${app01.server}" service="TransComService" failonerror="${ratout.service.errors}"  timeout="160000"/>
				<if test="${ClusterFeature=='true'}">
					<servicecontroller action="Stop" machine="${app02.server}" service="TransComService" failonerror="${ratout.service.errors}"  timeout="160000"/>
				</if>
			</if>
			<if test="${DMVWorkerFeature=='true'}">
				<servicecontroller action="Stop" machine="${ext01.server}" service="DMVService" failonerror="${ratout.service.errors}"  timeout="160000"/>
			</if>
			<if test="${RabbitMQCluster=='false'}">
				<servicecontroller action="Stop" machine="${rabbitmq.cluster}" service="RabbitMQ" failonerror="${ratout.service.errors}"  timeout="160000"/>
			</if>
#TODO Rabbit
			<if test="${RabbitMQCluster=='true'}">
			<foreach item="Line" in="RabbitNodes_${deployment.type}.txt" delim="," property="_rabbitnode1,_rabbitnode2,_rabbitnode3">
				<echo message="stop ${_rabbitnode3}" />
				<servicecontroller action="Stop" machine="${_rabbitnode3}" service="RabbitMQ" failonerror="${ratout.service.errors}"  timeout="160000"/>
				<echo message="stop ${_rabbitnode2}" />
				<sleep seconds="15" />
				<servicecontroller action="Stop" machine="${_rabbitnode2}" service="RabbitMQ" failonerror="${ratout.service.errors}"  timeout="160000"/>
				<echo message="stop ${_rabbitnode1}" />
				<sleep seconds="15" />
				<servicecontroller action="Stop" machine="${_rabbitnode1}" service="RabbitMQ" failonerror="${ratout.service.errors}"  timeout="160000"/>
			</foreach>
			</if>
	</target>

	<target name="install-cluster" description="install Cluster Test onwards">
	<if test="${ClusterFeature=='false'}">
	<echo message="I Am Not Clustered" />
	<call target="install-build" failonerror="true" />
	</if>
	<if test="${ClusterFeature=='true'}">
		<call target="install-build" failonerror="true" />
		<call target="install-build-02" failonerror="true" />
	</if>
	</target>

	<target name="install-build" description="Install for official Insight builds">
	<property name="MailLogger.failure.subject" value="ERROR: ${env} ${deployment.type} CPS - Install failed" />
	<property name="MailLogger.success.subject" value="SUCCESS: ${env} ${deployment.type} CPS - Install Succeeded" />
		<exec program="InstallBuild.bat" failonerror="true">
			<arg value="${app01.server}" />
			<arg value="${env}" />
			<arg value="${deployment.type}" />
			<arg value="${deployment.drive}" />
			<arg value="${dated.deployment.dir}" />
			<arg value="01" />
			<arg value="${web01.server}" />
			<arg value="${ext01.server}" />
			<arg value="${app03.server}" />
		</exec>

	<foreach item="Line" in="MultiServerList_${deployment.type}.txt" delim=";" property="_server,_type,_port,_status">
		<exec program="InstallNodes.bat" failonerror="true">
			<arg value="${_server}" />
			<arg value="${env}" />
			<arg value="${deployment.type}" />
			<arg value="${deployment.drive}" />
			<arg value="${dated.deployment.dir}" />
			<arg value="${_type}" />
			<arg value="${_port}" />
		</exec>
	</foreach>

	</target>

	<target name="install-build-02" description="Install for official Insight builds">
		<property name="MailLogger.failure.subject" value="ERROR: ${env} ${deployment.type} CPS - Install failed" />
		<property name="MailLogger.success.subject" value="SUCCESS: ${env} ${deployment.type} CPS - Install Succeeded" />
		<exec program="InstallBuild.bat" failonerror="true">
			<arg value="${app02.server}" />
			<arg value="${env}" />
			<arg value="${deployment.type}" />
			<arg value="${deployment.drive}" />
			<arg value="${dated.deployment.dir}" />
			<arg value="02" />
			<arg value="${web02.server}" />
			<arg value="${ext01.server}" />
		</exec>
	</target>

	<target name="deploy-reports" description="Deploy Reports for official Insight builds">
		<property name="MailLogger.failure.subject" value="ERROR: ${env} ${deployment.type} CPS - Deploy Tableau Reports failed" />
		<property name="MailLogger.success.subject" value="SUCCESS: ${env} ${deployment.type} CPS - Deploy Tableau  Reports Succeeded" />
		<exec program="DeployTableau.bat" failonerror="true">
		<arg value="${deployment.server}" />
		<arg value="${deployment.type}" />
		<arg value="${deployment.drive}" />
		<arg value="${dated.deployment.dir}" />
		<arg value="${TableauFeature}" />
		<arg value="${deployment.environment}" />
		<arg value="${tableau.DW.servername}" />
		<arg value="${tableau.DW.port}" />
		<arg value="${tableau.DW.username}" />
		<arg value="${tableau.DW.password}" />
		<arg value="${TableauDWFeature}" />
		<arg value="${tableau.tool.connectionServer}" />
		<arg value="${tableau.tool.connectionUser}" />
		<arg value="${tableau.tool.connectionPassword}" />
		<arg value="${tableau.tool.secret}" />
		</exec>
	</target>

	<target name="wake-cluster" description="wake Cluster Test onwards">
		<call target="post-install-startup" failonerror="true" />
	</target>

	<target name="post-install-startup" description="Run after installation and after DBA finished DB deployment">
		<property name="MailLogger.failure.subject" value="ERROR: ${env} ${deployment.type} CPS - Post-Install Startup failed" />
		<property name="MailLogger.success.subject" value="SUCCESS: ${env} ${deployment.type} CPS - Post-Install Startup Succeeded" />
		<!-- start rabbit -->
		<if test="${RabbitMQCluster=='false'}">
			<servicecontroller action="Start" machine="${rabbitmq.cluster}" service="RabbitMQ" failonerror="${ratout.service.errors}"  timeout="160000"/>
		</if>
		<if test="${RabbitMQCluster=='true'}">
			<foreach item="Line" in="RabbitNodes_${deployment.type}.txt" delim="," property="_rabbitnode1,_rabbitnode2,_rabbitnode3">
				<echo message="start ${_rabbitnode1}" />
				<servicecontroller action="Start" machine="${_rabbitnode1}" service="RabbitMQ" failonerror="${ratout.service.errors}"  timeout="160000"/>
				<echo message="start ${_rabbitnode2}" />
				<sleep seconds="15" />
				<servicecontroller action="Start" machine="${_rabbitnode2}" service="RabbitMQ" failonerror="${ratout.service.errors}"  timeout="160000"/>
				<echo message="start ${_rabbitnode3}" />
				<sleep seconds="15" />
				<servicecontroller action="Start" machine="${_rabbitnode3}" service="RabbitMQ" failonerror="${ratout.service.errors}"  timeout="160000"/>
			</foreach>
		</if>
		<!-- start DMV -->
		<if test="${DMVWorkerFeature=='true'}">
			<if test="${deployment.type=='PROD'}">
				<servicecontroller action="Start" machine="${ext01.server}" service="DMVService" failonerror="${ratout.service.errors}"  timeout="160000"/>
			</if>
			<if test="${deployment.type=='STAGE'}">
				<servicecontroller action="Start" machine="${ext01.server}" service="DMVService" failonerror="${ratout.service.errors}"  timeout="160000"/>
			</if>
		</if>
		<!-- Start Transcom -->
		<if test="${TransComServiceFeature=='true'}">
			<servicecontroller action="Start" machine="${app01.server}" service="TransComService" failonerror="${ratout.service.errors}"  timeout="160000"/>
		</if>
		<!-- Start AAR -->
		<if test="${AARFeature=='true'}">
			<servicecontroller action="Start" machine="${app01.server}" service="AARProcessingService" failonerror="${ratout.service.errors}"  timeout="160000"/>
		</if>
		<!-- Start CPS api and tasks -->
		<exec program="PostInstallStartup.bat" failonerror="true">
			<arg value="${app01.server}" />
			<arg value="${env}" />
			<arg value="${deployment.type}" />
			<arg value="${web01.server}" />
			<arg value="${SimulatorFeature}" />
			<arg value="01" />
			<arg value="${ADPollingTask}" />
			<arg value="${ext01.server}" />
			<arg value="${ZPIProcessTask}" />
			<arg value="${TransPortalFeature}" />
			<arg value="${transportal.db.server}" />
			<arg value="${dated.deployment.dir}" />
			<arg value="${assembly.version}" />
			<arg value="${transportal.db.listener}" />
			<arg value="${db.user}" />
			<arg value="${db.password}" />
			<arg value="${app03.server}" />
			<arg value="${DMVFeature}" />
		</exec>
		<if test="${ClusterFeature=='true'}">
			<exec program="PostInstallStartup.bat" failonerror="true">
				<arg value="${app02.server}" />
				<arg value="${env}" />
				<arg value="${deployment.type}" />
				<arg value="${web02.server}" />
				<arg value="${SimulatorFeature}" />
				<arg value="02" />
				<arg value="${ADPollingTask}" />
				<arg value="${ext01.server}" />
				<arg value="${ZPIProcessTask}" />
				<arg value="${TransPortalFeature}" />
				<arg value="${transportal.db.server}" />
				<arg value="${dated.deployment.dir}" />
				<arg value="${assembly.version}" />
				<arg value="${transportal.db.listener}" />
				<arg value="${db.user}" />
				<arg value="${db.password}" />
				<arg value="${app03.server}" />
				<arg value="${DMVFeature}" />
			</exec>
		</if>
		<!-- start IMS -->
		<foreach item="Line" in="MultiServerList_${deployment.type}.txt" delim=";" property="_server,_type,_port,_status">
		<if test="${_status=='enabled'}">
			<exec program="StartNodes.bat" failonerror="true">
				<arg value="${_server}" />
				<arg value="${_type}" />
				<arg value="${_port}" />
			</exec>
		</if>
		</foreach>
		<!-- start VRG -->
		<if test="${VRGFeature=='true'}">
			<servicecontroller action="Start" machine="${app01.server}" service="VRGService" failonerror="${ratout.service.errors}"  timeout="160000"/>
			<if test="${ClusterFeature=='true'}">
				<servicecontroller action="Start" machine="${app02.server}" service="VRGService" failonerror="${ratout.service.errors}"  timeout="160000"/>
			</if>
			<if test="${deployment.type == 'PROD'}">
				<servicecontroller action="Start" machine="${app03.server}" service="VRGService" failonerror="${ratout.service.errors}"  timeout="160000"/>
			</if>
		</if>
		<!-- Start DNA -->
		<if test="${DNAMatchFeature=='true'}">
			<servicecontroller action="Start" machine="${app01.server}" service="DNAMatchService" failonerror="${ratout.service.errors}"  timeout="160000"/>
		</if>
	</target>

	<target name="cleanup-deployment-cluster" description="Encrypt Config files (for PROD)">
		<if test="${ClusterFeature=='false'}">
	<echo message="I Am Not Clustered" />
	<call target="cleanup-deployment" failonerror="true" />
	</if>
	<if test="${ClusterFeature=='true'}">
		<call target="cleanup-deployment" failonerror="true" />
		<call target="cleanup-deployment-02" failonerror="true" />
	</if>
	</target>

	<target name="cleanup-deployment" description="Post install cleanup">
		<property name="MailLogger.failure.subject" value="ERROR: ${env} ${deployment.type} CPS - Deployment Cleanup Succeeded" />
		<property name="MailLogger.success.subject" value="SUCCESS: ${env} ${deployment.type} CPS -  Deployment Cleanup Succeeded" />
		<exec program="CleanUpDeployment.bat" failonerror="true">
		<arg value="${app01.server}" />
		<arg value="${web01.server}" />
		<arg value="${deployment.drive}" />
		<arg value="${deployment.user}" />
		<arg value="${deployment.password}" />
		<arg value="${ext01.server}" />
		</exec>
	</target>

	<target name="cleanup-deployment-02" description="Post install cleanup">
		<property name="MailLogger.failure.subject" value="ERROR: ${env} ${deployment.type} CPS - Deployment Cleanup Succeeded" />
		<property name="MailLogger.success.subject" value="SUCCESS: ${env} ${deployment.type} CPS -  Deployment Cleanup Succeeded" />
		<exec program="CleanUpDeployment.bat" failonerror="true">
		<arg value="${app02.server}" />
		<arg value="${web02.server}" />
		<arg value="${deployment.drive}" />
		<arg value="${deployment.user}" />
		<arg value="${deployment.password}" />
		<arg value="${ext01.server}" />
		</exec>
	</target>



</project>
