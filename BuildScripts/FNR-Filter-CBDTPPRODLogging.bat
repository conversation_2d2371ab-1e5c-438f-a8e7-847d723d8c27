@echo off
rem set variables
Set CURRENTDIR=%CD%
Set DEPLOYDIR=%1

"%CURRENTDIR%\fnr.exe" --cl --dir "%DEPLOYDIR%\ServiceHost" --fileMask "tcore.MOMSExternalServiceHost.exe.config" --excludeFileMask "*.dll, *.exe" --includeSubDirectories --find "<maximumFileSize value=""10000KB"" />" --replace "<!--maximumFileSize value=""10000KB"" /-->"
"%CURRENTDIR%\fnr.exe" --cl --dir "%DEPLOYDIR%\ServiceHost" --fileMask "tcore.MOMSServiceHost.exe.config" --excludeFileMask "*.dll, *.exe" --includeSubDirectories --find "<maximumFileSize value=""10000KB"" />" --replace "<!--maximumFileSize value=""10000KB"" /-->"
"%CURRENTDIR%\fnr.exe" --cl --dir "%DEPLOYDIR%\ScheduledTasks\MOMSNotificationEscalationTask" --fileMask "tcore.MOMSNotificationEscalation.exe.config" --excludeFileMask "*.dll, *.exe" --includeSubDirectories --find "<maximumFileSize value=""10000KB"" />" --replace "<!--maximumFileSize value=""10000KB"" /-->"
"%CURRENTDIR%\fnr.exe" --cl --dir "%DEPLOYDIR%\ScheduledTasks\MOMSWorkOrderMessagingTask" --fileMask "tcore.MOMSWorkOrderMessagingTask.exe.config" --excludeFileMask "*.dll, *.exe" --includeSubDirectories --find "<maximumFileSize value=""10000KB"" />" --replace "<!--maximumFileSize value=""10000KB"" /-->"