@echo off
rem set variables
Set CURRENTDIR=%CD%

echo ------- Your current directory is:  %CURRENTDIR% ---------
SET SERVER=%1
SET DEPLOYMENTDRIVE=%2
SET DEPLOYMENTDIR=%3

SET STEP="Encrypt FPCLIENT Config Files"
<PERSON>H<PERSON> encrypt FPCLient on %SERVER%
call Autobuild\do_invoke_any_locrem1.cmd %SERVER% %DEPLOYMENTDRIVE%:\Deployment-ImageReview\%DEPLOYMENTDIR%\AES "EncryptFingerprintClientConfigFile.cmd" %DEPLOYMENTDRIVE%:\Deployment-ImageReview\%DEPLOYMENTDIR%\AES\EncryptFingerprintClientConfigFile.cmd unused unused unused unused)
if %ERRORLEVEL% NEQ 0 GOTO :error



GOTO :END

:error
echo ------- AN ERROR OCCURED DURING THIS STEP: %STEP% ------
exit /b 1

:END
