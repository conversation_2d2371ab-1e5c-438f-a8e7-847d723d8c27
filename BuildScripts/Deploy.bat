@echo off
rem set variables
Set CURRENTDIR=%CD%

echo ------- Your current directory is:  %CURRENTDIR% ---------

set NOW=%date:~10,4%-%date:~4,2%-%date:~7,2%
echo Deployment beginning %NOW%
echo -----------------------------------
echo Connect to Server
echo -----------------------------------
SET BASEDIR=%1
SET USER=%2
SET PASSWORD=%3
SET DEPLOYMENTDRIVE=%4
SET DEPLOYMENTDIR=%5
SET DEPLOYMENTSERVER=%6
SET CLUSTER=%7
SET HRFEATURE=%8
SET BASEDIR2=%9
SHIFT
SHIFT
SHIFT
SHIFT
SHIFT
SHIFT
SHIFT
SHIFT
SHIFT
SET TABLEAUFEATURE=%1


:zipfiles
echo -----------------------------------
echo Zip Distribution files
echo -----------------------------------

del /s %BASEDIR%\*.zip

SET STEP="ZipFP"
cd \AppBuild-MIR\DIST\

if exist \AppBuild-MIR\DIST\FP01\ ( 
%CURRENTDIR%\zip -r -q FP01.zip FP01\*
)
if %ERRORLEVEL% NEQ 0 GOTO :error 

if exist \AppBuild-MIR\DIST\APP01\ ( 
%CURRENTDIR%\zip -r -q APP01.zip APP01\*
)
if %ERRORLEVEL% NEQ 0 GOTO :error

if exist \AppBuild-MIR\DIST\IRR01\ ( 
%CURRENTDIR%\zip -r -q IRR01.zip IRR01\*
)
if %ERRORLEVEL% NEQ 0 GOTO :error

if exist \AppBuild-MIR\DIST\HOCP01\ ( 
%CURRENTDIR%\zip -r -q HOCP01.zip HOCP01\*
)
if %ERRORLEVEL% NEQ 0 GOTO :error

if exist \AppBuild-MIR\DIST\PREHOCP01\ ( 
%CURRENTDIR%\zip -r -q PREHOCP01.zip PREHOCP01\*
)
if %ERRORLEVEL% NEQ 0 GOTO :error

if exist \AppBuild-MIR\DIST\TRANS01\ ( 
%CURRENTDIR%\zip -r -q TRANS01.zip TRANS01\*
)
if %ERRORLEVEL% NEQ 0 GOTO :error

SET STEP="Zip WEB" 
if exist \AppBuild-MIR\DIST\WEB01\ ( 
%CURRENTDIR%\zip -r -q WEB01.zip WEB01\*
)
if %ERRORLEVEL% NEQ 0 GOTO :error

SET STEP="Zip REPORTS" 
if exist \AppBuild-MIR\DIST\RPT01\ ( 
%CURRENTDIR%\zip -r -q RPT01.zip RPT01\*
)
if %ERRORLEVEL% NEQ 0 GOTO :error

SET STEP="Zip ImgReviewSendTransTask" 
if exist \AppBuild-MIR\DIST\ImgReviewSendTransTask\ ( 
%CURRENTDIR%\zip -r -q ImgReviewSendTransTask.zip ImgReviewSendTransTask\*
)
if %ERRORLEVEL% NEQ 0 GOTO :error

SET STEP="Zip GenerateROITask" 
if exist \AppBuild-MIR\DIST\GenerateROITask\ ( 
%CURRENTDIR%\zip -r -q GenerateROITask.zip GenerateROITask\*
)
if %ERRORLEVEL% NEQ 0 GOTO :error

SET STEP="Zip ManualResultsTask" 
if exist \AppBuild-MIR\DIST\ManualResultsTask\ ( 
%CURRENTDIR%\zip -r -q ManualResultsTask.zip ManualResultsTask\*
)
if %ERRORLEVEL% NEQ 0 GOTO :error

SET STEP="Zip AuditReviewResultsTask" 
if exist \AppBuild-MIR\DIST\AuditReviewResultsTask\ ( 
%CURRENTDIR%\zip -r -q AuditReviewResultsTask.zip AuditReviewResultsTask\*
)
if %ERRORLEVEL% NEQ 0 GOTO :error

SET STEP="Zip ImgReviewSendTransTaskRetries" 
if exist \AppBuild-MIR\DIST\ImgReviewSendTransTaskRetries\ ( 
%CURRENTDIR%\zip -r -q ImgReviewSendTransTaskRetries.zip ImgReviewSendTransTaskRetries\*
)
if %ERRORLEVEL% NEQ 0 GOTO :error

SET STEP="Zip GenerateROITaskRetries" 
if exist \AppBuild-MIR\DIST\GenerateROITaskRetries\ ( 
%CURRENTDIR%\zip -r -q GenerateROITaskRetries.zip GenerateROITaskRetries\*
)
if %ERRORLEVEL% NEQ 0 GOTO :error

SET STEP="Zip HighOcrConfProcessor" 
if exist \AppBuild-MIR\DIST\HighOcrConfProcessor\ ( 
%CURRENTDIR%\zip -r -q HighOcrConfProcessor.zip HighOcrConfProcessor\*
)
if %ERRORLEVEL% NEQ 0 GOTO :error
SET STEP="Zip HOCSendResultsProcessor" 
if exist \AppBuild-MIR\DIST\HOCSendResultsProcessor\ ( 
%CURRENTDIR%\zip -r -q HOCSendResultsProcessor.zip HOCSendResultsProcessor\*
)
if %ERRORLEVEL% NEQ 0 GOTO :error

SET STEP="Zip PreHighOcrConfProcessor" 
if exist \AppBuild-MIR\DIST\PreHighOcrConfProcessor\ ( 
%CURRENTDIR%\zip -r -q PreHighOcrConfProcessor.zip PreHighOcrConfProcessor\*
)
if %ERRORLEVEL% NEQ 0 GOTO :error
SET STEP="Zip PreHOCSendResultsProcessor" 
if exist \AppBuild-MIR\DIST\PreHOCSendResultsProcessor\ ( 
%CURRENTDIR%\zip -r -q PreHOCSendResultsProcessor.zip PreHOCSendResultsProcessor\*
)
if %ERRORLEVEL% NEQ 0 GOTO :error

SET STEP="Zip FPMSendResultsTask" 
if exist \AppBuild-MIR\DIST\FPMSendResultsTask\ ( 
%CURRENTDIR%\zip -r -q FPMSendResultsTask.zip FPMSendResultsTask\*
)
if %ERRORLEVEL% NEQ 0 GOTO :error

SET STEP="Zip FPPMatchPoolTask" 
if exist \AppBuild-MIR\DIST\FPPMatchPoolTask\ ( 
%CURRENTDIR%\zip -r -q FPPMatchPoolTask.zip FPPMatchPoolTask\*
)
if %ERRORLEVEL% NEQ 0 GOTO :error

SET STEP="Zip IRRSendResultsTask" 
if exist \AppBuild-MIR\DIST\IRRSendResultsTask\ ( 
%CURRENTDIR%\zip -r -q IRRSendResultsTask.zip IRRSendResultsTask\*
)
if %ERRORLEVEL% NEQ 0 GOTO :error

SET STEP="Zip IRRSendResultsTask" 
if exist \AppBuild-MIR\DIST\IRRSendResultsTaskRetries\ ( 
%CURRENTDIR%\zip -r -q IRRSendResultsTaskRetries.zip IRRSendResultsTaskRetries\*
)
if %ERRORLEVEL% NEQ 0 GOTO :error

SET STEP="Zip ActiveDirectoryPollingTask" 
if exist \AppBuild-MIR\DIST\ActiveDirectoryPollingTask\ ( 
%CURRENTDIR%\zip -r -q ActiveDirectoryPollingTask.zip ActiveDirectoryPollingTask\*
)
if %ERRORLEVEL% NEQ 0 GOTO :error

SET STEP="Zip VehicleDetectionTask" 
if exist \AppBuild-MIR\DIST\VehicleDetectionTask\ ( 
%CURRENTDIR%\zip -r -q VehicleDetectionTask.zip VehicleDetectionTask\*
)
if %ERRORLEVEL% NEQ 0 GOTO :error

SET STEP="Zip ImageClientServiceTask_RetrieveTransactions" 
if exist \AppBuild-MIR\DIST\ImageClientServiceTask_RetrieveTransactions\ ( 
%CURRENTDIR%\zip -r -q ImageClientServiceTask_RetrieveTransactions.zip ImageClientServiceTask_RetrieveTransactions\*
)
if %ERRORLEVEL% NEQ 0 GOTO :error

SET STEP="Zip ImageClientServiceTask_SendResponse" 
if exist \AppBuild-MIR\DIST\ImageClientServiceTask_SendResponse\ ( 
%CURRENTDIR%\zip -r -q ImageClientServiceTask_SendResponse.zip ImageClientServiceTask_SendResponse\*
)
if %ERRORLEVEL% NEQ 0 GOTO :error

SET STEP="Zip MIREmailNotificationTask" 
if exist \AppBuild-MIR\DIST\MIREmailNotificationTask\ ( 
%CURRENTDIR%\zip -r -q MIREmailNotificationTask.zip MIREmailNotificationTask\*
)
if %ERRORLEVEL% NEQ 0 GOTO :error

SET STEP="Zip HumanReadabilitySelectionTask" 
if exist \AppBuild-MIR\DIST\HumanReadabilitySelectionTask\ ( 
%CURRENTDIR%\zip -r -q HumanReadabilitySelectionTask.zip HumanReadabilitySelectionTask\*
)
if %ERRORLEVEL% NEQ 0 GOTO :error

SET STEP="Zip VOTTQueueTask" 
if exist \AppBuild-MIR\DIST\VOTTQueueTask\ ( 
%CURRENTDIR%\zip -r -q VOTTQueueTask.zip VOTTQueueTask\*
)
if %ERRORLEVEL% NEQ 0 GOTO :error

SET STEP="Zip Human Readability" 
if exist \AppBuild-MIR\DIST\HR01\ ( 
%CURRENTDIR%\zip -r -q HR01.zip HR01\*
)
if %ERRORLEVEL% NEQ 0 GOTO :error

if exist \AppBuild-MIR\DIST\FPClient\ ( 
%CURRENTDIR%\zip -r -q FPClient.zip FPClient\*
)
if %ERRORLEVEL% NEQ 0 GOTO :error 

SET STEP="Zip Tableau"
if exist \AppBuild-MIR\DIST\TABLEAU\ ( 
%CURRENTDIR%\zip -r -q TABLEAU.zip TABLEAU\*
)
if %ERRORLEVEL% NEQ 0 GOTO :error

SET STEP="Zip APP02" 
if %CLUSTER%==true ( %CURRENTDIR%\zip -r -q APP02.zip APP02\* )
if %ERRORLEVEL% NEQ 0 GOTO :error

SET STEP="Zip WEB02" 
if %CLUSTER%==true ( %CURRENTDIR%\zip -r -q WEB02.zip WEB02\* )
if %ERRORLEVEL% NEQ 0 GOTO :error

if exist \AppBuild-MIR\DIST\IRR02\ ( 
%CURRENTDIR%\zip -r -q IRR02.zip IRR02\*
)
if %ERRORLEVEL% NEQ 0 GOTO :error

if exist \AppBuild-MIR\DIST\HOCP02\ ( 
%CURRENTDIR%\zip -r -q HOCP02.zip HOCP02\*
)
if %ERRORLEVEL% NEQ 0 GOTO :error

if exist \AppBuild-MIR\DIST\PREHOCP02\ ( 
%CURRENTDIR%\zip -r -q PREHOCP02.zip PREHOCP02\*
)
if %ERRORLEVEL% NEQ 0 GOTO :error

SET STEP="Zip FP02" 
if exist \AppBuild-MIR\DIST\FP02\ ( 
%CURRENTDIR%\zip -r -q FP02.zip FP02\*
)
if %ERRORLEVEL% NEQ 0 GOTO :error 
SET STEP="Zip TRANS02" 
if exist \AppBuild-MIR\DIST\TRANS02\ ( 
%CURRENTDIR%\zip -r -q TRANS02.zip TRANS02\*
)
if %ERRORLEVEL% NEQ 0 GOTO :error

cd %CURRENTDIR%
%CURRENTDIR%\zip -r %DEPLOYROOTDIR%\AppBuild-MIR\AES.zip AES\*

cd %CURRENTDIR%

for /F %%i in ('dir /b "%DEPLOYROOTDIR%\Appbuild-MIR\DIST\BuildScripts.zip"') do (
	del /s /q BuildsScripts.zip
)

echo zip the scripts
%CURRENTDIR%\zip -r \AppBuild-MIR\BuildScripts.zip BuildScripts\* 
del /s /q BuildScripts\*
rmdir /s /q BuildScripts

cd %BASEDIR2%

echo zip TableauDeployTool

for /F %%i in ('dir /b "%BASEDIR2%\TableauDeployTool.zip"') do (
	del /s /q TableauDeployTool.zip
)

IF "%TABLEAUFEATURE%" == "false" GOTO CONTINUETODEPLOY
%CURRENTDIR%\zip -r TableauDeployTool.zip TableauDeployTool\* 
del /s /q TableauDeployTool\*
rmdir /s /q TableauDeployTool


:CONTINUETODEPLOY
echo ZIP DONE
echo Build script and TableauDeployTool ZIP DONE FOR ImageReview


cd %CURRENTDIR%

:deletemappeddrives
Set STEP="delete mapped drives"
net use j: /delete

:mapdrives
Set STEP="Map drives"
echo ------- map drive ----------
net use j: \\%DEPLOYMENTSERVER%\%DEPLOYMENTDRIVE%$ %PASSWORD% /user:%USER%

:destinationcleanup
echo ------- destination cleanup -----------
Set STEP="destination cleanup"
if exist j:\Staging-ImageReview\%DEPLOYMENTDIR% (rmdir /s /q j:\Staging-ImageReview\%DEPLOYMENTDIR%) 

:newdir
echo ------- Create dated deployment directory -----------------
Set STEP="Create Deployment Directories on target servers"
if not exist j:\Staging-ImageReview\%DEPLOYMENTDIR% (mkdir j:\Staging-ImageReview\%DEPLOYMENTDIR%)

if %ERRORLEVEL% NEQ 0 GOTO :error 

:deploybuildscripts
set STEP="Deploy Build Zip"
echo -----------------------------------
echo Deploy Build Scripts zip file to ImageReview Server (MIR task server)...
echo -----------------------------------
if exist j:\Staging-ImageReview\BuildScripts.Zip (del /s /q j:\Staging-ImageReview\BuildScripts.zip)
if %ERRORLEVEL% NEQ 0 GOTO :error 
if exist j:\Staging-ImageReview\BuildScripts (del /s /q j:\Staging-ImageReview\BuildScripts\*)
if %ERRORLEVEL% NEQ 0 GOTO :error 
rem xcopy /R "\AppBuild-MIR\BuildScripts.zip" "j:\Staging-ImageReview\*" 
rem robocopy /R:3  "\AppBuild-MIR\" "j:\Staging-ImageReview" BuildScripts.zip
robocopy /R:3 "\AppBuild-MIR" "j:\Staging-ImageReview" BuildScripts.zip
if %ERRORLEVEL% GEQ 8 GOTO :error 
unzip -ou j:\Staging-ImageReview\BuildScripts.zip -d  j:\Staging-ImageReview\
rem psexec -accepteula \\%DEPLOYMENTSERVER% cmd /c (unzip -q %DEPLOYMENTDRIVE%:\Staging-ImageReview\BuildScripts.zip -d  %DEPLOYMENTDRIVE%:\Staging-ImageReview\)
if %ERRORLEVEL% NEQ 0 GOTO :error 
echo %STEP% Deployment complete 
IF "%TABLEAUFEATURE%" == "false" GOTO deploypackages
:deploytableautool
echo -----------------------------------
echo Deploy TableauDeployTool zip file to MIR Server....
echo -----------------------------------
if exist j:\Staging-ImageReview\TableauDeployTool.Zip (del /s /q j:\Staging-ImageReview\TableauDeployTool.zip)
if %ERRORLEVEL% NEQ 0 GOTO :error 
if exist j:\Staging-ImageReview\TableauDeployTool (del /s /q j:\Staging-ImageReview\TableauDeployTool\*)
if %ERRORLEVEL% NEQ 0 GOTO :error 
xcopy /R "%BASEDIR2%\TableauDeployTool.zip" "j:\Staging-ImageReview\*" 
if %ERRORLEVEL% NEQ 0 GOTO :error 
echo %STEP% TableauDeployTool Deployment complete 

IF "%HRFEATURE%"=="true" GOTO HRFEATURE

:deploypackages
echo ------------------------------------
echo Copy ImageReview....
echo ------------------------------------
set STEP="Deploy ImageReview FPM/FPP"
if exist \AppBuild-MIR\DIST\FP01.zip  (
robocopy /R:3 \AppBuild-MIR\DIST\ j:\Staging-ImageReview\%DEPLOYMENTDIR% FP01.zip
)
if %ERRORLEVEL% GEQ 8 GOTO :error 

if exist \AppBuild-MIR\DIST\FPClient.zip  (
robocopy /R:3 \AppBuild-MIR\DIST\ j:\Staging-ImageReview\%DEPLOYMENTDIR% FPClient.zip
)
if %ERRORLEVEL% GEQ 8 GOTO :error 

set STEP="Deploy APP"
if exist \AppBuild-MIR\DIST\APP01.zip ( robocopy /R:3  \AppBuild-MIR\DIST\ j:\Staging-ImageReview\%DEPLOYMENTDIR% APP01.zip
)
if %ERRORLEVEL% GEQ 8  GOTO :error 

set STEP="Deploy TRANS"
if exist \AppBuild-MIR\DIST\TRANS01.zip ( robocopy /R:3  \AppBuild-MIR\DIST\ j:\Staging-ImageReview\%DEPLOYMENTDIR% TRANS01.zip
)
if %ERRORLEVEL% GEQ 8  GOTO :error 

set STEP="Deploy IRR"
if exist \AppBuild-MIR\DIST\IRR01.zip ( robocopy /R:3  \AppBuild-MIR\DIST\ j:\Staging-ImageReview\%DEPLOYMENTDIR% IRR01.zip
)
if %ERRORLEVEL% GEQ 8  GOTO :error 

set STEP="Deploy HOCP"
if exist \AppBuild-MIR\DIST\HOCP01.zip ( robocopy /R:3  \AppBuild-MIR\DIST\ j:\Staging-ImageReview\%DEPLOYMENTDIR% HOCP01.zip
)
if %ERRORLEVEL% GEQ 8  GOTO :error 

set STEP="Deploy PREHOCP"
if exist \AppBuild-MIR\DIST\PREHOCP01.zip ( robocopy /R:3  \AppBuild-MIR\DIST\ j:\Staging-ImageReview\%DEPLOYMENTDIR% PREHOCP01.zip
)
if %ERRORLEVEL% GEQ 8  GOTO :error 

set STEP="Deploy WEB"
if exist \AppBuild-MIR\DIST\WEB01.zip ( robocopy /R:3 \AppBuild-MIR\DIST\ j:\Staging-ImageReview\%DEPLOYMENTDIR% WEB01.zip
)
if %ERRORLEVEL% GEQ 8  GOTO :error 

set STEP="Deploy ImageReview REports"
if exist \AppBuild-MIR\DIST\RPT01.zip ( robocopy /R:3 \AppBuild-MIR\DIST\ j:\Staging-ImageReview\%DEPLOYMENTDIR% RPT01.zip
)
if %ERRORLEVEL% GEQ 8  GOTO :error 

set STEP="Deploy ImageReview HR"
if exist \AppBuild-MIR\DIST\HR01.zip ( robocopy /R:3 \AppBuild-MIR\DIST\ j:\Staging-ImageReview\%DEPLOYMENTDIR% HR01.zip
)
if %ERRORLEVEL% GEQ 8  GOTO :error 

set STEP="Deploy ImgReviewSendTransTask"
if exist \AppBuild-MIR\DIST\ImgReviewSendTransTask.zip ( robocopy /R:3 \AppBuild-MIR\DIST\ j:\Staging-ImageReview\%DEPLOYMENTDIR% ImgReviewSendTransTask.zip
)
if %ERRORLEVEL% GEQ 8  GOTO :error 

set STEP="Deploy ImgReviewSendTransTaskRetries"
if exist \AppBuild-MIR\DIST\ImgReviewSendTransTaskRetries.zip ( robocopy /R:3 \AppBuild-MIR\DIST\ j:\Staging-ImageReview\%DEPLOYMENTDIR% ImgReviewSendTransTaskRetries.zip
)
if %ERRORLEVEL% GEQ 8  GOTO :error 

set STEP="Deploy GenerateROITask"
if exist \AppBuild-MIR\DIST\GenerateROITask.zip ( robocopy /R:3 \AppBuild-MIR\DIST\ j:\Staging-ImageReview\%DEPLOYMENTDIR% GenerateROITask.zip
)
if %ERRORLEVEL% GEQ 8  GOTO :error 

set STEP="Deploy GenerateROITaskRetries"
if exist \AppBuild-MIR\DIST\GenerateROITaskRetries.zip ( robocopy /R:3 \AppBuild-MIR\DIST\ j:\Staging-ImageReview\%DEPLOYMENTDIR% GenerateROITaskRetries.zip
)
if %ERRORLEVEL% GEQ 8  GOTO :error 

set STEP="Deploy AuditReviewResultsTask"
if exist \AppBuild-MIR\DIST\AuditReviewResultsTask.zip ( robocopy /R:3 \AppBuild-MIR\DIST\ j:\Staging-ImageReview\%DEPLOYMENTDIR% AuditReviewResultsTask.zip
)
if %ERRORLEVEL% GEQ 8  GOTO :error 

set STEP="Deploy ManualResultsTask"
if exist \AppBuild-MIR\DIST\ManualResultsTask.zip ( robocopy /R:3 \AppBuild-MIR\DIST\ j:\Staging-ImageReview\%DEPLOYMENTDIR%  ManualResultsTask.zip
)
if %ERRORLEVEL% GEQ 8  GOTO :error 

set STEP="Deploy HighOcrConfProcessor"
if exist \AppBuild-MIR\DIST\HighOcrConfProcessor.zip ( robocopy /R:3 \AppBuild-MIR\DIST\ j:\Staging-ImageReview\%DEPLOYMENTDIR%  HighOcrConfProcessor.zip
)
if %ERRORLEVEL% GEQ 8  GOTO :error 

set STEP="DeployHOCSendResultsProcessor"
if exist \AppBuild-MIR\DIST\HOCSendResultsProcessor.zip ( robocopy /R:3 \AppBuild-MIR\DIST\ j:\Staging-ImageReview\%DEPLOYMENTDIR%  HOCSendResultsProcessor.zip
)
if %ERRORLEVEL% GEQ 8  GOTO :error 

set STEP="Deploy PreHighOcrConfProcessor"
if exist \AppBuild-MIR\DIST\PreHighOcrConfProcessor.zip ( robocopy /R:3 \AppBuild-MIR\DIST\ j:\Staging-ImageReview\%DEPLOYMENTDIR%  PreHighOcrConfProcessor.zip
)
if %ERRORLEVEL% GEQ 8  GOTO :error 

set STEP="Deploy PreHOCSendResultsProcessor"
if exist \AppBuild-MIR\DIST\PreHOCSendResultsProcessor.zip ( robocopy /R:3 \AppBuild-MIR\DIST\ j:\Staging-ImageReview\%DEPLOYMENTDIR%  PreHOCSendResultsProcessor.zip
)
if %ERRORLEVEL% GEQ 8  GOTO :error 

set STEP="Deploy FPMSendResultsTask"
if exist \AppBuild-MIR\DIST\FPMSendResultsTask.zip ( robocopy /R:3 \AppBuild-MIR\DIST\ j:\Staging-ImageReview\%DEPLOYMENTDIR% FPMSendResultsTask.zip
)
if %ERRORLEVEL% GEQ 8  GOTO :error 

set STEP="Deploy FPPMatchPoolTask"
if exist \AppBuild-MIR\DIST\FPPMatchPoolTask.zip ( robocopy /R:3 \AppBuild-MIR\DIST\ j:\Staging-ImageReview\%DEPLOYMENTDIR% FPPMatchPoolTask.zip
)
if %ERRORLEVEL% GEQ 8  GOTO :error 

set STEP="Deploy IRRSendResultsTask"
if exist \AppBuild-MIR\DIST\IRRSendResultsTask.zip ( robocopy /R:3 \AppBuild-MIR\DIST\ j:\Staging-ImageReview\%DEPLOYMENTDIR% IRRSendResultsTask.zip
)
if %ERRORLEVEL% GEQ 8  GOTO :error 

set STEP="Deploy IRRSendResultsTask"
if exist \AppBuild-MIR\DIST\IRRSendResultsTaskRetries.zip ( robocopy /R:3 \AppBuild-MIR\DIST\ j:\Staging-ImageReview\%DEPLOYMENTDIR% IRRSendResultsTaskRetries.zip
)
if %ERRORLEVEL% GEQ 8  GOTO :error 

set STEP="Deploy ActiveDirectoryPollingTask"
if exist \AppBuild-MIR\DIST\ActiveDirectoryPollingTask.zip ( robocopy /R:3 \AppBuild-MIR\DIST\ j:\Staging-ImageReview\%DEPLOYMENTDIR% ActiveDirectoryPollingTask.zip
)
if %ERRORLEVEL% GEQ 8  GOTO :error 

set STEP="Deploy VehicleDetectionTask"
if exist \AppBuild-MIR\DIST\VehicleDetectionTask.zip ( robocopy /R:3 \AppBuild-MIR\DIST\ j:\Staging-ImageReview\%DEPLOYMENTDIR% VehicleDetectionTask.zip
)
if %ERRORLEVEL% GEQ 8  GOTO :error 

set STEP="Deploy ImageClientServiceTask_RetrieveTransactions"
if exist \AppBuild-MIR\DIST\ImageClientServiceTask_RetrieveTransactions.zip ( robocopy /R:3 \AppBuild-MIR\DIST\ j:\Staging-ImageReview\%DEPLOYMENTDIR% ImageClientServiceTask_RetrieveTransactions.zip
)
if %ERRORLEVEL% GEQ 8  GOTO :error 

set STEP="Deploy ImageClientServiceTask_SendResponse"
if exist \AppBuild-MIR\DIST\ImageClientServiceTask_SendResponse.zip ( robocopy /R:3 \AppBuild-MIR\DIST\ j:\Staging-ImageReview\%DEPLOYMENTDIR% ImageClientServiceTask_SendResponse.zip
)
if %ERRORLEVEL% GEQ 8  GOTO :error 

set STEP="Deploy MIREmailNotificationTask"
if exist \AppBuild-MIR\DIST\MIREmailNotificationTask.zip ( robocopy /R:3 \AppBuild-MIR\DIST\ j:\Staging-ImageReview\%DEPLOYMENTDIR% MIREmailNotificationTask.zip
)
if %ERRORLEVEL% GEQ 8  GOTO :error 

set STEP="Deploy VOTTQueueTask"
if exist \AppBuild-MIR\DIST\VOTTQueueTask.zip ( robocopy /R:3 \AppBuild-MIR\DIST\ j:\Staging-ImageReview\%DEPLOYMENTDIR% VOTTQueueTask.zip
)
if %ERRORLEVEL% GEQ 8  GOTO :error 

set STEP="Deploy HumanReadabilitySelectionTask"
if exist \AppBuild-MIR\DIST\HumanReadabilitySelectionTask.zip ( robocopy /R:3 \AppBuild-MIR\DIST\ j:\Staging-ImageReview\%DEPLOYMENTDIR% HumanReadabilitySelectionTask.zip
)
if %ERRORLEVEL% GEQ 8  GOTO :error 

if exist \AppBuild-MIR\DIST\TABLEAU.zip ( robocopy /R:3  \AppBuild-MIR\DIST\ j:\Staging-ImageReview\%DEPLOYMENTDIR% TABLEAU.zip
)
if %ERRORLEVEL% GEQ 8  GOTO :error 

if %CLUSTER%==true ( set STEP="Deploy APP02.zip" 
)
if exist \AppBuild-MIR\DIST\APP02.zip ( robocopy /R:3 \AppBuild-MIR\DIST\ j:\Staging-ImageReview\%DEPLOYMENTDIR% APP02.zip
)
if %ERRORLEVEL% GEQ 8  GOTO :error 

if %CLUSTER%==true ( set STEP="Deploy WEB02.zip" 
)
if exist \AppBuild-MIR\DIST\APP02.zip ( robocopy /R:3 \AppBuild-MIR\DIST\ j:\Staging-ImageReview\%DEPLOYMENTDIR% WEB02.zip
)
if %ERRORLEVEL% GEQ 8  GOTO :error 

if %CLUSTER%==true ( set STEP="Deploy FP02.zip" 
)
if exist \AppBuild-MIR\DIST\APP02.zip ( robocopy /R:3 \AppBuild-MIR\DIST\ j:\Staging-ImageReview\%DEPLOYMENTDIR% FP02.zip
)
if %ERRORLEVEL% GEQ 8  GOTO :error 
set STEP="Deploy TRANS02"
if exist \AppBuild-MIR\DIST\TRANS02.zip ( robocopy /R:3  \AppBuild-MIR\DIST\ j:\Staging-ImageReview\%DEPLOYMENTDIR% TRANS02.zip
)
if %ERRORLEVEL% GEQ 8  GOTO :error 

set STEP="Deploy IRR02"
if exist \AppBuild-MIR\DIST\IRR02.zip ( robocopy /R:3  \AppBuild-MIR\DIST\ j:\Staging-ImageReview\%DEPLOYMENTDIR% IRR02.zip
)
if %ERRORLEVEL% GEQ 8  GOTO :error 

set STEP="Deploy HOCP02"
if exist \AppBuild-MIR\DIST\HOCP02.zip ( robocopy /R:3  \AppBuild-MIR\DIST\ j:\Staging-ImageReview\%DEPLOYMENTDIR% HOCP02.zip
)
if %ERRORLEVEL% GEQ 8  GOTO :error 

set STEP="Deploy PREHOCP02"
if exist \AppBuild-MIR\DIST\PREHOCP02.zip ( robocopy /R:3  \AppBuild-MIR\DIST\ j:\Staging-ImageReview\%DEPLOYMENTDIR% PREHOCP02.zip
)
if %ERRORLEVEL% GEQ 8  GOTO :error 


echo ------------------------------------
echo Copy AES scripts
echo ------------------------------------
set STEP="Deploy AES Scripts"
xcopy /R /Y %DEPLOYROOTDIR%\AppBuild-MIR\AES.zip j:\Staging-ImageReview\%DEPLOYMENTDIR% 
if %ERRORLEVEL% NEQ 0 GOTO :error 
echo %STEP% Deployment complete 

if %ERRORLEVEL% NEQ 0 GOTO :error 

echo %STEP% Deployment complete 
GOTO finish

:HRFEATURE
set STEP="Deploy ImageReview HR"
if exist \AppBuild-MIR\DIST\HR01.zip ( robocopy /R:3 \AppBuild-MIR\DIST\ j:\Staging-ImageReview\%DEPLOYMENTDIR% HR01.zip
)
if %ERRORLEVEL% GEQ 8  GOTO :error 

set STEP="Deploy ActiveDirectoryPollingTask"
if exist \AppBuild-MIR\DIST\ActiveDirectoryPollingTask.zip ( robocopy /R:3 \AppBuild-MIR\DIST\ j:\Staging-ImageReview\%DEPLOYMENTDIR% ActiveDirectoryPollingTask.zip
)
if %ERRORLEVEL% GEQ 8  GOTO :error 

set STEP="Deploy ImageReview IFX"
if exist \AppBuild-MIR\DIST\ImgReviewSendTransTask.zip ( robocopy /R:3 \AppBuild-MIR\DIST\ j:\Staging-ImageReview\%DEPLOYMENTDIR% ImgReviewSendTransTask.zip
)
if %ERRORLEVEL% GEQ 8  GOTO :error 

set STEP="Deploy ImageReview IFX"
if exist \AppBuild-MIR\DIST\ImgReviewSendTransTaskRetries.zip ( robocopy /R:3 \AppBuild-MIR\DIST\ j:\Staging-ImageReview\%DEPLOYMENTDIR% ImgReviewSendTransTaskRetries.zip
)
if %ERRORLEVEL% GEQ 8  GOTO :error 

set STEP="Deploy ActiveDirectoryPollingTask"
if exist \AppBuild-MIR\DIST\ActiveDirectoryPollingTask.zip ( robocopy /R:3 \AppBuild-MIR\DIST\ j:\Staging-ImageReview\%DEPLOYMENTDIR% ActiveDirectoryPollingTask.zip
)
if %ERRORLEVEL% GEQ 8  GOTO :error 

set STEP="Deploy HumanReadabilitySelectionTask"
if exist \AppBuild-MIR\DIST\HumanReadabilitySelectionTask.zip ( robocopy /R:3 \AppBuild-MIR\DIST\ j:\Staging-ImageReview\%DEPLOYMENTDIR% HumanReadabilitySelectionTask.zip
)
if %ERRORLEVEL% GEQ 8  GOTO :error 

set STEP="Deploy AES Scripts"
xcopy /R /Y %DEPLOYROOTDIR%\AppBuild-MIR\AES.zip j:\Staging-ImageReview\%DEPLOYMENTDIR% 
if %ERRORLEVEL% NEQ 0 GOTO :error 

:finish
CD %CURRENTDIR%
echo --------------------------------------------------------------------
echo Deployment Completed...
echo --------------------------------------------------------------------
echo DEPLOYMENT COMPLETE 
GOTO END

:error
echo ------- AN ERROR OCCURED DURING THIS STEP: %STEP% ------
exit /b 1

:END