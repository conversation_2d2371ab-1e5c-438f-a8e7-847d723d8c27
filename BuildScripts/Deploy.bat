@echo off
rem set variables
Set CURRENTDIR=%CD%

echo ------- Your current directory is:  %CURRENTDIR% ---------

set NOW=%date:~10,4%-%date:~4,2%-%date:~7,2%
echo Deployment beginning %NOW%
echo -----------------------------------
echo Connect to Server
echo -----------------------------------
SET BASEDIR=%1
SET DEPLOYSERVER=%2
SET USER=%3
SET PASSWORD=%4
SET DEPLOYMENTDRIVE=%5
SET DEPLOYMENTDIR=%6
SET CLUSTER=%7
net use s: /delete
SET DISTDIR=%BASEDIR%\DIST



:zipfiles
echo -----------------------------------
echo Zip Distribution files
echo -----------------------------------

del /s %1\*.zip
set str=%1
echo.%str%
set str=%str:~0,2%
echo.%str% 
%str%
cd %DISTDIR%

rem %CURRENTDIR%\zip -r -q APP01.zip APP01\*
rem Note: Change to use 7zip to zip the App01 folder with a password to avoid the firewall issue
%CURRENTDIR%\7z a -mf- APP01.7z  APP01 -pSECRET
%CURRENTDIR%\zip -r -q WEB01.zip WEB01\*
%CURRENTDIR%\zip -r -q IMS01.zip IMS\*
if exist "%DISTDIR%\TABLEAU" (%CURRENTDIR%\zip -r -q TABLEAU.zip TABLEAU\*)
if exist "%DISTDIR%\EXT01" ( %CURRENTDIR%\zip -r -q EXT01.zip EXT01\* 
)

if exist "%DISTDIR%\APP03" ( %CURRENTDIR%\7z a -mf- APP03.7z  APP03 -pSECRET
)
rem %CURRENTDIR%\zip -r -q EXT01.zip EXT01\*

rem if %CLUSTER%==true ( %CURRENTDIR%\zip -r -q APP02.zip APP02\* )
rem Note: Change to use 7zip to zip the App02 folder with a password to avoid the firewall issue
if %CLUSTER%==true ( %CURRENTDIR%\7z a APP02.7z APP02 -pSECRET )
if %CLUSTER%==true ( %CURRENTDIR%\zip -r -q WEB02.zip WEB02\* )


cd %CURRENTDIR%

for /F %%i in ('dir /b "%BASEDIR%\BuildScripts.zip"') do (
	del /s /q BuildsScripts.zip
)

%CURRENTDIR%\zip -r %BASEDIR%\BuildScripts.zip BuildScripts\* 
del /s /q BuildScripts\*
rmdir /s /q BuildScripts


cd %BASEDIR%

echo zip TableauDeployTool

for /F %%i in ('dir /b "%BASEDIR%\TableauDeployTool.zip"') do (
	del /s /q TableauDeployTool.zip
)

%CURRENTDIR%\zip -r TableauDeployTool.zip TableauDeployTool\* 
del /s /q TableauDeployTool\*
rmdir /s /q TableauDeployTool

echo ZIP DONE 
echo ZIP DONE FOR CPS

cd %CURRENTDIR%

ECHO %DEPLOYSERVER%
ECHO %DEPLOYMENTDRIVE%
ECHO %DEPLOYMENTDIR%
ECHO %USER%

net use s: /delete

echo ------- map drive ----------
net use s: \\%DEPLOYSERVER%\%DEPLOYMENTDRIVE%$ %PASSWORD% /user:%USER%
if %ERRORLEVEL% NEQ 0 GOTO :error 
echo ------- destination cleanup -----------

if exist s:\Staging-CPS\%DEPLOYMENTDIR%\nul goto delCPSdir
goto newdir

:deploy
echo -----------------------------------
echo Deploy Build Scripts zip file to CPS Server....
echo -----------------------------------
if exist s:\Staging-CPS\BuildScripts.Zip (del /s /q s:\Staging-CPS\BuildScripts.zip)
if %ERRORLEVEL% NEQ 0 GOTO :error 
if exist s:\Staging-CPS\BuildScripts (del /s /q s:\Staging-CPS\BuildScripts\*)
if %ERRORLEVEL% NEQ 0 GOTO :error 
xcopy /R "%BASEDIR%\BuildScripts.zip" "s:\Staging-CPS\*" 
if %ERRORLEVEL% NEQ 0 GOTO :error 
unzip -ou s:\Staging-CPS\BuildScripts.zip -d  s:\Staging-CPS\
rem psexec -accepteula \\%DEPLOYSERVER% cmd /c (unzip -q %DEPLOYMENTDRIVE%:\Staging-CPS\BuildScripts.zip -d  %DEPLOYMENTDRIVE%:\Staging-CPS\)
if %ERRORLEVEL% NEQ 0 GOTO :error 
echo %STEP% Deployment complete 


echo -----------------------------------
echo Deploy TableauDeployTool zip file to CPS Server....
echo -----------------------------------
if exist s:\Staging-CPS\TableauDeployTool.Zip (del /s /q s:\Staging-CPS\TableauDeployTool.zip)
if %ERRORLEVEL% NEQ 0 GOTO :error 
if exist s:\Staging-CPS\TableauDeployTool (del /s /q s:\Staging-CPS\TableauDeployTool\*)
if %ERRORLEVEL% NEQ 0 GOTO :error 
xcopy /R "%BASEDIR%\TableauDeployTool.zip" "s:\Staging-CPS\*" 
if %ERRORLEVEL% NEQ 0 GOTO :error 
echo %STEP% TableauDeployTool Deployment complete 


echo ------------------------------------
echo Copy CPS....
echo ------------------------------------
set STEP="Deploy APP01.7z"
for /F %%i in ('dir /b "%DISTDIR%\APP01.7z"') do (
rem xcopy %DISTDIR%\APP01.zip s:\Staging-CPS\%DEPLOYMENTDIR%  
robocopy /R:3 %DISTDIR% s:\Staging-CPS\%DEPLOYMENTDIR% APP01.7z
)
if %ERRORLEVEL% GEQ 8 GOTO :error 



if %ERRORLEVEL% GEQ 8 GOTO :error 
set STEP="Deploy APP03"
for /F %%i in ('dir /b "%DISTDIR%\APP03.7z"') do (
rem xcopy %DISTDIR%\APP01.zip s:\Staging-CPS\%DEPLOYMENTDIR%  
robocopy /R:3 %DISTDIR% s:\Staging-CPS\%DEPLOYMENTDIR% APP03.7z
)
if %ERRORLEVEL% GEQ 8 GOTO :error 

echo copy to APP02 if Cluster is set to True
if %CLUSTER%==true ( set STEP="Deploy APP02.7z" 
)
if %ERRORLEVEL% GEQ 8 GOTO :error 
rem if %CLUSTER%==true ( xcopy %DISTDIR%\APP02.7z s:\Staging-CPS\%DEPLOYMENTDIR%   
rem )
if %CLUSTER%==true ( robocopy /R:3 %DISTDIR% s:\Staging-CPS\%DEPLOYMENTDIR% APP02.7z 
)
if %ERRORLEVEL% GEQ 8 GOTO :error 

for /F %%i in ('dir /b "%DISTDIR%\WEB01.zip"') do (
rem xcopy %DISTDIR%\WEB01.zip s:\Staging-CPS\%DEPLOYMENTDIR%  
robocopy /R:3 %DISTDIR% s:\Staging-CPS\%DEPLOYMENTDIR% WEB01.zip
)
if %ERRORLEVEL% GEQ 8 GOTO :error 
echo copy to WEB02 if Cluster is set to True
if %CLUSTER%==true ( set STEP="Deploy WEB02.zip" 
)
if %ERRORLEVEL% GEQ 8 GOTO :error 
rem if %CLUSTER%==true ( xcopy %DISTDIR%\WEB02.zip s:\Staging-CPS\%DEPLOYMENTDIR%   
rem )
if %CLUSTER%==true ( robocopy /R:3 %DISTDIR% s:\Staging-CPS\%DEPLOYMENTDIR% WEB02.zip
)
if %ERRORLEVEL% GEQ 8 GOTO :error 
for /F %%i in ('dir /b "%DISTDIR%\IMS01.zip"') do (
rem xcopy %DISTDIR%\IMS01.zip s:\Staging-CPS\%DEPLOYMENTDIR%  
robocopy /R:3 %DISTDIR% s:\Staging-CPS\%DEPLOYMENTDIR% IMS01.zip 
)
if %ERRORLEVEL% GEQ 8 GOTO :error 


if %ERRORLEVEL% GEQ 8 GOTO :error 
set STEP="Deploy EXT01"
for /F %%i in ('dir /b "%DISTDIR%\EXT01.zip"') do (
rem xcopy %DISTDIR%\APP01.7z s:\Staging-CPS\%DEPLOYMENTDIR%  
robocopy /R:3 %DISTDIR% s:\Staging-CPS\%DEPLOYMENTDIR% EXT01.zip
)
if %ERRORLEVEL% GEQ 8 GOTO :error 




set STEP="Deploy TABLEAU"
for /F %%i in ('dir /b "%DISTDIR%\TABLEAU.zip"') do (
rem xcopy %DISTDIR%\APP01.zip s:\Staging-CPS\%DEPLOYMENTDIR%  
robocopy /R:3 %DISTDIR% s:\Staging-CPS\%DEPLOYMENTDIR% TABLEAU.zip
)
if %ERRORLEVEL% GEQ 8 GOTO :error 
echo %STEP% Deployment complete 


CD %CURRENTDIR%

echo --------------------------------------------------------------------
echo Deployment Completed...
echo --------------------------------------------------------------------
echo DEPLOYMENT COMPLETE 
GOTO END



:delCPSdir
echo ------- delete deployment directory ---------
if exist s:\Staging-CPS\%DEPLOYMENTDIR% (rmdir /s /q s:\Staging-CPS\%DEPLOYMENTDIR%) 
goto newdir

:newdir
echo ------- Create dated deployment directory -----------------
mkdir s:\Staging-CPS\%DEPLOYMENTDIR%
if %ERRORLEVEL% NEQ 0 GOTO :error 
goto deploy

:error
echo ------- AN ERROR OCCURED DURING THIS STEP: %STEP% ------
exit /b 1

:END