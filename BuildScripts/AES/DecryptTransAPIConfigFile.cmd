@ECHO OFF
rem - Encrypt configuration files for Image Review

echo --------------------------------------------------------------------
echo Decrypting TransactionsAPI Config Files - Production
echo --------------------------------------------------------------------


echo * Decrypting config file for TransactionsServer MIRInternalServiceHost...
for /F %%i in ('dir /b "C:\MIR\TransactionsServer\ServiceHost\Tcore.MIR.MIRInternalServiceHost.exe"') do (
AesHelper.exe /uo /exe C:\MIR\TransactionsServer\ServiceHost\Tcore.MIR.MIRInternalServiceHost.exe
 )
if %ERRORLEVEL% NEQ 0 GOTO :error


 
echo --------------------------------------------------------------------
echo Decrypting TransactionsAPI Internal Web Site Config Files - Production
echo --------------------------------------------------------------------

echo * Decrypting config file for Image Review TransactionServer WebAPI web site...
AesHelper.exe /uo /web /
if %ERRORLEVEL% NEQ 0 GOTO :error

echo --------------------------------------------------------------------
echo Decryption of TransactionsAPI Completed...
echo --------------------------------------------------------------------

GOTO END
:error
echo ------- AN ERROR OCCURED DURING THIS STEP: %STEP% ------
exit /b 1


GOTO END

:END
exit /B 0
goto :EOF
