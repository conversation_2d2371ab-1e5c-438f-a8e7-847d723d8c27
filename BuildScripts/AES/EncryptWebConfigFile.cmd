@ECHO OFF
rem - Encrypt configuration files for MOMS Scheduled Tasks, MOMS Services and MOMS Web Site.

echo --------------------------------------------------------------------
echo Encrypting Primary Internal Scheduled Task Config Files - Production
echo --------------------------------------------------------------------
echo create temp.log >temp.log
echo * Encrypting config file for CAMS_MOMSEventLoaderConsole...
if exist "C:\moms\ScheduledTasks\CAMS_MOMSEventLoader\tcore.MOMSEventLoaderConsole.exe" (
AesHelper.exe /po /exe C:\moms\ScheduledTasks\CAMS_MOMSEventLoader\tcore.MOMSEventLoaderConsole.exe >> temp.log
 )

echo * Encrypting config file for GFI_MOMSEventLoaderConsole...
if exist "C:\moms\ScheduledTasks\GFI_MOMSEventLoader\tcore.MOMSEventLoaderConsole.exe" (
AesHelper.exe /po /exe C:\moms\ScheduledTasks\GFI_MOMSEventLoader\tcore.MOMSEventLoaderConsole.exe >> temp.log
 )

echo * Encrypting config file for LANE_MOMSEventLoaderConsole...
if exist "C:\moms\ScheduledTasks\LANE_MOMSEventLoader\tcore.MOMSEventLoaderConsole.exe" (
AesHelper.exe /po /exe C:\moms\ScheduledTasks\LANE_MOMSEventLoader\tcore.MOMSEventLoaderConsole.exe >> temp.log
 )
 
echo * Encrypting config file for MOMSEventLoaderConsole...
if exist "C:\moms\ScheduledTasks\MOMSEventLoader\tcore.MOMSEventLoaderConsole.exe" (
AesHelper.exe /po /exe C:\moms\ScheduledTasks\MOMSEventLoader\tcore.MOMSEventLoaderConsole.exe >> temp.log
 )
 
echo * Encrypting config file for WhatsUpEventLoaderConsole...
if exist "C:\moms\ScheduledTasks\WhatsUp_MOMSEventLoader\tcore.MOMSEventLoaderConsole.exe" (
AesHelper.exe /po /exe C:\moms\ScheduledTasks\WhatsUp_MOMSEventLoader\tcore.MOMSEventLoaderConsole.exe >> temp.log
 )
 
echo * Encrypting config file for SolarWindsEventLoaderConsole...
if exist "C:\moms\ScheduledTasks\SolarWinds_MOMSEventLoader\tcore.MOMSEventLoaderConsole.exe" (
AesHelper.exe /po /exe C:\moms\ScheduledTasks\SolarWinds_MOMSEventLoader\tcore.MOMSEventLoaderConsole.exe >> temp.log
 )
 
echo * Encrypting config file for ExternalNotifierConsole...
if exist "C:\moms\ScheduledTasks\MOMSExternalNotifier\tcore.MOMSExternalNotifierConsole.exe" (
AesHelper.exe /po /exe C:\moms\ScheduledTasks\MOMSExternalNotifier\tcore.MOMSExternalNotifierConsole.exe >> temp.log
 )
 
echo * Encrypting config file for MOMSFailureAnalysisTaskConsole...
if exist "C:\moms\ScheduledTasks\MOMSFailureAnalysisTaskConsole\tcore.MOMSFailureAnalysisTaskConsole.exe" (
AesHelper.exe /po /exe C:\moms\ScheduledTasks\MOMSFailureAnalysisTaskConsole\tcore.MOMSFailureAnalysisTaskConsole.exe >> temp.log
 )

echo * Encrypting config file for MOMSInventoryLevelNotification...
if exist "C:\moms\ScheduledTasks\MOMSInventoryLevelNotification\tcore.MOMSInventoryLevelNotification.exe" (
AesHelper.exe /po /exe C:\moms\ScheduledTasks\MOMSInventoryLevelNotification\tcore.MOMSInventoryLevelNotification.exe >> temp.log
 )

echo * Encrypting config file for MOMSNotificationEscalation...
if exist "C:\moms\ScheduledTasks\MOMSNotificationEscalationTask\tcore.MOMSNotificationEscalation.exe" (
AesHelper.exe /po /exe C:\moms\ScheduledTasks\MOMSNotificationEscalationTask\tcore.MOMSNotificationEscalation.exe >> temp.log
 )

echo * Encrypting config file for MOMSPredictiveMaintenanceNotificationTaskConsole...
if exist "C:\moms\ScheduledTasks\MOMSPredictiveMaintenanceNotificationTaskConsole\tcore.MOMSPredictiveMaintenanceNotificationTaskConsole.exe" (
AesHelper.exe /po /exe C:\moms\ScheduledTasks\MOMSPredictiveMaintenanceNotificationTaskConsole\tcore.MOMSPredictiveMaintenanceNotificationTaskConsole.exe >> temp.log
 )

echo * Encrypting config file for MOMSPreventiveMaintenanceTask...
if exist "C:\moms\ScheduledTasks\MOMSPreventiveMaintenanceTask\tcore.MOMSPreventiveMaintenanceTask.exe" (
AesHelper.exe /po /exe C:\moms\ScheduledTasks\MOMSPreventiveMaintenanceTask\tcore.MOMSPreventiveMaintenanceTask.exe >> temp.log
 )

echo * Encrypting config file for MOMSUTSNotifierConsole...
if exist "C:\moms\ScheduledTasks\MOMSUTSNotifier\tcore.MOMSUTSNotifierConsole.exe" (
AesHelper.exe /po /exe C:\moms\ScheduledTasks\MOMSUTSNotifier\tcore.MOMSUTSNotifierConsole.exe >> temp.log
 )
 
echo * Encrypting config file for MOMSInfinityNotifierConsole...
if exist "C:\moms\ScheduledTasks\MOMSInfinityNotifier\tcore.MOMSInfinityNotifierConsole.exe" (
AesHelper.exe /po /exe C:\moms\ScheduledTasks\MOMSInfinityNotifier\tcore.MOMSInfinityNotifierConsole.exe >> temp.log
 )

echo * Encrypting config file for MOMSWorkOrderMessagingTask...
if exist "C:\moms\ScheduledTasks\MOMSWorkOrderMessagingTask\tcore.MOMSWorkOrderMessagingTask.exe" (
AesHelper.exe /po /exe C:\moms\ScheduledTasks\MOMSWorkOrderMessagingTask\tcore.MOMSWorkOrderMessagingTask.exe >> temp.log
 )
 
echo * Encrypting config file for Integrity_MOMSEventLoader...
if exist "C:\moms\ScheduledTasks\Integrity_MOMSEventLoader\tcore.MOMSEventLoaderConsole.exe" (
AesHelper.exe /po /exe C:\moms\ScheduledTasks\Integrity_MOMSEventLoader\tcore.MOMSEventLoaderConsole.exe >> temp.log
 )

echo * Encrypting config file for ImageReview_MOMSEventLoader...
if exist "C:\moms\ScheduledTasks\ImageReview_MOMSEventLoader\tcore.MOMSEventLoaderConsole.exe" (
AesHelper.exe /po /exe C:\moms\ScheduledTasks\ImageReview_MOMSEventLoader\tcore.MOMSEventLoaderConsole.exe >> temp.log
 )
 
echo * Encrypting config file for MOMSActiveDirectoryPolling...
if exist "C:\moms\ScheduledTasks\MOMSActiveDirectoryPolling\tcore.MOMSActiveDirectoryPolling.exe" (
AesHelper.exe /po /exe C:\moms\ScheduledTasks\MOMSActiveDirectoryPolling\tcore.MOMSActiveDirectoryPolling.exe >> temp.log
 )


echo --------------------------------------------------------------------
echo Encrypting Primary Internal Service Config Files - Production
echo --------------------------------------------------------------------

echo * Encrypting config file for MOMSExternalServiceHost...
if exist "C:\moms\ServiceHost\tcore.MOMSExternalServiceHost.exe" (
AesHelper.exe /po /exe C:\moms\ServiceHost\tcore.MOMSExternalServiceHost.exe >> temp.log
 )

echo * Encrypting config file for MOMSServiceHost...
AesHelper.exe /po /exe C:\moms\ServiceHost\tcore.MOMSServiceHost.exe >> temp.log
 )

echo * Encrypting config file for MOMSTrafficLoader...
if exist "C:\moms\ServiceHost\tcore.MOMSTrafficLoaderHost.exe" (
AesHelper.exe /po /exe C:\moms\ServiceHost\tcore.MOMSTrafficLoaderHost.exe >> temp.log
 )
 
 rem comment out until error is resolved
 rem ERROR:   Unrecognized attribute 'configProtectionProvider'. Note that attribute names are case-sensitive.  echo * Encrypting config file
 rem for MOMSGPSLocatorService... Restart the IIS 
if exist "C:\moms\ServiceHost\tcore.MOMSGPSLocatorServiceHost.exe" (
AesHelper.exe /po /exe C:\moms\ServiceHost\tcore.MOMSGPSLocatorServiceHost.exe >> temp.log
 )
 
echo * Encrypting config file for TransSuiteMOMSService...
if exist "C:\MOMSInterfaces\TransSuiteMOMSService\ServiceHost\tcore.TransSuite.TransSuiteMOMSServiceHost.exe" (
AesHelper.exe /po /exe C:\MOMSInterfaces\TransSuiteMOMSService\ServiceHost\tcore.TransSuite.TransSuiteMOMSServiceHost.exe >> temp.log
)

echo * Encrypting config file for InfinityMOMSService...
if exist "C:\MOMSInterfaces\InfinityMOMSService\ServiceHost\tcore.Infinity.InfinityMOMSServiceHost.exe" (
AesHelper.exe /po /exe C:\MOMSInterfaces\InfinityMOMSService\ServiceHost\tcore.Infinity.InfinityMOMSServiceHost.exe >> temp.log
)

echo * Encrypting config file for TcoreSSOService...
if exist "C:\MOMSInterfaces\TcoreSSO\ServiceHost\tcore.SSO.TcoreSSOServiceHost.exe" (
AesHelper.exe /po /exe C:\MOMSInterfaces\TcoreSSO\ServiceHost\tcore.SSO.TcoreSSOServiceHost.exe >> temp.log
)



echo --------------------------------------------------------------------
echo Encrypting Primary Internal Web Site Config Files - Production
echo --------------------------------------------------------------------

echo * Encrypting config file for MOMS web site...
AesHelper.exe /po /web / >> temp.log



echo --------------------------------------------------------------------
echo Encryption Completed...
echo --------------------------------------------------------------------


GOTO END
:error
echo ------- AN ERROR OCCURED DURING THIS STEP: %STEP% ------
exit /b 1

del /s /q temp.log
GOTO END

:END
exit /B 0
goto :EOF