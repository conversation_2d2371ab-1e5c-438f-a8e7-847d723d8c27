@ECHO OFF
rem - Encrypt configuration files for MIR Scheduled Tasks, MIR Services and MIR Web Site.

echo --------------------------------------------------------------------
echo Encrypting Primary Service Config Files - Production
echo --------------------------------------------------------------------

   
 echo * Encrypting config file for HealthCheckServiceHost...
for /F %%i in ('dir /b "C:\MIR\ImageReview\ServiceHost\HealthCheckServiceHost\tcore.ImageReview.HealthCheckServiceHost.exe"') do (
AesHelper.exe /po /exe C:\MIR\ImageReview\ServiceHost\HealthCheckServiceHost\tcore.ImageReview.HealthCheckServiceHost.exe
 )
if %ERRORLEVEL% NEQ 0 GOTO :error
 
 echo * Encrypting config file for ImageClientServiceHost...
for /F %%i in ('dir /b "C:\MIR\ImageReview\ServiceHost\ImageClientServiceHost\tcore.ImageReview.ImageClientServiceHost.exe"') do (
AesHelper.exe /po /exe C:\MIR\ImageReview\ServiceHost\ImageClientServiceHost\tcore.ImageReview.ImageClientServiceHost.exe
 )
if %ERRORLEVEL% NEQ 0 GOTO :error

 echo * Encrypting config file for IRServiceHost...
for /F %%i in ('dir /b "C:\MIR\ImageReview\ServiceHost\IRServiceHost\tcore.ImageReview.ImageReviewServiceHost.exe"') do (
AesHelper.exe /po /exe C:\MIR\ImageReview\ServiceHost\IRServiceHost\tcore.ImageReview.ImageReviewServiceHost.exe
 )
if %ERRORLEVEL% NEQ 0 GOTO :error
 
 echo * Encrypting config file for LicensePlateManagerHost...
for /F %%i in ('dir /b "C:\MIR\ImageReview\ServiceHost\LicensePlateManagerHost\tcore.ImageReview.LicensePlateManagerHost.exe"') do (
AesHelper.exe /po /exe C:\MIR\ImageReview\ServiceHost\LicensePlateManagerHost\tcore.ImageReview.LicensePlateManagerHost.exe
 )
if %ERRORLEVEL% NEQ 0 GOTO :error

 echo * Encrypting config file for TransactionQingServiceHost...
for /F %%i in ('dir /b "C:\MIR\ImageReview\ServiceHost\TransactionQingServiceHost\tcore.ImageReview.ImageTransactionQueueingService.exe"') do (
AesHelper.exe /po /exe C:\MIR\ImageReview\ServiceHost\TransactionQingServiceHost\tcore.ImageReview.ImageTransactionQueueingService.exe
 )
if %ERRORLEVEL% NEQ 0 GOTO :error

 echo * Encrypting config file for Image Review web site...
AesHelper.exe /po /web /
verify > nul
if %ERRORLEVEL% NEQ 0 GOTO :error


echo --------------------------------------------------------------------
echo Encryption Completed...
echo --------------------------------------------------------------------


GOTO END
:error
echo ------- AN ERROR OCCURED DURING THIS STEP: %STEP% ------
exit /b 1


GOTO END

:END
exit /B 0
goto :EOF