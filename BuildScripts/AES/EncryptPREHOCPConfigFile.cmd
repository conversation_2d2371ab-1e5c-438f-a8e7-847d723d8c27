@ECHO OFF
rem - Encrypt configuration files for Image Review

echo --------------------------------------------------------------------
echo Encrypting PREHOCP Internal Service Config Files - Production
echo --------------------------------------------------------------------


echo * Encrypting config file for PREHOCPInternalServiceHost...
for /F %%i in ('dir /b "C:\PREHOCP\ServiceHost\Tcore.HOCP.HOCPInternalServiceHost.exe"') do (
AesHelper.exe /po /exe C:\PREHOCP\ServiceHost\Tcore.HOCP.HOCPInternalServiceHost.exe
 )
if %ERRORLEVEL% NEQ 0 GOTO :error


echo --------------------------------------------------------------------
echo Encrypting PREHOCP Internal Web Site Config Files - Production
echo --------------------------------------------------------------------

echo * Encrypting config file for PREHOCP WebAPI web site...
AesHelper.exe /po /web /
if %ERRORLEVEL% NEQ 0 GOTO :error

echo --------------------------------------------------------------------
echo Encryption of PREHOCP Completed...
echo --------------------------------------------------------------------

GOTO END
:error
echo ------- AN ERROR OCCURED DURING THIS STEP: %STEP% ------
exit /b 1


GOTO END

:END
exit /B 0
goto :EOF
