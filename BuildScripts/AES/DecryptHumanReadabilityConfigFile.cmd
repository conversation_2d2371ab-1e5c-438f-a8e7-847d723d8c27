@ECHO OFF
rem - Decrypt configuration files for MIR Scheduled Tasks, MIR Services and MIR Web Site.

echo --------------------------------------------------------------------
echo Decrypting MIR Service Host Config Files - Production
echo --------------------------------------------------------------------

 
 echo * Decrypting config file for Tcore.MIR.MIRInternalServiceHost.exe...
for /F %%i in ('dir /b "C:\MIR\TransactionsServer\ServiceHost\Tcore.MIR.MIRInternalServiceHost.exe"') do (
AesHelper.exe /uo /exe C:\MIR\TransactionsServer\ServiceHost\Tcore.MIR.MIRInternalServiceHost.exe
 )

 
 echo * Decrypting config file for ImageREviewServiceHost...
for /F %%i in ('dir /b "C:\MIR\ImageReview\ServiceHost\IRServiceHost\tcore.ImageReview.ImageReviewServiceHost.exe"') do (
AesHelper.exe /uo /exe C:\MIR\ImageReview\ServiceHost\IRServiceHost\tcore.ImageReview.ImageReviewServiceHost.exe
 )



 echo * Decrypting config file for Image Review web site...
AesHelper.exe /uo /web /
verify > nul



echo --------------------------------------------------------------------
echo Encryption Completed...
echo --------------------------------------------------------------------


GOTO END
:error
echo ------- AN ERROR OCCURED DURING THIS STEP: %STEP% ------
exit /b 1


GOTO END

:END
exit /B 0
goto :EOF