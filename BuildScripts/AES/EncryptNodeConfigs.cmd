@echo off
rem set variables
Set CURRENTDIR=%CD%

echo ------- Your current directory is:  %CURRENTDIR% ---------

set NOW=%date:~10,4%-%date:~4,2%-%date:~7,2%
echo Deployment beginning %NOW%
echo -----------------------------------
echo Encrypt tasks
echo -----------------------------------
verify > nul
SET GENTASK=%1
SET TASKROADCOMBO=%2

SET BASEDIR=%JUMPDRIVE%:\Staging-ImageReview\%DEPLOYMENTDIR%

if "%GENTASK%"=="ActiveDirectoryPollingTask" IF EXIST C:\IRR\ScheduledTasks\%TASKROADCOMBO%\tcore.ADP.ActiveDirectoryPolling.exe.config (
AesHelper.exe /po /exe C:\IRR\ScheduledTasks\%TASKROADCOMBO%\tcore.ADP.ActiveDirectoryPolling.exe
 )
if %ERRORLEVEL% NEQ 0 GOTO :error

echo -----------------------------------
echo Encrypting MIR scheduled task for AuditReviewResultsTask....
echo -----------------------------------
if "%GENTASK%"=="AuditReviewResultsTask" IF EXIST C:\MIR\ManualResultsTask\ScheduledTasks\%TASKROADCOMBO%\Tcore.MIR.AuditReviewResultsTask.exe.config (
AesHelper.exe /po /exe C:\MIR\ManualResultsTask\ScheduledTasks\%TASKROADCOMBO%\Tcore.MIR.AuditReviewResultsTask.exe
)
if %ERRORLEVEL% NEQ 0 GOTO :error


echo -----------------------------------
echo Encrypting FPMSendResults scheduled task for Image Review Fingerprint....
echo -----------------------------------
if "%GENTASK%"=="FPMSendResultsTask" IF EXIST C:\FPM\ScheduledTasks\%TASKROADCOMBO%\Tcore.FPM.FPSendResultsTask.exe.config (
AesHelper.exe /po /exe C:\FPM\ScheduledTasks\%TASKROADCOMBO%\Tcore.FPM.FPSendResultsTask.exe
)
if %ERRORLEVEL% NEQ 0 GOTO :error

echo -----------------------------------
echo Encrypting FPPMatchPoolTask scheduled task for Image Review Fingerprint....
echo -----------------------------------
if "%GENTASK%"=="FPPMatchPoolTask" IF EXIST C:\FPP\ScheduledTasks\%TASKROADCOMBO%\Tcore.FPP.FPPMatchPoolTask.exe.config (
AesHelper.exe /po /exe C:\FPP\ScheduledTasks\%TASKROADCOMBO%\Tcore.FPP.FPPMatchPoolTask.exe
)
if %ERRORLEVEL% NEQ 0 GOTO :error

echo -----------------------------------
echo Encrypting GenerateROITask scheduled task....
echo -----------------------------------
if "%GENTASK%"=="GenerateROITask" IF EXIST  C:\MIR\ScheduledTasks\%TASKROADCOMBO%\Tcore.IFX.GenerateRoiCreateUrlTaskConsole.exe.config (
AesHelper.exe /po /exe C:\MIR\ScheduledTasks\%TASKROADCOMBO%\Tcore.IFX.GenerateRoiCreateUrlTaskConsole.exe
)
if %ERRORLEVEL% NEQ 0 GOTO :error

echo -----------------------------------
echo Encrypting GenerateROITaskRetries scheduled task....
echo -----------------------------------
if "%GENTASK%"=="GenerateROITaskRetries" IF EXIST C:\MIR\ScheduledTasks\%TASKROADCOMBO%\Tcore.IFX.GenerateRoiCreateUrlTaskConsole.exe.config (
AesHelper.exe /po /exe C:\MIR\ScheduledTasks\%TASKROADCOMBO%\Tcore.IFX.GenerateRoiCreateUrlTaskConsole.exe
)
if %ERRORLEVEL% NEQ 0 GOTO :error


echo -----------------------------------
echo Encrypting HOCP HighOcrConf Processor scheduled task for Image Review HOCP....
echo -----------------------------------
if "%GENTASK%"=="HighOcrConfProcessor" IF EXIST C:\HOCP\ScheduledTasks\%TASKROADCOMBO%\Tcore.HOCP.HighOcrConfProcessor.exe.config (
AesHelper.exe /po /exe C:\HOCP\ScheduledTasks\%TASKROADCOMBO%\Tcore.HOCP.HighOcrConfProcessor.exe
)
if %ERRORLEVEL% NEQ 0 GOTO :error

echo -----------------------------------
echo Encrypting HOCP SendResults scheduled task for Image Review HOCP ....
echo -----------------------------------
if "%GENTASK%"=="HOCSendResultsProcessor" IF EXIST C:\HOCP\ScheduledTasks\%TASKROADCOMBO%\Tcore.HOCP.HOCSendResultsProcessor.exe.config (
AesHelper.exe /po /exe  C:\HOCP\ScheduledTasks\%TASKROADCOMBO%\Tcore.HOCP.HOCSendResultsProcessor.exe
)
if %ERRORLEVEL% NEQ 0 GOTO :error


echo -----------------------------------
echo Encrypting PREHOCP HighOcrConf Processor scheduled task for Image Review PREHOCP....
echo -----------------------------------
if "%GENTASK%"=="PreHighOcrConfProcessor" IF EXIST C:\PREHOCP\ScheduledTasks\%TASKROADCOMBO%\Tcore.HOCP.HighOcrConfProcessor.exe.config (
AesHelper.exe /po /exe C:\PREHOCP\ScheduledTasks\%TASKROADCOMBO%\Tcore.HOCP.HighOcrConfProcessor.exe
)
if %ERRORLEVEL% NEQ 0 GOTO :error

echo -----------------------------------
echo Encrypting PREHOCP SendResults scheduled task for Image Review PREHOCP ....
echo -----------------------------------
if "%GENTASK%"=="PreHOCSendResultsProcessor" IF EXIST C:\PREHOCP\ScheduledTasks\%TASKROADCOMBO%\Tcore.HOCP.HOCSendResultsProcessor.exe.config (
AesHelper.exe /po /exe  C:\PREHOCP\ScheduledTasks\%TASKROADCOMBO%\Tcore.HOCP.HOCSendResultsProcessor.exe
)
if %ERRORLEVEL% NEQ 0 GOTO :error


echo -----------------------------------
echo Encrypting MIR scheduled task for HumanReadabilitySelectionTask....
echo -----------------------------------
if "%GENTASK%"=="HumanReadabilitySelectionTask" (
AesHelper.exe /po /exe  C:\MIR\ScheduledTasks\%TASKROADCOMBO%\HumanReadabilitySelectionTask.exe
)
if %ERRORLEVEL% NEQ 0 GOTO :error

echo -----------------------------------
echo Encrypting ImageClientServiceTask_RetrieveTransactions scheduled task....
echo -----------------------------------
if "%GENTASK%"=="ImageClientServiceTask_RetrieveTransactions" IF EXIST C:\MIR\ImageReview\ScheduledTasks\%TASKROADCOMBO%\tcore.ImageReview.ScheduledTasks.ImageClientServiceTask.exe.config (
AesHelper.exe /po /exe C:\MIR\ImageReview\ScheduledTasks\%TASKROADCOMBO%\tcore.ImageReview.ScheduledTasks.ImageClientServiceTask.exe
)
if %ERRORLEVEL% NEQ 0 GOTO :error

echo -----------------------------------
echo Encrypting ImageClientServiceTask_SendResponse scheduled task....
echo -----------------------------------
if "%GENTASK%"=="ImageClientServiceTask_SendResponse" IF EXIST C:\MIR\ImageReview\ScheduledTasks\%TASKROADCOMBO%\tcore.ImageReview.ScheduledTasks.ImageClientServiceTask.exe.config (
AesHelper.exe /po /exe  C:\MIR\ImageReview\ScheduledTasks\%TASKROADCOMBO%\tcore.ImageReview.ScheduledTasks.ImageClientServiceTask.exe
)
if %ERRORLEVEL% NEQ 0 GOTO :error

echo -----------------------------------
echo Encrypting MIR ManualResultsTask scheduled task ....
echo -----------------------------------
if "%GENTASK%"=="ManualResultsTask" IF EXIST C:\MIR\ManualResultsTask\ScheduledTasks\%TASKROADCOMBO%\Tcore.MIR.ManualResultsTask.exe.config (
AesHelper.exe /po /exe  C:\MIR\ManualResultsTask\ScheduledTasks\%TASKROADCOMBO%\Tcore.MIR.ManualResultsTask.exe
)
if %ERRORLEVEL% NEQ 0 GOTO :error

echo -----------------------------------
echo Encrypting ImgReviewSendTransTask scheduled task....
echo -----------------------------------
if "%GENTASK%"=="ImgReviewSendTransTask" IF EXIST C:\MIR\ScheduledTasks\%TASKROADCOMBO%\Tcore.IFX.ImgRevSendTrans.exe.config (
AesHelper.exe /po /exe  C:\MIR\ScheduledTasks\%TASKROADCOMBO%\Tcore.IFX.ImgRevSendTrans.exe
)
if %ERRORLEVEL% NEQ 0 GOTO :error

echo -----------------------------------
echo Encrypting ImgReviewSendTransTaskRetries scheduled task....
echo -----------------------------------
if "%GENTASK%"=="ImgReviewSendTransTaskRetries" IF EXIST C:\MIR\ScheduledTasks\%TASKROADCOMBO%\Tcore.IFX.ImgRevSendTrans.exe.config (
AesHelper.exe /po /exe  C:\MIR\ScheduledTasks\%TASKROADCOMBO%\Tcore.IFX.ImgRevSendTrans.exe 
)
if %ERRORLEVEL% NEQ 0 GOTO :error


echo -----------------------------------
echo Encrypting MIR IRRSendResults scheduled task ....
echo -----------------------------------
if "%GENTASK%"=="IRRSendResultsTask" IF EXIST C:\IRR\ScheduledTasks\%TASKROADCOMBO%\Tcore.irr.IRRSendResultsTask.exe.config (
AesHelper.exe /po /exe  C:\IRR\ScheduledTasks\%TASKROADCOMBO%\Tcore.irr.IRRSendResultsTask.exe
)
if %ERRORLEVEL% NEQ 0 GOTO :error



echo -----------------------------------
echo Encrypting MIR IRRSendResultsRetries scheduled task ....
echo -----------------------------------
if "%GENTASK%"=="IRRSendResultsTaskRetries" IF EXIST C:\IRR\ScheduledTasks\%TASKROADCOMBO%\Tcore.irr.IRRSendResultsTask.exe.config (
AesHelper.exe /po /exe  C:\IRR\ScheduledTasks\%TASKROADCOMBO%\Tcore.irr.IRRSendResultsTask.exe
)
if %ERRORLEVEL% NEQ 0 GOTO :error


echo -----------------------------------
echo Encrypting MIRNotificationEscalationTask scheduled task....
echo -----------------------------------
if "%GENTASK%"=="MIREmailNotificationTask" IF EXIST C:\MIR\ImageReview\ScheduledTasks\%TASKROADCOMBO%\tcore.ImageReview.ScheduledTasks.MIREmailNotificationTask.exe.config (
AesHelper.exe /po /exe  C:\MIR\ImageReview\ScheduledTasks\%TASKROADCOMBO%\tcore.ImageReview.ScheduledTasks.MIREmailNotificationTask.exe
)
if %ERRORLEVEL% NEQ 0 GOTO :error

echo -----------------------------------
echo Encrypting VerhicleDetectionTask scheduled task....
echo -----------------------------------
if "%GENTASK%"=="VehicleDetectionTask" IF EXIST C:\MIR\ImageReview\ScheduledTasks\%TASKROADCOMBO%\VehicleDetectionTask.exe.config (
AesHelper.exe /po /exe   C:\MIR\ImageReview\ScheduledTasks\%TASKROADCOMBO%\VehicleDetectionTask.exe
)
if %ERRORLEVEL% NEQ 0 GOTO :error

echo -----------------------------------
echo Encrypting VOTTQueueTask scheduled task....
echo -----------------------------------
if "%GENTASK%"=="VOTTQueueTask" IF EXIST C:\MIR\ImageReview\ScheduledTasks\%TASKROADCOMBO%\tcore.ImageReview.ScheduledTasks.VOTTQueueTask.exe.config (
AesHelper.exe /po /exe   C:\MIR\ImageReview\ScheduledTasks\%TASKROADCOMBO%\tcore.ImageReview.ScheduledTasks.VOTTQueueTask.exe
)
if %ERRORLEVEL% NEQ 0 GOTO :error

GOTO END



:error
echo ------- AN ERROR OCCURED DURING THIS STEP: Encrypt task %SERVER% %GENTASK% %road%------
exit /b 1

:END
echo -- Encrypt task %SERVER% %GENTASK% %road% is complete