@ECHO OFF
rem - Decrypt Configs for Image Review.


echo --------------------------------------------------------------------
echo Decrypting FPP Config Files - Production
echo --------------------------------------------------------------------


echo * Decrypting config file for FPPInternalServiceCleint..
for /F %%i in ('dir /b "C:\FPP\ServiceClient\Tcore.FPP.InternalServiceClient.exe"') do (
AesHelper.exe /uo /exe C:\FPP\ServiceClient\Tcore.FPP.InternalServiceClient.exe
 )
if %ERRORLEVEL% NEQ 0 GOTO :error

echo --------------------------------------------------------------------
echo Decryption Completed...
echo --------------------------------------------------------------------

GOTO END
:error
echo ------- AN ERROR OCCURED DURING THIS STEP: %STEP% ------
exit /b 1


GOTO END

:END
exit /B 0
goto :EOF
