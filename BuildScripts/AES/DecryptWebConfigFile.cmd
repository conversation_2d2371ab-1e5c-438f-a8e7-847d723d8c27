@ECHO OFF
rem - Decrypt Configs for Image Review.


echo --------------------------------------------------------------------
echo Decrypting Primary Internal Web Site Config Files - Production
echo --------------------------------------------------------------------

echo * Decrypting config file for Image Review web site...
AesHelper.exe /uo /web /
if %ERRORLEVEL% NEQ 0 GOTO :error

echo --------------------------------------------------------------------
echo Decryption Completed...
echo --------------------------------------------------------------------

GOTO END
:error
echo ------- AN ERROR OCCURED DURING THIS STEP: %STEP% ------
exit /b 1


GOTO END

:END
exit /B 0
goto :EOF
