@ECHO OFF
rem - Encrypt configuration files for MOMS Scheduled Tasks, MOMS Services and MOMS Web Site.

echo --------------------------------------------------------------------
echo Encrypting Primary Internal Scheduled Task Config Files - Production
echo --------------------------------------------------------------------
echo create temp.log >temp.log
echo * Encrypting config file for CAMS_MOMSEventLoaderConsole...
for /F %%i in ('dir /b "C:\moms\ScheduledTasks\CAMS_MOMSEventLoader\tcore.MOMSEventLoaderConsole.exe"') do (
AesHelper.exe /uo /exe C:\moms\ScheduledTasks\CAMS_MOMSEventLoader\tcore.MOMSEventLoaderConsole.exe >> temp.log
 )

echo * Encrypting config file for GFI_MOMSEventLoaderConsole...
for /F %%i in ('dir /b "C:\moms\ScheduledTasks\GFI_MOMSEventLoader\tcore.MOMSEventLoaderConsole.exe"') do (
AesHelper.exe /uo /exe C:\moms\ScheduledTasks\GFI_MOMSEventLoader\tcore.MOMSEventLoaderConsole.exe >> temp.log
 )

echo * Encrypting config file for LANE_MOMSEventLoaderConsole...
for /F %%i in ('dir /b "C:\moms\ScheduledTasks\LANE_MOMSEventLoader\tcore.MOMSEventLoaderConsole.exe"') do (
AesHelper.exe /uo /exe C:\moms\ScheduledTasks\LANE_MOMSEventLoader\tcore.MOMSEventLoaderConsole.exe >> temp.log
 )
 
echo * Encrypting config file for MOMSEventLoaderConsole...
for /F %%i in ('dir /b "C:\moms\ScheduledTasks\MOMSEventLoader\tcore.MOMSEventLoaderConsole.exe"') do (
AesHelper.exe /uo /exe C:\moms\ScheduledTasks\MOMSEventLoader\tcore.MOMSEventLoaderConsole.exe >> temp.log
 )
 
echo * Encrypting config file for WhatsUpEventLoaderConsole...
for /F %%i in ('dir /b "C:\moms\ScheduledTasks\WhatsUp_MOMSEventLoader\tcore.MOMSEventLoaderConsole.exe"') do (
AesHelper.exe /uo /exe C:\moms\ScheduledTasks\WhatsUp_MOMSEventLoader\tcore.MOMSEventLoaderConsole.exe >> temp.log
 )
 
echo * Encrypting config file for SolarWindsEventLoaderConsole...
for /F %%i in ('dir /b "C:\moms\ScheduledTasks\SolarWinds_MOMSEventLoader\tcore.MOMSEventLoaderConsole.exe"') do (
AesHelper.exe /uo /exe C:\moms\ScheduledTasks\SolarWinds_MOMSEventLoader\tcore.MOMSEventLoaderConsole.exe >> temp.log
 )
 
echo * Encrypting config file for ExternalNotifierConsole...
for /F %%i in ('dir /b "C:\moms\ScheduledTasks\MOMSExternalNotifier\tcore.MOMSExternalNotifierConsole.exe"') do (
AesHelper.exe /uo /exe C:\moms\ScheduledTasks\MOMSExternalNotifier\tcore.MOMSExternalNotifierConsole.exe >> temp.log
 )
 
echo * Encrypting config file for MOMSFailureAnalysisTaskConsole...
for /F %%i in ('dir /b "C:\moms\ScheduledTasks\MOMSFailureAnalysisTaskConsole\tcore.MOMSFailureAnalysisTaskConsole.exe"') do (
AesHelper.exe /uo /exe C:\moms\ScheduledTasks\MOMSFailureAnalysisTaskConsole\tcore.MOMSFailureAnalysisTaskConsole.exe >> temp.log
 )

echo * Encrypting config file for MOMSInventoryLevelNotification...
for /F %%i in ('dir /b "C:\moms\ScheduledTasks\MOMSInventoryLevelNotification\tcore.MOMSInventoryLevelNotification.exe"') do (
AesHelper.exe /uo /exe C:\moms\ScheduledTasks\MOMSInventoryLevelNotification\tcore.MOMSInventoryLevelNotification.exe >> temp.log
 )

echo * Encrypting config file for MOMSNotificationEscalation...
for /F %%i in ('dir /b "C:\moms\ScheduledTasks\MOMSNotificationEscalationTask\tcore.MOMSNotificationEscalation.exe"') do (
AesHelper.exe /uo /exe C:\moms\ScheduledTasks\MOMSNotificationEscalationTask\tcore.MOMSNotificationEscalation.exe >> temp.log
 )

echo * Encrypting config file for MOMSPredictiveMaintenanceNotificationTaskConsole...
for /F %%i in ('dir /b "C:\moms\ScheduledTasks\MOMSPredictiveMaintenanceNotificationTaskConsole\tcore.MOMSPredictiveMaintenanceNotificationTaskConsole.exe"') do (
AesHelper.exe /uo /exe C:\moms\ScheduledTasks\MOMSPredictiveMaintenanceNotificationTaskConsole\tcore.MOMSPredictiveMaintenanceNotificationTaskConsole.exe >> temp.log
 )

echo * Encrypting config file for MOMSPreventiveMaintenanceTask...
for /F %%i in ('dir /b "C:\moms\ScheduledTasks\MOMSPreventiveMaintenanceTask\tcore.MOMSPreventiveMaintenanceTask.exe"') do (
AesHelper.exe /uo /exe C:\moms\ScheduledTasks\MOMSPreventiveMaintenanceTask\tcore.MOMSPreventiveMaintenanceTask.exe >> temp.log
 )

echo * Encrypting config file for MOMSUTSNotifierConsole...
for /F %%i in ('dir /b "C:\moms\ScheduledTasks\MOMSUTSNotifier\tcore.MOMSUTSNotifierConsole.exe"') do (
AesHelper.exe /uo /exe C:\moms\ScheduledTasks\MOMSUTSNotifier\tcore.MOMSUTSNotifierConsole.exe >> temp.log
 )
 
echo * Encrypting config file for MOMSInfinityNotifierConsole...
for /F %%i in ('dir /b "C:\moms\ScheduledTasks\MOMSInfinityNotifier\tcore.MOMSInfinityNotifierConsole.exe"') do (
AesHelper.exe /uo /exe C:\moms\ScheduledTasks\MOMSInfinityNotifier\tcore.MOMSInfinityNotifierConsole.exe >> temp.log
 )

echo * Encrypting config file for MOMSWorkOrderMessagingTask...
for /F %%i in ('dir /b "C:\moms\ScheduledTasks\MOMSWorkOrderMessagingTask\tcore.MOMSWorkOrderMessagingTask.exe"') do (
AesHelper.exe /uo /exe C:\moms\ScheduledTasks\MOMSWorkOrderMessagingTask\tcore.MOMSWorkOrderMessagingTask.exe >> temp.log
 )
 
echo * Encrypting config file for Integrity_MOMSEventLoader...
for /F %%i in ('dir /b "C:\moms\ScheduledTasks\Integrity_MOMSEventLoader\tcore.MOMSEventLoaderConsole.exe"') do (
AesHelper.exe /uo /exe C:\moms\ScheduledTasks\Integrity_MOMSEventLoader\tcore.MOMSEventLoaderConsole.exe >> temp.log
 )

echo * Encrypting config file for ImageReview_MOMSEventLoader...
for /F %%i in ('dir /b "C:\moms\ScheduledTasks\ImageReview_MOMSEventLoader\tcore.MOMSEventLoaderConsole.exe"') do (
AesHelper.exe /uo /exe C:\moms\ScheduledTasks\ImageReview_MOMSEventLoader\tcore.MOMSEventLoaderConsole.exe >> temp.log
 )
 
echo * Encrypting config file for MOMSActiveDirectoryPolling...
for /F %%i in ('dir /b "C:\moms\ScheduledTasks\MOMSActiveDirectoryPolling\tcore.MOMSActiveDirectoryPolling.exe"') do (
AesHelper.exe /uo /exe C:\moms\ScheduledTasks\MOMSActiveDirectoryPolling\tcore.MOMSActiveDirectoryPolling.exe >> temp.log
 )


echo --------------------------------------------------------------------
echo Encrypting Primary Internal Service Config Files - Production
echo --------------------------------------------------------------------

echo * Encrypting config file for MOMSExternalServiceHost...
for /F %%i in ('dir /b "C:\moms\ServiceHost\tcore.MOMSExternalServiceHost.exe"') do (
AesHelper.exe /uo /exe C:\moms\ServiceHost\tcore.MOMSExternalServiceHost.exe >> temp.log
 )

echo * Encrypting config file for MOMSServiceHost...
AesHelper.exe /uo /exe C:\moms\ServiceHost\tcore.MOMSServiceHost.exe >> temp.log
 )

echo * Encrypting config file for MOMSTrafficLoader...
for /F %%i in ('dir /b "C:\moms\ServiceHost\tcore.MOMSTrafficLoaderHost.exe"') do (
AesHelper.exe /uo /exe C:\moms\ServiceHost\tcore.MOMSTrafficLoaderHost.exe >> temp.log
 )
 
 rem comment out until error is resolved
 rem ERROR:   Unrecognized attribute 'configProtectionProvider'. Note that attribute names are case-sensitive.  echo * Encrypting config file
 rem for MOMSGPSLocatorService... Restart the IIS 
for /F %%i in ('dir /b "C:\moms\ServiceHost\tcore.MOMSGPSLocatorServiceHost.exe"') do (
AesHelper.exe /uo /exe C:\moms\ServiceHost\tcore.MOMSGPSLocatorServiceHost.exe >> temp.log
 )
 
echo * Encrypting config file for TransSuiteMOMSService...
for /F %%i in ('dir /b "C:\MOMSInterfaces\TransSuiteMOMSService\ServiceHost\tcore.TransSuite.TransSuiteMOMSServiceHost.exe"') do (
AesHelper.exe /uo /exe C:\MOMSInterfaces\TransSuiteMOMSService\ServiceHost\tcore.TransSuite.TransSuiteMOMSServiceHost.exe >> temp.log
)

echo * Encrypting config file for InfinityMOMSService...
for /F %%i in ('dir /b "C:\MOMSInterfaces\InfinityMOMSService\ServiceHost\tcore.Infinity.InfinityMOMSServiceHost.exe"') do (
AesHelper.exe /uo /exe C:\MOMSInterfaces\InfinityMOMSService\ServiceHost\tcore.Infinity.InfinityMOMSServiceHost.exe >> temp.log
)

echo * Encrypting config file for TcoreSSOService...
for /F %%i in ('dir /b "C:\MOMSInterfaces\TcoreSSO\ServiceHost\tcore.SSO.TcoreSSOServiceHost.exe"') do (
AesHelper.exe /uo /exe C:\MOMSInterfaces\TcoreSSO\ServiceHost\tcore.SSO.TcoreSSOServiceHost.exe >> temp.log
)



echo --------------------------------------------------------------------
echo Encrypting Primary Internal Web Site Config Files - Production
echo --------------------------------------------------------------------

echo * Encrypting config file for MOMS web site...
AesHelper.exe /uo /web / >> temp.log



echo --------------------------------------------------------------------
echo Encryption Completed...
echo --------------------------------------------------------------------


GOTO END
:error
echo ------- AN ERROR OCCURED DURING THIS STEP: %STEP% ------
exit /b 1

del /s /q temp.log
GOTO END

:END
pause