@ECHO OFF
rem - Encrypt configuration files for MOMS Scheduled Tasks, MOMS Services and MOMS Web Site.

echo --------------------------------------------------------------------
echo Encrypting Primary Internal Scheduled Task Config Files - Production
echo --------------------------------------------------------------------
echo create temp.log >temp.log


echo * Encrypting config file for Integrity_MOMSEventLoader...
if exist "C:\moms\ScheduledTasks\Integrity_MOMSEventLoader\tcore.MOMSEventLoaderConsole.exe" (
AesHelper.exe /po /exe C:\moms\ScheduledTasks\Integrity_MOMSEventLoader\tcore.MOMSEventLoaderConsole.exe >> temp.log
 )


echo --------------------------------------------------------------------
echo Encryption Completed...
echo --------------------------------------------------------------------


GOTO END
:error
echo ------- AN ERROR OCCURED DURING THIS STEP: %STEP% ------
exit /b 1

del /s /q temp.log
GOTO END

:END
exit /B 0
goto :EOF