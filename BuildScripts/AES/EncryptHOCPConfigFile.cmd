@ECHO OFF
rem - Encrypt configuration files for Image Review

echo --------------------------------------------------------------------
echo Encrypting HOCP Internal Service Config Files - Production
echo --------------------------------------------------------------------


echo * Encrypting config file for HOCP.HOCPInternalServiceHost...
for /F %%i in ('dir /b "C:\HOCP\ServiceHost\Tcore.HOCP.HOCPInternalServiceHost.exe"') do (
AesHelper.exe /po /exe C:\HOCP\ServiceHost\Tcore.HOCP.HOCPInternalServiceHost.exe 
 )
if %ERRORLEVEL% NEQ 0 GOTO :error


echo --------------------------------------------------------------------
echo Encrypting HOCP Internal Web Site Config Files - Production
echo --------------------------------------------------------------------

echo * Encrypting config file for Image Review HOCP WebAPI web site...
AesHelper.exe /po /web / 
if %ERRORLEVEL% NEQ 0 GOTO :error

echo --------------------------------------------------------------------
echo Encryption of HOCP Completed...
echo --------------------------------------------------------------------

GOTO END
:error
echo ------- AN ERROR OCCURED DURING THIS STEP: %STEP% ------
exit /b 1


GOTO END

:END
exit /B 0
goto :EOF
