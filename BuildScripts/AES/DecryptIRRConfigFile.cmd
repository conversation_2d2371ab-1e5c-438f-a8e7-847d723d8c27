@ECHO OFF
rem - Decrypt configuration files for Image Review

echo --------------------------------------------------------------------
echo Decrypting IRR Config Files - Production
echo --------------------------------------------------------------------


echo * Decrypting config file for Tcore.IRR.IRRInternalServiceHost.exe...
for /F %%i in ('dir /b "C:\IRR\ServiceHost\Tcore.IRR.IRRInternalServiceHost.exe"') do (
AesHelper.exe /uo /exe C:\IRR\ServiceHost\Tcore.IRR.IRRInternalServiceHost.exe
 )
if %ERRORLEVEL% NEQ 0 GOTO :error


echo --------------------------------------------------------------------
echo Decrypting IRR Web Site Config Files - Production
echo --------------------------------------------------------------------

echo * Decrypting config file for Image Review IRR WebAPI web site...
AesHelper.exe /uo /web /
if %ERRORLEVEL% NEQ 0 GOTO :error

echo --------------------------------------------------------------------
echo Decryption of IRR Completed...
echo --------------------------------------------------------------------

GOTO END
:error
echo ------- AN ERROR OCCURED DURING THIS STEP: %STEP% ------
exit /b 1


GOTO END

:END
exit /B 0
goto :EOF
