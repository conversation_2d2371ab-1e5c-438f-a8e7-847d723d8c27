@ECHO OFF
rem - Encrypt configuration files for Image Review

echo --------------------------------------------------------------------
echo Encrypting FPP Client Service Config Files
echo --------------------------------------------------------------------


echo * Encrypting config file for FPPInternalServiceCleint..
for /F %%i in ('dir /b "C:\FPP\ServiceClient\Tcore.FPP.InternalServiceClient.exe"') do (
AesHelper.exe /po /exe C:\FPP\ServiceClient\Tcore.FPP.InternalServiceClient.exe
 )
 
if %ERRORLEVEL% NEQ 0 GOTO :error
 
GOTO END
:error
echo ------- AN ERROR OCCURED DURING THIS STEP: %STEP% ------
exit /b 1


GOTO END

:END
exit /B 0
goto :EOF
