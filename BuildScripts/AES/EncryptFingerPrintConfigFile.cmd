@ECHO OFF
rem - Encrypt configuration files for Image Review

echo --------------------------------------------------------------------
echo Encrypting FingerPrint Internal Service Config Files - Production
echo --------------------------------------------------------------------


echo * Encrypting config file for FPMInternalServiceHost...
for /F %%i in ('dir /b "C:\FPM\ServiceHost\TCore.FPM.FPMInternalServiceHost.exe"') do (
AesHelper.exe /po /exe C:\FPM\ServiceHost\TCore.FPM.FPMInternalServiceHost.exe
)
if %ERRORLEVEL% NEQ 0 GOTO :error

echo * Encrypting config file for FPPInternalServiceHost...
for /F %%i in ('dir /b "C:\FPP\ServiceHost\TCore.FPP.InternalServiceHost.exe"') do (
AesHelper.exe /po /exe C:\FPP\ServiceHost\TCore.FPP.InternalServiceHost.exe
)
if %ERRORLEVEL% NEQ 0 GOTO :error
 
 
echo * Encrypting config file for FPPInternalServiceCleint..
for /F %%i in ('dir /b "C:\FPP\ServiceClient\Tcore.FPP.InternalServiceClient.exe"') do (
AesHelper.exe /po /exe C:\FPP\ServiceClient\Tcore.FPP.InternalServiceClient.exe
)
if %ERRORLEVEL% NEQ 0 GOTO :error
 

echo --------------------------------------------------------------------
echo Encrypting Primary Internal Web Site Config Files - Production
echo --------------------------------------------------------------------

echo * Encrypting config file for Image Review FPM WebAPI web site...
AesHelper.exe /po /web / 
if %ERRORLEVEL% NEQ 0 GOTO :error

echo --------------------------------------------------------------------
echo Encryption Completed...
echo --------------------------------------------------------------------

GOTO END
:error
echo ------- AN ERROR OCCURED DURING THIS STEP: %STEP% ------
exit /b 1


GOTO END

:END
exit /B 0
goto :EOF
