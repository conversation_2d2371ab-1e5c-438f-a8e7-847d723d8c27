@ECHO OFF
rem - Encrypt configuration files for Image Review

echo --------------------------------------------------------------------
echo Encrypting IRR Config Files - Production
echo --------------------------------------------------------------------


echo * Encrypting config file for Tcore.IRR.IRRInternalServiceHost.exe...
for /F %%i in ('dir /b "C:\IRR\ServiceHost\Tcore.IRR.IRRInternalServiceHost.exe"') do (
AesHelper.exe /po /exe C:\IRR\ServiceHost\Tcore.IRR.IRRInternalServiceHost.exe
 )
if %ERRORLEVEL% NEQ 0 GOTO :error


echo --------------------------------------------------------------------
echo Encrypting IRR Web Site Config Files - Production
echo --------------------------------------------------------------------

echo * Encrypting config file for Image Review IRR WebAPI web site...
AesHelper.exe /po /web /
if %ERRORLEVEL% NEQ 0 GOTO :error

echo --------------------------------------------------------------------
echo Encryption of IRR Completed...
echo --------------------------------------------------------------------

GOTO END
:error
echo ------- AN ERROR OCCURED DURING THIS STEP: %STEP% ------
exit /b 1


GOTO END

:END
exit /B 0
goto :EOF
