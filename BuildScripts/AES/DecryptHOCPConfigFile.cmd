@ECHO OFF
rem - Decrypt Configs for Image Review.


echo --------------------------------------------------------------------
echo Decrypting HOCP Internal Service Config Files - Production
echo --------------------------------------------------------------------

echo * Decrypting config file for HOCPInternalServiceHost...
for /F %%i in ('dir /b "C:\HOCP\ServiceHost\Tcore.HOCP.HOCPInternalServiceHost.exe"') do (
AesHelper.exe /uo /exe C:\HOCP\ServiceHost\Tcore.HOCP.HOCPInternalServiceHost.exe
 )
if %ERRORLEVEL% NEQ 0 GOTO :error

 
echo --------------------------------------------------------------------
echo Decrypting HOCP Internal Web Site Config Files - Production
echo --------------------------------------------------------------------

echo * Decrypting config file for HOCP webAPI web site...
AesHelper.exe /uo /web /
if %ERRORLEVEL% NEQ 0 GOTO :error

echo --------------------------------------------------------------------
echo Decryption HOCP Completed...
echo --------------------------------------------------------------------

GOTO END
:error
echo ------- AN ERROR OCCURED DURING THIS STEP: %STEP% ------
exit /b 1


GOTO END

:END
exit /B 0
goto :EOF
