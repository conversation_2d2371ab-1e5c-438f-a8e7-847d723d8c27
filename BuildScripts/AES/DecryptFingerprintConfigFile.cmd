@ECHO OFF
rem - Decrypt Configs for Image Review.


echo --------------------------------------------------------------------
echo Decrypting FingerPrint Internal Service Config Files - Production
echo --------------------------------------------------------------------

echo * Decrypting config file for FPMInternalServiceHost...
for /F %%i in ('dir /b "C:\FPM\ServiceHost\TCore.FPM.FPMInternalServiceHost.exe"') do (
AesHelper.exe /uo /exe C:\FPM\ServiceHost\TCore.FPM.FPMInternalServiceHost.exe
 )
if %ERRORLEVEL% NEQ 0 GOTO :error

echo * Decrypting config file for FPPInternalServiceHost...
for /F %%i in ('dir /b "C:\FPP\ServiceHost\TCore.FPP.InternalServiceHost.exe"') do (
AesHelper.exe /uo /exe C:\FPP\ServiceHost\TCore.FPP.InternalServiceHost.exe
 )
if %ERRORLEVEL% NEQ 0 GOTO :error

echo * Decrypting config file for FPPInternalServiceCleint..
for /F %%i in ('dir /b "C:\FPP\ServiceClient\Tcore.FPP.InternalServiceClient.exe"') do (
AesHelper.exe /uo /exe C:\FPP\ServiceClient\Tcore.FPP.InternalServiceClient.exe
 )
if %ERRORLEVEL% NEQ 0 GOTO :error

 
echo --------------------------------------------------------------------
echo Decrypting Primary Internal Web Site Config Files - Production
echo --------------------------------------------------------------------

echo * Decrypting config file for FPM webAPI web site...
AesHelper.exe /uo /web /
if %ERRORLEVEL% NEQ 0 GOTO :error

echo --------------------------------------------------------------------
echo Decryption Completed...
echo --------------------------------------------------------------------

GOTO END
:error
echo ------- AN ERROR OCCURED DURING THIS STEP: %STEP% ------
exit /b 1


GOTO END

:END
exit /B 0
goto :EOF
