@ECHO OFF
ver > nul
Set CURRENTDIR=%CD%
Set BUILDDIR=%1
Set BASEDIR=%2
Set VEHICLEDETECTIONFEATURE=%3
Set IMGSENDTRANSFEATURE=%4
Set HUMANREADABILITYFEATURE=%5
Set GenerateROIFeature=%6
SET STEP=text
echo error level is %ERRORLEVEL%
if %ERRORLEVEL% NEQ 0 GOTO :error 
echo clear packages
rem call dotnet nuget locals -c all  
echo update nuget version
rem call nuget update -self
rem echo copy config
rem copy NuGet.config %BUILDDIR%\CPS\*
echo done with copy, onwards...
echo run dotnet
cd %BUILDDIR%
call cd
IF "%GenerateROIFeature%"=="false" GOTO :SKIPPEDGENERATEROIFEATURE 
:GenerateROITask-nuget
set step=nuget-GenerateROITask
echo error level is %ERRORLEVEL%
echo run nuget GenerateROITask
cd %BUILDDIR%\GenerateROITask
call nuget restore GenerateROITask.sln
if %ERRORLEVEL% NEQ 0 GOTO :error 
:GenerateROITask-dotnet
set step=dotenet-GenerateROITask
cd %BUILDDIR%\GenerateROITask\ScheduledTask\GenerateRoiCreateUrlTaskConsole
call dotnet restore
call dotnet publish -c Release --output "%BASEDIR%\GenerateROITask" --self-contained --runtime win10-x64
if %ERRORLEVEL% NEQ 0 GOTO :error 
cd %CURRENTDIR%
:GenerateROITaskRetries-dotnet
set step=dotenet-GenerateROITaskRetries
cd %BUILDDIR%\GenerateROITask\ScheduledTask\GenerateRoiCreateUrlTaskConsole
call dotnet restore
call dotnet publish -c Release --output "%BASEDIR%\GenerateROITaskRetries" --self-contained --runtime win10-x64
if %ERRORLEVEL% NEQ 0 GOTO :error 
cd %CURRENTDIR%
:SKIPPEDGENERATEROIFEATURE 
IF "%VEHICLEDETECTIONFEATURE%"=="false" GOTO :SKIPPEDVEHICLEDETECTIONFEATURE 
:VehicleDetectionTask-nuget
set step=nuget-vehicledetectiotask
echo error level is %ERRORLEVEL%
echo run nuget VehicleDetectionTask
cd %BUILDDIR%\VehicleDetectionTask
call nuget restore VehicleDetectionTask.sln
if %ERRORLEVEL% NEQ 0 GOTO :error 
:VehicleDetectionTask-dotnet
set step=dotenet-vehicledetectiontask
cd %BUILDDIR%\VehicleDetectionTask
call dotnet restore
call dotnet publish -c Release --output "%BASEDIR%\VehicleDetectionTask" --self-contained --runtime win10-x64
if %ERRORLEVEL% NEQ 0 GOTO :error 
cd %CURRENTDIR%
:SKIPPEDVEHICLEDETECTIONFEATURE 
IF "%IMGSENDTRANSFEATURE%"=="false" GOTO :SKIPPEDIMGSENDTRANSFEATURE 
:ImgReviewSendTransTask-nuget
set step=nuget-ImgReviewSendTransTask
echo error level is %ERRORLEVEL%
echo run nuget ImgReviewSendTransTask
cd %BUILDDIR%\ImgReviewSendTransTask
call nuget restore ImgReviewSendTransTask.sln
if %ERRORLEVEL% NEQ 0 GOTO :error 
:ImgReviewSendTransTask-dotnet
set step=dotenet-ImgReviewSendTransTask
cd %BUILDDIR%\ImgReviewSendTransTask\ScheduledTask\ImgRevSendTrans
call dotnet restore
call dotnet publish -c Release --output "%BASEDIR%\ImgReviewSendTransTask" --self-contained --runtime win10-x64
if %ERRORLEVEL% NEQ 0 GOTO :error 
cd %CURRENTDIR%
:ImgReviewSendTransTaskRetries-dotnet
set step=dotenet-ImgReviewSendTransTaskRetries
cd %BUILDDIR%\ImgReviewSendTransTask\ScheduledTask\ImgRevSendTrans
call dotnet restore
call dotnet publish -c Release --output "%BASEDIR%\ImgReviewSendTransTaskRetries" --self-contained --runtime win10-x64
if %ERRORLEVEL% NEQ 0 GOTO :error 
cd %CURRENTDIR%
:SKIPPEDIMGSENDTRANSFEATURE 
IF "%HUMANREADABILITYFEATURE%"=="false" GOTO :SKIPPEDHUMANREADABILITYFEATURE
:ImgReviewSendTransTask-nuget
set step=nuget-ImgReviewSendTransTask
echo error level is %ERRORLEVEL%
echo run nuget ImgReviewSendTransTask
cd %BUILDDIR%\ImgReviewSendTransTask
call nuget restore ImgReviewSendTransTask.sln
if %ERRORLEVEL% NEQ 0 GOTO :error 
:ImgReviewSendTransTask-dotnet
set step=dotenet-ImgReviewSendTransTask
cd %BUILDDIR%\ImgReviewSendTransTask\ScheduledTask\ImgRevSendTrans
call dotnet restore
call dotnet publish -c Release --output "%BASEDIR%\ImgReviewSendTransTask" --self-contained --runtime win10-x64
if %ERRORLEVEL% NEQ 0 GOTO :error 
cd %CURRENTDIR%
:ImgReviewSendTransTaskRetries-dotnet
set step=dotenet-ImgReviewSendTransTaskRetries
cd %BUILDDIR%\ImgReviewSendTransTask\ScheduledTask\ImgRevSendTrans
call dotnet restore
call dotnet publish -c Release --output "%BASEDIR%\ImgReviewSendTransTaskRetries" --self-contained --runtime win10-x64
if %ERRORLEVEL% NEQ 0 GOTO :error 
:HumanReadabilitySelectionTask-dotnet
set step=dotenet-HumanReadabilitySelectionTask
cd %BUILDDIR%\ImgReviewSendTransTask\ScheduledTask\HumanReadabilitySelectionTask
call dotnet restore
call dotnet publish -c Release --output "%BASEDIR%\HumanReadabilitySelectionTask" --self-contained --runtime win10-x64
if %ERRORLEVEL% NEQ 0 GOTO :error 
cd %CURRENTDIR%

:SKIPPEDHUMANREADABILITYFEATURE


goto :END

:error
echo ------- ERRORLEVEL dotnet returned ------
echo %ERRORLEVEL% is the errorlevel and failure is on %STEP%
cd %CURRENTDIR%
exit /b 1

:END
echo -- dotnet success


exit /b 0