@ECHO OFF
ver > nul
Set CURRENTDIR=%CD%
Set BUILDDIR=%1
Set BASEDIR=%2
Set ZPITASK=%3
Set AARFeature=%4
Set DMVFeature=%5
Set IASFeature=%6
Set NYCDOTFeature=%7
Set RaasAcknowledgementFeature=%8
Set TPIFeature=%9
SHIFT
SHIFT
SHIFT
SHIFT
SHIFT
SHIFT
SHIFT
SHIFT
SHIFT		
Set WorkflowFeature=%1
Set VRGFeature=%2
Set TableauFeature=%3
Set DNAMatchFeature=%4
Set TransComFeature=%5
Set DNAMatchCPSVersion=%6
Set DNAMatchCommonVersion=%7
Set release.version=%8


SET STEP=CompileDotNet
echo error level is %ERRORLEVEL%
if %ERRORLEVEL% NEQ 0 GOTO :error 

echo clear packages
rem call dotnet nuget locals -c all  
echo update nuget version
call nuget update -self
echo copy config
rem copy NuGet.config %BUILDDIR%\CPS\*
echo done with copy, onwards...
echo run dotnet
cd %BUILDDIR%
call cd

:webapi
set STEP=webapi
cd %BUILDDIR%\CPS
del /q Nuget.config
rem   rem call nuget sources add -name TranscoreAzureDevOpsNuget -source https://pkgs.dev.azure.com/infinity-tcore/ffe950cf-3987-49e7-a081-e9594bdbb522/_packaging/infinity/nuget/v3/index.json -username "sd-ifxteamcity" -password "kih3wxklng44hpln7dymgn6sil5rsyoc6ate7vpg3gilcbw4yniq" 
call nuget restore CPS.sln
set step=webapi-publish
cd %BUILDDIR%\CPS\WebAPI
call dotnet publish -c Release --output "%BASEDIR%\DIST\APP01\CPS\WebAPI"  --no-self-contained --runtime win-x64 /p:Version=%release.version% 

if %ERRORLEVEL% NEQ 0 GOTO :error 
echo ZPI tasks is %ZPITASK%
IF "%ZPITASK%"=="false" GOTO :TableauTaskTool 

:ZPIProcessTask
cd %BUILDDIR%\CPS\ScheduledTasks\ZPIProcess
set STEP=zpiprocess
call dotnet publish -c Release --output %BASEDIR%\DIST\EXT01\CPS\ThirdPartyScheduledTasks\ZPIProcessTask  --no-self-contained --runtime win-x64 /p:Version=%release.version% 

if %ERRORLEVEL% NEQ 0 GOTO :error 


:TableauTaskTool
if "%TableauFeature%"=="false" GOTO :ADPollingTask
cd %CURRENTDIR%
call copy NuGet.config %BUILDDIR%\TableauImagePollingTask\ScheduledTasks\TableauImagesPollingTask

cd %BUILDDIR%\TableauImagePollingTask\ScheduledTasks\TableauImagesPollingTask
call dotnet restore --configfile Nuget.config
rem   rem call nuget sources add -name TranscoreAzureDevOpsNuget -source https://pkgs.dev.azure.com/infinity-tcore/ffe950cf-3987-49e7-a081-e9594bdbb522/_packaging/infinity/nuget/v3/index.json -username "sd-ifxteamcity" -password "kih3wxklng44hpln7dymgn6sil5rsyoc6ate7vpg3gilcbw4yniq" 

set STEP=polling
call dotnet publish -c Release --output %BASEDIR%\DIST\APP01\CPS\ScheduledTasks\TableauImagesPollingTask --no-self-contained --runtime win-x64 /p:Version=%release.version%

if %ERRORLEVEL% NEQ 0 GOTO :error 

:ADPollingTask
cd %BUILDDIR%\CPS\ScheduledTasks\ActiveDirectoryPollingTask
set STEP=polling
call dotnet publish -c Release --output %BASEDIR%\DIST\APP01\CPS\ScheduledTasks\ActiveDirectoryPollingTask --no-self-contained --runtime win-x64 /p:Version=%release.version%

if %ERRORLEVEL% NEQ 0 GOTO :error 

:CPSEmailNotification
cd %BUILDDIR%\CPS\ScheduledTasks\CPSEmailNotification
set STEP=polling
call dotnet publish -c Release --output %BASEDIR%\DIST\APP01\CPS\ScheduledTasks\CPSEmailNotification --no-self-contained --runtime win-x64 /p:Version=%release.version%

if %ERRORLEVEL% NEQ 0 GOTO :error 

:IMS-NUGET
set step=nuget-ims-api
rem copy %CURRENTDIR%\NuGet.config %BUILDDIR%\Services\IMS\*
echo done with copy, onwards... 
echo error level is %ERRORLEVEL%
echo run nuget ims
cd %BUILDDIR%\Services\IMS
 rem call nuget sources add -name TranscoreAzureDevOpsNuget -source https://pkgs.dev.azure.com/infinity-tcore/ffe950cf-3987-49e7-a081-e9594bdbb522/_packaging/infinity/nuget/v3/index.json -username "sd-ifxteamcity" -password "kih3wxklng44hpln7dymgn6sil5rsyoc6ate7vpg3gilcbw4yniq" 
call nuget restore IMS.sln 
if %ERRORLEVEL% NEQ 0 GOTO :error

:DM-NUGET
set step=nuget-dm-api
rem copy %CURRENTDIR%\NuGet.config %BUILDDIR%\Services\DM\*
echo done with copy, onwards... 
echo error level is %ERRORLEVEL%
echo run nuget DM
cd %BUILDDIR%\Services\DM
 rem call nuget sources add -name TranscoreAzureDevOpsNuget -source https://pkgs.dev.azure.com/infinity-tcore/ffe950cf-3987-49e7-a081-e9594bdbb522/_packaging/infinity/nuget/v3/index.json -username "sd-ifxteamcity" -password "kih3wxklng44hpln7dymgn6sil5rsyoc6ate7vpg3gilcbw4yniq" 
call nuget restore DM.sln 
if %ERRORLEVEL% NEQ 0 GOTO :error 

cd %CURRENTDIR%

:VRG-NUGET
if "%VRGFeature%"=="false" GOTO :NYCDOT-NUGET
set step=nuget-vrg
rem copy %CURRENTDIR%\NuGet.config %BUILDDIR%\Services\VRG\*
echo done with copy, onwards... 
echo error level is %ERRORLEVEL%
echo run nuget VRG
cd %BUILDDIR%\Services\VRG
 rem call nuget sources add -name TranscoreAzureDevOpsNuget -source https://pkgs.dev.azure.com/infinity-tcore/ffe950cf-3987-49e7-a081-e9594bdbb522/_packaging/infinity/nuget/v3/index.json -username "sd-ifxteamcity" -password "kih3wxklng44hpln7dymgn6sil5rsyoc6ate7vpg3gilcbw4yniq" 
call nuget restore VRG.sln 
if %ERRORLEVEL% NEQ 0 GOTO :error 
cd %CURRENTDIR%

:NYCDOT-NUGET
if "%NYCDOTFeature%"=="false" GOTO :TRANSCOM-NUGET 
set step=nuget-nycdot-api
rem copy %CURRENTDIR%\NuGet.config %BUILDDIR%\Services\NYCDOT\*
echo done with copy, onwards... 
echo error level is %ERRORLEVEL%
echo run nuget nycdot
cd %BUILDDIR%\Services\NYCDOT
 rem call nuget sources add -name TranscoreAzureDevOpsNuget -source https://pkgs.dev.azure.com/infinity-tcore/ffe950cf-3987-49e7-a081-e9594bdbb522/_packaging/infinity/nuget/v3/index.json -username "sd-ifxteamcity" -password "kih3wxklng44hpln7dymgn6sil5rsyoc6ate7vpg3gilcbw4yniq" 
call nuget restore NYCDOT.sln 
if %ERRORLEVEL% NEQ 0 GOTO :error

:TRANSCOM-NUGET
if "%TransComFeature%"=="false" GOTO :ConfigTrackingService-NUGET
set step=nuget-transcom
rem copy %CURRENTDIR%\NuGet.config %BUILDDIR%\Services\TransComService\*
echo done with copy, onwards... 
echo error level is %ERRORLEVEL%
echo run nuget TransCom
cd %BUILDDIR%\Services\TransComService
 rem call nuget sources add -name TranscoreAzureDevOpsNuget -source https://pkgs.dev.azure.com/infinity-tcore/ffe950cf-3987-49e7-a081-e9594bdbb522/_packaging/infinity/nuget/v3/index.json -username "sd-ifxteamcity" -password "kih3wxklng44hpln7dymgn6sil5rsyoc6ate7vpg3gilcbw4yniq" 
call nuget restore TransComService.sln 
if %ERRORLEVEL% NEQ 0 GOTO :error 
cd %CURRENTDIR%

:ConfigTrackingService-NUGET
set step=nuget-cfts
rem copy %CURRENTDIR%\NuGet.config %BUILDDIR%\Services\ConfigTrackingService\*
echo done with copy, onwards... 
echo error level is %ERRORLEVEL%
echo run nuget cfts
cd %BUILDDIR%\Services\ConfigTrackingService
 rem call nuget sources add -name TranscoreAzureDevOpsNuget -source https://pkgs.dev.azure.com/infinity-tcore/ffe950cf-3987-49e7-a081-e9594bdbb522/_packaging/infinity/nuget/v3/index.json -username "sd-ifxteamcity" -password "kih3wxklng44hpln7dymgn6sil5rsyoc6ate7vpg3gilcbw4yniq" 
call nuget restore ConfigTrackingService.sln 
if %ERRORLEVEL% NEQ 0 GOTO :error 

cd %CURRENTDIR%

:AAR-NUGET
if "%AARFeature%"=="false" GOTO :RaasAcknowledge-NUGET
set step=nuget-aar
rem copy %CURRENTDIR%\NuGet.config %BUILDDIR%\Services\AAR\*
echo done with copy, onwards... 
echo error level is %ERRORLEVEL%
echo run nuget aar
cd %BUILDDIR%\Services\AAR
 rem call nuget sources add -name TranscoreAzureDevOpsNuget -source https://pkgs.dev.azure.com/infinity-tcore/ffe950cf-3987-49e7-a081-e9594bdbb522/_packaging/infinity/nuget/v3/index.json -username "sd-ifxteamcity" -password "kih3wxklng44hpln7dymgn6sil5rsyoc6ate7vpg3gilcbw4yniq" 
call nuget restore AAR.sln 
if %ERRORLEVEL% NEQ 0 GOTO :error 

:AARProcessing
set step=aar-processing
cd %BUILDDIR%\Services\AAR\AAR.ProcessingService
call dotnet restore
call dotnet publish -c Release --output "%BASEDIR%\DIST\APP01\AARProcessingService\AAR.ProcessingService" --no-self-contained --runtime win-x64 /p:Version=%release.version%
if %ERRORLEVEL% NEQ 0 GOTO :error 
cd %CURRENTDIR%

:RaasAcknowledge-NUGET
if "%RaasAcknowledgementFeature%"=="false" GOTO :DNAMatch-NUGET
set step=nuget-raas
rem copy %CURRENTDIR%\NuGet.config %BUILDDIR%\Services\RaasAcknowledge\*
echo done with copy, onwards... 
echo error level is %ERRORLEVEL%
echo run nuget RaasAcknowledge
cd %BUILDDIR%\Services\RaasAcknowledge
 rem call nuget sources add -name TranscoreAzureDevOpsNuget -source https://pkgs.dev.azure.com/infinity-tcore/ffe950cf-3987-49e7-a081-e9594bdbb522/_packaging/infinity/nuget/v3/index.json -username "sd-ifxteamcity" -password "kih3wxklng44hpln7dymgn6sil5rsyoc6ate7vpg3gilcbw4yniq" 
call nuget restore RaasAcknowledge.sln 
if %ERRORLEVEL% NEQ 0 GOTO :error 
cd %CURRENTDIR%

rem  Comment out since no project has the IAS feature
rem :IAS-NUGET
rem if "%IASFeature%"=="false" GOTO :DNAMatch-NUGET
rem set step=nuget-ias
rem rem copy %CURRENTDIR%\NuGet.config %BUILDDIR%\Services\IAS\*
rem echo done with copy, onwards... 
rem echo error level is %ERRORLEVEL%
rem echo run nuget ias
rem cd %BUILDDIR%\Services\Core
rem call nuget restore
rem cd %BUILDDIR%\Services\IAS
rem  rem call nuget sources add -name TranscoreAzureDevOpsNuget -source https://infinity-tcore.pkgs.visualstudio.com/ffe950cf-3987-49e7-a081-e9594bdbb522/_packaging/infinity/nuget/v3/index.json -username "sd-ifxteamcity" -password "kih3wxklng44hpln7dymgn6sil5rsyoc6ate7vpg3gilcbw4yniq" 
rem all nuget restore IAS.sln 
rem  %ERRORLEVEL% NEQ 0 GOTO :error 

:DNAMatch-NUGET
if "%DNAMatchFeature%"=="false" GOTO :ChangeDir

:DNAMATCHCOMMON-NUGET
set step=nuget-dnamatchcommon
rem copy %CURRENTDIR%\NuGet.config %BUILDDIR%\Common\Services\DNAMatch\*
echo Building Common DNAMatch
echo done with copy, onwards... 
echo error level is %ERRORLEVEL%
echo run nuget DNAMatchCommon
cd %BUILDDIR%\Common\Services\DNAMatch
 rem call nuget sources add -name TranscoreAzureDevOpsNuget -source https://pkgs.dev.azure.com/infinity-tcore/ffe950cf-3987-49e7-a081-e9594bdbb522/_packaging/infinity/nuget/v3/index.json -username "sd-ifxteamcity" -password "kih3wxklng44hpln7dymgn6sil5rsyoc6ate7vpg3gilcbw4yniq" 
call nuget restore DNAMatch.sln 
if %ERRORLEVEL% NEQ 0 GOTO :error 

:DNAMATCHCOMMON-SERVICE
set step=dnamatchcommon-service
cd %BUILDDIR%\Common\Services\DNAMatch\DNAMatch.Worker
call dotnet restore
call dotnet publish -c Release --output "%BASEDIR%\DIST\APP01\DNAMatch\DNAMatchService" --no-self-contained --runtime win-x64 /p:Version=%release.version%
if %ERRORLEVEL% NEQ 0 GOTO :error 

:ChangeDir
cd %CURRENTDIR%

:WorkFlow-NUGET
if "%WorkflowFeature%"=="false" GOTO :DMV-NUGET
set step=nuget-workflow
rem copy %CURRENTDIR%\NuGet.config %BUILDDIR%\Services\Workflow\*
echo done with copy, onwards... 
echo error level is %ERRORLEVEL%
echo run nuget workflow
cd %BUILDDIR%\Services\Workflow
 rem call nuget sources add -name TranscoreAzureDevOpsNuget -source https://pkgs.dev.azure.com/infinity-tcore/ffe950cf-3987-49e7-a081-e9594bdbb522/_packaging/infinity/nuget/v3/index.json -username "sd-ifxteamcity" -password "kih3wxklng44hpln7dymgn6sil5rsyoc6ate7vpg3gilcbw4yniq" 
call nuget restore Workflow.sln 
if %ERRORLEVEL% NEQ 0 GOTO :error 

:WorkFlowHost
set step=workflow-host
cd %BUILDDIR%\Services\Workflow\WorkflowHost
call dotnet restore
call dotnet publish -c Release --output "%BASEDIR%\DIST\EXT01\CPS\Services\Workflow\WorkflowHost" --no-self-contained --runtime win-x64 /p:Version=%release.version%
copy web.config %BASEDIR%\DIST\EXT01\CPS\Services\Workflow\WorkflowHost\*
rem del /S /Q %BASEDIR%\DIST\EXT01\CPS\Services\Workflow\WorkflowHost\imports\*.*
if %ERRORLEVEL% NEQ 0 GOTO :error 

:WorkFlowDesigner
set step=workflow-designer
cd %BUILDDIR%\Services\Workflow\WorkflowDesigner
call dotnet restore
call dotnet publish -c Release --output "%BASEDIR%\DIST\EXT01\CPS\Services\Workflow\WorkflowDesigner" --no-self-contained --runtime win-x64 /p:Version=%release.version%
if %ERRORLEVEL% NEQ 0 GOTO :error 

:TPI-NUGET
set step=nuget-allservices
rem copy %CURRENTDIR%\NuGet.config %BUILDDIR%\Services\TPI\*
echo done with copy, onwards... 
echo error level is %ERRORLEVEL%
echo run nuget for TPI
cd %BUILDDIR%\Services\TPI
 rem call nuget sources add -name TranscoreAzureDevOpsNuget -source https://pkgs.dev.azure.com/infinity-tcore/ffe950cf-3987-49e7-a081-e9594bdbb522/_packaging/infinity/nuget/v3/index.json -username "sd-ifxteamcity" -password "kih3wxklng44hpln7dymgn6sil5rsyoc6ate7vpg3gilcbw4yniq" 
call nuget restore Tcore.CPS.TPI.sln 
if %ERRORLEVEL% NEQ 0 GOTO :error 


:DMV-NUGET
if "%DMVFeature%"=="false" GOTO :ChangeDir2
set step=nuget-dmv
rem copy %CURRENTDIR%\NuGet.config %BUILDDIR%\Services\DMV\*
echo done with copy, onwards... 
echo error level is %ERRORLEVEL%
echo run nuget DMV
cd %BUILDDIR%\Services\DMV
 rem call nuget sources add -name TranscoreAzureDevOpsNuget -source https://pkgs.dev.azure.com/infinity-tcore/ffe950cf-3987-49e7-a081-e9594bdbb522/_packaging/infinity/nuget/v3/index.json -username "sd-ifxteamcity" -password "kih3wxklng44hpln7dymgn6sil5rsyoc6ate7vpg3gilcbw4yniq" 
call nuget restore DMV.sln 
if %ERRORLEVEL% NEQ 0 GOTO :error 



:DMVLookup-SERVICE
set step=DMVLookup-service
cd %BUILDDIR%\Services\DMV\DMVLookup
set step=DMVLookup-restore
call dotnet restore
if %ERRORLEVEL% NEQ 0 GOTO :error 
set step=DMVLookup-publish
call dotnet publish -c Release --output "%BASEDIR%\DIST\EXT01\CPS\ThirdPartyScheduledTasks\DMVLookup" --no-self-contained --runtime win-x64 /p:Version=%release.version%
if %ERRORLEVEL% NEQ 0 GOTO :error 

:ChangeDir2
cd %CURRENTDIR%

goto :END

:error
echo ------- ERRORLEVEL dotnet returned ------
echo %ERRORLEVEL% is the errorlevel and failure is on %STEP%
cd %CURRENTDIR%
exit /b 1

:END
echo -- dotnet success


exit /b 0