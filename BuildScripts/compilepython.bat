@ECHO OFF
ver > nul
Set CURRENTDIR=%CD%
Set BUILDDIR=%1
Set BASEDIR=%2

cd %BUILDDIR%
call cd
:AVIPromotion
set STEP=AVIPromotion
cd %BUILDDIR%\DataAnalytics\AVIPromotion
call pyinstaller --hidden-import=IPython --hidden-import=win32com --onefile AVI_Promotion.py
if %ERRORLEVEL% NEQ 0 GOTO :error
cd %BUILDDIR%
:DigitalPromotion
set STEP=DigitalPromotion
cd %BUILDDIR%\DataAnalytics\DigitalPromotion
call pyinstaller --onefile DigitalDevice_Promotion.py
if %ERRORLEVEL% NEQ 0 GOTO :error 
cd %CURRENTDIR%

goto :END

:error
echo ------- ERRORLEVEL dotnet returned ------
echo %ERRORLEVEL% is the errorlevel and failure is on %STEP%
cd %CURRENTDIR%
exit /b 1

:END
echo -- python success


exit /b 0