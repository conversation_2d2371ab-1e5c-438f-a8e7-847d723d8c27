@echo off
set NOW=%date:~10,4%-%date:~4,2%-%date:~7,2%
set /P user=[Enter Project Deployment User i.e. ncta\kelleym]:=
set "psCommand=powershell -Command "$pword = read-host 'Enter Password' -AsSecureString ; ^
    $BSTR=[System.Runtime.InteropServices.Marshal]::SecureStringToBSTR($pword); ^
        [System.Runtime.InteropServices.Marshal]::PtrToStringAuto($BSTR)""
for /f "usebackq delims=" %%p in (`%psCommand%`) do set pw=%%p
set scripts-dir=%CD%

NAnt.exe -buildfile:Product_build.build -logger:NAnt.Core.MailLogger -logfile:AppLogs\5-jumpunzip.log deploy-jump unzip-packages -D:deployment.user=%user% -D:deployment.password=%pw%
pause 


