@ECHO OFF

set NOW=%date:~10,4%-%date:~4,2%-%date:~7,2%
Set CURRENTDIR=%CD%
set GULPPATH=%1
set SOURCEDRIVE=%2

echo %GULPPATH%
echo source drive is %SOURCEDRIVE%

%SOURCEDRIVE%:
cd %GULPPATH%
echo %CD%

rem call npm install -g gulp
rem if %ERRORLEVEL% NEQ 0 GOTO :error 

rem call npm install -g gulp-cli
rem if %ERRORLEVEL% NEQ 0 GOTO :error 

rem call npm install gulp-sass
rem if %ERRORLEVEL% NEQ 0 GOTO :error 

call gulp -b %GULPPATH% --color --gulpfile "%GULPPATH%\Gulpfile.js" 
if %ERRORLEVEL% NEQ 0 GOTO :error 
%SOURCEDRIVE%:
cd %CURRENTDIR%
if %ERRORLEVEL% NEQ 0 GOTO :error 
goto :END

:error
echo ------- AN ERROR OCCURED DURING SCSS compile ------


exit /b 1

:END
echo -- SCSS compile is complete


exit /b 0
