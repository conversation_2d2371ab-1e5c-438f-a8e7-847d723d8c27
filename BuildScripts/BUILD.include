<?xml version="1.0" encoding="UTF-8"?>
<project name="ImageReview 1.0" default="build" basedir=".">
	<include buildfile="ENVIRONMENT.include"/>
    
	<property name="now" value="${environment::get-variable('NOW')}" />
	<property name="scripts.dir" value="${environment::get-variable('scripts-dir')}" />
	
	<!-- Email Server used by the build process - sdexchange server -->
	<property name="MailLogger.mailhost" value="sdexch.tcore.com" />
	<property name="MailLogger.success.subject" value="CPS ${deployment.environment} - Build Successfully Completed." />
	<property name="MailLogger.from" value="<EMAIL>"/>	
	<property name="MailLogger.success.notify" value="true" />
	<property name="MailLogger.failure.notify" value="true" />
	<property name="ratout.service.errors"    value="true"   readonly="false" />  
	
	<!-- Drive the the source is located on usually the D drive -->
	<property name="source_drive" value="D" />
	
	<!-- Change these values to your email address to receive build messages -->
	<property name="MailLogger.failure.to" value="<EMAIL>" />
	<property name="MailLogger.success.to" value="<EMAIL>" />
		
	<!-- SVN credentials -->
	<property name="svn.user" value="tcore\SD-IFXMOMSADMIN" />
	<property name="svn.password" value="S@ndieg0" />
	
	<!-- Directory definition -->
	<property name="base.dir" value="${source_drive}:\AppBuild-CPS" />	
	
	<property name="build.dir"		value="${base.dir}\Source\DBS" />
	<property name="web01.dist.dir" value="${base.dir}\DIST\WEB01" />
	<property name="web02.dist.dir" value="${base.dir}\DIST\WEB02" /> 
	<property name="app01.dist.dir" value="${base.dir}\DIST\APP01" />
	<property name="app02.dist.dir" value="${base.dir}\DIST\APP02" />
	<property name="app03.dist.dir" value="${base.dir}\DIST\APP03" />
	<property name="rpt01.dist.dir" value="${base.dir}\DIST\RPT01" />
	<property name="ims.dist.dir" value="${base.dir}\DIST\IMS" />
	<property name="ext01.dist.dir" value="${base.dir}\DIST\EXT01" />
	<property name="tableau.dist.dir" value="${base.dir}\DIST\TABLEAU" />
	<property name="dist.dir" value="${base.dir}\DIST" />
		
	<!-- appsettings json file paths -->
	<property name="app01.webapi.json" value="${app01.dist.dir}\CPS\WebAPI"/>
	<property name="app01.dm.api.json" value="${app01.dist.dir}\DM\API"/>
	<property name="app01.vrg.service.json" value="${app01.dist.dir}\VRG\VRGService"/>
	<property name="app01.dnamatch.service.json" value="${app01.dist.dir}\DNAMatch\DNAMatchService"/>
	<property name="app01.cfts.service.json" value="${app01.dist.dir}\CFTS\ConfigTrackingService"/>	
	<property name="app01.transcom.service.json" value="${app01.dist.dir}\TransComService"/>	
	<property name="app01.activedirectory.json" value="${app01.dist.dir}\CPS\ScheduledTasks\ActiveDirectoryPollingTask"/>
	<property name="app01.cpsemailnotification.json" value="${app01.dist.dir}\CPS\ScheduledTasks\CPSEmailNotification"/>
	<property name="app01.image-queue-processor.json" value="${app01.dist.dir}\CPS\Services\ImageQueueProcessor"/>
	<property name="app01.image-filter-simulator.json" value="${app01.dist.dir}\CPS_Simulators\ImageFilter"/>
	<property name="app01.scheduledtasks.txt" value="${app01.dist.dir}\CPS\ScheduledTasks"/>
	<property name="app01.generic.json" value="${app01.dist.dir}"/>
	<property name="ims.api.json" value="${ims.dist.dir}\API"/>
	<!-- web cofnig path -->
	<property name="web01.web.config" value="${web01.dist.dir}\CPS\WebUI"/>
	<property name="web01.extweb.config" value="${web01.dist.dir}\CPS\ExtWebUI"/>
	<property name="web01.webreportviewer.config.file" value="${web01.dist.dir}\CPS\WebReportViewer\Web.config"/>
	
	
	
	<!-- Database Connection Strings -->
	<property name="db.connection.string" value="server=${db.listener};Database=CPS;Integrated Security=SSPI;Min Pool Size=1;Max Pool Size=100;Connect Timeout=120;TrustServerCertificate=true" />
	<property name="rpt.connection.string" value="Database=CCD;server=${rpt.listener};Trusted_Connection=True;Min Pool Size=1;Max Pool Size=100;Connect Timeout=120;TrustServerCertificate=true" />
	<property name="db.ccd.connection.string" value="server=${db.listener};Database=CCD;Integrated Security=SSPI;Min Pool Size=1;Max Pool Size=100;Connect Timeout=120;TrustServerCertificate=true" />
	<property name="db.aar.connection.string" value="Data Source=${db.listener};Initial Catalog=ICD;Integrated Security=True;Pooling=False;Max Pool Size=200;MultipleActiveResultSets=True;TrustServerCertificate=true" />
	<property name="db.uvid.connection.string" value="Data Source=${db.listener};Initial Catalog=UVID;Integrated Security=True;Pooling=False;Max Pool Size=200;MultipleActiveResultSets=True;TrustServerCertificate=true" /> 
	<property name="db.trip.connection.string" value="Database=TRIP;Server=${db.listener};Trusted_Connection=true;TrustServerCertificate=true;Min Pool Size=1;Max Pool Size=100;Connect Timeout=120" /> 
	<property name="db.dmv.connection.string" value="server=${db.listener};Database=DMV;Integrated Security=True;Min Pool Size=1;Max Pool Size=100;Connect Timeout=120;TrustServerCertificate=true" />
	<property name="db.det.connection.string" value="server=${insight.db.server};Database=DET;Integrated Security=True;Min Pool Size=1;Max Pool Size=100;Connect Timeout=120;TrustServerCertificate=true" />
	<property name="db.dna.connection.string" value="server=${db.listener};Database=DNA;Integrated Security=True;Min Pool Size=1;Max Pool Size=100;Connect Timeout=120;TrustServerCertificate=true" />
	<property name="db.rpt.connection.string" value="server=${rpt.listener};Database=RPT;Integrated Security=True;Min Pool Size=1;Max Pool Size=100;Connect Timeout=120;TrustServerCertificate=true" />
	
	<property name="iisserver" value="" />
	
	<property name="devops.dir"		value="${base.dir}" />
	<property name="devops.repo" value="https://infinity-tcore.visualstudio.com/Infinity%%20Tolling/_git/InfinityHost-DBS" />
	<property name="devops.repo.common" value="https://infinity-tcore.visualstudio.com/Infinity%%20Tolling/_git/Infinity-Common" />
	<property name="devops.repo.tools" value="https://infinity-tcore.visualstudio.com/SD-Tools/_git/SD-Common" />

</project>