<?xml version="1.0" encoding="UTF-8"?>
<project name="MOMS 2.0" default="build" basedir=".">
	<include buildfile="ENVIRONMENT.include"/>
    
	<property name="now" value="${environment::get-variable('NOW')}" />
	
	<!-- Email Server used by the build process - sdexchange server -->
	<property name="MailLogger.mailhost" value="sdexch.tcore.com" />
	<property name="MailLogger.success.subject" value="MOMS ${deployment.environment} - Build Successfully Completed." />
	<property name="MailLogger.from" value="<EMAIL>"/>	
	<property name="MailLogger.success.notify" value="true" />
	<property name="MailLogger.failure.notify" value="true" />
	<property name="ratout.service.errors"    value="true"   readonly="false" />  
	<property name="source_drive" value="D" />
	
	<!-- Change these values to your email address to receive build messages -->
	<property name="MailLogger.failure.to" value="<EMAIL>" />
	<property name="MailLogger.success.to" value="<EMAIL>" />
	
	<!-- Autobuild email -->
	<property name="autobuild.email.address" value="<EMAIL>; <EMAIL>" />
	<property name="autobuild.email.address.from" value="<EMAIL>" />
	
	<!-- SVN credentials -->
	<property name="svn.user" value="tcore\SD-IFXMOMSADMIN" />
	<property name="svn.password" value="S@ndieg0" />
	
	<!-- Data script svn locale -->
	<property name="svn.url-projectdatascripts" value="https://sd-vcmsvn02.tcore.com/svn/MOMS/Products/MOMSInsight/ProjectDataScripts" />
	
	<!-- MOMS Directory definition -->
	<property name="base.dir" value="D:\AppBuild-MOMS" />	
	<property name="build.dir" value="${base.dir}\Source\INS\MOMS" />
	<property name="pds.dir" value="${base.dir}\Source\INS-PDS" />
	<property name="dist.dir" value="${base.dir}\Distribution\MOMS" />
	<property name="Build.OutputFolder" value="${base.dir}\AppLogs" />
	<property name="moms.dist.AppLayer.dir" value="${dist.dir}\AppLayer" />
	<property name="moms.dist.CommonLayer.dir" value="${dist.dir}\CommonLayer" />
	<property name="moms.dist.ServiceClient.dir" value="${dist.dir}\ServiceClient" />
	<property name="moms.dist.ThirdParty.dir" value="${dist.dir}\ThirdParty" />
	
	
	<!-- MOMS Mobile Directory definition -->
	<property name="mobile.build.dir" value="${base.dir}\Source\INS\MOMSMobile" />
	<property name="mobile.dist.dir" value="${base.dir}\Distribution\MOMSMobile" />	
	<property name="mobile.build.dir.ThirdPartyDependencies" value="${build.dir}\ThirdPartyDependencies" />	
	
	<!--MOMS Interfaces-->
	<property name="interfaces.dist.dir" value="${base.dir}\Distribution\MOMSInterfaces" />
	<property name="interfaces.release.directory" value="C:\MOMSInterfaces" />
	<property name="interfaces.dist.ThirdParty.dir" value="${interfaces.dist.dir}\ThirdParty" />
	
	<!--Insight-->
	<property name="insight.build.dir" value="${base.dir}\Source\INS\Insight" />
	<property name="insight.dist.dir" value="${base.dir}\Distribution\Insight" />	
	<property name="insight.mobile.dist.dir" value="${base.dir}\InsightMobile" />	
	<property name="insight.mobile.release.directory" value="C:\InsightMobile" />	
	
	
	<!-- Infinity Directory definition -->
	<property name="infinity.build.dir"			value="${base.dir}\Source\INS\MOMSInterfaces\InfinityMOMSService" />
	<property name="infinity.dist.dir" 			value="${base.dir}\Distribution\MOMSInterfaces\InfinityMOMSService" />
	<property name="infinity.release.directory" value="C:\MOMSInterfaces\InfinityMOMSService" />
	<property name="infinity.dist.AppLayer.dir" value="${infinity.dist.dir}\AppLayer" />
	<property name="infinity.dist.CommonLayer.dir" value="${infinity.dist.dir}\Common" />
	<property name="infinity.dist.ServiceClient.dir" value="${infinity.dist.dir}\ServiceClient" />
	<property name="infinity.dist.DataContract.dir" value="${infinity.dist.dir}\DataContract" />
	
	<!--Integrity diectory definition -->
	<property name="integrity.build.dir"			value="${base.dir}\Source\INS\MOMSInterfaces\IntegrityMOMSService" />
	<property name="integrity.dist.dir" 			value="${base.dir}\Distribution\MOMSInterfaces\IntegrityMOMSService" />
	<property name="integrity.release.directory" value="C:\MOMSInterfaces\IntegrityMOMSService" />
	<property name="integrity.dist.AppLayer.dir" value="${integrity.dist.dir}\AppLayer" />
	<property name="integrity.dist.CommonLayer.dir" value="${integrity.dist.dir}\Common" />
	<property name="integrity.dist.ServiceClient.dir" value="${integrity.dist.dir}\ServiceClient" />
	<property name="integrity.dist.DataContract.dir" value="${integrity.dist.dir}\DataContract" />
	
	<!-- TransSuite Directory definition -->
	<property name="transsuite.build.dir"			value="${base.dir}\Source\INS\MOMSInterfaces\TransSuiteMOMSService" />
	<property name="transsuite.dist.dir" 			value="${base.dir}\Distribution\MOMSInterfaces\TransSuiteMOMSService" />
	<property name="transsuite.release.directory" value="C:\MOMSInterfaces\TransSuiteMOMSService" />
	<property name="transsuite.dist.AppLayer.dir" value="${transsuite.dist.dir}\AppLayer" />
	<property name="transsuite.dist.CommonLayer.dir" value="${transsuite.dist.dir}\Common" />
	<property name="transsuite.dist.ServiceClient.dir" value="${transsuite.dist.dir}\ServiceClient" />
	<property name="transsuite.dist.DataContract.dir" value="${transsuite.dist.dir}\DataContract" />
	
		
	<!-- Solution File -->
	<property name="solution.file" value="${build.dir}\MOMS.sln" />	
	<!-- MOMSMobile Solution File -->
	<property name="mobile.solution.file" value="${mobile.build.dir}\MOMSMobile.sln" />
	<!-- Infinity Service Solution File -->
	<property name="infinity.solution.file" value="${infinity.build.dir}\InfinityMOMSServiceSolution\InfinityMOMSServiceSolution.sln" />
    <!-- Integrity Service Solution File -->
	<property name="integrity.solution.file" value="${integrity.build.dir}\IntegrityMOMSservice\IntegrityMOMSservice.sln" />
	<!-- TransSuite Service Solution File -->
	<property name="transsuite.solution.file" value="${transsuite.build.dir}\TransSuiteMOMSServiceSolution\TransSuiteMOMSServiceSolution.sln" />
	<!-- Insight Solution File -->
	<property name="insight.solution.file" value="${insight.build.dir}\Insight.sln" />
	
	<!-- DB Connection Strings -->
	<property name="moms.connection.string" value="Database=MOMS;Server=${moms.db.server};UID=MOMSAppUser;PWD=*******;Min Pool Size=1;Max Pool Size=100;Connect Timeout=120;"/>	
	<property name="moms.secure.connection.string" value="Database=MOMS;Server=${moms.db.server};Integrated Security=SSPI;Min Pool Size=1;Max Pool Size=100;Connect Timeout=120;"/>	
	<if test="${HasDMS=='true'}">
		<property name="dms.connection.string" value="Database=DMS;Server=${dms.db.server};UID=${dms.db.user};PWD=${dms.db.password};Min Pool Size=1;Max Pool Size=100;Connect Timeout=120;"/>	
	</if>
	<!-- Used in Traffic Loader service -->
	<!-- used in Event and Traffic Loader -->
	<if test="${property::exists('GFIEventLoader')}">
		<property name="gfi.connection.string" value="Database=NSMRulestat;Server=${gfi.db.server};UID=MOMSAppUser;PWD=*******;Min Pool Size=1;Max Pool Size=100;Connect Timeout=120;"/>
	</if>
	<if test="${MOMSEventLoader=='true'}">
		<property name="eventloader.connection.string" value="Database=${eventloader.database};Server=${eventloader.db.server};UID=${eventloader.user};PWD=${eventloader.password};Min Pool Size=1;Max Pool Size=100;Connect Timeout=120;"/>
		<property name="eventloader.secure.connection.string" value="Database=${eventloader.database};Server=${eventloader.db.server};Integrated Security=SSPI;Min Pool Size=1;Max Pool Size=100;Connect Timeout=120;"/>
	
	</if>
	
	<if test="${ImageReviewEventLoader=='true'}">
		<property name="imagereview.eventloader.connection.string" value="Database=${imagereview.eventloader.database};Server=${imagereview.eventloader.db.server};UID=${imagereview.eventloader.user};PWD=${imagereview.eventloader.password};Min Pool Size=1;Max Pool Size=100;Connect Timeout=120;"/>
		<property name="imagereview.eventloader.secure.connection.string" value="Database=${imagereview.eventloader.database};Server=${imagereview.eventloader.db.server};Integrated Security=SSPI;Min Pool Size=1;Max Pool Size=100;Connect Timeout=120;"/>
	</if>
	
	<if test="${IntegrityEventLoader=='true'}">
		<property name="integrity.eventloader.connection.string" value="Database=${integrity.eventloader.database};Server=${integrity.eventloader.db.server};UID=${integrity.eventloader.user};PWD=${integrity.eventloader.password};Min Pool Size=1;Max Pool Size=100;Connect Timeout=120;"/>
		<property name="integrity.eventloader.secure.connection.string" value="Database=${integrity.eventloader.database};Server=${integrity.eventloader.db.server};Integrated Security=SSPI;Min Pool Size=1;Max Pool Size=100;Connect Timeout=120;"/>
	</if>
	<if test="${TransPortalEventLoader=='true'}">
		<property name="transportal.eventloader.connection.string" value="Database=${transportal.eventloader.database};Server=${transportal.db.listener};UID=${transportal.eventloader.user};PWD=${transportal.eventloader.password};Min Pool Size=1;Max Pool Size=100;Connect Timeout=120;"/>
		<property name="transportal.eventloader.secure.connection.string" value="Database=${transportal.eventloader.database};Server=${transportal.db.listener};Integrated Security=SSPI;Min Pool Size=1;Max Pool Size=100;Connect Timeout=120;"/>
	</if>
	<if test="${CPSEventLoader=='true'}">
		<property name="cps.eventloader.connection.string" value="Database=${cps.eventloader.database};Server=${cps.eventloader.db.server};UID=${cps.eventloader.user};PWD=${cps.eventloader.password};Min Pool Size=1;Max Pool Size=100;Connect Timeout=120;"/>
		<property name="cps.eventloader.secure.connection.string" value="Database=${cps.eventloader.database};Server=${cps.eventloader.db.server};Integrated Security=SSPI;Min Pool Size=1;Max Pool Size=100;Connect Timeout=120;"/>
	</if>
	<if test="${VOTTEventLoader=='true'}">
		<property name="vott.eventloader.connection.string" value="Database=${vott.eventloader.database};Server=${vott.eventloader.db.server};UID=${vott.eventloader.user};PWD=${vott.eventloader.password};Min Pool Size=1;Max Pool Size=100;Connect Timeout=120;"/>
		<property name="vott.eventloader.secure.connection.string" value="Database=${vott.eventloader.database};Server=${vott.eventloader.db.server};Integrated Security=SSPI;Min Pool Size=1;Max Pool Size=100;Connect Timeout=120;"/>
	</if>
	<if test="${SolarWindsFeature=='true'}">
		<property name="solarwinds.connection.string" value="Database=${solarwinds.eventloader.database};Server=${solarwinds.db.server};UID=${solarwinds.eventloader.user};PWD=${solarwinds.eventloader.password};Min Pool Size=1;Max Pool Size=100;Connect Timeout=120;"/>
		<property name="solarwinds.eventloader.secure.connection.string" value="Database=${solarwinds.eventloader.database};Server=${solarwinds.db.server};Integrated Security=SSPI;Min Pool Size=1;Max Pool Size=100;Connect Timeout=120;"/>
	</if>
	<if test="${WhatsUpFeature=='true'}">
		<property name="whatsup.connection.string" value="Database=${whatsup.eventloader.database};Server=${whatsup.db.server};UID=${whatsup.eventloader.user};PWD=${whatsup.eventloader.password};Min Pool Size=1;Max Pool Size=100;Connect Timeout=120;"/>
		<property name="whatsup.eventloader.secure.connection.string" value="Database=${whatsup.eventloader.database};Server=${whatsup.db.server};Integrated Security=SSPI;Min Pool Size=1;Max Pool Size=100;Connect Timeout=120;"/>		
	</if>
	<if test="${MobileFeature=='true'}">
		<property name="momsmobile.connection.string" value="Database=MOMS;Server=${momsmobile.db.server};UID=MOMSAppUser;PWD=*******;Min Pool Size=1;Max Pool Size=100;Connect Timeout=120;"/>
		<property name="momsmobile.secure.connection.string" value="Database=MOMS;Server=${momsmobile.db.server};Integrated Security=SSPI;Min Pool Size=1;Max Pool Size=100;Connect Timeout=120;"/>
	</if>

	<!-- MOMS Report Resource staging directory -->
	<property name="moms.resources.dir" value="${base.dir}\Resources" />
	
	<!-- MOMS Distribution directories -->
	<property name="app.moms.internal.dist.dir" value="${dist.dir}\ServiceHost" />
	<property name="app.moms.external.dist.dir" value="${dist.dir}\ServiceHost" />
	<property name="app.moms.traffic.loader.dist.dir" value="${dist.dir}\ServiceHost" />
	<property name="app.moms.gps.locator.dist.dir" value="${dist.dir}\ServiceHost" />
	<property name="tasks.moms.dist.dir" value="${dist.dir}\ScheduledTasks" />
  	<property name="unit.test.dir" value="${build.dir}\UnitTests" />
	
	<!-- MOMS Mobile Distribution directories -->
	<property name="app.momsmobile.internal.dist.dir" value="${mobile.dist.dir}\ServiceHosts" />
	<property name="web.momsmobile.dist.dir" value="${mobile.dist.dir}\webLayer\www" />
	<property name="webapi.momsmobile.dist.dir" value="${mobile.dist.dir}\webLayer\MomsMobileWebApi" />
	<property name="momsmobile.dist.dir.ThirdPartyDependencies" value="${mobile.dist.dir}\ThirdPartyDependencies" />
	<property name="momsmobile.dist.dir.js" value="${mobile.dist.dir}\webLayer\www\js" />
	

	<!--Insight Distribution Directory -->
	<property name="web.insight.dist.dir" value="${insight.dist.dir}\webLayer\InsightWeb" />
	<property name="webapi.insight.dist.dir" value="${insight.dist.dir}\webLayer\InsightWebApi" />
	<property name="rpts.insight.dist.dir" value="${insight.dist.dir}\webLayer\InsightReports" />
	<property name="offlinemaps.insight.dist.dir" value="${insight.dist.dir}\webLayer\InsightOfflineMaps" />
	<property name="insight.dist.dir.js" value="${insight.dist.dir}\webLayer\InsightWeb\app\shared\common\constants" />
	<property name="insight.dist.dir.css" value="${insight.dist.dir}\webLayer\InsightWeb\dist\css" />
	<property name="insight.dist.dir.scss" value="${insight.dist.dir}\webLayer\InsightWeb\app\assets\scss\includes" />	
	
	<!-- Infinity Service Distribution directory -->
	<property name="app.Infinity.dist.dir" 		value="${infinity.dist.dir}\ServiceHost" />
	
    <!-- Integrity Service Distribution directory -->
	<property name="app.Integrity.dist.dir" 		value="${integrity.dist.dir}\ServiceHost" />
	
	<!-- TransSuite Distribution directory -->
	<property name="app.TransSuite.dist.dir" 		value="${transsuite.dist.dir}\ServiceHost" />
		
	<!-- Internal MOMS Service Host App Settings -->
	<property name="InternalServiceName" value="MOMSService"/>
	<property name="InternalServiceDisplayName" value="MOMSService"/>
	<property name="InternalServiceDescription" value="WCF service host for MOMS Internal Service"/>
	<property name="app.log.file" value="${log.dir}/moms_MOMSServiceHost.log"/>
	
	<!-- External MOMS Service Host App Settings -->
	<property name="ExternalServiceName" value="MOMSExternalService"/>
	<property name="ExternalServiceDisplayName" value="MOMSExternalService"/>
	<property name="ExternalServiceDescription" value="WCF service host for MOMS External Interfaces Service"/>
	<property name="external.app.log.file" value="${log.dir}/moms_MOMSExternalServiceHost.log"/>
	<property name="AESKey" value="TOkwxR+IDX0EKFodpGP56w=="/>
	
	<!-- Traffic Loader MOMS Service Host App Settings -->
	<property name="TrafficServiceName" value="MOMSTrafficLoaderService"/>
	<property name="TrafficServiceDisplayName" value="MOMSTrafficLoaderService"/>
	<property name="TrafficServiceDescription" value="Window service host for MOMS Traffic Loader service"/>
	<property name="ClientSettingsProvider.ServiceUri" value=""/>
	<property name="traffic.loader.app.log.file" value="${log.dir}/moms_MOMSTrafficLoaderServiceHost.log" />
	
	<!-- GPS Locator MOMS Service Host App Settings -->
	<property name="GPSLocatorServiceName" value="MOMSGPSLocatorService"/>
	<property name="GPSLocatorServiceDisplayName" value="MOMSGPSLocatorService"/>
	<property name="GPSLocatorServiceDescription" value="Window service host for MOMS GPS Locator service"/>
	<property name="gps.locator.app.log.file" value="${log.dir}/moms_MOMSGPSLocatorServiceHost.log"/>
	
	
	<!-- NotificationEscalation Task -->
	<property name="notificationEscalation.task.moms.dist.dir" value="${tasks.moms.dist.dir}\MOMSNotificationEscalationTask" />
	<property name="notificationEscalation.task.log.file" value="${log.dir}/moms_NotificationEscalationTask.log"/>
	
	<!-- MOMS Event Loader Task (generic)-->
	<property name="MOMSEventLoader.task.moms.dist.dir" value="${tasks.moms.dist.dir}\MOMSEventLoader" />
	<property name="event.loader.task.log.file" value="${log.dir}/moms_MOMS_EventLoaderTask.log"/>
		
	<!-- SolarWinds Event Loader Task -->
	<property name="SolarWinds.MOMSEventLoader.task.moms.dist.dir" value="${tasks.moms.dist.dir}\SolarWinds_MOMSEventLoader" />
	<property name="SolarWinds.event.loader.task.log.file" value="${log.dir}/moms_SolarWinds_EventLoaderTask.log"/>
	
	<!-- ImageReview Event Loader Task -->
	<property name="ImageReview.MOMSEventLoader.task.moms.dist.dir" value="${tasks.moms.dist.dir}\ImageReview_MOMSEventLoader" />
	<property name="ImageReview.event.loader.task.log.file" value="${log.dir}/moms_ImageReview_EventLoaderTask.log"/>
	
	<!-- Integrity Event Loader Task -->
	<property name="Integrity.MOMSEventLoader.task.moms.dist.dir" value="${tasks.moms.dist.dir}\Integrity_MOMSEventLoader" />
	<property name="Integrity.event.loader.task.log.file" value="${log.dir}/moms_Integrity_EventLoaderTask.log"/>
	
	<!-- Transportal Event Loader Task -->
	<property name="TransPortal.MOMSEventLoader.task.moms.dist.dir" value="${tasks.moms.dist.dir}\TransPortal_MOMSEventLoader" />
	<property name="TransPortal.event.loader.task.log.file" value="${log.dir}/moms_TransPortal_EventLoaderTask.log"/>
	
	<!-- CPS Event Loader Task -->
	<property name="CPS.MOMSEventLoader.task.moms.dist.dir" value="${tasks.moms.dist.dir}\CPS_MOMSEventLoader" />
	<property name="CPS.event.loader.task.log.file" value="${log.dir}/moms_CPS_EventLoaderTask.log"/>
	
	<!-- VOTT Event Loader Task -->
	<property name="VOTT.MOMSEventLoader.task.moms.dist.dir" value="${tasks.moms.dist.dir}\VOTT_MOMSEventLoader" />
	<property name="VOTT.event.loader.task.log.file" value="${log.dir}/moms_VOTT_EventLoaderTask.log"/>
	
	<!-- WhatsUp Event Loader Task -->
	<property name="WhatsUp.MOMSEventLoader.task.moms.dist.dir" value="${tasks.moms.dist.dir}\WhatsUp_MOMSEventLoader" />
	<property name="WhatsUp.event.loader.task.log.file" value="${log.dir}/moms_WhatsUp_EventLoaderTask.log"/>

	<!-- Failure Analysis Task -->
	<property name="MOMSFailureAnalysis.task.moms.dist.dir" value="${tasks.moms.dist.dir}\MOMSFailureAnalysisTaskConsole" />
	<property name="failure.analysis.task.log.file" value="${log.dir}/moms_FailureAnalysisTask.log"/>
	
	<!-- Work Order Messaging Task -->
	<property name="workOrderMessaging.task.moms.dist.dir" value="${tasks.moms.dist.dir}\MOMSWorkOrderMessagingTask" />
	<property name="workorder.messaging.task.log.file" value="${log.dir}/moms_WorkOrderMessageTask.log"/>
	
	<!-- Predictive, Preventive and Failure Analysis Tasks -->
	<property name="MOMSPredictiveMaintenanceNotificationTaskConsole.task.moms.dist.dir" value="${tasks.moms.dist.dir}\MOMSPredictiveMaintenanceNotificationTaskConsole" />	
	<property name="MOMSPreventiveMaintenanceTask.task.moms.dist.dir" value="${tasks.moms.dist.dir}\MOMSPreventiveMaintenanceTask" />
	<property name="MOMSFailureAnalysisTaskConsole.task.moms.dist.dir" value="${tasks.moms.dist.dir}\MOMSFailureAnalysisTaskConsole" />
	
	<property name="failure.analysis.task.log.file" value="${log.dir}/moms_MOMSFailureAnalysisTask.log"/>
	<property name="predictive.maint.task.log.file" value="${log.dir}/moms_PredictiveMaintenanceNotificationTask.log"/>
	<property name="preventive.maint.task.log.file" value="${log.dir}/moms_PreventiveMaintenanceTask.log"/>
	
	<!-- Inventory Level Notification Task -->
	<property name="InventoryLevelNotification.task.moms.dist.dir" value="${tasks.moms.dist.dir}\MOMSInventoryLevelNotification" />	
	<property name="inventory.level.task.log.file" value="${log.dir}/moms_InventoryLevel.log"/>
	
	<!-- GFI Event Loader Task -->
	<property name="GFI.MOMSEventLoader.task.moms.dist.dir" value="${tasks.moms.dist.dir}\GFI_MOMSEventLoader" />
	<property name="GFI.event.loader.task.log.file" value="${log.dir}/moms_GFI_EventLoaderTask.log"/>
	
	<!-- External Notification Task -->
	<property name="ExternalNotifier.task.moms.dist.dir"	value="${tasks.moms.dist.dir}\MOMSExternalNotifier" />
	<property name="Externalnotifier.task.log.file" value="${log.dir}/moms_ExternalNotifierTask.log"/>
	
	<!-- Active Directory Task -->
	<property name="ActiveDirectoryPollingTask.task.moms.dist.dir"	value="${tasks.moms.dist.dir}\MOMSActiveDirectoryPolling" />
	<property name="ActiveDirectoryPollingTask.task.log.file" value="${log.dir}/moms_ActiveDirectoryPolling.log"/>
	
	
	<!--MOMSTrafficDataLoader Task -->
	<property name="MOMSTrafficDataLoader.task.moms.dist.dir"	value="${tasks.moms.dist.dir}\MOMSTrafficDataLoader" />
	<property name="MOMSTrafficDataLoader.task.log.file" value="${log.dir}/moms_TrafficDataLoader.log"/>
	

	<!-- Insight API Config settings -->
	<property name="insight.webapi.log.file" value="${log.dir}/InsightWebApi.log"/>
	<property name="insight.external.webapi.log.file" value="${log.dir}/InsightExternalWebApi.log"/>
	
	<!-- Insight API Elmah dir settings -->
	<property name="insight.elmah.webapi.log.dir" value="${log.dir}/Elmah_InsightWebAPI"/>
	
	
	
	<!-- MOMS Mobile Web config settings -->
	<property name="PreserveLoginUrl" value="true"/>
	<property name="ClientValidationEnabled" value="true" />
	<property name="UnobtrusiveJavaScriptEnabled" value="true" />	
	<property name="webapi.log.file" value="${log.dir}/MomsMobileWebApi.log"/>
	<property name="mobile.app.log.file" value="${log.dir}/MomsMobileWcfInternalHost.log"/>
	

	<!--GPS Api Config App Settings parameters -->
	<property name="GPSServiceUsername" value="gpsUser"/>
	<property name="GPSServicePassword" value="gpsPassword"/>
	
	<!-- WhatsUp Config settings  -->
	<property name="WhatsUpIDConcat" value="222"/>
	
	<!-- SolarWinds Config Settings -->
	<property name="SolarWindsIDConcat" value="444"/>

	<!-- WCF Service Config Files Definition -->
	
	<property name="moms.internal.service.host.appconfig.file" value="${app.moms.internal.dist.dir}\tcore.MOMSServiceHost.exe.config" />
	<property name="moms.external.service.host.appconfig.file" value="${app.moms.external.dist.dir}\tcore.MOMSExternalServiceHost.exe.config" />
	<property name="moms.traffic.loader.service.appconfig.file" value="${app.moms.traffic.loader.dist.dir}\tcore.MOMSTrafficLoaderHost.exe.config" />
	<property name="moms.gps.locator.service.appconfig.file" value="${app.moms.gps.locator.dist.dir}\tcore.MOMSGPSLocatorServiceHost.exe.config" />
	
	<!-- MOMSMobile -->
	<property name="momsmobile.webapi.config.file" value="${webapi.momsmobile.dist.dir}\Web.config" />
	<property name="momsmobile.internal.service.host.appconfig.file" value="${app.momsmobile.internal.dist.dir}\tcore.MomsMobile.MomsMobileWcfHost.exe.config" />
	
	<!-- Insight -->
	<property name="insight.webapi.config.file" value="${webapi.insight.dist.dir}\Web.config" />
	<property name="insight.webapi.keys.config.file" value="${webapi.insight.dist.dir}\keys.config" />
	<property name="insight.rpts.config.file" value="${rpts.insight.dist.dir}\Web.config" />
	<property name="insight.web.config.file" value="${web.insight.dist.dir}\Web.config" />
	<property name="insight.mobile.webapi.config.file" value="${insight.mobile.dist.dir}\WebAPI\Web.config" />
		
	<!-- Infinity Service -->
	<property name="Infinity.service.host.appconfig.file" value="${infinity.dist.dir}\ServiceHost\tcore.Infinity.InfinityMOMSservicehost.exe.config" />	
	<!-- Integrity Service -->
	<property name="Integrity.service.host.appconfig.file" value="${integrity.dist.dir}\ServiceHost\tcore.Integrity.IntegrityMOMSservicehost.exe.config" />	
	<!-- TransSuite service -->
	<property name="TransSuite.service.host.appconfig.file" value="${transsuite.dist.dir}\ServiceHost\tcore.TransSuite.TransSuiteMOMSservicehost.exe.config" />	
	
	<!-- Task Config files -->
	<property name="notificationEscalation.task.moms.config.file" value="${notificationEscalation.task.moms.dist.dir}\tcore.MOMSNotificationEscalation.exe.config" />
	<property name="MOMSEventLoader.task.moms.config.file" value="${MOMSEventLoader.task.moms.dist.dir}\tcore.MOMSEventLoaderConsole.exe.config" />		
	<property name="GFI.MOMSEventLoader.task.moms.config.file" value="${GFI.MOMSEventLoader.task.moms.dist.dir}\tcore.MOMSEventLoaderConsole.exe.config" />		
	<property name="SolarWinds.MOMSEventLoader.task.moms.config.file" value="${SolarWinds.MOMSEventLoader.task.moms.dist.dir}\tcore.MOMSEventLoaderConsole.exe.config" />
	<property name="NewSolarWinds.MOMSEventLoader.task.moms.config.file" value="${SolarWinds.MOMSEventLoader.task.moms.dist.dir}\tcore.MOMSSolarWindsEventLoaderConsole.exe.config" />
	<property name="ImageReview.MOMSEventLoader.task.moms.config.file" value="${ImageReview.MOMSEventLoader.task.moms.dist.dir}\tcore.MOMSEventLoaderConsole.exe.config" />
	<property name="Integrity.MOMSEventLoader.task.moms.config.file" value="${Integrity.MOMSEventLoader.task.moms.dist.dir}\tcore.MOMSEventLoaderConsole.exe.config" />
	<property name="TransPortal.MOMSEventLoader.task.moms.config.file" value="${TransPortal.MOMSEventLoader.task.moms.dist.dir}\tcore.MOMSEventLoaderConsole.exe.config" />
	<property name="CPS.MOMSEventLoader.task.moms.config.file" value="${CPS.MOMSEventLoader.task.moms.dist.dir}\tcore.MOMSCpsEventLoaderConsole.exe.config" />
	<property name="VOTT.MOMSEventLoader.task.moms.config.file" value="${VOTT.MOMSEventLoader.task.moms.dist.dir}\tcore.MOMSEventLoaderConsole.exe.config" />
	<property name="WhatsUp.MOMSEventLoader.task.moms.config.file" value="${WhatsUp.MOMSEventLoader.task.moms.dist.dir}\tcore.MOMSEventLoaderConsole.exe.config" />	
	<property name="workOrderMessaging.task.moms.config.file" value="${workOrderMessaging.task.moms.dist.dir}\tcore.MOMSWorkOrderMessagingTask.exe.config" />
	<property name="MOMSPredictiveMaintenanceNotificationTaskConsole.task.moms.config.file" value="${MOMSPredictiveMaintenanceNotificationTaskConsole.task.moms.dist.dir}\tcore.MOMSPredictiveMaintenanceNotificationTaskConsole.exe.config" />
	<property name="MOMSFailureAnalysisTaskConsole.task.moms.config.file" value="${MOMSFailureAnalysisTaskConsole.task.moms.dist.dir}\tcore.MOMSFailureAnalysisTaskConsole.exe.config" />
	<property name="InventoryLevelNotification.moms.config.file" value="${InventoryLevelNotification.task.moms.dist.dir}\tcore.MOMSInventoryLevelNotification.exe.config" />
	<property name="MOMSPreventiveMaintenanceTask.moms.config.file" value="${MOMSPreventiveMaintenanceTask.task.moms.dist.dir}\tcore.MOMSPreventiveMaintenanceTask.exe.config" />
	<property name="MOMSExternalNotifier.task.moms.config.file" value="${ExternalNotifier.task.moms.dist.dir}\tcore.MOMSExternalNotifierConsole.exe.config" />	
	<property name="MOMSActiveDirectoryPolling.task.moms.config.file" value="${ActiveDirectoryPollingTask.task.moms.dist.dir}\tcore.MOMSActiveDirectoryPolling.exe.config" />	
	<property name="MOMSTrafficDataLoader.task.moms.config.file" value="${MOMSTrafficDataLoader.task.moms.dist.dir}\tcore.MOMSTrafficDataLoader.exe.config" />	
	
	<!-- Reports deployer settings --> 
	<property name="reports.deployer.exec.file" value="${base.dir}\ReportsDeployer.exe" />	
	<property name="reports.deployer.appconfig.file" value="${base.dir}\ReportsDeployer.exe.config" />
	<property name="report.datasource.moms" value="data source=${moms.db.server};initial catalog=MOMS" />	
	<property name="deploy.Reports.From.Dir" value="MOMSReports\MOMSReports" />	
	<property name="deploy.Reports.Reports.Path" value="${report.server.URL}/MOMSReports" />	
	<property name="deploy.Reports.Reports.Overwrite" value="true" />	
	<property name="deploy.Reports.DataSets.Overwrite" value="true" />	
	<property name="deploy.Reports.Shared.DataSource.FolderName" value="Data Sources" />	
	<property name="deploy.Reports.DataSet.DataSource.Path" value="/${deploy.Reports.Shared.DataSource.FolderName}/MOMSLive" />	
	<property name="deploy.Reports.Shared.DataSet.FolderName" value="DataSets" />	
	<property name="deploy.Reports.DataSource.Overwrite" value="false" />	
	<property name="deploy.Reports.Write.Exception.To.Console" value="true" />	
		
	<!-- SSL PORTS -->
	<property name="insight.webapi.sslport" value="8443" />	
	<property name="insight.mobile.webapi.sslport" value="5443" />	
	
	<property name="iisserver" value="" />
	
	<!--Devops -->
	<property name="devops.dir"		value="${base.dir}\Source" />
	<property name="devops.repo" value="https://infinity-tcore.visualstudio.com/SD-INS/_git/INS" />
	<property name="devops.repo.pds" value="https://infinity-tcore.visualstudio.com/SD-INS/_git/INS-PDS" />
</project>