<?xml version="1.0" encoding="UTF-8"?>
<project name="ImageReview 1.0" default="build" basedir=".">
	<include buildfile="ENVIRONMENT.include"/>
	<property name="now" value="${environment::get-variable('NOW')}" />
	<property name="scripts.dir" value="${environment::get-variable('scripts-dir')}" />
	
	<!-- Email Server used by the build process - sdexchange server -->
	<property name="MailLogger.mailhost" value="sdexch.tcore.com" />
	<property name="MailLogger.success.subject" value="ImageReview ${deployment.environment} - Build Successfully Completed." />
	<property name="MailLogger.from" value="<EMAIL>"/>	
	<property name="MailLogger.success.notify" value="true" />
	<property name="MailLogger.failure.notify" value="true" />
	<property name="ratout.service.errors"    value="true"   readonly="false" />  
	<property name="source_drive" value="D" />
	
	<!-- Change these values to your email address to receive build messages -->
	<property name="MailLogger.failure.to" value="<EMAIL>" />
	<property name="MailLogger.success.to" value="<EMAIL>" />
	
	<!-- Autobuild email -->
	<property name="autobuild.email.address" value="<EMAIL>; <EMAIL>" />
	<property name="autobuild.email.address.from" value="<EMAIL>" />
	
	<!-- SVN credentials -->
	<property name="svn.user" value="tcore\SD-IFXMOMSADMIN" />
	<property name="svn.password" value="S@ndieg0" />
	
	<!-- ImageReview Directory definition -->
	<property name="base.dir" value="D:\AppBuild-MIR" />	
	<property name="base.dist.dir" value="D:\AppBuild-MIR\DIST" />	
	<!--<property name="build.dir" value="${base.dir}\Source\ImageReview" />
	<property name="dist.dir" value="${base.dir}\Distribution\ImageReview" />-->
	<property name="Build.OutputFolder" value="${base.dir}\AppLogs" />
	
	
	<property name="imagereview.build.dir"			value="${base.dir}\Source\MIR" />
	<property name="imagereview.fingerprint.dist.dir" value="${base.dist.dir}\FP01" />
	<property name="imagereview.dist.reports.dir" value="${base.dist.dir}\RPT01\Reports" />
	<property name="activedirectory.build.dir"			value="${base.dir}\Source\ActiveDirectory" />
	
	<!-- AD release dir -->
	<property name="activedirectory.release.directory" value="C:\ActiveDirectoryPolling\ScheduledTasks" />
		
	
	<!--ImageReview FPM-->
	<property name="imagereview.fpm.dist.dir" value="${base.dist.dir}\FP01\FPM" />
	<property name="imagereview.fpm.release.directory" value="C:\FPM" />
	<property name="imagereview.fpm.dist.ThirdParty.dir" value="${imagereview.fpm.dist.dir}\ThirdParty" />
	<property name="imagereview.fpm.dist.AppLayer.dir" value="${imagereview.fpm.dist.dir}\AppLayer" />
	<property name="imagereview.fpm.dist.CommonLayer.dir" value="${imagereview.fpm.dist.dir}\CommonLayer" />
	<property name="imagereview.fpm.dist.ServiceClient.dir" value="${imagereview.fpm.dist.dir}\ServiceClient" />
	<property name="imagereview.fpm.dist.ServiceHost.dir" value="${imagereview.fpm.dist.dir}\ServiceHost" />
	<property name="imagereview.fpm.dist.WebAPI.dir" value="${imagereview.fpm.dist.dir}\WebApi" />
	<property name="imagereview.fpm.dist.ScheduledTasks.dir" value="${imagereview.fpm.dist.dir}\ScheduledTasks" />

	
	<!--ImageReview FPP-->
	<property name="imagereview.fpp.dist.dir" value="${base.dist.dir}\FP01\FPP" />
	<property name="imagereview.fpc.dist.dir" value="${base.dist.dir}\FPClient" />
	<property name="imagereview.fpc.fpp.dist.dir" value="${base.dist.dir}\FPClient\FPP" />
	<property name="imagereview.fpp.release.directory" value="C:\FPP" />
	<property name="imagereview.fpp.dist.ThirdParty.dir" value="${imagereview.fpp.dist.dir}\ThirdParty" />
	<property name="imagereview.fpp.dist.AppLayer.dir" value="${imagereview.fpp.dist.dir}\AppLayer" />
	<property name="imagereview.fpp.dist.CommonLayer.dir" value="${imagereview.fpp.dist.dir}\CommonLayer" />
	<property name="imagereview.fpp.dist.ServiceHost.dir" value="${imagereview.fpp.dist.dir}\ServiceHost" />
	<property name="imagereview.fpp.dist.ServiceClient.dir" value="${imagereview.fpc.fpp.dist.dir}\ServiceClient" />
	<property name="imagereview.fpp.dist.ScheduledTasks.dir" value="${imagereview.fpp.dist.dir}\ScheduledTasks" />
	
	<!--ImageReview HOCP-->
	<property name="imagereview.hocp.dist.dir" value="${base.dist.dir}\HOCP01\HOCP" />
	<property name="imagereview.hocp.release.directory" value="C:\HOCP" />
	<property name="imagereview.hocp.dist.ThirdParty.dir" value="${imagereview.hocp.dist.dir}\ThirdParty" />
	<property name="imagereview.hocp.dist.AppLayer.dir" value="${imagereview.hocp.dist.dir}\AppLayer" />
	<property name="imagereview.hocp.dist.CommonLayer.dir" value="${imagereview.hocp.dist.dir}\CommonLayer" />
	<property name="imagereview.hocp.dist.ServiceClient.dir" value="${imagereview.hocp.dist.dir}\ServiceClient" />
	<property name="imagereview.hocp.dist.ServiceHost.dir" value="${imagereview.hocp.dist.dir}\ServiceHost" />
	<property name="imagereview.hocp.dist.WebAPI.dir" value="${imagereview.hocp.dist.dir}\WebApi" />
	<property name="imagereview.hocp.dist.ScheduledTasks.dir" value="${imagereview.hocp.dist.dir}\ScheduledTasks" />
	
	<!--ImageReview HOCP-->
	<property name="imagereview.prehocp.dist.dir" value="${base.dist.dir}\PREHOCP01\PREHOCP" />
	<property name="imagereview.prehocp.dist.ThirdParty.dir" value="${imagereview.prehocp.dist.dir}\ThirdParty" />
	<property name="imagereview.prehocp.dist.AppLayer.dir" value="${imagereview.prehocp.dist.dir}\AppLayer" />
	<property name="imagereview.prehocp.dist.CommonLayer.dir" value="${imagereview.prehocp.dist.dir}\CommonLayer" />
	<property name="imagereview.prehocp.dist.ServiceClient.dir" value="${imagereview.prehocp.dist.dir}\ServiceClient" />
	<property name="imagereview.prehocp.dist.ServiceHost.dir" value="${imagereview.prehocp.dist.dir}\ServiceHost" />
	<property name="imagereview.prehocp.dist.WebAPI.dir" value="${imagereview.prehocp.dist.dir}\WebApi" />
	<property name="imagereview.prehocp.dist.ScheduledTasks.dir" value="${imagereview.prehocp.dist.dir}\ScheduledTasks" />
	
	
	<!--ImageReview IRR-->
	<property name="imagereview.irr.dist.dir" value="${base.dist.dir}\IRR01\IRR" />
	<property name="imagereview.irr.release.directory" value="C:\IRR" />
	<property name="imagereview.irr.dist.ThirdParty.dir" value="${imagereview.irr.dist.dir}\ThirdParty" />
	<property name="imagereview.irr.dist.AppLayer.dir" value="${imagereview.irr.dist.dir}\AppLayer" />
	<property name="imagereview.irr.dist.CommonLayer.dir" value="${imagereview.irr.dist.dir}\CommonLayer" />
	<property name="imagereview.irr.dist.ServiceClient.dir" value="${imagereview.irr.dist.dir}\ServiceClient" />
	<property name="imagereview.irr.dist.ServiceHost.dir" value="${imagereview.irr.dist.dir}\ServiceHost" />
	<property name="imagereview.irr.dist.WebAPI.dir" value="${imagereview.irr.dist.dir}\WebApi" />
	<property name="imagereview.irr.dist.ScheduledTasks.dir" value="${imagereview.irr.dist.dir}\ScheduledTasks" />
	
	<!--ImageReview MIR Task -->
	<property name="imagereview.mir.mrt.dist.dir" value="${base.dist.dir}\APP01\MIR\ManualResultsTask" />
	<property name="imagereview.mir.mrt.release.directory" value="C:\MIR\ManualResultsTask" />
	<property name="imagereview.mir.mrt.dist.ThirdParty.dir" value="${imagereview.mir.mrt.dist.dir}\ThirdParty" />
	<property name="imagereview.mir.mrt.dist.AppLayer.dir" value="${imagereview.mir.mrt.dist.dir}\AppLayer" />
	<property name="imagereview.mir.mrt.dist.CommonLayer.dir" value="${imagereview.mir.mrt.dist.dir}\CommonLayer" />
	<property name="imagereview.mir.mrt.dist.ScheduledTasks.dir" value="${imagereview.mir.mrt.dist.dir}\ScheduledTasks" />
	
	<!--ImageReview Transaction Server -->
	<property name="imagereview.trans.dist.dir" value="${base.dist.dir}\TRANS01\MIR\TransactionsServer" />
	<property name="imagereview.trans.release.directory" value="C:\MIR\TransactionsServer" />
	<property name="imagereview.trans.dist.ThirdParty.dir" value="${imagereview.trans.dist.dir}\ThirdParty" />
	<property name="imagereview.trans.dist.AppLayer.dir" value="${imagereview.trans.dist.dir}\AppLayer" />
	<property name="imagereview.trans.dist.CommonLayer.dir" value="${imagereview.trans.dist.dir}\CommonLayer" />
	<property name="imagereview.trans.dist.ServiceClient.dir" value="${imagereview.trans.dist.dir}\ServiceClient" />
	<property name="imagereview.trans.dist.ServiceHost.dir" value="${imagereview.trans.dist.dir}\ServiceHost" />
	<property name="imagereview.trans.dist.WebLayer.dir" value="${imagereview.trans.dist.dir}\WebLayer" />
	<property name="imagereview.trans.dist.WebAPI.dir" value="${imagereview.trans.dist.dir}\WebLayer\WebAPI" />
	
	<!--Integrity Image Review Modules -->
	<property name="imagereview.mir.main.dist.dir" value="${base.dist.dir}\APP01\MIR" />
	<property name="imagereview.mir.dist.dir" value="${base.dist.dir}\APP01\MIR\ImageReview" />
	<property name="imagereview.mir.web.dist.dir" value="${base.dist.dir}\WEB01\MIR\ImageReviewWeb" />
	<property name="imagereview.mir.release.directory" value="C:\MIR\ImageReview" />
	<property name="imagereview.mir.web.release.directory" value="C:\MIR\ImageReviewWeb" />
	<property name="imagereview.mir.main.release.directory" value="C:\MIR" />
	<property name="imagereview.mir.main.dist.ThirdParty.dir" value="${imagereview.mir.main.dist.dir}\ImageReview\ThirdParty" />
	<property name="imagereview.mir.main.dist.Plugin.dir" value="${imagereview.mir.dist.dir}\Plugins" />
	<property name="imagereview.mir.dist.AppLayer.dir" value="${imagereview.mir.dist.dir}\AppLayer" />
	<property name="imagereview.mir.dist.CommonLayer.dir" value="${imagereview.mir.dist.dir}\CommonLayer" />
	<property name="imagereview.mir.dist.ServiceHost.dir" value="${imagereview.mir.dist.dir}\ServiceHost" />
	<property name="imagereview.mir.dist.ScheduledTasks.dir" value="${imagereview.mir.dist.dir}\ScheduledTasks" />
	<property name="imagereview.mir.dist.ServiceClient.dir" value="${imagereview.mir.dist.dir}\ServiceClient" />
	<property name="imagereview.mir.dist.Workflows.dir" value="${imagereview.mir.dist.dir}\Workflows" />
	<property name="imagereview.mir.dist.packages.dir" value="${imagereview.mir.dist.dir}\packages" />
	<property name="imagereview.mir.web.dist.WebAPI.dir" value="${imagereview.mir.web.dist.dir}\WebLayer\WebAPI" />
	<!-- MIR Web folders -->
	<property name="imagereview.mir.web.dist.Workflows.dir" value="${imagereview.mir.web.dist.dir}\Workflows" />
	<property name="imagereview.mir.web.dist.WebLayer.dir" value="${imagereview.mir.web.dist.dir}\WebLayer" />	
	<property name="imagereview.mir.web.dist.ThirdParty.dir" value="${imagereview.mir.web.dist.dir}\ThirdParty" />
	<property name="imagereview.mir.web.dist.packages.dir" value="${imagereview.mir.web.dist.dir}\packages" />
	<!--<property name="imagereview.mir.web.dist.ThirdParty.dir" value="${imagereview.mir.web.dist.dir}\ThirdParty" /> -->
	
	<!-- REPORTS -->
	<property name="imagereview.rpt.dist.dir" value="${base.dist.dir}\RPT01" />
	<property name="imagereview.rpt.reports.dist.dir" value="${base.dist.dir}\RPT01\Reports" />
	<property name="imagereview.rpt.resources.dist.dir" value="${base.dist.dir}\RPT01\Resources" />
	<!-- TABLEAU -->
	<property name="tableau.dist.dir" value="${base.dist.dir}\TABLEAU" />
	
	<!-- Solution File -->
	
	<!-- ImageReview Solution Files -->
	<property name="imagereview.fpm.solution.file" value="${imagereview.build.dir}\FingerPrintMatching\FingerPrintMatching.sln" />
	<property name="imagereview.fpp.solution.file" value="${imagereview.build.dir}\FingerPrintProcessor\FingerPrintProcessor.sln" />
	<property name="imagereview.hocp.solution.file" value="${imagereview.build.dir}\HighOcrConfProcessing\HighOcrConfProcessing.sln" />
	<property name="imagereview.irr.solution.file" value="${imagereview.build.dir}\ImageReviewResult\ImageReviewResult.sln" />
	<property name="imagereview.mir.mrt.solution.file" value="${imagereview.build.dir}\ImageReviewManualResults\ImageReviewManualResults.sln" />
	<property name="imagereview.trans.solution.file" value="${imagereview.build.dir}\ImageReviewTransactionsServer\ImageReviewTransactionsServer.sln" />
	<property name="imagereview.mir.solution.file" value="${imagereview.build.dir}\IntegrityImageReview\ImageReview.sln" />
	<property name="activedirectory.solution.file" value="${activedirectory.build.dir}\ActiveDirectoryPolling.sln" />
	<property name="imagereview.plugin.solution.file" value="${imagereview.build.dir}\IntegrityImageReview\Plugins\Qfree\QFreePlugin.sln" />
	
	<!-- Config files -->
	
	<!-- FPM API Config settings -->
	<property name="imagereview.fpm.webapi.config.file" value="${imagereview.fpm.dist.WebAPI.dir}/Web.config"/>
	<property name="imagereview.fpm.webapi.log.file" value="${imagereview.log.dir}/FPMLogs/FPMWebApi.log"/>
	
	<!-- HOCP API Config settings -->
	<property name="imagereview.hocp.webapi.config.file" value="${imagereview.hocp.dist.WebAPI.dir}/Web.config"/>
	<property name="imagereview.hocp.webapi.log.file" value="${imagereview.log.dir}/HOCPLogs/HOCPWebApi.log"/>
	
	<!-- PREHOCP API Config settings -->
	<property name="imagereview.prehocp.webapi.config.file" value="${imagereview.prehocp.dist.WebAPI.dir}/Web.config"/>
	<property name="imagereview.prehocp.webapi.log.file" value="${imagereview.log.dir}/PREHOCPLogs/PREHOCPWebApi.log"/>
	
	<!-- IRR API Config settings -->
	<property name="imagereview.irr.webapi.config.file" value="${imagereview.irr.dist.WebAPI.dir}/Web.config"/>
	<property name="imagereview.irr.webapi.log.file" value="${imagereview.log.dir}/IRRLogs/IRRWebApi.log"/>
	
	<!-- TRANS API Config settings -->
	<property name="imagereview.trans.webapi.config.file" value="${imagereview.trans.dist.WebAPI.dir}/Web.config"/>
	<property name="imagereview.trans.webapi.log.file" value="${imagereview.log.dir}/MIRLogs/MIRWebAPI.log"/>
	
	<!-- MIR WEb API Config settings -->
	<property name="imagereview.mir.webapi.config.file" value="${imagereview.mir.web.dist.WebAPI.dir}/Web.config"/>
	
	<!-- MIR web Config settings -->
	<property name="imagereview.mir.web.config.file" value="${imagereview.mir.web.dist.dir}/WebLayer/WebUI/Web.config"/>
	<!-- MIR web Config settings -->
	<property name="imagereview.mir.reports.web.config.file" value="${imagereview.mir.web.dist.dir}/WebLayer/ImageReviewReports/Web.config"/>
	
	<!-- Transactions web Config settings -->
	<property name="imagereview.mir.web.trans.config.file" value="${imagereview.mir.web.dist.dir}/WebLayer/ExternalTransactions/Web.config"/>
	
	<!-- MIR QFree Plugin Config settingss -->
	<property name="imagereview.mir.qfree.plugin.config.file" value="${imagereview.mir.main.dist.Plugin.dir}/Qfree/tcore.ImageReview.ImageClient.Plugins.Qfree.dll.config"/>

	
	<!-- Service Hosts -->
	<property name="imagereview.fpm.service.host.appconfig.file" value="${imagereview.fpm.dist.dir}\ServiceHost\Tcore.FPM.FPMInternalServiceHost.exe.config" />
	<property name="imagereview.fpp.service.host.appconfig.file" value="${imagereview.fpp.dist.dir}\ServiceHost\Tcore.FPP.InternalServiceHost.exe.config" />
	<property name="imagereview.fpp.service.client.appconfig.file" value="${imagereview.fpc.fpp.dist.dir}\ServiceClient\Tcore.FPP.InternalServiceClient.exe.config" />
	<property name="imagereview.hocp.service.host.appconfig.file" value="${imagereview.hocp.dist.dir}\ServiceHost\Tcore.HOCP.HOCPInternalServiceHost.exe.config" />
	<property name="imagereview.irr.service.host.appconfig.file" value="${imagereview.irr.dist.dir}\ServiceHost\Tcore.IRR.IRRInternalServiceHost.exe.config" />
	<property name="imagereview.trans.service.host.appconfig.file" value="${imagereview.trans.dist.dir}\ServiceHost\Tcore.MIR.MIRInternalServiceHost.exe.config" />
	<property name="imagereview.prehocp.service.host.appconfig.file" value="${imagereview.prehocp.dist.dir}\ServiceHost\Tcore.HOCP.HOCPInternalServiceHost.exe.config" />
	
	<!-- MIR Service Hosts -->
	<property name="imagereview.mir.service.host.healthcheck.appconfig.file" value="${imagereview.mir.dist.ServiceHost.dir}\HealthCheckServiceHost\tcore.ImageReview.HealthCheckServiceHost.exe.config" />
	
	<property name="imagereview.mir.service.host.ir.appconfig.file" value="${imagereview.mir.dist.ServiceHost.dir}\IRServiceHost\tcore.ImageReview.ImageReviewServiceHost.exe.config" />
	<!--<property name="imagereview.mir.service.host.licenseplatemanager.appconfig.file" value="${imagereview.mir.dist.ServiceHost.dir}\LicensePlateManagerHost\tcore.ImageReview.LicensePlateManagerHost.exe.config" />-->
	<property name="imagereview.mir.service.host.transactionqing.appconfig.file" value="${imagereview.mir.dist.ServiceHost.dir}\TransactionQingServiceHost\tcore.ImageReview.ImageTransactionQueueingService.exe.config" />
	
	<!-- Tasks -->
	<property name="imagereview.fpm.task.sendresults.config.file" value="${base.dist.dir}\FPMSendResultsTask\Tcore.FPM.FPSendResultsTask.exe.config" />
	<property name="imagereview.hocp.task.processor.config.file" value="${base.dist.dir}\HighOcrConfProcessor\Tcore.HOCP.HighOcrConfProcessor.exe.config" />
	<property name="imagereview.hocp.task.sendresults.config.file" value="${base.dist.dir}\HOCSendResultsProcessor\Tcore.HOCP.HOCSendResultsProcessor.exe.config" />
	<property name="imagereview.irr.task.sendresults.config.file" value="${base.dist.dir}\IRRSendResultsTask\Tcore.IRR.IRRSendResultsTask.exe.config" />
	<property name="imagereview.irr.task.sendresultsretries.config.file" value="${base.dist.dir}\IRRSendResultsTaskRetries\Tcore.IRR.IRRSendResultsTask.exe.config" />
	<property name="imagereview.mir.mrt.task.manualresults.config.file" value="${base.dist.dir}\ManualResultsTask\Tcore.MIR.ManualResultsTask.exe.config" />
	<property name="imagereview.mir.mrt.task.auditreviewresults.config.file" value="${base.dist.dir}\AuditReviewResultsTask\Tcore.MIR.AuditReviewResultsTask.exe.config" />
	<property name="imagereview.activedirectory.task.config.file" value="${base.dist.dir}\ActiveDirectoryPollingTask\tcore.ADP.ActiveDirectoryPolling.exe.config" />
	<property name="imagereview.fpp.task.fppmatchpooltask.config.file" value="${base.dist.dir}\FPPMatchPoolTask\Tcore.FPP.FPPMatchPoolTask.exe.config" />
	<property name="imagereview.mir.task.imageclient.retrievetrans.config.file" value="${base.dist.dir}\ImageClientServiceTask_RetrieveTransactions\tcore.ImageReview.ScheduledTasks.ImageClientServiceTask.exe.config" />
	<property name="imagereview.mir.task.imageclient.sendresponse.config.file" value="${base.dist.dir}\ImageClientServiceTask_SendResponse\tcore.ImageReview.ScheduledTasks.ImageClientServiceTask.exe.config" />
	<property name="imagereview.mir.task.vottqueuetask.config.file" value="${base.dist.dir}\VOTTQueueTask\tcore.ImageReview.ScheduledTasks.VOTTQueueTask.exe.config" />
	<property name="imagereview.mir.task.miremailnotificationtask.config.file" value="${base.dist.dir}\MIREmailNotificationTask\tcore.ImageReview.ScheduledTasks.MIREmailNotificationTask.exe.config" />
	<property name="imagereview.prehocp.task.processor.config.file" value="${base.dist.dir}\PreHighOcrConfProcessor\Tcore.HOCP.HighOcrConfProcessor.exe.config" />
	<property name="imagereview.prehocp.task.sendresults.config.file" value="${base.dist.dir}\PreHOCSendResultsProcessor\Tcore.HOCP.HOCSendResultsProcessor.exe.config" />
	
	<!-- exe config file for Workflow)-->
	<property name="imagereview.mir.worklow.policyruleengine.config.file" value="${imagereview.mir.web.dist.dir}/Workflows/PolicyRuleEngineMaterial/tcore.ImageReview.PolicyRuleEngineMaterial.exe.config"/>
	
	<!-- Reports deployer settings --> 
	<property name="reports.deployer.exec.file" value="${imagereview.rpt.dist.dir}\ReportsDeployer.exe" />	
	<property name="reports.deployer.appconfig.file" value="${imagereview.rpt.dist.dir}\ReportsDeployer.exe.config" />
	<property name="report.datasource.sso" value="data source=${imagereview.report.server};initial catalog=IPS" />	
	<property name="deploy.Reports.From.Dir" value="Reports" />	
	<property name="deploy.Reports.Reports.Path" value="${report.server.URL}/ImageReviewReports" />	
	<property name="deploy.Reports.Reports.Overwrite" value="false" />	
	<property name="deploy.Reports.DataSets.Overwrite" value="true" />	
	<property name="deploy.Reports.Remove.All.Reports" value="false" />	
	<property name="deploy.Reports.Shared.DataSource.FolderName" value="Data Sources" />	
	<property name="deploy.Reports.DataSet.DataSource.Path" value="/${deploy.Reports.Shared.DataSource.FolderName}/IPSLive" />	
	<property name="deploy.Reports.Shared.DataSet.FolderName" value="MIR DataSets" />	
	<property name="deploy.Reports.DataSource.Overwrite" value="false" />	
	<property name="deploy.Reports.Write.Exception.To.Console" value="true" />	
	
	<!--Connection Strings -->
	<!-- Database Connection Strings 
	<property name="ips.fingerprint.connection.string" value="server=${imagereview.db.server};Database=IPS.Fingerprint;uid=amsInternalAppUser;pwd=*******;Min Pool Size=1;Max Pool Size=100;Connect Timeout=120;" />
	<property name="fpm.connection.string" value="server=${imagereview.db.server};Database=FPM;uid=amsInternalAppUser;pwd=*******;Min Pool Size=1;Max Pool Size=100;Connect Timeout=120;" />
	<property name="hocp.connection.string" value="server=${imagereview.db.server};Database=IPS.HOC;uid=amsInternalAppUser;pwd=*******;Min Pool Size=1;Max Pool Size=100;Connect Timeout=120;" />
	<property name="irr.connection.string" value="server=${imagereview.db.server};Database=IPS.IRR;uid=amsInternalAppUser;pwd=*******;Min Pool Size=1;Max Pool Size=100;Connect Timeout=120;" />
	<property name="ips.connection.string" value="server=${imagereview.db.server};Database=IPS;uid=amsInternalAppUser;pwd=*******;Min Pool Size=1;Max Pool Size=100;Connect Timeout=120;" />-->
	
	<!-- Database Connection Strings -->
	<property name="ips.fingerprint.connection.string" value="server=${imagereview.fingerprint.db.listener};Database=IPS.Fingerprint;Integrated Security=SSPI;Min Pool Size=1;Max Pool Size=100;Connect Timeout=120;" />
	<property name="fpm.connection.string" value="server=${imagereview.fpm.db.listener};Database=FPM;Integrated Security=SSPI;Min Pool Size=1;Max Pool Size=100;Connect Timeout=120;" />
	<property name="hocp.connection.string" value="server=${imagereview.hocp.db.listener};Database=IPS.HOC;Integrated Security=SSPI;Min Pool Size=1;Max Pool Size=100;Connect Timeout=120;" />
	<property name="irr.connection.string" value="server=${imagereview.irr.db.listener};Database=IPS.IRR;Integrated Security=SSPI;Min Pool Size=1;Max Pool Size=100;Connect Timeout=120;" />
	<property name="ips.connection.string" value="server=${imagereview.ips.db.listener};Database=IPS;Integrated Security=SSPI;Min Pool Size=1;Max Pool Size=100;Connect Timeout=120;" />
	<property name="ips.trans.connection.string" value="server=${imagereview.trans.db.listener};Database=IPS_Transactions;Integrated Security=SSPI;trustservercertificate=True;MultipleActiveResultSets=True;App=EntityFramework" />
	<property name="rpt.connection.string" value="server=${imagereview.rpt.db.listener};Database=IPS;Integrated Security=SSPI;Min Pool Size=1;Max Pool Size=100;Connect Timeout=120;" />
	<property name="prehocp.connection.string" value="server=${imagereview.prehocp.db.listener};Database=IPS.PREHOC;Integrated Security=SSPI;Min Pool Size=1;Max Pool Size=100;Connect Timeout=120;" />
	
	<property name="iisserver" value="" />
	
		<!--Devops -->
	<property name="devops.dir"		value="${base.dir}\Source" />
	<property name="devops.repo" value="https://infinity-tcore.visualstudio.com/SD-MIR/_git/MIR" />
	<property name="devops.common.repo" value="https://infinity-tcore.visualstudio.com/SD-Tools/_git/SD-Common" />
	
	
</project>