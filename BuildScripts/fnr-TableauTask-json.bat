@echo off
rem set variables
Set CURRENTDIR=%CD%
Set SOURCE=%1
Set PARENTFOLDER=%2
Set CONTENTURLVAL=%3
set scripts-dir=%CD%

echo parentfolder is %PARENTFOLDER%
echo %SOURCE%
echo %CONTENTURLVAL%

"%CURRENTDIR%\fnr.exe" --cl --dir "%SOURCE%"  --fileMask "appsettings.json" --includeSubDirectories --excludeFileMask "*.dll, *.exe" --includeSubDirectories --find "TableauContentUrlValue" --replace "%CONTENTURLVAL%"
if "%PARENTFOLDER%" == "none" GOTO :NOFOLDER
"%CURRENTDIR%\fnr.exe" --cl --dir "%SOURCE%"  --fileMask "appsettings.json" --includeSubDirectories  --excludeFileMask "*.dll, *.exe" --includeSubDirectories --find "TableauTopLevelValue" --replace "%PARENTFOLDER%%"
GOTO END
:NOFOLDER
"%CURRENTDIR%\fnr.exe" --cl --dir "%SOURCE%"  --fileMask "appsettings.json"  --includeSubDirectories --find "TableauTopLevelValue" --replace ""
:END