@echo off
rem set variables
Set CURRENTDIR=%CD%

echo ------- Your current directory is:  %CURRENTDIR% ---------

set NOW=%date:~10,4%-%date:~4,2%-%date:~7,2%

SET BASEDIR=%1
SET DEPLOYSERVER=%2
SET USER=%3
SET PASSWORD=%4
SET DEPLOYMENTDRIVE=%5
SET DEPLOYMENTDIR=%6
SET CREATEDB=%7

echo %DEPLOYMENTDIR%

:zipfiles
echo -----------------------------------
echo Zip Distribution files
echo -----------------------------------

del /s %BASEDIR%\*.zip
set str=%BASEDIR%
echo.%str%
set str=%str:~0,2%
echo.%str% 
%str%
cd %BASEDIR%\

SET STEP="Zip Distribution"

if exist "%BASEDIR%\InsightMobile" (
%CURRENTDIR%\zip -r -q InsightMobile.zip InsightMobile\*
)

%CURRENTDIR%\zip -r -q Distribution.zip Distribution\*
if %ERRORLEVEL% NEQ 0 GOTO :error 

	

for /F %%i in ('dir /b "%BASEDIR%\Distribution\MOMS\ServiceHost\tcore.MOMSResources.dll"') do (
	cd %BASEDIR%\Distribution\MOMS\ServiceHost
)

for /F %%i in ('dir /b "%BASEDIR%\Distribution\MOMS\CommonLayer\tcore.MOMSResources.dll"') do (
	cd %BASEDIR%\Distribution\MOMS\CommonLayer
)


SET STEP="Zip Resources"
%CURRENTDIR%\zip -r %BASEDIR%\Resources.zip tcore.MOMSResources.dll fr\* es\* ar\*  
if %ERRORLEVEL% NEQ 0 GOTO :error 
cd %BASEDIR%\Source\INS\MOMS\MOMSReports\


SET STEP="Zip MOMSReports"
%CURRENTDIR%\zip -r %BASEDIR%\MOMSReports.zip MOMSReports\*  
if %ERRORLEVEL% NEQ 0 GOTO :error 

cd %BASEDIR%\Source\INS\MOMS
%CURRENTDIR%\zip -r %BASEDIR%\DBScripts.zip LocalDBScripts\*   
if %ERRORLEVEL% NEQ 0 GOTO :error 

cd %BASEDIR%\Source\INS\MOMS
%CURRENTDIR%\zip -r %BASEDIR%\MOMSDatabase.zip Database\*  
if %ERRORLEVEL% NEQ 0 GOTO :error

cd %CURRENTDIR%
%CURRENTDIR%\zip -r %BASEDIR%\ScheduledTasks.zip ScheduledTasks\*
 
SET STEP="Zip Build Scripts"


cd %CURRENTDIR%

%CURRENTDIR%\zip -r %BASEDIR%\AES.zip AES\*

if exist %CURRENTDIR%\BuildScripts  (del /s /q BuildScripts\*
)
if exist %CURRENTDIR%\BuildScripts  (rmdir /s /q BuildScripts
)

mkdir BuildScripts
mkdir BuildScripts\Autobuild
mkdir BuildScripts\AppLogs
mkdir BuildScripts\AES

xcopy *.cmd BuildScripts\*
xcopy *.bat BuildScripts\*
xcopy *.dll BuildScripts\*
xcopy *.build BuildScripts\*
xcopy *.include BuildScripts\*
xcopy Autobuild\*.* BuildScripts\Autobuild\*
xcopy AES\*.* BuildScripts\AES\*

del /s /q BuildScripts\Autobuild.bat
del /s /q BuildScripts\AutobuildStart.bat
del /s /q BuildScripts\ReportsDeployer.exe

for /F %%i in ('dir /b "%BASEDIR%\BuildScripts.zip"') do (
	del /s /q BuildsScripts.zip
)

%CURRENTDIR%\zip -r %BASEDIR%\BuildScripts.zip BuildScripts\* 
del /s /q BuildScripts\*
rmdir /s /q BuildScripts

echo ZIP DONE
rem Added for KCScout
GOTO :COMPLETED
SET STEP="Start Connection"

echo -----------------------------------
echo Connect to Server
echo -----------------------------------

echo Deployment beginning %DEPLOYMENTDIR%

net use x: /delete

echo ------- map drive ----------
net use x: \\%DEPLOYSERVER%\%DEPLOYMENTDRIVE%$ %PASSWORD% /user:%USER%
if %ERRORLEVEL% NEQ 0 GOTO :error 
echo ------- destination cleanup -----------
rem if exist x:\Staging\%DEPLOYMENTDIR%\nul goto deldir
SET STEP="delete dir if exist"
rem psexec -accepteula -h -u %USER% -p %PASSWORD%  \\%DEPLOYSERVER% cmd /c if exist %DEPLOYMENTDRIVE%:\Staging-Insight\%DEPLOYMENTDIR%\ (rmdir /s /q %DEPLOYMENTDRIVE%:\Staging-Insight\%DEPLOYMENTDIR%)
if exist x:\Staging-Insight\%DEPLOYMENTDIR% (rmdir /s /q x:\Staging-Insight\%DEPLOYMENTDIR%)
if %ERRORLEVEL% NEQ 0 GOTO :error 
echo ------- Create dated deployment directory -----------------
echo create dated deployment directory
SET STEP="create deploymentdir"
echo create directory %DEPLYOMENTDIR%
mkdir x:\Staging-Insight\%DEPLOYMENTDIR%
rem echo psexec -accepteula -h -u %USER% -p %PASSWORD% \\%DEPLOYSERVER% cmd /c  "mkdir %DEPLOYMENTDRIVE%:\Staging-Insight\%DEPLOYMENTDIR%"
rem psexec -accepteula -h -u %USER% -p %PASSWORD% \\%DEPLOYSERVER% cmd /c  "mkdir %DEPLOYMENTDRIVE%:\Staging-Insight\%DEPLOYMENTDIR%"
echo successfully created deployment directory
if %ERRORLEVEL% NEQ 0 GOTO :error 

:deploy
echo -----------------------------------
echo Deploy Build Scripts zip file to MOMS Server....
echo -----------------------------------
SET STEP="Deploy BuildScripts.zip"
if exist x:\Staging-Insight\BuildScripts.Zip (del /s /q x:\Staging-Insight\BuildScripts.zip)
if %ERRORLEVEL% NEQ 0 GOTO :error 
echo remove build scripts
if exist x:\Staging-Insight\BuildScripts (del /s /q x:\Staging-Insight\BuildScripts\*)
echo remove build scripts dir complete
if %ERRORLEVEL% NEQ 0 GOTO :error 
echo copy build scripts zip
robocopy /R:3 "\AppBuild-MOMS" "x:\Staging-Insight" BuildScripts.zip
rem xcopy /R "%BASEDIR%\BuildScripts.zip" "x:\Staging-Insight\*" 
echo copy build scripts zip done
echo error level is %ERRORLEVEL%
if %ERRORLEVEL% GEQ 8 GOTO :error 
echo unzip build scripts
unzip -ou x:\Staging-Insight\BuildScripts.zip -d  x:\Staging-Insight\
rem psexec -accepteula \\%DEPLOYSERVER% cmd /c (unzip -q %DEPLOYMENTDRIVE%:\Staging-Insight\BuildScripts.zip -d %DEPLOYMENTDRIVE%:\Staging-Insight\)
if %ERRORLEVEL% NEQ 0 GOTO :error 
echo unzip complete
echo %STEP% Deployment complete 


echo -----------------------------------
echo Deploy Distribution zip file to MOMS Server....
echo -----------------------------------
SET STEP="Deploy Distribution.zip"
rem ROBOCOPY %BASEDIR% x:\Staging-Insight\%NOW% Distribution.zip > DEPLOY.log
echo start xcopy
rem echo xcopy /Z "%BASEDIR%\Distribution.zip" "x:\Staging-Insight\%DEPLOYMENTDIR%\*"
xcopy /Z "%BASEDIR%\Distribution.zip" "x:\Staging-Insight\%DEPLOYMENTDIR%\*" 
echo stop xcopy
rem ErrorLevel is set at %ERRORLEVEL%
if %ERRORLEVEL% NEQ 0 GOTO :error 
if exist "%BASEDIR%\InsightMobile.zip" (xcopy /Z "%BASEDIR%\InsightMobile.zip" "x:\Staging-Insight\%DEPLOYMENTDIR%\*"  )
echo stop xcopy
rem ErrorLevel is set at %ERRORLEVEL%
if %ERRORLEVEL% NEQ 0 GOTO :error 
echo %STEP% Deployment complete 


echo -----------------------------------
echo Deploy resources to the report server....
echo -----------------------------------
SET STEP="Deploy Resources.zip"
xcopy %BASEDIR%\Resources.zip x:\Staging-Insight\%DEPLOYMENTDIR%\*  
if %ERRORLEVEL% NEQ 0 GOTO :error
echo %STEP% Deployment complete 

echo -----------------------------------
echo Copy reports to the report server....
echo -----------------------------------
SET STEP="Deploy MOMSReports.zip"
xcopy %BASEDIR%\MOMSReports.zip x:\Staging-Insight\%DEPLOYMENTDIR% 
if %ERRORLEVEL% NEQ 0 GOTO :error 
echo %STEP% Deployment complete 


echo ------------------------------------
echo Copy Scheduled Tasks creation scripts
echo ------------------------------------
set STEP="Deploy Schedule Tasks Scripts"

echo xcopy %BASEDIR%\ScheduledTasks.zip x:\Staging-Insight\%DEPLOYMENTDIR%
xcopy %BASEDIR%\ScheduledTasks.zip x:\Staging-Insight\%DEPLOYMENTDIR%
if %ERRORLEVEL% NEQ 0 GOTO :error 
echo %STEP% Deployment complete 


cd %CURRENTDIR%

if "%CREATEDB%"=="N" GOTO :COPYREPORTFILES
:COPYDBFILES
set STEP="Deploy DB Files"
SET STEP="Zip DBScripts"

echo ------------------------------------
echo Copy db scripts to server for DB creation
echo ------------------------------------
set STEP="Deploy DB Creation Scripts"
xcopy %BASEDIR%\DBScripts.zip x:\Staging-Insight\%DEPLOYMENTDIR% 
if %ERRORLEVEL% NEQ 0 GOTO :error 
echo %STEP% Deployment complete 

echo ------------------------------------
echo Copy db source to server for DB creation
echo ------------------------------------
set STEP="Deploy DB Creation Scripts"
xcopy %BASEDIR%\MOMSDatabase.zip x:\Staging-Insight\%DEPLOYMENTDIR% 
if %ERRORLEVEL% NEQ 0 GOTO :error 
echo %STEP% Deployment complete 


:COPYREPORTFILES
echo ------------------------
echo copy report files
echo -----------------------
if exist x:\Staging-Insight\%DEPLOYMENTDIR%\AESHelper.exe del /Q x:\Staging-Insight\%DEPLOYMENTDIR%\AESHelper.exe
if exist x:\Staging-Insight\%DEPLOYMENTDIR%\AESHelper.exe.config del /Q x:\Staging-Insight\%DEPLOYMENTDIR%\AESHelper.exe.config
xcopy /Y %BASEDIR%\ReportsDeployer.exe x:\Staging-Insight\%DEPLOYMENTDIR%\*
xcopy /Y %BASEDIR%\ReportsDeployer.exe.config x:\Staging-Insight\%DEPLOYMENTDIR%\*


CD %CURRENTDIR%

rem Added for KCScout
:COMPLETED
echo --------------------------------------------------------------------
echo Deployment Completed...
echo --------------------------------------------------------------------
echo DEPLOYMENT COMPLETE 
GOTO END


:error
echo ------- AN ERROR OCCURED DURING THIS STEP: %STEP% ------
exit /b 1

:END