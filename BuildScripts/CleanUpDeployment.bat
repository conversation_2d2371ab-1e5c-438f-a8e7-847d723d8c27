@echo off
rem set variables
Set CURRENTDIR=%CD%

echo ------- Your current directory is:  %CURRENTDIR% ---------

set NOW=%date:~10,4%-%date:~4,2%-%date:~7,2%
echo Deployment Cleanup beginning %NOW% 
echo -----------------------------------
echo Connect to Servers for File Cleanup
echo -----------------------------------

SET APPSERVER=%1
SET DEPLOYMENTDRIVE=%2
SET USER=%3
SET PASSWORD=%4

net use x: /delete



echo ------- map drive ----------
net use x: \\%APPSERVER%\%DEPLOYMENTDRIVE%$ %PASSWORD% /user:%USER%
if %ERRORLEVEL% NEQ 0 GOTO :error 
echo ------- destination cleanup -----------
Set STEP="destination cleanup"
if exist x:\Deployment (del /s /q x:\Deployment\*.config) 
if exist x:\Deployment (del /s /q x:\Deployment\*.include) 
if exist x:\Deployment (del /s /q x:\Deployment\*.zip) 
if exist x:\Deployment (del /s /q x:\Deployment\*.sql) 
if %ERRORLEVEL% NEQ 0 GOTO :error 

:Finalize
echo remove all includes from staging if staging exists on the APP server.
if exist x:\Staging-Insight (del /s /q x:\Staging-Insight\*.include) 
del /s /q %CURRENTDIR%\*.include 


echo %STEP% Deployment complete 
GOTO END


:error
echo ------- AN ERROR OCCURED DURING THIS STEP: %STEP% ------
exit /b 1

:END