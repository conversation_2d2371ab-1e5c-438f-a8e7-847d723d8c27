@echo off
rem set variables
Set CURRENTDIR=%CD%

echo ------- Your current directory is:  %CURRENTDIR% ---------

set NOW=%date:~10,4%-%date:~4,2%-%date:~7,2%
echo Deployment Cleanup beginning %NOW%
echo -----------------------------------
echo Connect to Servers for File Cleanup
echo -----------------------------------

SET APPSERVER=%1
SET WEBSERVER=%2
SET DEPLOYMENTDRIVE=%3
SET USER=%4
SET PASSWORD=%5

net use t: /delete
net use u: /delete




echo ------- map drive ----------
net use t: \\%APPSERVER%\%DEPLOYMENTDRIVE%$ %PASSWORD% /user:%USER%
net use u: \\%WEBSERVER%\%DEPLOYMENTDRIVE%$ %PASSWORD% /user:%USER%

if %ERRORLEVEL% NEQ 0 GOTO :error 
echo ------- destination cleanup -----------
Set STEP="destination cleanup"
rem if exist t:\Deployment (del /s /q t:\Deployment-CPS\*.config) >> %CURRENTDIR%\AppLogs\Cleanup.log
if exist t:\Deployment (del /s /q t:\Deployment-CPS\*.include) 
if exist t:\Deployment (del /s /q t:\Deployment-CPS\*.zip)
rem if exist t:\Deployment (del /s /q t:\Deployment-CPS\*.sql) >> %CURRENTDIR%\AppLogs\Cleanup.log
rem if exist u:\Deployment (del /s /q u:\Deployment-CPS\*.config) >> %CURRENTDIR%\AppLogs\Cleanup.log
if exist u:\Deployment (del /s /q u:\Deployment-CPS\*.zip) 
if %ERRORLEVEL% NEQ 0 GOTO :error 


:Finalize
echo remove all includes from staging if staging exists on the APP server.
if exist t:\Staging-CPS (del /s /q t:\Staging-CPS\*.include) 
net use t: /delete
net use u: /delete

echo %STEP% Post installation cleanup complete 
GOTO END


:error
echo ------- AN ERROR OCCURED DURING THIS STEP: %STEP% ------
exit /b 1

:END