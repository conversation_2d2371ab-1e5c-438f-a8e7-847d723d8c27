@echo off
rem set variables
Set CURRENTDIR=%CD%

echo ------- Your current directory is:  %CURRENTDIR% ---------

set NOW=%date:~10,4%-%date:~4,2%-%date:~7,2%
echo Deployment Cleanup beginning %NOW%
echo -----------------------------------
echo Connect to Servers for File Cleanup
echo -----------------------------------

SET APPSERVER=%1
SET WEBSERVER=%2
SET FPMSERVER=%3
SET DEPLOYMENTDRIVE=%4
SET USER=%5
SET PASSWORD=%6
SET APP02SERVER=%7
SET WEB02SERVER=%8
SET FPM02SERVER=%9
SHIFT
SHIFT
SHIFT
SHIFT
SHIFT
SHIFT
SHIFT
SHIFT
SHIFT
SET CLUSTERFEATURE=%1
SET FINGERPRINTFEATURE=%2
SET IRRFEATURE=%3



if %ERRORLEVEL% NEQ 0 GOTO :error 
echo ------- destination cleanup -----------
Set STEP="destination cleanup"
if exist \\%APPSERVER%\%DEPLOYMENTDRIVE%$\Deployment-ImageReview (del /s /q \\%APPSERVER%\%DEPLOYMENTDRIVE%$\Deployment-ImageReview\*.config) 
if exist \\%APPSERVER%\%DEPLOYMENTDRIVE%$\Deployment-ImageReview (del /s /q \\%APPSERVER%\%DEPLOYMENTDRIVE%$\Deployment-ImageReview\*.include) 
if exist \\%APPSERVER%\%DEPLOYMENTDRIVE%$\Deployment-ImageReview (del /s /q \\%APPSERVER%\%DEPLOYMENTDRIVE%$\Deployment-ImageReview\*.zip) 
if exist \\%APPSERVER%\%DEPLOYMENTDRIVE%$\Deployment-ImageReview (del /s /q \\%APPSERVER%\%DEPLOYMENTDRIVE%$\Deployment-ImageReview\*.sql) 
if exist \\%WEBSERVER%\%DEPLOYMENTDRIVE%$\Deployment-ImageReview (del /s /q \\%WEBSERVER%\%DEPLOYMENTDRIVE%$\Deployment-ImageReview\*.config) 
if exist \\%WEBSERVER%\%DEPLOYMENTDRIVE%$\Deployment-ImageReview (del /s /q \\%WEBSERVER%\%DEPLOYMENTDRIVE%$\Deployment-ImageReview\*.zip) 
if %ERRORLEVEL% NEQ 0 GOTO :error 


if "%FINGERPRINTFEATURE%"=="false" GOTO CheckCluster

:DOFPM
echo --------------------------------
echo Clean up FPM files
echo --------------------------------
if %ERRORLEVEL% NEQ 0 GOTO :error 

if exist \\%FPMSERVER%\%DEPLOYMENTDRIVE%$\Deployment-ImageReview (del /s /q \\%FPMSERVER%\%DEPLOYMENTDRIVE%$\Deployment-ImageReview\*.config) 
if exist \\%FPMSERVER%\%DEPLOYMENTDRIVE%$\Deployment-ImageReview (del /s /q \\%FPMSERVER%\%DEPLOYMENTDRIVE%$\Deployment-ImageReview\*.zip) 
if %ERRORLEVEL% NEQ 0 GOTO :error 


:CheckCluster
if "%CLUSTERFEATURE%"=="true" GOTO DOCLUSTER
GOTO :Finalize


:DOCLUSTER

if %ERRORLEVEL% NEQ 0 GOTO :error 
echo ------- destination cleanup -----------
Set STEP="destination cleanup"
if exist \\%APP02SERVER%\%DEPLOYMENTDRIVE%$\Deployment-ImageReview (del /s /q \\%APP02SERVER%\%DEPLOYMENTDRIVE%$\Deployment-ImageReview\*.config) 
if exist \\%APP02SERVER%\%DEPLOYMENTDRIVE%$\Deployment-ImageReview (del /s /q \\%APP02SERVER%\%DEPLOYMENTDRIVE%$\Deployment-ImageReview\*.include) 
if exist \\%APP02SERVER%\%DEPLOYMENTDRIVE%$\Deployment-ImageReview (del /s /q \\%APP02SERVER%\%DEPLOYMENTDRIVE%$\Deployment-ImageReview\*.zip) 
if exist \\%APP02SERVER%\%DEPLOYMENTDRIVE%$\Deployment-ImageReview (del /s /q \\%APP02SERVER%\%DEPLOYMENTDRIVE%$\Deployment-ImageReview\*.sql) 
if exist \\%WEB02SERVER%\%DEPLOYMENTDRIVE%$\Deployment-ImageReview (del /s /q \\%WEB02SERVER%\%DEPLOYMENTDRIVE%$\Deployment-ImageReview\*.config) 
if exist \\%WEB02SERVER%\%DEPLOYMENTDRIVE%$\Deployment-ImageReview (del /s /q \\%WEB02SERVER%\%DEPLOYMENTDRIVE%$\Deployment-ImageReview\*.zip) 
if %ERRORLEVEL% NEQ 0 GOTO :error 


if "%FINGERPRINTFEATURE%"=="false" GOTO Finalize

:DOFPM02
echo --------------------------------
echo Clean up FPM02 files
echo --------------------------------
if %ERRORLEVEL% NEQ 0 GOTO :error 


if exist \\%FPM02SERVER%\%DEPLOYMENTDRIVE%$\Deployment-ImageReview (del /s /q \\%FPM02SERVER%\%DEPLOYMENTDRIVE%$\Deployment-ImageReview\*.config) 
if exist \\%FPM02SERVER%\%DEPLOYMENTDRIVE%$\Deployment-ImageReview (del /s /q \\%FPM02SERVER%\%DEPLOYMENTDRIVE%$\Deployment-ImageReview\*.zip) 
if %ERRORLEVEL% NEQ 0 GOTO :error 


:Finalize
echo remove all includes from staging if staging exists on the APP server.
if exist \\%APPSERVER%\%DEPLOYMENTDRIVE%$\Staging-IFX (del /s /q \\%APPSERVER%\%DEPLOYMENTDRIVE%$\Staging-ImageReview\*.include) 
del /s /q %CURRENTDIR%\*.include 

echo %STEP% Deployment complete 
GOTO END


:error
echo ------- AN ERROR OCCURED DURING THIS STEP: %STEP% ------
exit /b 1

:END