@ECHO OFF

set NOW=%date:~10,4%-%date:~4,2%-%date:~7,2%
Set CURRENTDIR=%CD%
SET DBSERVER=%1
SET PROJECT=%2
SET ENVTYPE=%3
SET DEPLOYMENTDRIVE=%4
SET DEPLOYMENTDIR=%5
SET SQLDRIVE=%6
SET SQLDIR=%7
SET RPTSERVER=%8

echo %RPTSERVER%

echo MOMS/Insight Reports Deployer is starting at %DEPLOYMENTDIR%
echo ---------------------------------- 


verify > nul
:DeployReports
SET STEP="Deploy Reports"
echo Step %STEP% commencing...  --------- 
rem call Autobuild\do_invoke_any_locrem1.cmd %RPTSERVER% %DEPLOYMENTDRIVE%:\Deployment\%DEPLOYMENTDIR% "ReportsDeployer.exe" %DEPLOYMENTDRIVE%:\Deployment\%DEPLOYMENTDIR%\ReportsDeployer.exe unused unused unused unused
psexec -accepteula \\%RPTSERVER% -w %DEPLOYMENTDRIVE%:\Deployment\%DEPLOYMENTDIR% cmd /c %DEPLOYMENTDRIVE%:\Deployment\%DEPLOYMENTDIR%\ReportsDeployer.exe
if %ERRORLEVEL% NEQ 0 GOTO :error
echo Step %STEP% completed...  --------- 

GOTO END


:error
echo ------- AN ERROR OCCURED DURING THIS STEP: %STEP% ------
echo ------- AN ERROR OCCURED DURING THIS STEP: %STEP% ------ 
rem NAnt.exe -buildfile:MOMS_build.build -logger:NAnt.Core.MailLogger -logfile:AppLogs\build.log autobuild-error
exit /b 1

:END
echo -- Install of MOMS/INSIGHT is complete, wait for DBA to run last step
echo -- Install of MOMS/INSIGHT is complete 
rem NAnt.exe -buildfile:MOMS_build.build -logger:NAnt.Core.MailLogger -logfile:AppLogs\build.log autobuild-complete
exit /b 0


