@ECHO OFF

set NOW=%date:~10,4%-%date:~4,2%-%date:~7,2%
Set CURRENTDIR=%CD%
SET RPTSERVER=%1
SET PROJECT=%2
SET ENVTYPE=%3
SET DEPLOYMENTDRIVE=%4
SET DEPLOYMENTDIR=%5
SET SQLDRIVE=%6
SET SQLDIR=%7
SET RESOURCESPATH=%8
set fullstamp=%YYYY%-%MM%-%DD%_%HH%-%Min%-%Sec%


echo %TRANSPORTALSERVER%



echo IMage Review Reports Deployment is starting at %fullstamp%
echo ---------------------------------- 

VERIFY > nul



:DeployReports
SET STEP="Deploy Reports"
echo Step %STEP% commencing...  --------- 
psexec -accepteula \\%RPTSERVER% -w %DEPLOYMENTDRIVE%:\Deployment-ImageReview\%DEPLOYMENTDIR%\RPT01 cmd /c %DEPLOYMENTDRIVE%:\Deployment-ImageReview\%DEPLOYMENTDIR%\RPT01\ReportsDeployer.exe
if %ERRORLEVEL% NEQ 0 GOTO :error
echo Step %STEP% completed...  --------- 


GOTO END


:error
echo ------- AN ERROR OCCURED DURING THIS STEP: %STEP% ------
echo ------- AN ERROR OCCURED DURING THIS STEP: %STEP% ------ 
rem NAnt.exe -buildfile:ImageReview_build.build -logger:NAnt.Core.MailLogger -logfile:AppLogs\build.log autobuild-error
exit /b 1

:END
echo -- Install of Image Review Reports is complete, wait for DBA to run last step
echo -- Install of Image Review Reportsis complete 
rem NAnt.exe -buildfile:ImageReview_build.build -logger:NAnt.Core.MailLogger -logfile:AppLogs\build.log autobuild-complete
exit /b 0


