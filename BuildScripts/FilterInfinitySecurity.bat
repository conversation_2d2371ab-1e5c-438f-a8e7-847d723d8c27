@echo off
rem set variables
Set CURRENTDIR=%CD%

%CURRENTDIR%\fnr.exe --cl --dir "%1\Distribution\MOMSInterfaces\InfinityMOMSService\ServiceHost"  --fileMask "tcore.Infinity.InfinityMOMSServiceHost.exe.config" --excludeFileMask "*.dll, *.exe" --includeSubDirectories --find "<security mode=""Message"">" --replace "<security mode=""None"">"
%CURRENTDIR%\fnr.exe --cl --dir "%1\Distribution\MOMSInterfaces\InfinityMOMSService\ServiceHost"  --fileMask "tcore.Infinity.InfinityMOMSServiceHost.exe.config" --excludeFileMask "*.dll, *.exe" --includeSubDirectories --find "<security mode=""Transport"">\n            <transport clientCredentialType=""Windows"" protectionLevel=""EncryptAndSign"" />\n            <message clientCredentialType=""Windows"" />" --replace "<security mode=""None"">"

GOTO END
:END