@ECHO OFF
set /P InsightServer=Enter the Insight server (ex. SDV-INSDEV01):=
set /P InsightMobileServer=Enter the Insight Mobile server:=


rem Insight Server
echo change permissions on the Insight Server MOMS Folder
if exist \\%InsightServer%\C$\MOMS (cacls \\%InsightServer%\C$\MOMS /t /e /g Users:f)

rem echo change permissions on the Insight Server MOMSInsight Folder
rem if exist \\%InsightServer%\C$\MOMSInsight (cacls \\%InsightServer%\C$\MOMSInsight /t /e /g Users:f)

echo change permissions on the Insight Server MOMSInterfaces Folder
if exist \\%InsightServer%\C$\MOMSInterfaces (cacls \\%InsightServer%\C$\MOMSInterfaces /t /e /g Users:f)

echo change permissions on the Insight Server Insight Folder
if exist \\%InsightServer%\C$\Insight (cacls \\%InsightServer%\C$\Insight /t /e /g Users:f)



echo change permissions on the Insight Server MOMS_bu Folder
if exist \\%InsightServer%\C$\MOMS_bu (cacls \\%InsightServer%\C$\MOMS_bu /t /e /g Users:f)

rem echo change permissions on the Insight Server MOMSInsight_bu Folder
rem if exist \\%InsightServer%\C$\MOMSInsight_bu (cacls \\%InsightServer%\C$\MOMSInsight_bu /t /e /g Users:f)

echo change permissions on the Insight Server MOMSInterfaces_bu Folder
if exist \\%InsightServer%\C$\MOMSInterfaces_bu (cacls \\%InsightServer%\C$\MOMSInterfaces_bu /t /e /g Users:f)

echo change permissions on the Insight Server Insight_bu Folder
if exist \\%InsightServer%\C$\Insight_bu (cacls \\%InsightServer%\C$\Insight_bu /t /e /g Users:f)



rem Insight Mobile Server
echo change permissions on the Insight Mobile Server InsightMobile Folder
if exist \\%InsightMobileServer%\C$\InsightMobile (cacls \\%InsightMobileServer%\C$\InsightMobile /t /e /g Users:f)

echo change permissions on the Insight Mobile Server InsightMobile_bu Folder
if exist \\%InsightMobileServer%\C$\InsightMobile_bu (cacls \\%InsightMobileServer%\C$\InsightMobile_bu /t /e /g Users:f)


echo done
goto :EOF