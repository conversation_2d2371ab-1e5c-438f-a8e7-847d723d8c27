@ECHO OFF

set NOW=%date:~10,4%-%date:~4,2%-%date:~7,2%
Set CURRENTDIR=%CD%
SET SVNBRANCH=%1
SET DESTINATIONDIR=%2
SET SVNPASSWORD=%3
SET REVISION=%4
SET SVNUSER=%5

echo %MOMSSERVER%
echo %TRANSPORTALSERVER%


:CHECKOUT
SET STEP="In Autobuild checkout now..."
echo In Autobuild checkout now..
echo Step %STEP% commencing...  --------- 
rem call svn checkout %SVNBRANCH% %DESTINATIONDIR% --no-auth-cache --password=%SVNPASSWORD% --quiet --non-interactive --trust-server-cert --revision=%REVISION% --username=%SVNUSER%
call svn export --username %SVNUSER% --password=%SVNPASSWORD% -r %REVISION% %SVNBRANCH% %DESTINATIONDIR% --non-interactive --force --trust-server-cert

if %ERRORLEVEL% NEQ 0 GOTO :error 
echo Step deploy completed..  --------- 
GOTO END


:error
echo ------- AN ERROR OCCURED DURING THIS STEP: %STEP% ------
echo ------- AN ERROR OCCURED DURING THIS STEP: %STEP% ------ 
rem NAnt.exe -buildfile:MOMS_build.build -logger:NAnt.Core.MailLogger -logfile:AppLogs\build.log autobuild-error
exit /b 1

:END
echo -- AUTOBUILD  Checkout of MOMS/INSIGHT/TRANSPORTAL is complete
echo -- AUTOBUILD Checkout of MOMS/INSIGHT/TRANSPORTAL is complete 
rem NAnt.exe -buildfile:MOMS_build.build -logger:NAnt.Core.MailLogger -logfile:AppLogs\build.log autobuild-complete
exit /b 0


