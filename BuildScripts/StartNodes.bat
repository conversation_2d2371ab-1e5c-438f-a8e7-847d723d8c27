@echo off
rem set variables
Set CURRENTDIR=%CD%

echo ------- Your current directory is:  %CURRENTDIR% ---------

set NOW=%date:~10,4%-%date:~4,2%-%date:~7,2%
echo Deployment beginning %NOW%
echo -----------------------------------
echo Connect to Server
echo -----------------------------------
verify > nul
SET SERVER=%1
SET TYPE=%2
SET PORT=%3

ECHO %PORT%
If "%PORT%" == "none" set PORT=
echo port = %PORT%

rem call Autobuild\do_service_start_locrem1.cmd "Windows Process Activation Service" %SERVER%
rem call Autobuild\do_service_start_locrem1.cmd "World Wide Web Publishing Service" %SERVER%
rem if %ERRORLEVEL% NEQ 0 GOTO :error 
verify > nul
psexec -accepteula \\%SERVER% cmd /c  powershell -Command "& {Start-IISSite -Name "IMS%PORT%Api"}"
Set OK=false
IF "%ERRORLEVEL%"=="0" set OK=true
IF  "%ERRORLEVEL%"=="1062" set OK=true
IF "%OK%"=="false" GOTO :error

psexec -accepteula \\%SERVER% cmd  /c (C:\Windows\System32\inetsrv\appcmd start apppool /apppool.name:"IMS%PORT%Api" ) 
Set OK=false
IF "%ERRORLEVEL%"=="0" set OK=true
IF  "%ERRORLEVEL%"=="1062" set OK=true
IF "%OK%"=="false" GOTO :error
GOTO END

:error
echo ------- AN ERROR OCCURED DURING THIS STEP: %STEP% %SERVER% %TYPE% ------

exit /b 1

:END
echo -- Start APIS of Nodes %SERVER% %TYPE% is complete
exit /b 0