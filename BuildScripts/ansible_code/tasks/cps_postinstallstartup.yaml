- name: Notify task start
  debug:
    msg: "CPS Post-Installation Startup is starting at {{ timestamp }}"
  run_once: true
  delegate_to: localhost
  ignore_errors: true
  tags:
    - cps

# Start DMVService on primary ext server
- name: Start DMVService on ext01
  ansible.windows.win_service:
    name: DMVService
    state: started
  when: dmv_worker_feature
  delegate_to: "{{ ext01_server }}"
  async: "{{ service_timeout }}"
  poll: 5
  tags:
    - cps

# Start TransComService on primary app server
- name: Start TransComService on app01
  ansible.windows.win_service:
    name: TransComService
    state: started
  when: trans_com_service_feature
  delegate_to: "{{ app01_server }}"
  async: "{{ service_timeout }}"
  poll: 5
  tags:
    - cps

# Start AARProcessingService on primary app server
- name: Start AARProcessingService on app01
  ansible.windows.win_service:
    name: AARProcessingService
    state: started
  when: aar_feature
  delegate_to: "{{ app01_server }}"
  async: "{{ service_timeout }}"
  poll: 5
  tags:
    - cps

- name: Update Release version and date
  debug:
    msg: "Update the Release version and date in the TransPortal DB"
  run_once: true
  delegate_to: localhost
  ignore_errors: true
  tags:
    - cps

- name: Check if vcTagVer column exists and update stbApp table
  ansible.windows.win_shell: |
    sqlcmd -b -U "{{ db_user }}" -P "{{ db_password }}" -Q
    "IF EXISTS (SELECT 1 FROM sys.columns WHERE Name=N'vcTagVer')
    BEGIN EXEC sp_executesql N'update stbApp set vcTagVer=''{{ assembly_version }}'',
    dtReleaseDate=''{{ dated_deployment_dir }}'' where iAppID=859' END" -S
    "{{ transportal_db_listener }}" -d SSO
  delegate_to: "{{ transportal_db_server: }}"
  when: transportal_feature
  tags:
    - cps

- name: Update Release version and date
  debug:
    msg: "Update the Release version and date in the TransPortal DB"
  run_once: true
  delegate_to: localhost
  ignore_errors: true
  tags:
    - cps

# Start CPS Services
- name: Start step - Start CPS Services
  debug:
    msg: |
      Step Start CPS Services commencing...
      ------------------------------------
      Try starting service through NANT....
      ------------------------------------
  delegate_to: localhost
  tags:
    - cps

- name: Start Windows Process Activation Service on app01
  ansible.windows.win_service:
    name: Windows Process Activation
    state: started
  delegate_to: "{{ app01_server }}"
  tags:
    - cps

- name: Start World Wide Web Publishing Service Service on app01
  ansible.windows.win_service:
    name: World Wide Web Publishing Service
    state: started
  delegate_to: "{{ app01_server }}"
  tags:
    - cps

- name: Start Windows Process Activation Service on web01
  ansible.windows.win_service:
    name: Windows Process Activation
    state: started
  delegate_to: "{{ web01_server }}"
  tags:
    - cps

- name: Start World Wide Web Publishing Service Service on web01
  ansible.windows.win_service:
    name: World Wide Web Publishing Service
    state: started
  delegate_to: "{{ web01_server }}"
  tags:
    - cps

- name: Start App pools and Sites
  ansible.windows.win_shell: |
    C:\Windows\System32\inetsrv\appcmd start apppool /apppool.name:"{{ item }}"
    C:\Windows\System32\inetsrv\appcmd start site /site.name:"{{ item }}"
  loop:
    - DMAPI
    - CFTSAPI
    - CPSWebAPI
  tags:
    - cps

# Enable external IIS
- name: Start step - Enable EXT IIS
  debug:
    msg: "Step Enable EXT IIS commencing...  ---------"
  delegate_to: localhost
  tags:
    - cps

- name: Enable External Services
  block:
    - name: Start external App pools and Sites
      ansible.windows.win_shell: |
        C:\Windows\System32\inetsrv\appcmd start apppool /apppool.name:"{{ item }}"
        C:\Windows\System32\inetsrv\appcmd start site /site.name:"{{ item }}"
      delegate_to: "{{ ext01_server }}"
      loop:
        - DMVAPI
        - IAS
        - RAAS
        - TPI

    - name: Start NYCDOT App pool and Site
      ansible.windows.win_shell: |
        C:\Windows\System32\inetsrv\appcmd start apppool /apppool.name:"NYCDOT"
        C:\Windows\System32\inetsrv\appcmd start site /site.name:"NYCDOT"
      delegate_to: "{{ ext01_server }}"
      when: deployment_type != "STAGE"

    - name: Start DMAPI App pool and Site
      ansible.windows.win_shell: |
        C:\Windows\System32\inetsrv\appcmd start site /site.name:"DMAPI"
        Start-IISSite -Name "DMAPI"
      delegate_to: "{{ app03_server }}"
      when: env_type != "PROD"

    - name: End step - Enable EXT IIS
      debug:
        msg: "Step Enable EXT IIS completed..."
      delegate_to: localhost

    - name: Start step - Enable CPS Tasks
      debug:
        msg: |
          Step Start Enable CPS Tasks commencing...
          ------------------------------------
          Enable  scheduled tasks for CPS....
          ------------------------------------
      delegate_to: localhost

    - name: Start and disable a scheduled task ActiveDirectoryPollingTask
      ansible.windows.win_shell: |
          $taskName = "ActiveDirectoryPollingTask"
          $taskPath = "\\"
          $task = Get-ScheduledTask -TaskName $taskName -TaskPath $taskPath -ErrorAction SilentlyContinue
          if ($task) {
            Enable-ScheduledTask -TaskName $taskName -TaskPath $taskPath
          }
      when: ad_polling_task
      delegate_to: "{{ app01_server }}"

    - name: Start and enable a scheduled task ImageFilterSimulator
      ansible.windows.win_shell: |
          $taskName = "ImageFilterSimulator"
          $taskPath = "\\"
          $task = Get-ScheduledTask -TaskName $taskName -TaskPath $taskPath -ErrorAction SilentlyContinue
          if ($task) {
            Enable-ScheduledTask -TaskName $taskName -TaskPath $taskPath
          }
      when: simulator
      delegate_to: "{{ app01_server }}"

    - name: Process each scheduled task
      block:
        - name: Check if {{ cps_tasks_path }}{{ item }} exists
          ansible.windows.win_stat:
            path: "{{ cps_tasks_path }}{{ item }}"
          register: task_dir

        - name: Start and enable a scheduled task {{ item }}
          ansible.windows.win_shell: |
            $taskName = "{{ item }}"
            $taskPath = "\\"
            $task = Get-ScheduledTask -TaskName $taskName -TaskPath $taskPath -ErrorAction SilentlyContinue
            if ($task) {
              Enable-ScheduledTask -TaskName $taskName -TaskPath $taskPath
            }
          when: task_dir.stat.exists
      delegate_to: "{{ app01_server }}"
      loop:
        - CPSEmailNotification
        - AVIPromotion
        - DigitalPromotion
        - TableauImagesPollingTask


    - name: Start step - Enable EXT Scheduled Tasks
      debug:
        msg: |
          Step Start Enable EXT Scheduled Tasks commencing...
          ---------------------------------------------------
          Enable External scheduled tasks for CPS....
          ---------------------------------------------------
      delegate_to: localhost

    - name: Start and enable a scheduled task ZPIProcessTask
      ansible.windows.win_shell: |
          $taskName = "ZPIProcessTask"
          $taskPath = "\\"
          $task = Get-ScheduledTask -TaskName $taskName -TaskPath $taskPath -ErrorAction SilentlyContinue
          if ($task) {
            Enable-ScheduledTask -TaskName $taskName -TaskPath $taskPath
          }
      delegate_to: "{{ ext01_server }}"
      when: zpi_process_task and env_type != "PROD" and env_type != "STAGE"

    - name: Start and enable a scheduled task {{ item }}
      ansible.windows.win_shell: |
          $taskName = "{{ item }}"
          $taskPath = "\\"
          $task = Get-ScheduledTask -TaskName $taskName -TaskPath $taskPath -ErrorAction SilentlyContinue
          if ($task) {
            Enable-ScheduledTask -TaskName $taskName -TaskPath $taskPath
          }
      delegate_to: "{{ ext01_server }}"
      when: dmv_feature
      loop:
        - DMVLookupMD
        - MVLookupXP
        - DMVLookupNJ

    - name: Force run Active Directory Polling Task
      ansible.windows.win_shell: |
          $taskName = "ActiveDirectoryPollingTask"
          $taskPath = "\\"
          $task = Get-ScheduledTask -TaskName $taskName -TaskPath $taskPath -ErrorAction SilentlyContinue
          if ($task) {
            Enable-ScheduledTask -TaskName $taskName -TaskPath $taskPath
          }
      delegate_to: "{{ app01_server }}"
      when: ad_polling_task

  when: cluster_value != '02'
  tags:
    - cps

- name: Display completion message
  debug:
    msg: "Post-Build-Startup of CPS is complete"
  delegate_to: localhost
  tags:
    - cps

# Start IMS
- name: Start IIS App Pools and Sites
  ansible.windows.win_shell: |
    Start-IISSite -Name "IMS{{ item.1 }}Api"
    C:\Windows\System32\inetsrv\appcmd start apppool /apppool.name:"IMS{{ item.1 }}Api"
  delegate_to: "{{ item.0 }}"
  loop: "{{ lookup('subelements', multi_server_list, 'ports') }}"
  loop_control:
    label: "{{ item.0 }} - Port {{ item.1 }}"
  tags:
    - cps

# Start VRGService on primary app server
- name: Start VRGService on app01
  ansible.windows.win_service:
    name: VRGService
    state: started
  when: vrg_feature
  delegate_to: "{{ app01_server }}"
  async: "{{ service_timeout }}"
  poll: 5
  tags:
    - cps

# Start VRGService on app02 (if clustered)
- name: Start VRGService on app02
  ansible.windows.win_service:
    name: VRGService
    state: started
  when: vrg_feature and cluster_feature
  delegate_to: "{{ app02_server }}"
  async: "{{ service_timeout }}"
  poll: 5
  tags:
    - cps

# Start VRGService on app03 (only in PROD)
- name: Start VRGService on app03 for PROD
  ansible.windows.win_service:
    name: VRGService
    state: started
  when: vrg_feature and deployment_type == 'PROD'
  delegate_to: "{{ app03_server }}"
  async: "{{ service_timeout }}"
  poll: 5
  tags:
    - cps

# Start DNAMatchService on primary app server
- name: Start DNAMatchService on app01
  ansible.windows.win_service:
    name: DNAMatchService
    state: started
  when: dna_match_feature
  delegate_to: "{{ app01_server }}"
  async: "{{ service_timeout }}"
  poll: 5
  tags:
    - cps

# Stop DNAMatchService on app02 (if clustered)
- name: Stop DNAMatchService on app02
  ansible.windows.win_service:
    name: DNAMatchService
    state: stopped
  when: dna_match_feature and cluster_feature
  delegate_to: "{{ app02_server }}"
  async: "{{ service_timeout }}"
  poll: 5
  tags:
    - cps
