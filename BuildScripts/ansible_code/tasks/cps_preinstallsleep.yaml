- name: Notify task start
  debug:
    msg: "Put the application to sleep (stop tasks/services) at {{ timestamp }}"
  run_once: true
  delegate_to: localhost
  ignore_errors: true
  tags:
    - cps

# Stop DNAMatchService on primary app server
- name: Stop DNAMatchService on app01
  ansible.windows.win_service:
    name: DNAMatchService
    state: stopped
  when: dna_match_feature
  delegate_to: "{{ app01_server }}"
  async: "{{ service_timeout }}"
  poll: 5
  tags:
    - cps

# Stop DNAMatchService on app02 (if clustered)
- name: Stop DNAMatchService on app02
  ansible.windows.win_service:
    name: DNAMatchService
    state: stopped
  when: dna_match_feature and cluster_feature
  delegate_to: "{{ app02_server }}"
  async: "{{ service_timeout }}"
  poll: 5
  tags:
    - cps

  # Stop VRGService on primary app server
- name: Stop VRGService on app01
  ansible.windows.win_service:
    name: VRGService
    state: stopped
  when: vrg_feature
  delegate_to: "{{ app01_server }}"
  async: "{{ service_timeout }}"
  poll: 5
  tags:
    - cps

# Stop VRGService on app02 (if clustered)
- name: Stop VRGService on app02
  ansible.windows.win_service:
    name: VRGService
    state: stopped
  when: vrg_feature and cluster_feature
  delegate_to: "{{ app02_server }}"
  async: "{{ service_timeout }}"
  poll: 5
  tags:
    - cps

# Stop VRGService on app03 (only in PROD)
- name: Stop VRGService on app03 for PROD
  ansible.windows.win_service:
    name: VRGService
    state: stopped
  when: vrg_feature and deployment_type == 'PROD'
  delegate_to: "{{ app03_server }}"
  async: "{{ service_timeout }}"
  poll: 5
  tags:
    - cps

# Stop IMS
- name: Stop IIS App Pools and Sites
  ansible.windows.win_shell: |
    C:\Windows\System32\inetsrv\appcmd stop apppool /apppool.name:"IMS{{ item.1 }}Api"
    C:\Windows\System32\inetsrv\appcmd stop site /site.name:"
    IMS{{ item.1 }}Api"
  delegate_to: "{{ item.0 }}"
  loop: "{{ lookup('subelements', multi_server_list, 'ports') }}"
  loop_control:
    label: "{{ item.0 }} - Port {{ item.1 }}"
  tags:
    - cps

# Stop CPS API's and scheduled tasks
- name: Output starting message
  debug:
    msg: "CPS Pre-Install is starting at {{ timestamp }}"
  delegate_to: localhost
  tags:
    - cps

# Disable CPS Tasks section
- name: Start step - Disable CPS Tasks
  debug:
    msg: |
      Step Disable CPS Tasks commencing...
      ------------------------------------
      Disable scheduled tasks for CPS.....
      ------------------------------------
  delegate_to: localhost
  tags:
    - cps

- name: Disable CPS Tasks
  block:
    - name: Stop and disable a scheduled task ActiveDirectoryPollingTask
      ansible.windows.win_shell: |
          $taskName = "ActiveDirectoryPollingTask"
          $taskPath = "\\"
          $task = Get-ScheduledTask -TaskName $taskName -TaskPath $taskPath -ErrorAction SilentlyContinue
          if ($task) {
            Stop-ScheduledTask -TaskName $taskName -TaskPath $taskPath
            Disable-ScheduledTask -TaskName $taskName -TaskPath $taskPath
          }
      when: ad_polling_task

    - name: Stop and disable a scheduled task ImageFilterSimulator
      ansible.windows.win_shell: |
          $taskName = "ImageFilterSimulator"
          $taskPath = "\\"
          $task = Get-ScheduledTask -TaskName $taskName -TaskPath $taskPath -ErrorAction SilentlyContinue
          if ($task) {
            Stop-ScheduledTask -TaskName $taskName -TaskPath $taskPath
            Disable-ScheduledTask -TaskName $taskName -TaskPath $taskPath
          }
      when: simulator

    - name: Process each scheduled task
      block:
        - name: Check if {{ cps_tasks_path }}{{ item }} exists
          ansible.windows.win_stat:
            path: "{{ cps_tasks_path }}{{ item }}"
          register: task_dir

        - name: Stop and disable a scheduled task {{ item }}
          ansible.windows.win_shell: |
            $taskName = "{{ item }}"
            $taskPath = "\\"
            $task = Get-ScheduledTask -TaskName $taskName -TaskPath $taskPath -ErrorAction SilentlyContinue
            if ($task) {
              Stop-ScheduledTask -TaskName $taskName -TaskPath $taskPath
              Disable-ScheduledTask -TaskName $taskName -TaskPath $taskPath
            }
          when: task_dir.stat.exists
      loop:
        - CPSEmailNotification
        - AVIPromotion
        - DigitalPromotion
        - TableauImagesPollingTask

    - name: End step - Disable CPS Tasks
      debug:
        msg: "Step Disable CPS Tasks completed..."
      delegate_to: localhost
  delegate_to: "{{ app01_server }}"
  when: cluster_value == '01'
  tags:
    - cps

# Stop IIS section
- name: Start step - Stop iis
  debug:
    msg: "Step Stop iis commencing..."
  delegate_to: localhost
  tags:
    - cps

- name: Stop IIS Services
  block:
    - name: Reset IIS
      ansible.windows.win_shell: iisreset

    - name: Stop App pools and Sites
      ansible.windows.win_shell: |
        C:\Windows\System32\inetsrv\appcmd stop apppool /apppool.name:"{{ item }}"
        C:\Windows\System32\inetsrv\appcmd stop site /site.name:"{{ item }}"
      loop:
        - DMAPI
        - CFTSAPI
        - CPSWebAPI

    - name: Stop World Wide Web Publishing Service Service
      ansible.windows.win_service:
        name: World Wide Web Publishing Service
        state: stopped
      delegate_to: "{{ web02_server }}"

    - name: Stop Windows Process Activation Service
      ansible.windows.win_service:
        name: Windows Process Activation
        state: stopped
      delegate_to: "{{ web02_server }}"

    - name: End step - Stop IIS
      debug:
        msg: "Step Stop IIS completed..."
      delegate_to: localhost
  delegate_to: "{{ app02_server }}"
  when: cluster_value == '02'
  tags:
    - cps

# Stop external services section
- name: Start step - Stop ext services
  debug:
    msg: "Step Stop ext services commencing..."
  delegate_to: localhost
  tags:
    - cps

- name: Stop External Services
  block:
    - name: Stop and disable a scheduled task ZPIProcessTask
      ansible.windows.win_shell: |
          $taskName = "ZPIProcessTask"
          $taskPath = "\\"
          $task = Get-ScheduledTask -TaskName $taskName -TaskPath $taskPath -ErrorAction SilentlyContinue
          if ($task) {
            Stop-ScheduledTask -TaskName $taskName -TaskPath $taskPath
            Disable-ScheduledTask -TaskName $taskName -TaskPath $taskPath
          }
      delegate_to: "{{ ext01_server }}"
      when: zpi_process_task

    - name: Stop and disable a scheduled task {{ item }}
      ansible.windows.win_shell: |
          $taskName = "{{ item }}"
          $taskPath = "\\"
          $task = Get-ScheduledTask -TaskName $taskName -TaskPath $taskPath -ErrorAction SilentlyContinue
          if ($task) {
            Stop-ScheduledTask -TaskName $taskName -TaskPath $taskPath
            Disable-ScheduledTask -TaskName $taskName -TaskPath $taskPath
          }
      delegate_to: "{{ ext01_server }}"
      when: dmv_feature
      loop:
        - DMVLookupMD
        - MVLookupXP
        - DMVLookupNJ

    - name: Stop external App pools and Sites
      ansible.windows.win_shell: |
        C:\Windows\System32\inetsrv\appcmd stop apppool /apppool.name:"{{ item }}"
        C:\Windows\System32\inetsrv\appcmd stop site /site.name:"{{ item }}"
      delegate_to: "{{ ext01_server }}"
      loop:
        - WorkflowAPI
        - AAR
        - IAS
        - RAAS
        - TPI
        - TPI02
        - DMVAPI

    - name: Stop NYCDOT App pool and Site
      ansible.windows.win_shell: |
        C:\Windows\System32\inetsrv\appcmd stop apppool /apppool.name:"NYCDOT"
        C:\Windows\System32\inetsrv\appcmd stop site /site.name:"NYCDOT"
      delegate_to: "{{ ext01_server }}"
      when: deployment_type != "STAGE"

    - name: Stop DMAPI App pool and Site
      ansible.windows.win_shell: |
        C:\Windows\System32\inetsrv\appcmd stop site /site.name:"DMAPI"
        C:\Windows\System32\inetsrv\appcmd stop apppool /apppool.name:"DMAPI"
      delegate_to: "{{ app03_server }}"
      when: env_type == "PROD"

    - name: End step - Stop iis
      debug:
        msg: "Step Stop ext services completed..."
      delegate_to: localhost
  when: cluster_value != '02'
  tags:
    - cps

- name: Output completion message
  debug:
    msg: "SLEEP of CPS is complete"
  delegate_to: localhost
  tags:
    - cps

# Stop AARProcessingService on primary app server
- name: Stop AARProcessingService on app01
  ansible.windows.win_service:
    name: AARProcessingService
    state: stopped
  when: aar_feature
  delegate_to: "{{ app01_server }}"
  async: "{{ service_timeout }}"
  poll: 5
  tags:
    - cps

# Stop AARProcessingService on app02 (if clustered)
- name: Stop AARProcessingService on app02
  ansible.windows.win_service:
    name: AARProcessingService
    state: stopped
  when: aar_feature and cluster_feature
  delegate_to: "{{ app02_server }}"
  async: "{{ service_timeout }}"
  poll: 5
  tags:
    - cps

# Stop TransComService on primary app server
- name: Stop TransComService on app01
  ansible.windows.win_service:
    name: TransComService
    state: stopped
  when: trans_com_service_feature
  delegate_to: "{{ app01_server }}"
  async: "{{ service_timeout }}"
  poll: 5
  tags:
    - cps

# Stop TransComService on app02 (if clustered)
- name: Stop TransComService on app02
  ansible.windows.win_service:
    name: TransComService
    state: stopped
  when: trans_com_service_feature and cluster_feature
  delegate_to: "{{ app02_server }}"
  async: "{{ service_timeout }}"
  poll: 5
  tags:
    - cps

# Stop DMVService on primary ext server
- name: Stop DMVService on ext01
  ansible.windows.win_service:
    name: DMVService
    state: stopped
  when: dmv_worker_feature
  delegate_to: "{{ ext01_server }}"
  async: "{{ service_timeout }}"
  poll: 5
  tags:
    - cps

# Reboot app01 and wait for it
- name: Reboot the Windows machine
  ansible.windows.win_reboot:
    reboot_timeout: 600      # Wait up to 10 minutes
    test_command: 'whoami'   # Ensures WinRM is fully responsive before continuing
  delegate_to: "{{ app01_server }}"
  when: not cluster_feature
  tags:
    - cps

# Reboot app01 and app02 and wait for them (if clustered)
- name: Reboot the Windows machines
  ansible.windows.win_reboot:
    reboot_timeout: 600      # Wait up to 10 minutes
    test_command: 'whoami'   # Ensures WinRM is fully responsive before continuing
  delegate_to: "{{ item }}"
  loop:
    - "{{ app01_server }}"
    - "{{ app02_server }}"
  when: cluster_feature
  tags:
    - cps
