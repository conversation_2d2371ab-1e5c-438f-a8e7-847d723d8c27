# Current date in YYYY-MM-DD format
timestamp: "{{ lookup('pipe', 'date +%Y-%m-%d') }}"

# Server definitions
app01_server: "{{ app01_server | default('stgcpsapp01.int.cbdtp.net') }}"
app02_server: "{{ app02_server | default('stgcpsapp02.int.cbdtp.net') }}"
app03_server: "{{ app03_server | default('n.a') }}"
web01_server: "{{ web01_server | default('stgcpsweb01.int.cbdtp.net') }}"
web02_server: "{{ web02_server | default('stgcpsweb02.int.cbdtp.net') }}"
ext01_server: "{{ ext01_server | default('stgcpsext01.int.cbdtp.net') }}"
transportal_db_server: "{{ transportal_db_server | default('stgtxp01.int.cbdtp.net') }}"
transportal_db_listener: "{{ transportal_db_listener | default('stgtxp01.int.cbdtp.net') }}"

# Load Balancing/Cluster
cluster_feature: "{{ cluster_feature | default('true') }}" # ClusterFeature

# Is Project using TransPortal?
transportal_feature: "{{ cluster_feature | default('true') }}" # TransPortalFeature

# Define the Deployment Project Environment
deployment_type: "{{ deployment_type | default('STAGE') }}"

# Set features for CBDTP
dna_match_feature: "{{ dna_match_feature | default('true') }}" # DNAMatchFeature
vrg_feature: "{{ vrg_feature | default('true') }}" # VRGFeature
aar_feature: "{{ vrg_feature | default('false') }}" # AARFeature
trans_com_service_feature: "{{ trans_com_service_feature | default('false') }}" # TransComServiceFeature
dmv_worker_feature: "{{ dmv_worker_feature | default('true') }}" # DMVWorkerFeature
dmv_feature: "{{ dmv_worker_feature | default('true') }}" # DMVFeature

# Service timeout in seconds (160000ms in original = 160s)
service_timeout: 160

# MultiServerList
multi_server_list:
  stgcpsapp01.int.cbdtp.net:
    ports:
      - 4501
      - 4502
  stgcpsapp02.int.cbdtp.net:
    ports:
      - 4501
      - 4503

# Is Project using ZPIProcess
zpi_process_task: "{{ zpi_process_task | default('true') }}" # ZPIProcessTask

# Is Project using ADPollingTask
ad_polling_task: "{{ ad_polling_task | default('false') }}" # ADPollingTask

# Build the simluators? (DEV/QA only)
simulator_feature: "{{ simulator_feature | default('false') }}" # SimulatorFeature

cluster_value: "{{ cluster_value | default('') }}"

cps_tasks_path: "C:\\CPS\\ScheduledTasks\\"

# Used by the build, do not modify the below lines
dated_deployment_dir: "2025-04-29"
assembly_version: "{{ assembly_version | default('4.20.0-rc2') }}"

# If TransPortal Feature = true, need the following DB information to update TransPortal DB
db_user: "{{ db_user | default('') }}"
db_password: "{{ db_password | default('') }}"
