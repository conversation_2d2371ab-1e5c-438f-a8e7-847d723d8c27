@ECHO OFF
ver > nul
Set CURRENTDIR=%CD%
Set BUILDDIR=%1
Set BASEDIR=%2
Set TAG=%3
Set REPO=%4
Set COMMONREPO=%5
Set TOOLSREPO=%6

SET STEP=devopsfetch
echo error level is %ERRORLEVEL%
if %ERRORLEVEL% NEQ 0 GOTO :error 
echo %CURRENTDIR%
cd %BUILDDIR%
call git clone -b %TAG% --depth 1 %REPO%
call attrib -r /s *
call cacls %BUILDDIR%\InfinityHost-DBS /t /e /g Users:f
call xcopy InfinityHost-DBS\* Source /S /Q /Y /I
rmdir /s /q %BUILDDIR%\InfinityHost-DBS
if %ERRORLEVEL% NEQ 0 GOTO :error 
call git clone -b %TAG% --depth 1 %COMMONREPO%
call attrib -r /s *
if %ERRORLEVEL% NEQ 0 GOTO :error 
call xcopy Infinity-Common\Common\Services\DNAMatch\* Source\DBS\Common\Services\DNAMatch /S /Q /Y /I
if %ERRORLEVEL% NEQ 0 GOTO :error 
call xcopy Infinity-Common\Common\Services\DataAccess\* Source\DBS\Common\Services\DataAccess /S /Q /Y /I
if %ERRORLEVEL% NEQ 0 GOTO :error 
call xcopy Infinity-Common\Common\Services\TcoreCommon\* Source\DBS\Common\Services\TcoreCommon /S /Q /Y /I
if %ERRORLEVEL% NEQ 0 GOTO :error 
call xcopy Infinity-Common\Common\Shared\* Source\DBS\Common\Shared /S /Q /Y /I
if %ERRORLEVEL% NEQ 0 GOTO :error 
rmdir /s /q %BUILDDIR%\Infinity-Common
call git clone -b main --depth 1 %TOOLSREPO%
if %ERRORLEVEL% NEQ 0 GOTO :error 
xcopy SD-Common\TableauImagePollingTask\* Source\DBS\TableauImagePollingTask /S /Q /Y /I
rmdir /s /q %BUILDDIR%\SD-Common

goto :END

:error
echo ------- ERRORLEVEL devops fetch returned ------
echo %ERRORLEVEL% is the errorlevel and failure is on %STEP%
cd %CURRENTDIR%
exit /b 1

:END
echo -- devopsfetch success


exit /b 0