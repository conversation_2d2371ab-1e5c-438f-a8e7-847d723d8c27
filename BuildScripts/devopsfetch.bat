@ECHO OFF
ver > nul
Set CURRENTDIR=%CD%
Set BUILDDIR=%1
Set BASEDIR=%2
Set TAG=%3
Set REPO=%4
Set COMMONREPO=%5

SET STEP=text
echo error level is %ERRORLEVEL%
if %ERRORLEVEL% NEQ 0 GOTO :error 
echo clear packages
rem call dotnet nuget locals -c all  
echo update nuget version
call nuget update -self
echo copy config
echo %CURRENTDIR%
cd %BUILDDIR%
call git clone -b %TAG% --depth 1 %REPO%
if %ERRORLEVEL% NEQ 0 GOTO :error 
cd %BASEDIR%
call git clone -b main --depth 1 %COMMONREPO%
if %ERRORLEVEL% NEQ 0 GOTO :error 
xcopy SD-Common\ActiveDirectoryTask\* Source\ActiveDirectory /S /Q /Y /I
rmdir /s /q %BASEDIR%\SD-Common

cd %CURRENTDIR%

goto :END

:error
echo ------- ERRORLEVEL dotnet returned ------
echo %ERRORLEVEL% is the errorlevel and failure is on %STEP%
cd %CURRENTDIR%
exit /b 1

:END
echo -- dotnet success


exit /b 0