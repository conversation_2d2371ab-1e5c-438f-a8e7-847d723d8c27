@echo off
rem set variables
Set CURRENTDIR=%CD%

echo ------- Your current directory is:  %CURRENTDIR% ---------

set NOW=%date:~10,4%-%date:~4,2%-%date:~7,2%
echo Deployment beginning %NOW%
echo -----------------------------------
echo Connect to Server
echo -----------------------------------
verify > nul
SET SERVER=%2
SET user=%3
SET pw=%4
SET DEPLOYMENTDRIVE=%5
SET DEPLOYMENTDIR=%6
SET JUMPDRIVE=%7
SET GENTASK=%8
SET road=%9
SHIFT
SHIFT
SHIFT
SHIFT
SHIFT
SHIFT
SHIFT
SHIFT
SHIFT
Set maindir=%1
SET subdir=%2

echo 

ECHO %road%
If NOT "%road%" == "none" SET road=-%road%
If "%road%" == "none" SET road=
echo road = %road%

SET BASEDIR=%JUMPDRIVE%:\Staging-ImageReview\%DEPLOYMENTDIR%

for /f "tokens=1-3 delims=/ " %%a in ('doff mm/dd/yyyy +1') do (
set mm=%%a
set dd=%%b
set yyyy=%%c)
set tomorrow=%mm%/%dd%/%yyyy%
echo Tomorrow %tomorrow%

for /f "tokens=1-3 delims=/ " %%d in ('doff mm/dd/yyyy') do (
set tmm=%%d
set tdd=%%e
set tyyyy=%%f)
set today=%tmm%/%tdd%/%tyyyy%
echo Today %today%

for /f "tokens=1-2 delims=: " %%h in ('doff hh:mi +10m') do (
set hh=%%h
set mi=%%i)
set soon=%hh%:%mi%
echo Soon %soon%

set /A mi+=1
set soon2=%hh%:%mi%
echo soon2

for /f "tokens=1-2 delims=: " %%h in ('doff hh:mi +10m') do (
set hh=%%h
set mi=%%i)
set soon=%hh%:%mi%
echo Soon %soon%
set var1=11
for /f "tokens=1*" %%Y in ('
  powershell -NoP -C "(Get-Date).AddMinutes(%var1%).ToString('yyyy/MM/dd HH:mm')"
') do (
  Set "MyDate=%%Y"
  set "MyTime=%%Z"
)

echo %MyDate%
echo my time %MyTime%

echo Server: %SERVER% GENTASK: %GENTASK% ROAD: %road%


echo -----------------------------------
echo Creating MIR scheduled task for ActiveDirectoryPollingTask....
echo -----------------------------------
if "%GENTASK%"=="ActiveDirectoryPollingTask" (
psexec -accepteula \\%SERVER% cmd /c  C:\Windows\system32\schtasks.exe /Create /F /TN ActiveDirectoryPollingTask%road% /RU %user% /RP "%pw%"  /RL HIGHEST /SC MINUTE /MO 5 /SD %today%  /ST %soon%  /TR C:\%maindir%\%subdir%\ActiveDirectoryPollingTask%road%\tcore.ADP.ActiveDirectoryPolling.exe  >> ScheduledTasksResults.log 2>&1
psexec -accepteula \\%SERVER% cmd /c  C:\Windows\system32\schtasks.exe /Run /TN ActiveDirectoryPollingTask%road%
psexec -accepteula \\%SERVER% cmd /c  C:\Windows\system32\schtasks.exe /End /TN ActiveDirectoryPollingTask%road%
psexec -accepteula \\%SERVER% cmd /c  C:\Windows\system32\schtasks.exe /Change /TN ActiveDirectoryPollingTask%road%  /DISABLE
echo ActiveDirectoryPollingTask%road% created
)
if %ERRORLEVEL% NEQ 0 GOTO :error

echo -----------------------------------
echo Creating MIR scheduled task for AuditReviewResultsTask....
echo -----------------------------------
if "%GENTASK%"=="AuditReviewResultsTask" (
psexec -accepteula \\%SERVER% cmd /c  C:\Windows\system32\schtasks.exe /Create /F /TN AuditReviewResultsTask%road% /RU %user% /RP "%pw%"  /RL HIGHEST /SC MINUTE /MO 5 /SD %today%  /ST %soon%  /TR C:\%maindir%\%subdir%\AuditReviewResultsTask%road%\Tcore.MIR.AuditReviewResultsTask.exe  >> ScheduledTasksResults.log 2>&1
psexec -accepteula \\%SERVER% cmd /c  C:\Windows\system32\schtasks.exe /Run /TN AuditReviewResultsTask%road%
psexec -accepteula \\%SERVER% cmd /c  C:\Windows\system32\schtasks.exe /End /TN AuditReviewResultsTask%road%
psexec -accepteula \\%SERVER% cmd /c  C:\Windows\system32\schtasks.exe /Change /TN AuditReviewResultsTask%road%  /DISABLE
echo AuditReviewResultsTask%road% created
)
if %ERRORLEVEL% NEQ 0 GOTO :error


echo -----------------------------------
echo Creating FPMSendResults scheduled task for Image Review Fingerprint....
echo -----------------------------------
if "%GENTASK%"=="FPMSendResultsTask" (
psexec -accepteula \\%SERVER% cmd /c  C:\Windows\system32\schtasks.exe /Create /F /TN FPMSendResultsTask%road% /RU %user% /RP "%pw%"  /RL HIGHEST /SC MINUTE /MO 5 /SD %today%  /ST %soon%  /TR C:\%maindir%\%subdir%\FPMSendResultsTask%road%\Tcore.FPM.FPSendResultsTask.exe >> ScheduledTasksResults.log 2>&1
psexec -accepteula \\%SERVER% cmd /c  C:\Windows\system32\schtasks.exe /Run /TN FPMSendResultsTask%road%
psexec -accepteula \\%SERVER% cmd /c  C:\Windows\system32\schtasks.exe /End /TN FPMSendResultsTask%road%
psexec -accepteula \\%SERVER% cmd /c  C:\Windows\system32\schtasks.exe /Change /TN FPMSendResultsTask%road%  /DISABLE
echo FPMSendResultsTask%road% created
)
if %ERRORLEVEL% NEQ 0 GOTO :error

echo -----------------------------------
echo Creating FPPMatchPoolTask scheduled task for Image Review Fingerprint....
echo -----------------------------------
if "%GENTASK%"=="FPPMatchPoolTask" (
psexec -accepteula \\%SERVER% cmd /c  C:\Windows\system32\schtasks.exe /Create /F /TN FPPMatchPoolTask%road% /RU %user% /RP "%pw%"  /RL HIGHEST /SC MINUTE /MO 5 /SD %today%  /ST %soon%  /TR C:\%maindir%\%subdir%\FPPMatchPoolTask%road%\Tcore.FPP.FPPMatchPoolTask.exe >> ScheduledTasksResults.log 2>&1
psexec -accepteula \\%SERVER% cmd /c  C:\Windows\system32\schtasks.exe /Run /TN FPPMatchPoolTask%road%
psexec -accepteula \\%SERVER% cmd /c  C:\Windows\system32\schtasks.exe /End /TN FPPMatchPoolTask%road%
psexec -accepteula \\%SERVER% cmd /c  C:\Windows\system32\schtasks.exe /Change /TN FPPMatchPoolTask%road%  /DISABLE
echo FPPMatchPoolTask%road% created
)
if %ERRORLEVEL% NEQ 0 GOTO :error

echo -----------------------------------
echo Creating GenerateROITask scheduled task....
echo -----------------------------------
if "%GENTASK%"=="GenerateROITask"  (
psexec -accepteula \\%SERVER% cmd /c  C:\Windows\system32\schtasks.exe /Create /F /TN GenerateROITask%road%  /RL HIGHEST /SC MINUTE /RU %user% /RP "%pw%" /MO 5 /SD %today%  /ST %soon%   /TR C:\%maindir%\%subdir%\GenerateROITask%road%\Tcore.IFX.GenerateRoiCreateUrlTaskConsole.exe >> ScheduledTasksResults.log 2>&1
psexec -accepteula \\%SERVER% cmd /c  C:\Windows\system32\schtasks.exe /Run /TN GenerateROITask%road%
psexec -accepteula \\%SERVER% cmd /c  C:\Windows\system32\schtasks.exe /End /TN GenerateROITask%road%
psexec -accepteula \\%SERVER% cmd /c  C:\Windows\system32\schtasks.exe /Change /TN GenerateROITask%road% /DISABLE
echo GenerateROITask%road% created
)
if %ERRORLEVEL% NEQ 0 GOTO :error

echo -----------------------------------
echo Creating GenerateROITaskRetries scheduled task....
echo -----------------------------------
if "%GENTASK%"=="GenerateROITaskRetries" (
psexec -accepteula \\%SERVER% cmd /c  C:\Windows\system32\schtasks.exe /Create /F /TN GenerateROITaskRetries%road%  /RL HIGHEST /SC MINUTE /RU %user% /RP "%pw%" /MO 5 /SD %today%  /ST %MyTime%   /TR C:\%maindir%\%subdir%\GenerateROITaskRetries%road%\Tcore.IFX.GenerateRoiCreateUrlTaskConsole.exe >> ScheduledTasksResults.log 2>&1
psexec -accepteula \\%SERVER% cmd /c  C:\Windows\system32\schtasks.exe /Run /TN GenerateROITaskRetries%road%
psexec -accepteula \\%SERVER% cmd /c  C:\Windows\system32\schtasks.exe /End /TN GenerateROITaskRetries%road%
psexec -accepteula \\%SERVER% cmd /c  C:\Windows\system32\schtasks.exe /Change /TN GenerateROITaskRetries%road% /DISABLE
echo GenerateROITaskRetries%road% created
)
if %ERRORLEVEL% NEQ 0 GOTO :error


echo -----------------------------------
echo Creating HOCP HighOcrConf Processor scheduled task for Image Review HOCP....
echo -----------------------------------
if "%GENTASK%"=="HighOcrConfProcessor" (
psexec -accepteula \\%SERVER% cmd /c  C:\Windows\system32\schtasks.exe /Create /F /TN HighOcrConfProcessor%road% /RU %user% /RP "%pw%"  /RL HIGHEST /SC MINUTE /MO 5 /SD %today%  /ST %soon%  /TR C:\%maindir%\%subdir%\HighOcrConfProcessor%road%\Tcore.HOCP.HighOcrConfProcessor.exe  >> ScheduledTasksResults.log 2>&1
psexec -accepteula \\%SERVER% cmd /c  C:\Windows\system32\schtasks.exe /Run /TN HighOcrConfProcessor%road%
psexec -accepteula \\%SERVER% cmd /c  C:\Windows\system32\schtasks.exe /End /TN HighOcrConfProcessor%road%
psexec -accepteula \\%SERVER% cmd /c  C:\Windows\system32\schtasks.exe /Change /TN HighOcrConfProcessor%road%  /DISABLE
echo HighOcrConfProcessor%road% Task created
)
if %ERRORLEVEL% NEQ 0 GOTO :error

echo -----------------------------------
echo Creating HOCP SendResults scheduled task for Image Review HOCP ....
echo -----------------------------------
if "%GENTASK%"=="HOCSendResultsProcessor" (
psexec -accepteula \\%SERVER% cmd /c  C:\Windows\system32\schtasks.exe /Create /F /TN HOCSendResultsProcessor%road% /RU %user% /RP "%pw%"  /RL HIGHEST /SC MINUTE /MO 5 /SD %today%  /ST %soon%  /TR C:\%maindir%\%subdir%\HOCSendResultsProcessor%road%\Tcore.HOCP.HOCSendResultsProcessor.exe  >> ScheduledTasksResults.log 2>&1
psexec -accepteula \\%SERVER% cmd /c  C:\Windows\system32\schtasks.exe /run /TN HOCSendResultsProcessor%road%
psexec -accepteula \\%SERVER% cmd /c  C:\Windows\system32\schtasks.exe /End /TN HOCSendResultsProcessor%road%
psexec -accepteula \\%SERVER% cmd /c  C:\Windows\system32\schtasks.exe /Change /TN HOCSendResultsProcessor%road%  /DISABLE
echo %road%HOCPSendResults Task created
)
if %ERRORLEVEL% NEQ 0 GOTO :error

echo -----------------------------------
echo Creating MIR scheduled task for HumanReadabilitySelectionTask....
echo -----------------------------------
if "%GENTASK%"=="HumanReadabilitySelectionTask" (
psexec -accepteula \\%SERVER% cmd /c  C:\Windows\system32\schtasks.exe /Create /F /TN HumanReadabilitySelectionTask%road% /RU %user% /RP "%pw%"  /RL HIGHEST /SC MINUTE /MO 5 /SD %today%  /ST %soon2%  /TR C:\%maindir%\%subdir%\HumanReadabilitySelectionTask%road%\HumanReadabilitySelectionTask.exe  >> ScheduledTasksResults.log 2>&1
psexec -accepteula \\%SERVER% cmd /c  C:\Windows\system32\schtasks.exe /Run /TN HumanReadabilitySelectionTask
psexec -accepteula \\%SERVER% cmd /c  C:\Windows\system32\schtasks.exe /End /TN HumanReadabilitySelectionTask
psexec -accepteula \\%SERVER% cmd /c  C:\Windows\system32\schtasks.exe /Change /TN HumanReadabilitySelectionTask  /DISABLE
echo HumanReadabilitySelectionTask%road% Task created
)
if %ERRORLEVEL% NEQ 0 GOTO :error

echo -----------------------------------
echo Creating ImageClientServiceTask_RetrieveTransactions%road% scheduled task....
echo -----------------------------------
if "%GENTASK%"=="ImageClientServiceTask_RetrieveTransactions" (
psexec -accepteula \\%SERVER% cmd /c  C:\Windows\system32\schtasks.exe /Create /F /TN ImageClientServiceTask_RetrieveTransactions%road%  /RL HIGHEST /SC MINUTE /RU %user% /RP "%pw%" /MO 3 /SD %today%  /ST %soon%   /TR C:\%maindir%\%subdir%\ImageClientServiceTask_RetrieveTransactions%road%\tcore.ImageReview.ScheduledTasks.ImageClientServiceTask.exe >> ScheduledTasksResults.log 2>&1
psexec -accepteula \\%SERVER% cmd /c  C:\Windows\system32\schtasks.exe /Run /TN ImageClientServiceTask_RetrieveTransactions%road%
psexec -accepteula \\%SERVER% cmd /c  C:\Windows\system32\schtasks.exe /End /TN ImageClientServiceTask_RetrieveTransactions%road%
psexec -accepteula \\%SERVER% cmd /c  C:\Windows\system32\schtasks.exe /Change /TN ImageClientServiceTask_RetrieveTransactions%road% /DISABLE
echo ImageClientServiceTask_RetrieveTransactions%road% created
)
if %ERRORLEVEL% NEQ 0 GOTO :error

echo -----------------------------------
echo Creating ImageClientServiceTask_SendResponse%road% scheduled task....
echo -----------------------------------
if "%GENTASK%"=="ImageClientServiceTask_SendResponse" (
psexec -accepteula \\%SERVER% cmd /c  C:\Windows\system32\schtasks.exe /Create /F /TN ImageClientServiceTask_SendResponse%road%  /RL HIGHEST /SC MINUTE /RU %user% /RP "%pw%" /MO 3 /SD %today%  /ST %MyTime%   /TR C:\%maindir%\%subdir%\ImageClientServiceTask_SendResponse%road%\tcore.ImageReview.ScheduledTasks.ImageClientServiceTask.exe >> ScheduledTasksResults.log 2>&1
psexec -accepteula \\%SERVER% cmd /c  C:\Windows\system32\schtasks.exe /Run /TN ImageClientServiceTask_SendResponse%road%
psexec -accepteula \\%SERVER% cmd /c  C:\Windows\system32\schtasks.exe /End /TN ImageClientServiceTask_SendResponse%road%
psexec -accepteula \\%SERVER% cmd /c  C:\Windows\system32\schtasks.exe /Change /TN ImageClientServiceTask_SendResponse%road% /DISABLE
echo ImageClientServiceTask_SendResponse%road% created
)
if %ERRORLEVEL% NEQ 0 GOTO :error

echo -----------------------------------
echo Creating MIR ManualResultsTask scheduled task ....
echo -----------------------------------
if "%GENTASK%"=="ManualResultsTask" (
psexec -accepteula \\%SERVER% cmd /c  C:\Windows\system32\schtasks.exe /Create /F /TN ManualResultsTask%road% /RU %user% /RP "%pw%"  /RL HIGHEST /SC MINUTE /MO 5 /SD %today%  /ST %soon%  /TR C:\%maindir%\%subdir%\ManualResultsTask%road%\Tcore.MIR.ManualResultsTask.exe  >> ScheduledTasksResults.log 2>&1
psexec -accepteula \\%SERVER% cmd /c  C:\Windows\system32\schtasks.exe /Run /TN ManualResultsTask%road%
psexec -accepteula \\%SERVER% cmd /c  C:\Windows\system32\schtasks.exe /End /TN ManualResultsTask%road%
psexec -accepteula \\%SERVER% cmd /c  C:\Windows\system32\schtasks.exe /Change /TN ManualResultsTask%road%  /DISABLE
echo ManualResultsTask%road% created
)
if %ERRORLEVEL% NEQ 0 GOTO :error

echo -----------------------------------
echo Creating ImgReviewSendTransTask scheduled task....
echo -----------------------------------
if "%GENTASK%"=="ImgReviewSendTransTask" (
psexec -accepteula \\%SERVER% cmd /c  C:\Windows\system32\schtasks.exe /Create /F /TN ImgReviewSendTransTask%road%  /RL HIGHEST /SC MINUTE /RU %user% /RP "%pw%" /MO 5 /SD %today%  /ST %soon%   /TR C:\%maindir%\%subdir%\ImgReviewSendTransTask%road%\Tcore.IFX.ImgRevSendTrans.exe >> ScheduledTasksResults.log 2>&1
psexec -accepteula \\%SERVER% cmd /c  C:\Windows\system32\schtasks.exe /Run /TN ImgReviewSendTransTask%road%
psexec -accepteula \\%SERVER% cmd /c  C:\Windows\system32\schtasks.exe /End /TN ImgReviewSendTransTask%road%
psexec -accepteula \\%SERVER% cmd /c  C:\Windows\system32\schtasks.exe /Change /TN ImgReviewSendTransTask%road% /DISABLE
echo ImgReviewSendTransTask%road% created
)
if %ERRORLEVEL% NEQ 0 GOTO :error

echo -----------------------------------
echo Creating ImgReviewSendTransTaskRetries scheduled task....
echo -----------------------------------
if "%GENTASK%"=="ImgReviewSendTransTaskRetries" (
psexec -accepteula \\%SERVER% cmd /c  C:\Windows\system32\schtasks.exe /Create /F /TN ImgReviewSendTransTaskRetries%road%  /RL HIGHEST /SC MINUTE /RU %user% /RP "%pw%" /MO 5 /SD %today%  /ST %MyTime% /TR C:\%maindir%\%subdir%\ImgReviewSendTransTaskRetries%road%\Tcore.IFX.ImgRevSendTrans.exe >> ScheduledTasksResults.log 2>&1
psexec -accepteula \\%SERVER% cmd /c  C:\Windows\system32\schtasks.exe /Run /TN ImgReviewSendTransTaskRetries%road%
psexec -accepteula \\%SERVER% cmd /c  C:\Windows\system32\schtasks.exe /End /TN ImgReviewSendTransTaskRetries%road%
psexec -accepteula \\%SERVER% cmd /c  C:\Windows\system32\schtasks.exe /Change /TN ImgReviewSendTransTaskRetries%road% /DISABLE
echo ImgReviewSendTransTaskRetries%road% created
)
if %ERRORLEVEL% NEQ 0 GOTO :error


echo -----------------------------------
echo Creating MIR IRRSendResults scheduled task ....
echo -----------------------------------
if "%GENTASK%"=="IRRSendResultsTask" (
psexec -accepteula \\%SERVER% cmd /c  C:\Windows\system32\schtasks.exe /Create /F /TN IRRSendResultsTask%road% /RU %user% /RP "%pw%"  /RL HIGHEST /SC MINUTE /MO 5 /SD %today%  /ST %soon%  /TR C:\%maindir%\%subdir%\IRRSendResultsTask%road%\Tcore.irr.IRRSendResultsTask.exe  >> ScheduledTasksResults.log 2>&1
psexec -accepteula \\%SERVER% cmd /c  C:\Windows\system32\schtasks.exe /Run /TN IRRSendResultsTask%road%
psexec -accepteula \\%SERVER% cmd /c  C:\Windows\system32\schtasks.exe /End /TN IRRSendResultsTask%road%
psexec -accepteula \\%SERVER% cmd /c  C:\Windows\system32\schtasks.exe /Change /TN IRRSendResultsTask%road%  /DISABLE
echo IRRSendResultsTask%road% created
)
if %ERRORLEVEL% NEQ 0 GOTO :error



echo -----------------------------------
echo Creating MIR IRRSendResultsRetries scheduled task ....
echo -----------------------------------
if "%GENTASK%"=="IRRSendResultsTaskRetries" (
psexec -accepteula \\%SERVER% cmd /c  C:\Windows\system32\schtasks.exe /Create /F /TN IRRSendResultsTaskRetries%road% /RU %user% /RP "%pw%"  /RL HIGHEST /SC MINUTE /MO 5 /SD %today%  /ST %soon%  /TR C:\%maindir%\%subdir%\IRRSendResultsTaskRetries%road%\Tcore.irr.IRRSendResultsTask.exe  >> ScheduledTasksResults.log 2>&1
psexec -accepteula \\%SERVER% cmd /c  C:\Windows\system32\schtasks.exe /Run /TN IRRSendResultsTaskRetries%road%
psexec -accepteula \\%SERVER% cmd /c  C:\Windows\system32\schtasks.exe /End /TN IRRSendResultsTaskRetries%road%
psexec -accepteula \\%SERVER% cmd /c  C:\Windows\system32\schtasks.exe /Change /TN IRRSendResultsTaskRetries%road%  /DISABLE
echo IRRSendResultsTaskRetries%road% created
)
if %ERRORLEVEL% NEQ 0 GOTO :error


echo -----------------------------------
echo Creating MIRNotificationEscalationTask scheduled task....
echo -----------------------------------
if "%GENTASK%"=="MIREmailNotificationTask" (
psexec -accepteula \\%SERVER% cmd /c  C:\Windows\system32\schtasks.exe /Create /F /TN MIREmailNotificationTask%road%  /RL HIGHEST /SC MINUTE /RU %user% /RP "%pw%" /MO 5 /SD %today%  /ST %soon%  /TR C:\%maindir%\%subdir%\MIREmailNotificationTask%road%\tcore.ImageReview.ScheduledTasks.MIREmailNotificationTask.exe >> ScheduledTasksResults.log 2>&1
psexec -accepteula \\%SERVER% cmd /c  C:\Windows\system32\schtasks.exe /Run /TN MIREmailNotificationTask%road%
psexec -accepteula \\%SERVER% cmd /c  C:\Windows\system32\schtasks.exe /End /TN MIREmailNotificationTask%road%
psexec -accepteula \\%SERVER% cmd /c  C:\Windows\system32\schtasks.exe /Change /TN MIREmailNotificationTask%road% /DISABLE
echo MIREmailNotificationTask%road% created
)
if %ERRORLEVEL% NEQ 0 GOTO :error

echo -----------------------------------
echo Creating VerhicleDetectionTask scheduled task....
echo -----------------------------------
if "%GENTASK%"=="VehicleDetectionTask" (
psexec -accepteula \\%SERVER% cmd /c  C:\Windows\system32\schtasks.exe /Create /F /TN VehicleDetectionTask%road%  /RL HIGHEST /SC MINUTE /RU %user% /RP "%pw%" /MO 5 /SD %today%  /ST %soon%  /TR C:\%maindir%\%subdir%\VehicleDetectionTask%road%\VehicleDetectionTask.exe >> ScheduledTasksResults.log 2>&1
psexec -accepteula \\%SERVER% cmd /c  C:\Windows\system32\schtasks.exe /Run /TN VehicleDetectionTask%road%
psexec -accepteula \\%SERVER% cmd /c  C:\Windows\system32\schtasks.exe /End /TN VehicleDetectionTask%road%
psexec -accepteula \\%SERVER% cmd /c  C:\Windows\system32\schtasks.exe /Change /TN VehicleDetectionTask%road% /DISABLE
echo VehicleDetectionTask%road% created
)
if %ERRORLEVEL% NEQ 0 GOTO :error

echo -----------------------------------
echo Creating VOTTQueueTask scheduled task....
echo -----------------------------------
if "%GENTASK%"=="VOTTQueueTask" (
psexec -accepteula \\%SERVER% cmd /c  C:\Windows\system32\schtasks.exe /Create /F /TN VOTTQueueTask%road%  /RL HIGHEST /SC MINUTE /RU %user% /RP "%pw%" /MO 3 /SD %today%  /ST %soon%   /TR C:\%maindir%\%subdir%\VOTTQueueTask%road%\tcore.ImageReview.ScheduledTasks.VOTTQueueTask.exe >> ScheduledTasksResults.log 2>&1
psexec -accepteula \\%SERVER% cmd /c  C:\Windows\system32\schtasks.exe /Run /TN VOTTQueueTask%road%
psexec -accepteula \\%SERVER% cmd /c  C:\Windows\system32\schtasks.exe /End /TN VOTTQueueTask%road%
psexec -accepteula \\%SERVER% cmd /c  C:\Windows\system32\schtasks.exe /Change /TN VOTTQueueTask%road% /DISABLE
echo VOTTQueueTask%road% created
)
if %ERRORLEVEL% NEQ 0 GOTO :error

echo -----------------------------------
echo Creating PREHOCP HighOcrConf Processor scheduled task for Image Review PREHOCP....
echo -----------------------------------
if "%GENTASK%"=="PreHighOcrConfProcessor" (
psexec -accepteula \\%SERVER% cmd /c  C:\Windows\system32\schtasks.exe /Create /F /TN PreHighOcrConfProcessor%road% /RU %user% /RP "%pw%"  /RL HIGHEST /SC MINUTE /MO 5 /SD %today%  /ST %soon%  /TR C:\%maindir%\%subdir%\PreHighOcrConfProcessor%road%\Tcore.HOCP.HighOcrConfProcessor.exe  >> ScheduledTasksResults.log 2>&1
psexec -accepteula \\%SERVER% cmd /c  C:\Windows\system32\schtasks.exe /Run /TN PreHighOcrConfProcessor%road%
psexec -accepteula \\%SERVER% cmd /c  C:\Windows\system32\schtasks.exe /End /TN PreHighOcrConfProcessor%road%
psexec -accepteula \\%SERVER% cmd /c  C:\Windows\system32\schtasks.exe /Change /TN PreHighOcrConfProcessor%road%  /DISABLE
echo PreHighOcrConfProcessor%road% Task created
)
if %ERRORLEVEL% NEQ 0 GOTO :error

echo -----------------------------------
echo Creating PREHOCP SendResults scheduled task for Image Review PREHOCP ....
echo -----------------------------------
if "%GENTASK%"=="PreHOCSendResultsProcessor" (
psexec -accepteula \\%SERVER% cmd /c  C:\Windows\system32\schtasks.exe /Create /F /TN PreHOCSendResultsProcessor%road% /RU %user% /RP "%pw%"  /RL HIGHEST /SC MINUTE /MO 5 /SD %today%  /ST %soon%  /TR C:\%maindir%\%subdir%\PreHOCSendResultsProcessor%road%\Tcore.HOCP.HOCSendResultsProcessor.exe  >> ScheduledTasksResults.log 2>&1
psexec -accepteula \\%SERVER% cmd /c  C:\Windows\system32\schtasks.exe /run /TN PreHOCSendResultsProcessor%road%
psexec -accepteula \\%SERVER% cmd /c  C:\Windows\system32\schtasks.exe /End /TN PreHOCSendResultsProcessor%road%
psexec -accepteula \\%SERVER% cmd /c  C:\Windows\system32\schtasks.exe /Change /TN PreHOCSendResultsProcessor%road%  /DISABLE
echo %road%PreHOCPSendResults Task created
)
if %ERRORLEVEL% NEQ 0 GOTO :error

GOTO END



:error
echo ------- AN ERROR OCCURED DURING THIS STEP: Create task %SERVER% %GENTASK% %road%------
exit /b 1

:END
echo -- Create task %SERVER% %GENTASK% %road% is complete