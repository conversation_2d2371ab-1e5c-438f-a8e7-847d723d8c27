@ECHO OFF

set NOW=%date:~10,4%-%date:~4,2%-%date:~7,2%
Set CURRENTDIR=%CD%
SET DEPLOYTYPE=%1
echo %DEPLOYTYPE%
set DEPLOYTYPE=%DEPLOYTYPE:~0,1%
echo %DEPLOYTYPE%
set MO<PERSON>LEPATH=%2
set SOURCEDRIVE=%3

echo %MOBILEPATH%
echo source drive is %SOURCEDRIVE%

cd %MO<PERSON>LEPATH%
%SOURCEDRIVE%:
echo %CD%
echo delete package lock file
rem del /s /q package-lock.json
call npm cache clean --force
call npm cache verify
set STEP=npminstall
echo NPM INSTALL
call npm install --cache tmp/empty-cache --force
rem call npm install
if %ERRORLEVEL% NEQ 0 GOTO :error 
echo uninstall IONIC *************
call npm uninstall ionic
echo reinstall IONIC 4.12.0 ****************
call npm install ionic@4.12.0
if %ERRORLEVEL% NEQ 0 GOTO :error 
call npm run build:PROD
if %ERRORLEVEL% NEQ 0 GOTO :error 
cd %CURRENTDIR%
goto :END

:error
echo ------- AN ERROR OCCURED DURING Inisght MOBILE BUILD ------


exit /b 1

:END
echo -- Insight Mobile Build is complete


exit /b 0
