@echo off
rem set variables
Set CURRENTDIR=%CD%
set scripts-dir=%CD%


echo ------- Your current directory is:  %CURRENTDIR% ---------

set NOW=%date:~10,4%-%date:~4,2%-%date:~7,2%
echo Deployment beginning %NOW%
echo -----------------------------------
echo Connect to Server
echo -----------------------------------
SET BASEDIR=%1
SET DEPLOYSERVER=%2
SET USER=%3
SET PASSWORD=%4
SET DEPLOYMENTDRIVE=%5
SET DEPLOYMENTDIR=%6
SET CLUSTER=%7
net use s: /delete
SET DISTDIR=%BASEDIR%\DIST

if exist "%BASEDIR%\BuildScripts.zip" ( del /s /q %BASEDIR%\BuildScripts.zip
)
if exist "%CURRENTDIR%\BuildScripts" ( del /s /q BuildScripts\* 
)
if exist "%CURRENTDIR%\BuildScripts" ( rmdir /s /q BuildScripts 
)

if exist "%BASEDIR%\TableauDeployTool.zip" ( del /s /q %BASEDIR%\TableauDeployTool.zip
)
if exist "%BASEDIR%\TableauDeployTool" ( del /s /q %BASEDIR%\TableauDeployTool\* 
)
if exist "%CURRENTDIR%\TableauDeployTool" ( rmdir /s /q %CURRENTDIR%\TableauDeployTool 
)

cd %CURRENTDIR%
SET STEP="Build Scripts creation"
mkdir BuildScripts
mkdir BuildScripts\Autobuild
mkdir BuildScripts\AppLogs
mkdir BuildScripts\AES
xcopy *.cmd BuildScripts\*
xcopy *.bat BuildScripts\*
xcopy *.dll BuildScripts\*
xcopy *.build BuildScripts\*
xcopy *.include BuildScripts\*
xcopy *.txt BuildScripts\*
xcopy Autobuild\*.* BuildScripts\Autobuild\*
xcopy AES\*.* BuildScripts\AES\*
del /s /q BuildScripts\ReportsDeployer.exe

CD %CURRENTDIR%

echo --------------------------------------------------------------------
echo Build Script preparation Completed...
echo --------------------------------------------------------------------
echo  Build Script preparation Completed

cd %CURRENTDIR%

echo --------------------------------------------------------------------
echo TableauDeployTool preparation Started...
echo --------------------------------------------------------------------
echo  TableauDeployTool preparation Started

SET STEP="Tableau Deploy Tool creation"
cd %BASEDIR%
mkdir TableauDeployTool

cd %CURRENTDIR%
xcopy ..\TableauDeployTool\* %BASEDIR%\TableauDeployTool\* /E /H /C /I

CD %CURRENTDIR%

echo --------------------------------------------------------------------
echo TableauDeployTool preparation Completed...
echo --------------------------------------------------------------------
echo  TableauDeployTool preparation Completed



GOTO END



:error
echo ------- AN ERROR OCCURED DURING THIS STEP: %STEP% ------
exit /b 1

:END