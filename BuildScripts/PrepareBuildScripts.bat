@echo off
rem set variables
Set CURRENTDIR=%CD%

echo ------- Your current directory is:  %CURRENTDIR% ---------

set NOW=%date:~10,4%-%date:~4,2%-%date:~7,2%
echo Build script prep beginning %NOW%
SET BASEDIR=%1
SET DISTDIR=%BASEDIR%\DIST

if exist "%BASEDIR%\BuildScripts.zip" ( del /s /q %BASEDIR%\BuildScripts.zip
)
if exist "%CURRENTDIR%\BuildScripts" ( del /s /q BuildScripts\* 
)
if exist "%CURRENTDIR%\BuildScripts" ( rmdir /s /q BuildScripts 
)

if exist "%BASEDIR%\TableauDeployTool.zip" ( del /s /q %BASEDIR%\TableauDeployTool.zip
)
if exist "%BASEDIR%\TableauDeployTool" ( del /s /q %BASEDIR%\TableauDeployTool\* 
)
if exist "%CURRENTDIR%\TableauDeployTool" ( rmdir /s /q %CURRENTDIR%\TableauDeployTool 
)

cd %CURRENTDIR%
SET STEP="Build Scripts creation"
mkdir BuildScripts
mkdir BuildScripts\Autobuild
mkdir BuildScripts\AppLogs
mkdir BuildScripts\AES
xcopy *.cmd BuildScripts\*
rem xcopy *.exe BuildScripts\*
xcopy *.bat BuildScripts\*
xcopy *.dll BuildScripts\*
xcopy *.build BuildScripts\*
xcopy *.include BuildScripts\*
xcopy Autobuild\*.* BuildScripts\Autobuild\*
xcopy AES\*.* BuildScripts\AES\*
xcopy *.txt BuildScripts\*
del /s /q BuildScripts\Autobuild.bat
del /s /q BuildScripts\AutobuildStart.bat
del /s /q BuildScripts\ReportsDeployer.exe

CD %CURRENTDIR%

echo --------------------------------------------------------------------
echo Build Script preparation Completed...
echo --------------------------------------------------------------------
echo  Build Script preparation Completed


cd %CURRENTDIR%

echo --------------------------------------------------------------------
echo TableauDeployTool preparation Started...
echo --------------------------------------------------------------------
echo  TableauDeployTool preparation Started

SET STEP="Tableau Deploy Tool creation"
cd %BASEDIR%
mkdir TableauDeployTool

cd %CURRENTDIR%
xcopy ..\TableauDeployTool\* %BASEDIR%\TableauDeployTool\* /E /H /C /I

CD %CURRENTDIR%

echo --------------------------------------------------------------------
echo TableauDeployTool preparation Completed...
echo --------------------------------------------------------------------
echo  TableauDeployTool preparation Completed


GOTO END


:error
echo ------- AN ERROR OCCURED DURING THIS STEP: %STEP% ------
exit /b 1

:END