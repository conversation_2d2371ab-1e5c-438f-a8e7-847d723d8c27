@ECHO OFF
ver > nul
Set CURRENTDIR=%CD%
Set BUILDDIR=%1%
set DEPLOYMENTTYPE=%2
SET EXTINTWEB=%4
SET STEP=text
cd %BUILDDIR%\CPS\%EXTINTWEB%\ClientApp
echo run webui
echo delete package lock file
rem del /s /q package-lock.json
if %ERRORLEVEL% NEQ 0 GOTO :error
set STEP=npminstall
rem call npm install

call npm install --cache tmp/empty-cache
if %ERRORLEVEL% NEQ 0 GOTO :error
SET KENDO_UI_LICENSE=eyJhbGciOiJSUzI1NiIsInR5cCI6IkxJQyJ9.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.K_sRHS27oD1JIRH_liojoM8pa4Vpt0wPWCGtlS7C06_r7d6ZIJmlZtg5ub9kgrpRKxL2BZ0jaoUoE2m5MLuvsQs5fOxRS6800sDwv4ql16LuzEbTX7kawfTj_Ebe22vXEhH2ziFzkPW7RRas5mIdaeFCUZ6Xsar16Y6qL9dPUxs9Rx5HPddp31jW7-ee7Ehy05VkCt-xmIJOhN-LK4YFJFkAPkJRCZfeQCBlf84N6y1b5Qrt_ZD1qzWxFLtkn6Edas_wc_UjlC4UvsReDGxZqsZ0rLELA8DilI10cm3jGVXtwXC85pES72YD4KfT29VXIQxYz8UXR2a8uUcE2bSBnw
echo Kendo license is %KENDO_UI_LICENSE%
echo error level is %ERRORLEVEL%
call npx kendo-ui-license activate
echo error level is %ERRORLEVEL%
if %ERRORLEVEL% NEQ 0 GOTO :error 
echo error level is %ERRORLEVEL%
set STEP=Angular
IF NOT "%DEPLOYMENTTYPE%"=="PROD" call npm run build_MAIN
if %ERRORLEVEL% NEQ 0 GOTO :error
IF "%DEPLOYMENTTYPE%"=="PROD" call npm run build_PROD
if %ERRORLEVEL% NEQ 0 GOTO :error
cd %CURRENTDIR%
goto :END

:error
echo ------- ERRORLEVEL webui returned ------
echo %ERRORLEVEL% is the errorlevel and failure is on %STEP%
cd %CURRENTDIR%
exit /b 1

:END
echo -- webui success


exit /b 0