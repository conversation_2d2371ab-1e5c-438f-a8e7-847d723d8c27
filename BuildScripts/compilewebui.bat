@ECHO OFF
ver > nul
Set CURRENTDIR=%CD%
Set BUILDDIR=%1%
SET STEP=text
echo error level is %ERRORLEVEL%
if %ERRORLEVEL% NEQ 0 GOTO :error 
echo set node
rem call nvm use 12.16.1
call nvm list
echo run webui
cd %BUILDDIR%\IntegrityImageReview\WebLayer\WebSite\ClientApp
call npm cache clean --force
call npm cache verify
set STEP=npminstall
call npm install --cache tmp/empty-cache --force
if %ERRORLEVEL% NEQ 0 GOTO :error
set STEP=Angular
call npm run build_DEV
if %ERRORLEVEL% NEQ 0 GOTO :error
rem call npm cache clean --force
rmdir node_modules /s /q
cd %CURRENTDIR%
goto :END

:error
echo ------- ERRORLEVEL webui returned ------
echo %ERRORLEVEL% is the errorlevel and failure is on %STEP%
cd %CURRENTDIR%
exit /b 1

:END
echo -- webui success


exit /b 0