@ECHO OFF

set NOW=%date:~10,4%-%date:~4,2%-%date:~7,2%
Set CURRENTDIR=%CD%
SET MOMSSERVER=%1
SET PROJECT=%2
SET ENVTYPE=%3
SET DEPLOYMENTDRIVE=%4
SET DEPLOYMENTDIR=%5
SET INSIGHTMOBILESERVER=%6
SET INSIGHTMOBILEFEATURE=%7
SET USER=%8
SET PASSWORD=%9
SHIFT
SHIFT
SHIFT
SHIFT
SHIFT
SHIFT
SHIFT
SHIFT
SHIFT
SET DBSERVER=%1
SET REPORTSERVER=%2
SET RPTDRIVE=%3
SET DBDRIVE=%4
SET CREATEDB=%5

echo %MOMSSERVER%


echo MOMS/Insight Unzip is starting at %DEPLOYMENTDIR% 
echo ---------------------------------- 

:MOMS_Delete_Current_Unzips
SET STEP="Remove all current directories from MOMS server Unzipped"
echo Step %STEP% commencing...  --------- 
echo remove resources
echo rpt server is %REPORTSERVER%
echo deployment drive is %DEPLOYMENTDRIVE%
if exist \\%REPORTSERVER%\%DEPLOYMENTDRIVE%$\Deployment\%DEPLOYMENTDIR%\Resources (rmdir /s /q \\%REPORTSERVER%\%DEPLOYMENTDRIVE%$\Deployment\%DEPLOYMENTDIR%\Resources)
if %ERRORLEVEL% NEQ 0 GOTO :error  
echo remove reports
if exist \\%REPORTSERVER%\%DEPLOYMENTDRIVE%$\Deployment\%DEPLOYMENTDIR%\MOMSReports (rmdir /s /q \\%REPORTSERVER%\%DEPLOYMENTDRIVE%$\Deployment\%DEPLOYMENTDIR%\MOMSReports)
if %ERRORLEVEL% NEQ 0 GOTO :error  
echo remove distribution
if exist \\%MOMSSERVER%\%DEPLOYMENTDRIVE%$\Deployment\%DEPLOYMENTDIR%\Distribution (rmdir /s /q  \\%MOMSSERVER%\%DEPLOYMENTDRIVE%$\Deployment\%DEPLOYMENTDIR%\Distribution)
if %ERRORLEVEL% NEQ 0 GOTO :error  
echo Step %STEP% completed...  --------- 


:UnzipMOMS
SET STEP="Unzip MOMS/Insight"
echo Step %STEP% commencing...  --------- 
unzip -ou \\%REPORTSERVER%\%DEPLOYMENTDRIVE%$\Deployment\%DEPLOYMENTDIR%\Resources.zip -d  \\%REPORTSERVER%\%DEPLOYMENTDRIVE%$\Deployment\%DEPLOYMENTDIR%\Resources\
if %ERRORLEVEL% NEQ 0 GOTO :error
unzip -ou \\%REPORTSERVER%\%DEPLOYMENTDRIVE%$\Deployment\%DEPLOYMENTDIR%\MOMSReports.zip -d \\%REPORTSERVER%\%DEPLOYMENTDRIVE%$\Deployment\%DEPLOYMENTDIR%\MOMSReports\
if %ERRORLEVEL% NEQ 0 GOTO :error
rem call Autobuild\do_unzip_locrem1.cmd Distribution.zip %DEPLOYMENTDRIVE%:\Deployment\%DEPLOYMENTDIR%\ -q %MOMSSERVER%
unzip -ou \\%MOMSSERVER%\%DEPLOYMENTDRIVE%$\Deployment\%DEPLOYMENTDIR%\Distribution.zip -d \\%MOMSSERVER%\%DEPLOYMENTDRIVE%$\Deployment\%DEPLOYMENTDIR%\
if %ERRORLEVEL% NEQ 0 GOTO :error
echo Step %STEP% completed...  --------- 



if "%CREATEDB%"=="N" GOTO :INSIGHTMOBILEUnZIP
:UnzipDB
SET STEP="UnzipDB"
if exist \\%DBSERVER%\%DEPLOYMENTDRIVE%$\Deployment\%DEPLOYMENTDIR%\LocalDBScripts (rmdir /s /q \\%DBSERVER%\%DEPLOYMENTDRIVE%$\Deployment\%DEPLOYMENTDIR%\LocalDBScripts)
if %ERRORLEVEL% NEQ 0 GOTO :error  
echo remove database
if exist \\%DBSERVER%\%DEPLOYMENTDRIVE%$\Deployment\%DEPLOYMENTDIR%\Database (rmdir /s /q \\%DBSERVER%\%DEPLOYMENTDRIVE%$\Deployment\%DEPLOYMENTDIR%\Database)
if %ERRORLEVEL% NEQ 0 GOTO :error  
unzip -ou \\%DBSERVER%\%DEPLOYMENTDRIVE%$\Deployment\%DEPLOYMENTDIR%\DBScripts.zip -d \\%DBSERVER%\%DEPLOYMENTDRIVE%$\Deployment\%DEPLOYMENTDIR%\
if %ERRORLEVEL% NEQ 0 GOTO :error
unzip -ou \\%DBSERVER%\%DEPLOYMENTDRIVE%$\Deployment\%DEPLOYMENTDIR%\MOMSDatabase.zip -d \\%DBSERVER%\%DEPLOYMENTDRIVE%$\Deployment\%DEPLOYMENTDIR%\
if %ERRORLEVEL% NEQ 0 GOTO :error 


:INSIGHTMOBILEUnZIP
if "%INSIGHTMOBILEFEATURE%"=="false" GOTO END

SET STEP="UnzipINSIGHTMOBILE"
echo remove INSIGHTMOBILE directory if exist 
psexec -accepteula \\%INSIGHTMOBILESERVER% cmd /c if exist %DEPLOYMENTDRIVE%:\Deployment-InsightMobile\%DEPLOYMENTDIR%\InsightMobile (rmdir /s /q %DEPLOYMENTDRIVE%:\Deployment-InsightMobile\%DEPLOYMENTDIR%\InsightMobile)
if %ERRORLEVEL% NEQ 0 GOTO :error 
call Autobuild\do_unzip_locrem1.cmd InsightMobile.zip %DEPLOYMENTDRIVE%:\Deployment-InsightMobile\%DEPLOYMENTDIR%\ -q %INSIGHTMOBILESERVER%
if %ERRORLEVEL% NEQ 0 GOTO :error 
echo Step %STEP% completed...  --------- 
GOTO END


GOTO END

:error
echo ------- AN ERROR OCCURED DURING THIS STEP: %STEP% ------
echo ------- AN ERROR OCCURED DURING THIS STEP: %STEP% ------ 
rem NAnt.exe -buildfile:MOMS_build.build -logger:NAnt.Core.MailLogger -logfile:AppLogs\build.log autobuild-error
exit /b 1

:END
net use q: /delete
net use y: /delete
net use x: /delete
echo -- Unzip Packages of MOMS/INSIGHT is complete
echo -- Unzip Packages of MOMS/INSIGHT is complete 
rem NAnt.exe -buildfile:MOMS_build.build -logger:NAnt.Core.MailLogger -logfile:AppLogs\build.log autobuild-complete
exit /b 0


