@ECHO OFF
verify > nul
set NOW=%date:~10,4%-%date:~4,2%-%date:~7,2%
Set CURRENTDIR=%CD%
SET APP01SERVER=%1
SET PROJECT=%2
SET ENVTYPE=%3
SET DEPLOYMENTDRIVE=%4
SET DEPLOYMENTDIR=%5
SET CLUSTER=%6
SET APP02SERVER=%7
SET WEB01SERVER=%8
SET WEB02SERVER=%9
SHIFT
SHIFT
SHIFT
SHIFT
SHIFT
SHIFT
SHIFT
SHIFT
SHIFT
SET EXT01SERVER=%1
SET DEPLOYMENTSERVER=%2
SET TABLEAUFEATURE=%3
SET APP03SERVER=%4

set fullstamp=%YYYY%-%MM%-%DD%_%HH%-%Min%-%Sec%

echo %APP01SERVER%


echo CPS Unzip is starting at %fullstamp% 
echo ---------------------------------- 

:CPS_Delete_Current_unzips
SET STEP="Remove all unzipped files"
echo Step %STEP% commencing...  --------- 
psexec -accepteula \\%APP01SERVER% cmd /c if exist %DEPLOYMENTDRIVE%:\Deployment-CPS\%DEPLOYMENTDIR%\APP01 (rmdir /s /q %DEPLOYMENTDRIVE%:\Deployment-CPS\%DEPLOYMENTDIR%\APP01)
if %ERRORLEVEL% NEQ 0 GOTO :error 
psexec -accepteula \\%WEB01SERVER% cmd /c if exist %DEPLOYMENTDRIVE%:\Deployment-CPS\%DEPLOYMENTDIR%\WEB01 (rmdir /s /q %DEPLOYMENTDRIVE%:\Deployment-CPS\%DEPLOYMENTDIR%\WEB01)
if %ERRORLEVEL% NEQ 0 GOTO :error 
psexec -accepteula \\%EXT01SERVER% cmd /c if exist %DEPLOYMENTDRIVE%:\Deployment-CPS\%DEPLOYMENTDIR%\EXT01 (rmdir /s /q %DEPLOYMENTDRIVE%:\Deployment-CPS\%DEPLOYMENTDIR%\EXT01)
if %ERRORLEVEL% NEQ 0 GOTO :error 

psexec -accepteula \\%DEPLOYMENTSERVER% cmd /c if exist %DEPLOYMENTDRIVE%:\Deployment-CPS\%DEPLOYMENTDIR%\TABLEAU (rmdir /s /q %DEPLOYMENTDRIVE%:\Deployment-CPS\%DEPLOYMENTDIR%\TABLEAU)
if %ERRORLEVEL% NEQ 0 GOTO :error 
psexec -accepteula \\%DEPLOYMENTSERVER% cmd /c if exist %DEPLOYMENTDRIVE%:\Staging-CPS\TableauDeployTool (rmdir /s /q %DEPLOYMENTDRIVE%:\Staging-CPS\TableauDeployTool)
if %ERRORLEVEL% NEQ 0 GOTO :error 
echo Step %STEP% completed..  --------- 

:UnzipCPS
SET STEP="Unzip CPS APP01"
echo Step %STEP% commencing...  --------- 
rem call Autobuild\do_unzip_locrem1.cmd APP01.zip %DEPLOYMENTDRIVE%:\Deployment-CPS\%DEPLOYMENTDIR%\ -qo %APP01SERVER%
rem call powershell.exe Invoke-Command -ComputerName  %APP01SERVER% -ScriptBlock {Expand-Archive %DEPLOYMENTDRIVE%:\Deployment-CPS\%DEPLOYMENTDIR%\APP01.zip -DestinationPath %DEPLOYMENTDRIVE%:\Deployment-CPS\%DEPLOYMENTDIR%\}
rem Note: Change to use 7zip to zip and unzip the App01 folder with a password to avoid the firewall issue
call powershell.exe Invoke-Command -ComputerName  %APP01SERVER% -ScriptBlock {7z x -mf- %DEPLOYMENTDRIVE%:\Deployment-CPS\%DEPLOYMENTDIR%\APP01.7z -o%DEPLOYMENTDRIVE%:\Deployment-CPS\%DEPLOYMENTDIR%\ -pSECRET} 
if %ERRORLEVEL% NEQ 0 GOTO :error


SET STEP="Unzip CPS WEB01"
echo Step %STEP% commencing...  --------- 

call powershell.exe Invoke-Command -ComputerName  %WEB01SERVER% -ScriptBlock {Expand-Archive %DEPLOYMENTDRIVE%:\Deployment-CPS\%DEPLOYMENTDIR%\WEB01.zip -DestinationPath %DEPLOYMENTDRIVE%:\Deployment-CPS\%DEPLOYMENTDIR%\}
if %ERRORLEVEL% NEQ 0 GOTO :error

SET STEP="Unzip CPS EXT01"
echo Step %STEP% commencing...  --------- 
if exist \\%EXT01SERVER%\%DEPLOYMENTDRIVE%$\Deployment-CPS\%DEPLOYMENTDIR%\EXT01.zip (call powershell.exe Invoke-Command -ComputerName  %EXT01SERVER% -ScriptBlock {Expand-Archive %DEPLOYMENTDRIVE%:\Deployment-CPS\%DEPLOYMENTDIR%\EXT01.zip -DestinationPath %DEPLOYMENTDRIVE%:\Deployment-CPS\%DEPLOYMENTDIR%\})

SET STEP="Unzip CPS TABLEAU"
echo Step %STEP% commencing...  --------- 
IF "%TABLEAUFEATURE%"=="true" ( call Autobuild\do_unzip_locrem1.cmd TABLEAU.zip %DEPLOYMENTDRIVE%:\Staging-CPS\%DEPLOYMENTDIR%\ -qo %DEPLOYMENTSERVER% )
if %ERRORLEVEL% NEQ 0 GOTO :error

SET STEP="Unzip CPS TableauDeployTool"
echo Step %STEP% commencing...  --------- 
if exist \\%DEPLOYMENTSERVER%\%DEPLOYMENTDRIVE%$\Staging-CPS\TableauDeployTool.zip (call powershell.exe Invoke-Command -ComputerName  %DEPLOYMENTSERVER% -ScriptBlock {Expand-Archive %DEPLOYMENTDRIVE%:\Staging-CPS\TableauDeployTool.zip -DestinationPath %DEPLOYMENTDRIVE%:\Staging-CPS\})
if %ERRORLEVEL% NEQ 0 GOTO :error


IF NOT "%ENVTYPE%"=="PROD" (GOTO CHECKCLUSTER) 
psexec -accepteula \\%APP03SERVER% cmd /c if exist %DEPLOYMENTDRIVE%:\Deployment-CPS\%DEPLOYMENTDIR%\APP03 (rmdir /s /q %DEPLOYMENTDRIVE%:\Deployment-CPS\%DEPLOYMENTDIR%\APP03)
if %ERRORLEVEL% NEQ 0 GOTO :error 

call powershell.exe Invoke-Command -ComputerName  %APP03SERVER% -ScriptBlock {7z x -mf- %DEPLOYMENTDRIVE%:\Deployment-CPS\%DEPLOYMENTDIR%\APP03.7z -o%DEPLOYMENTDRIVE%:\Deployment-CPS\%DEPLOYMENTDIR%\ -pSECRET} 
if %ERRORLEVEL% NEQ 0 GOTO :error

echo Step %STEP% completed...  --------- 

CD %CURRENTDIR%
:CHECKCLUSTER
echo ---------------------------------
echo if Cluster is True deploy to APP02
echo ---------------------------------
if %CLUSTER%==true GOTO deploy02
GOTO END

:deploy02
SET STEP="Remove all unzipped files from App02"
echo Step %STEP% commencing...  --------- 
psexec -accepteula \\%APP02SERVER% cmd /c if exist %DEPLOYMENTDRIVE%:\Deployment-CPS\%DEPLOYMENTDIR%\APP02 (rmdir /s /q %DEPLOYMENTDRIVE%:\Deployment-CPS\%DEPLOYMENTDIR%\APP02)
echo Step %STEP% commencing...  --------- 
SET STEP="Remove all unzipped files from Web02"
psexec -accepteula \\%WEB02SERVER% cmd /c if exist %DEPLOYMENTDRIVE%:\Deployment-CPS\%DEPLOYMENTDIR%\WEB02 (rmdir /s /q %DEPLOYMENTDRIVE%:\Deployment-CPS\%DEPLOYMENTDIR%\WEB02)
if %ERRORLEVEL% NEQ 0 GOTO :error
echo Step %STEP% completed..  --------- 

:UnzipCPS02
SET STEP="Unzip CPS APP02"
echo Step %STEP% commencing...  --------- 
rem call powershell.exe Invoke-Command -ComputerName  %APP02SERVER% -ScriptBlock {Expand-Archive %DEPLOYMENTDRIVE%:\Deployment-CPS\%DEPLOYMENTDIR%\APP02.zip -DestinationPath %DEPLOYMENTDRIVE%:\Deployment-CPS\%DEPLOYMENTDIR%\}
rem Note: Change to use 7zip to zip and unzip the App02 folder with a password to avoid the firewall issue
call powershell.exe Invoke-Command -ComputerName  %APP02SERVER% -ScriptBlock {7z x -mf- %DEPLOYMENTDRIVE%:\Deployment-CPS\%DEPLOYMENTDIR%\APP02.7z -o%DEPLOYMENTDRIVE%:\Deployment-CPS\%DEPLOYMENTDIR%\ -pSECRET}
if %ERRORLEVEL% NEQ 0 GOTO :error


SET STEP="Unzip CPS WEB02"
echo Step %STEP% commencing...  --------- 

call powershell.exe Invoke-Command -ComputerName  %WEB02SERVER% -ScriptBlock {Expand-Archive %DEPLOYMENTDRIVE%:\Deployment-CPS\%DEPLOYMENTDIR%\WEB02.zip -DestinationPath %DEPLOYMENTDRIVE%:\Deployment-CPS\%DEPLOYMENTDIR%\}
if %ERRORLEVEL% NEQ 0 GOTO :error

GOTO END


:error
echo ------- AN ERROR OCCURED DURING THIS STEP: %STEP% ------
exit /b 1

:END
echo -- Unzip Packages of CPS is complete
exit /b 0


