@ECHO OFF

set NOW=%date:~10,4%-%date:~4,2%-%date:~7,2%
Set CURRENTDIR=%CD%
SET FPSERVER=%1
SET APPSERVER=%2
SET PROJECT=%3
SET ENVTYPE=%4
SET DEPLOYMENTDRIVE=%5
SET DEPLOYMENTDIR=%6
SET FINGERPRINTFEATURE=%7
SET HOCPFEATURE=%8
SET WEBSERVER=%9
SHIFT
SHIFT
SHIFT
SHIFT
SHIFT
SHIFT
SHIFT
SHIFT
SHIFT
SET DBSERVER=%1
SET CLUSTER=%2
SET APP02SERVER=%3
SET WEB02SERVER=%4
SET FP02SERVER=%5
SET TRANSSERVER=%6
SET TRANS02SERVER=%7
SET IFXSERVER=%8
SET IFX02SERVER=%9
SHIFT
SHIFT
SHIFT
SHIFT
SHIFT
SHIFT
SHIFT
SHIFT
SHIFT
SET HUMANREADSERVER=%1
SET HRFEATURE=%2
SET USER=%3
SET PASSWORD=%4
SET HOCP01SERVER=%5
SET HOCP02SERVER=%6
SET IRR01SERVER=%7
SET IRR02SERVER=%8
SET HOCPFEATURE=%9
SHIFT
SHIFT
SHIFT
SHIFT
SHIFT
SHIFT
SHIFT
SHIFT
SHIFT
SET IRRFEATURE=%1
SET TransAPIandMIRIntServiceFeature=%2
SET PREHOCPFEATURE=%3
SET PREHOCP01SERVER=%4
SET PREHOCP02SERVER=%5
SET QFREETASKSERVER=%6
SET DEPLOYMENTSERVER=%7

set fullstamp=%YYYY%-%MM%-%DD%_%HH%-%Min%-%Sec%

echo %FPSERVER%
echo %HOCPSERVER%
echo %DBSERVER%
echo IFX server: %IFXSERVER% 
echo IFX02 server: %IFX02SERVER% 
echo HR server: %HUMANREADSERVER% 
echo IMG review task: %IMGREVIEWTASK% 
echo HR feature: %HRFEATURE% 

echo IRR01Server: %IRR01SERVER%
echo QFREETASKSERVER: %QFREETASKSERVER%

net use p: /delete
echo ------- map drive ----------
net use p: \\%WEBSERVER%\%DEPLOYMENTDRIVE%$ %PASSWORD% /user:%USER%

echo Image Review Unzip is starting at %fullstamp%
echo ---------------------------------- 
if "%HRFEATURE%"=="true" GOTO HRFEATURE

:MIR_Delete_Current_unzips
SET STEP="Remove all _bu directories from Image Review server Unzipped"
echo Step %STEP% commencing...  --------- 
psexec -accepteula \\%FPSERVER% cmd /c if exist %DEPLOYMENTDRIVE%:\Deployment-ImageReview\%DEPLOYMENTDIR%\FP01 (rmdir /s /q %DEPLOYMENTDRIVE%:\Deployment-ImageReview\%DEPLOYMENTDIR%\FP01)
if %ERRORLEVEL% NEQ 0 GOTO :error  
psexec -accepteula \\%HOCP01SERVER% cmd /c if exist %DEPLOYMENTDRIVE%:\Deployment-ImageReview\%DEPLOYMENTDIR%\HOCP01\HOCP (rmdir /s /q %DEPLOYMENTDRIVE%:\Deployment-ImageReview\%DEPLOYMENTDIR%\HOCP01\HOCP)
if %ERRORLEVEL% NEQ 0 GOTO :error 
psexec -accepteula \\%PREHOCP01SERVER% cmd /c if exist %DEPLOYMENTDRIVE%:\Deployment-ImageReview\%DEPLOYMENTDIR%\PREHOCP01\PREHOCP (rmdir /s /q %DEPLOYMENTDRIVE%:\Deployment-ImageReview\%DEPLOYMENTDIR%\PREHOCP01\PREHOCP)
if %ERRORLEVEL% NEQ 0 GOTO :error 
psexec -accepteula \\%IRR01SERVER% cmd /c if exist %DEPLOYMENTDRIVE%:\Deployment-ImageReview\%DEPLOYMENTDIR%\IRR01\IRR (rmdir /s /q %DEPLOYMENTDRIVE%:\Deployment-ImageReview\%DEPLOYMENTDIR%\IRR01\IRR)
if %ERRORLEVEL% NEQ 0 GOTO :error 
psexec -accepteula \\%APPSERVER% cmd /c if exist %DEPLOYMENTDRIVE%:\Deployment-ImageReview\%DEPLOYMENTDIR%\APP01\MIR (rmdir /s /q %DEPLOYMENTDRIVE%:\Deployment-ImageReview\%DEPLOYMENTDIR%\APP01\MIR)
psexec -accepteula \\%TRANSSERVER% cmd /c if exist %DEPLOYMENTDRIVE%:\Deployment-ImageReview\%DEPLOYMENTDIR%\TRANS01\MIR (rmdir /s /q %DEPLOYMENTDRIVE%:\Deployment-ImageReview\%DEPLOYMENTDIR%\TRANS01\MIR)
psexec -accepteula \\%APPSERVER% cmd /c if exist %DEPLOYMENTDRIVE%:\Deployment-ImageReview\%DEPLOYMENTDIR%\APP01 (rmdir /s /q %DEPLOYMENTDRIVE%:\Deployment-ImageReview\%DEPLOYMENTDIR%\APP01)
if %ERRORLEVEL% NEQ 0 GOTO :error
if exist \\%WEBSERVER%\%DEPLOYMENTDRIVE%$\Deployment-ImageReview\%DEPLOYMENTDIR%\WEB01 (rmdir /s /q \\%WEBSERVER%\%DEPLOYMENTDRIVE%$\Deployment-ImageReview\%DEPLOYMENTDIR%\WEB01)
if %ERRORLEVEL% NEQ 0 GOTO :error
psexec -accepteula \\%DBSERVER% cmd /c if exist %DEPLOYMENTDRIVE%:\Deployment-ImageReview\%DEPLOYMENTDIR%\RPT01 (rmdir /s /q %DEPLOYMENTDRIVE%:\Deployment-ImageReview\%DEPLOYMENTDIR%\RPT01)
if %ERRORLEVEL% NEQ 0 GOTO :error
if exist \\%WEBSERVER%\%DEPLOYMENTDRIVE%$\Deployment-ImageReview\%DEPLOYMENTDIR%\AES (rmdir /s /q \%WEBSERVER%\%DEPLOYMENTDRIVE%$\Deployment-ImageReview\%DEPLOYMENTDIR%\AES)
if %ERRORLEVEL% NEQ 0 GOTO :error 
psexec -accepteula \\%APPSERVER% cmd /c if exist %DEPLOYMENTDRIVE%:\Deployment-ImageReview\%DEPLOYMENTDIR%\AES (rmdir /s /q %DEPLOYMENTDRIVE%:\Deployment-ImageReview\%DEPLOYMENTDIR%\AES)
if %ERRORLEVEL% NEQ 0 GOTO :error 
psexec -accepteula \\%HOCP01SERVER% cmd /c if exist %DEPLOYMENTDRIVE%:\Deployment-ImageReview\%DEPLOYMENTDIR%\AES (rmdir /s /q %DEPLOYMENTDRIVE%:\Deployment-ImageReview\%DEPLOYMENTDIR%\AES)
if %ERRORLEVEL% NEQ 0 GOTO :error 
psexec -accepteula \\%PREHOCP01SERVER% cmd /c if exist %DEPLOYMENTDRIVE%:\Deployment-ImageReview\%DEPLOYMENTDIR%\AES (rmdir /s /q %DEPLOYMENTDRIVE%:\Deployment-ImageReview\%DEPLOYMENTDIR%\AES)
if %ERRORLEVEL% NEQ 0 GOTO :error 
psexec -accepteula \\%IRR01SERVER% cmd /c if exist %DEPLOYMENTDRIVE%:\Deployment-ImageReview\%DEPLOYMENTDIR%\AES (rmdir /s /q %DEPLOYMENTDRIVE%:\Deployment-ImageReview\%DEPLOYMENTDIR%\AES)
if %ERRORLEVEL% NEQ 0 GOTO :error 
psexec -accepteula \\%FPSERVER% cmd /c if exist %DEPLOYMENTDRIVE%:\Deployment-ImageReview\%DEPLOYMENTDIR%\AES (rmdir /s /q %DEPLOYMENTDRIVE%:\Deployment-ImageReview\%DEPLOYMENTDIR%\AES)
if %ERRORLEVEL% NEQ 0 GOTO :error 
psexec -accepteula \\%TRANSSERVER% cmd /c if exist %DEPLOYMENTDRIVE%:\Deployment-ImageReview\%DEPLOYMENTDIR%\AES (rmdir /s /q %DEPLOYMENTDRIVE%:\Deployment-ImageReview\%DEPLOYMENTDIR%\AES)
if %ERRORLEVEL% NEQ 0 GOTO :error 
psexec -accepteula \\%DEPLOYMENTSERVER% cmd /c if exist %DEPLOYMENTDRIVE%:\Staging-ImageReview\%DEPLOYMENTDIR%\TABLEAU (rmdir /s /q %DEPLOYMENTDRIVE%:\Staging-ImageReview\%DEPLOYMENTDIR%\TABLEAU)
if %ERRORLEVEL% NEQ 0 GOTO :error 
psexec -accepteula \\%DEPLOYMENTSERVER% cmd /c if exist %DEPLOYMENTDRIVE%:\Staging-ImageReview\TableauDeployTool (rmdir /s /q %DEPLOYMENTDRIVE%:\Staging-ImageReview\TableauDeployTool)
if %ERRORLEVEL% NEQ 0 GOTO :error 

echo Step %STEP% completed..  --------- 

:Unzip
SET STEP="Unzip Files"
echo Step %STEP% commencing...  --------- 
if "%FINGERPRINTFEATURE%"=="true" (call Autobuild\do_unzip_locrem1.cmd FP01.zip %DEPLOYMENTDRIVE%:\Deployment-ImageReview\%DEPLOYMENTDIR%\ -q %FPSERVER% )
if %ERRORLEVEL% NEQ 0 GOTO :error
SET STEP="Unzip APP"
call Autobuild\do_unzip_locrem1.cmd APP01.zip %DEPLOYMENTDRIVE%:\Deployment-ImageReview\%DEPLOYMENTDIR%\ -qo %APPSERVER%
if %ERRORLEVEL% NEQ 0 GOTO :error
SET STEP="Unzip HOCP"
if "%HOCPFEATURE%"=="true"  call Autobuild\do_unzip_locrem1.cmd HOCP01.zip %DEPLOYMENTDRIVE%:\Deployment-ImageReview\%DEPLOYMENTDIR%\ -qo %HOCP01SERVER%
if %ERRORLEVEL% NEQ 0 GOTO :error
SET STEP="Unzip PREHOCP"
if "%PREHOCPFEATURE%"=="true"  call Autobuild\do_unzip_locrem1.cmd PREHOCP01.zip %DEPLOYMENTDRIVE%:\Deployment-ImageReview\%DEPLOYMENTDIR%\ -qo %PREHOCP01SERVER%
if %ERRORLEVEL% NEQ 0 GOTO :error
SET STEP="Unzip IRR"
if "%IRRFEATURE%"=="true"  call Autobuild\do_unzip_locrem1.cmd IRR01.zip %DEPLOYMENTDRIVE%:\Deployment-ImageReview\%DEPLOYMENTDIR%\ -qo %IRR01SERVER%
if %ERRORLEVEL% NEQ 0 GOTO :error
SET STEP="Unzip WEB"
rem call Autobuild\do_unzip_locrem1.cmd WEB01.zip %DEPLOYMENTDRIVE%:\Deployment-ImageReview\%DEPLOYMENTDIR%\ -qo %WEBSERVER%
unzip -ou p:\Deployment-ImageReview\%DEPLOYMENTDIR%\WEB01.zip -d  p:\Deployment-ImageReview\%DEPLOYMENTDIR%\
if %ERRORLEVEL% NEQ 0 GOTO :error
SET STEP="Unzip RPT"
call Autobuild\do_unzip_locrem1.cmd RPT01.zip %DEPLOYMENTDRIVE%:\Deployment-ImageReview\%DEPLOYMENTDIR%\ -qo %DBSERVER%
if %ERRORLEVEL% NEQ 0 GOTO :error
SET STEP="Unzip APP"
call Autobuild\do_unzip_locrem1.cmd AES.zip %DEPLOYMENTDRIVE%:\Deployment-ImageReview\%DEPLOYMENTDIR%\ -qo  %APPSERVER%
if %ERRORLEVEL% NEQ 0 GOTO :error
SET STEP="Unzip TRANS"
if "%TransAPIandMIRIntServiceFeature%"=="true" (call Autobuild\do_unzip_locrem1.cmd TRANS01.zip %DEPLOYMENTDRIVE%:\Deployment-ImageReview\%DEPLOYMENTDIR%\ -qo  %TRANSSERVER%)
if %ERRORLEVEL% NEQ 0 GOTO :error
SET STEP="Unzip WEB AES"
if not "%APPSERVER%"=="%WEBSERVER%" (unzip -ou p:\Deployment-ImageReview\%DEPLOYMENTDIR%\AES.zip -d  p:\Deployment-ImageReview\%DEPLOYMENTDIR%\
)
SET STEP="Unzip FP AES"
if not "%APPSERVER%"=="%FPSERVER%" (call Autobuild\do_unzip_locrem1.cmd AES.zip %DEPLOYMENTDRIVE%:\Deployment-ImageReview\%DEPLOYMENTDIR%\ -qo  %FPSERVER%
)
if %ERRORLEVEL% NEQ 0 GOTO :error
SET STEP="Unzip TRANS AES"
if not "%APPSERVER%"=="%TRANSSERVER%" (call Autobuild\do_unzip_locrem1.cmd AES.zip %DEPLOYMENTDRIVE%:\Deployment-ImageReview\%DEPLOYMENTDIR%\ -qo  %TRANSSERVER%
)
if %ERRORLEVEL% NEQ 0 GOTO :error
SET STEP="Unzip HOCP AES"
if not "%APPSERVER%"=="%HOCP01SERVER%" (call Autobuild\do_unzip_locrem1.cmd AES.zip %DEPLOYMENTDRIVE%:\Deployment-ImageReview\%DEPLOYMENTDIR%\ -qo  %HOCP01SERVER%
)
if %ERRORLEVEL% NEQ 0 GOTO :error
SET STEP="Unzip PREHOCP AES"
if not "%APPSERVER%"=="%PREHOCP01SERVER%" (call Autobuild\do_unzip_locrem1.cmd AES.zip %DEPLOYMENTDRIVE%:\Deployment-ImageReview\%DEPLOYMENTDIR%\ -qo  %PREHOCP01SERVER%
)
if %ERRORLEVEL% NEQ 0 GOTO :error
SET STEP="Unzip IRR AES"
if not "%APPSERVER%"=="%IRR01SERVER%" (call Autobuild\do_unzip_locrem1.cmd AES.zip %DEPLOYMENTDRIVE%:\Deployment-ImageReview\%DEPLOYMENTDIR%\ -qo  %IRR01SERVER%
)
if %ERRORLEVEL% NEQ 0 GOTO :error
SET STEP="Copy QFree File to QFree Task server"
if not "%APPSERVER%"=="%QFREETASKSERVER%" if exist \\%APPSERVER%\%DEPLOYMENTDRIVE%$\Deployment-ImageReview\%DEPLOYMENTDIR%\APP01\MIR\ImageReview\Plugins (xcopy /Y /s /i \\%APPSERVER%\%DEPLOYMENTDRIVE%$\Deployment-ImageReview\%DEPLOYMENTDIR%\APP01\MIR\ImageReview\Plugins \\%QFREETASKSERVER%\%DEPLOYMENTDRIVE%$\Deployment-ImageReview\%DEPLOYMENTDIR%\Plugins
)
if %ERRORLEVEL% NEQ 0 GOTO :error
SET STEP="Unzip TABLEAU"
echo Step %STEP% commencing...  --------- 
if exist \\%DEPLOYMENTSERVER%\%DEPLOYMENTDRIVE%$\Staging-ImageReview\%DEPLOYMENTDIR%\TABLEAU.zip (call powershell.exe Invoke-Command -ScriptBlock {Expand-Archive %DEPLOYMENTDRIVE%:\Staging-ImageReview\%DEPLOYMENTDIR%\TABLEAU.zip -DestinationPath %DEPLOYMENTDRIVE%:\Staging-ImageReview\%DEPLOYMENTDIR%\})
if %ERRORLEVEL% NEQ 0 GOTO :error
SET STEP="Unzip MIR TableauDeployTool"
echo Step %STEP% commencing...  --------- 
if exist \\%DEPLOYMENTSERVER%\%DEPLOYMENTDRIVE%$\Staging-ImageReview\TableauDeployTool.zip (call powershell.exe Invoke-Command -ScriptBlock {Expand-Archive %DEPLOYMENTDRIVE%:\Staging-ImageReview\TableauDeployTool.zip -DestinationPath %DEPLOYMENTDRIVE%:\Staging-ImageReview\})
if %ERRORLEVEL% NEQ 0 GOTO :error


net use p: /delete

if %ERRORLEVEL% NEQ 0 GOTO :error

CD %CURRENTDIR%
echo ---------------------------------
echo if Cluster is True deploy to APP02
echo ---------------------------------
if %CLUSTER%==true GOTO deploy02
GOTO END

:deploy02
:MIR_Delete_Current_unzips02
SET STEP="Remove all _bu directories from Image Review server Unzipped"
echo Step %STEP% commencing...  --------- 
psexec -accepteula \\%FP02SERVER% cmd /c if exist %DEPLOYMENTDRIVE%:\Deployment-ImageReview\%DEPLOYMENTDIR%\FP02 (rmdir /s /q %DEPLOYMENTDRIVE%:\Deployment-ImageReview\%DEPLOYMENTDIR%\FP02)
if %ERRORLEVEL% NEQ 0 GOTO :error  
psexec -accepteula \\%HOCP02SERVER% cmd /c if exist %DEPLOYMENTDRIVE%:\Deployment-ImageReview\%DEPLOYMENTDIR%\HOCP02\HOCP (rmdir /s /q %DEPLOYMENTDRIVE%:\Deployment-ImageReview\%DEPLOYMENTDIR%\HOCP02\HOCP)
if %ERRORLEVEL% NEQ 0 GOTO :error 
psexec -accepteula \\%PREHOCP02SERVER% cmd /c if exist %DEPLOYMENTDRIVE%:\Deployment-ImageReview\%DEPLOYMENTDIR%\PREHOCP02\PREHOCP (rmdir /s /q %DEPLOYMENTDRIVE%:\Deployment-ImageReview\%DEPLOYMENTDIR%\PREHOCP02\PREHOCP)
if %ERRORLEVEL% NEQ 0 GOTO :error 
psexec -accepteula \\%IRR02SERVER% cmd /c if exist %DEPLOYMENTDRIVE%:\Deployment-ImageReview\%DEPLOYMENTDIR%\IRR02\IRR (rmdir /s /q %DEPLOYMENTDRIVE%:\Deployment-ImageReview\%DEPLOYMENTDIR%\IRR02\IRR)
if %ERRORLEVEL% NEQ 0 GOTO :error 
psexec -accepteula \\%APP02SERVER% cmd /c if exist %DEPLOYMENTDRIVE%:\Deployment-ImageReview\%DEPLOYMENTDIR%\APP02\MIR (rmdir /s /q %DEPLOYMENTDRIVE%:\Deployment-ImageReview\%DEPLOYMENTDIR%\APP02\MIR)
if %ERRORLEVEL% NEQ 0 GOTO :error
psexec -accepteula \\%APP02SERVER% cmd /c if exist %DEPLOYMENTDRIVE%:\Deployment-ImageReview\%DEPLOYMENTDIR%\APP02 (rmdir /s /q %DEPLOYMENTDRIVE%:\Deployment-ImageReview\%DEPLOYMENTDIR%\APP02)
if %ERRORLEVEL% NEQ 0 GOTO :error
psexec -accepteula \\%WEB02SERVER% cmd /c if exist %DEPLOYMENTDRIVE%:\Deployment-ImageReview\%DEPLOYMENTDIR%\WEB02 (rmdir /s /q %DEPLOYMENTDRIVE%:\Deployment-ImageReview\%DEPLOYMENTDIR%\WEB02)
if %ERRORLEVEL% NEQ 0 GOTO :error
psexec -accepteula \\%WEB02SERVER% cmd /c if exist %DEPLOYMENTDRIVE%:\Deployment-ImageReview\%DEPLOYMENTDIR%\AES (rmdir /s /q %DEPLOYMENTDRIVE%:\Deployment-ImageReview\%DEPLOYMENTDIR%\AES)
if %ERRORLEVEL% NEQ 0 GOTO :error 
psexec -accepteula \\%APP02SERVER% cmd /c if exist %DEPLOYMENTDRIVE%:\Deployment-ImageReview\%DEPLOYMENTDIR%\AES (rmdir /s /q %DEPLOYMENTDRIVE%:\Deployment-ImageReview\%DEPLOYMENTDIR%\AES)
if %ERRORLEVEL% NEQ 0 GOTO :error 
psexec -accepteula \\%FP02SERVER% cmd /c if exist %DEPLOYMENTDRIVE%:\Deployment-ImageReview\%DEPLOYMENTDIR%\AES (rmdir /s /q %DEPLOYMENTDRIVE%:\Deployment-ImageReview\%DEPLOYMENTDIR%\AES)
if %ERRORLEVEL% NEQ 0 GOTO :error 
psexec -accepteula \\%TRANS02SERVER% cmd /c if exist %DEPLOYMENTDRIVE%:\Deployment-ImageReview\%DEPLOYMENTDIR%\AES (rmdir /s /q %DEPLOYMENTDRIVE%:\Deployment-ImageReview\%DEPLOYMENTDIR%\AES)
if %ERRORLEVEL% NEQ 0 GOTO :error 
psexec -accepteula \\%TRANS02SERVER% cmd /c if exist %DEPLOYMENTDRIVE%:\Deployment-ImageReview\%DEPLOYMENTDIR%\TRANS02\MIR (rmdir /s /q %DEPLOYMENTDRIVE%:\Deployment-ImageReview\%DEPLOYMENTDIR%\TRANS02\MIR)


echo Step %STEP% completed..  --------- 

:Unzip02
SET STEP="Unzip Fingerprint"
echo Step %STEP% commencing...  --------- 
if "%FINGERPRINTFEATURE%"=="true" (call Autobuild\do_unzip_locrem1.cmd FP02.zip %DEPLOYMENTDRIVE%:\Deployment-ImageReview\%DEPLOYMENTDIR%\ -q %FP02SERVER% )
if %ERRORLEVEL% NEQ 0 GOTO :error
SET STEP="Unzip APP"
call Autobuild\do_unzip_locrem1.cmd APP02.zip %DEPLOYMENTDRIVE%:\Deployment-ImageReview\%DEPLOYMENTDIR%\ -qo %APP02SERVER%
if %ERRORLEVEL% NEQ 0 GOTO :error
SET STEP="Unzip HOCP02"
if "%HOCPFEATURE%"=="true"  call Autobuild\do_unzip_locrem1.cmd HOCP02.zip %DEPLOYMENTDRIVE%:\Deployment-ImageReview\%DEPLOYMENTDIR%\ -qo %HOCP02SERVER%
if %ERRORLEVEL% NEQ 0 GOTO :error
SET STEP="Unzip PREHOCP02"
if "%PREHOCPFEATURE%"=="true"  call Autobuild\do_unzip_locrem1.cmd PREHOCP02.zip %DEPLOYMENTDRIVE%:\Deployment-ImageReview\%DEPLOYMENTDIR%\ -qo %PREHOCP02SERVER%
if %ERRORLEVEL% NEQ 0 GOTO :error
SET STEP="Unzip IRR02"
if "%IRRFEATURE%"=="true"  call Autobuild\do_unzip_locrem1.cmd IRR02.zip %DEPLOYMENTDRIVE%:\Deployment-ImageReview\%DEPLOYMENTDIR%\ -qo %IRR02SERVER%
if %ERRORLEVEL% NEQ 0 GOTO :error
SET STEP="Unzip WEB02"
call Autobuild\do_unzip_locrem1.cmd WEB02.zip %DEPLOYMENTDRIVE%:\Deployment-ImageReview\%DEPLOYMENTDIR%\ -qo %WEB02SERVER%
if %ERRORLEVEL% NEQ 0 GOTO :error
SET STEP="Unzip TRANS02"
if "%TransAPIandMIRIntServiceFeature%"=="true" (call Autobuild\do_unzip_locrem1.cmd TRANS02.zip %DEPLOYMENTDRIVE%:\Deployment-ImageReview\%DEPLOYMENTDIR%\ -qo  %TRANS02SERVER%)
if %ERRORLEVEL% NEQ 0 GOTO :error
SET STEP="Unzip AES"
call Autobuild\do_unzip_locrem1.cmd AES.zip %DEPLOYMENTDRIVE%:\Deployment-ImageReview\%DEPLOYMENTDIR%\ -qo  %APP02SERVER%
if %ERRORLEVEL% NEQ 0 GOTO :error

if %ERRORLEVEL% NEQ 0 GOTO :error
if not "%APP02SERVER%"=="%WEB02SERVER%" (call Autobuild\do_unzip_locrem1.cmd AES.zip %DEPLOYMENTDRIVE%:\Deployment-ImageReview\%DEPLOYMENTDIR%\ -qo  %WEB02SERVER%
)
if %ERRORLEVEL% NEQ 0 GOTO :error
if not "%APP02SERVER%"=="%FP02SERVER%" (call Autobuild\do_unzip_locrem1.cmd AES.zip %DEPLOYMENTDRIVE%:\Deployment-ImageReview\%DEPLOYMENTDIR%\ -qo  %FP02SERVER%
)
if %ERRORLEVEL% NEQ 0 GOTO :error
if not "%APP02SERVER%"=="%TRANS02SERVER%" (call Autobuild\do_unzip_locrem1.cmd AES.zip %DEPLOYMENTDRIVE%:\Deployment-ImageReview\%DEPLOYMENTDIR%\ -qo  %TRANS02SERVER%
)
if %ERRORLEVEL% NEQ 0 GOTO :error
SET STEP="Unzip HOCP AES"
if not "%APP02SERVER%"=="%HOCP02SERVER%" (call Autobuild\do_unzip_locrem1.cmd AES.zip %DEPLOYMENTDRIVE%:\Deployment-ImageReview\%DEPLOYMENTDIR%\ -qo  %HOCP02SERVER%
)
if %ERRORLEVEL% NEQ 0 GOTO :error
SET STEP="Unzip PREHOCP AES"
if not "%HOCP02SERVER%"=="%PREHOCP02SERVER%" (call Autobuild\do_unzip_locrem1.cmd AES.zip %DEPLOYMENTDRIVE%:\Deployment-ImageReview\%DEPLOYMENTDIR%\ -qo  %PREHOCP02SERVER%
)
if %ERRORLEVEL% NEQ 0 GOTO :error
SET STEP="Unzip IRR AES"
if not "%APP02SERVER%"=="%IRR02SERVER%" (call Autobuild\do_unzip_locrem1.cmd AES.zip %DEPLOYMENTDRIVE%:\Deployment-ImageReview\%DEPLOYMENTDIR%\ -qo  %IRR02SERVER%
)
if %ERRORLEVEL% NEQ 0 GOTO :error

CD %CURRENTDIR%
GOTO END

:HRFEATURE
if "%HRFEATURE%" == "true" if exist \\%HUMANREADSERVER%\%DEPLOYMENTDRIVE%$\Deployment-ImageReview\%DEPLOYMENTDIR%\AES (rmdir /s /q \\%HUMANREADSERVER%\%DEPLOYMENTDRIVE%$\Deployment-ImageReview\%DEPLOYMENTDIR%\AES)
if %ERRORLEVEL% NEQ 0 GOTO :error 

if "%HRFEATURE%" == "true" if exist \\%HUMANREADSERVER%\%DEPLOYMENTDRIVE%$\Deployment-ImageReview\%DEPLOYMENTDIR%\HR01 (rmdir /s /q \\%HUMANREADSERVER%\%DEPLOYMENTDRIVE%$\Deployment-ImageReview\%DEPLOYMENTDIR%\HR01)
if %ERRORLEVEL% NEQ 0 GOTO :error 

SET STEP="Unzip HR AES"
if "%HRFEATURE%" == "true"  (call Autobuild\do_unzip_locrem1.cmd AES.zip %DEPLOYMENTDRIVE%:\Deployment-ImageReview\%DEPLOYMENTDIR%\ -qo  %HUMANREADSERVER%
)
if %ERRORLEVEL% NEQ 0 GOTO :error
SET STEP="Unzip HR"
if "%HRFEATURE%" == "true"  (call Autobuild\do_unzip_locrem1.cmd HR01.zip %DEPLOYMENTDRIVE%:\Deployment-ImageReview\%DEPLOYMENTDIR%\ -qo  %HUMANREADSERVER%
)
rem echo unzip HR with powershell... be patient
rem if "%HRFEATURE%" == "true"  (call powershell.exe Invoke-Command -ComputerName  %HUMANREADSERVER% -ScriptBlock {Expand-Archive %DEPLOYMENTDRIVE%:\Deployment-ImageReview\%DEPLOYMENTDIR%\HR01.zip -DestinationPath %DEPLOYMENTDRIVE%:\Deployment-ImageReview\%DEPLOYMENTDIR%\} 
rem )
if %ERRORLEVEL% NEQ 0 GOTO :error

CD %CURRENTDIR%
GOTO END


:error
echo ------- AN ERROR OCCURED DURING THIS STEP: %STEP% ------
echo ------- AN ERROR OCCURED DURING THIS STEP: %STEP% ------ 
rem NAnt.exe -buildfile:ImageReview_build.build -logger:NAnt.Core.MailLogger -logfile:AppLogs\build.log autobuild-error
exit /b 1

:END
echo -- Unzip Packages of Image Review is complete
echo -- Unzip Packages of Image Reviewis complete 
rem NAnt.exe -buildfile:ImageReview_build.build -logger:NAnt.Core.MailLogger -logfile:AppLogs\build.log autobuild-complete
exit /b 0


