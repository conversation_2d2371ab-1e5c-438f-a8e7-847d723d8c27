<?xml version="1.0" encoding="utf-8" ?>

<project name="CPS" >


    <!-- GET target deployment environment from the user -->
	<include buildfile="UserInput.include"/>
	<include buildfile="BUILD.include"/>
	<include buildfile="ENVIRONMENT.include"/>
    <include buildfile="..\Common\KEY.include"/>
	<property name="env" value="${deployment.environment}"/>
	
  	
	<!-- Include environment specific properties -->
	<echo message="****** Deployment Target is CPS ${env} *******" />	

	<!-- Filters all environment specific variables like servers, db, etc -->
	<target name="filter-config-file" description="Filters all environment specific variables like servers, db, etc">
		<property name="MailLogger.failure.subject" value="CPS ${deployment.environment} ${deployment.type}  - Filter step failed" />
		<property name="MailLogger.success.subject" value="CPS ${deployment.environment} ${deployment.type}  - Step 3 Configuration Successfully Completed." />
			<call target="filter-CPS"/>
			<if test="${deployment.type=='PROD'}">	
				<exec program="fnr-Logging.bat" failonerror="true" >
				<arg value="${dist.dir}" />
				<arg value="${AARFeature}" />
				</exec>
			</if>
			<if test="${DATAANALYTICFeature=='true'}">
				<call target="filter-data-analytics"/>
			</if>
			<!--  Replace the project's web.config file if there is any -->
			<copy todir="${dist.dir}\WEB01\CPS\WebUI" overwrite="true" failonerror="false">
					<fileset basedir="${build.dir}\CPS\WebUI\Projects\${deployment.environment}\${deployment.type}" />
			</copy>					
			<if test="${ClusterFeature=='true'}">	
				<delete dir="${web02.dist.dir}" failonerror="false" />
				<delete dir="${app02.dist.dir}" failonerror="false" />
				<copy todir="${base.dir}\DIST\APP02">
				<fileset basedir="${base.dir}\DIST\APP01" />
				</copy>	
				<copy todir="${base.dir}\DIST\WEB02">
				<fileset basedir="${base.dir}\DIST\WEB01" />
				</copy>	
				<exec program="fnr-CPS-DNAMatch.bat" failonerror="true" >
					<arg value="${dist.dir}" />
				</exec>	
				<exec program="ReplaceUtil.exe" failonerror="true">
					<arg value="${base.dir}\DIST\APP02\DM\API" />
					<arg value="json"/>
					<arg value="app01" />
					<arg value="app02" />
					<arg value="ALL"/>
				</exec>
				
				<exec program="fnr-CPS-APP02.bat" failonerror="true" >
					<arg value="${dist.dir}" />
				</exec>	
				
				<if test="${deployment.environment=='CBDTP'}">	
				<if test="${deployment.type=='PROD'}">	
					<copy todir="${base.dir}\DIST\APP03\DM">
						<fileset basedir="${base.dir}\DIST\APP01\DM" />
					</copy>
					<copy todir="${base.dir}\DIST\APP03\VRG">
						<fileset basedir="${base.dir}\DIST\APP01\VRG" />
					</copy>
				<exec program="fnr-CPS-APP03.bat" failonerror="true" >
					<arg value="${dist.dir}" />	
				</exec>
				<exec program="ReplaceUtil.exe" failonerror="true">
					<arg value="${base.dir}\DIST\APP03\DM\API" />
					<arg value="json"/>
					<arg value="app01" />
					<arg value="app03" />
					<arg value="ALL"/>
				</exec>
				<exec program="fnr-CPS-APP03.bat" failonerror="true" >
					<arg value="${dist.dir}" />
				</exec>					
				</if>
			</if>
			</if>
			<call target="filter-key" />
	</target>

	<!-- Filter the CPS environment specific strings -->
	<target name="filter-CPS" description="Filter environment specific variables like servers, db, etc">	
		<exec program="ReplaceUtil.exe" failonerror="false">
			<arg value="${ext01.dist.dir}" />
			<arg value="json"/>
			<arg value="Data Source=cps-db-server;Initial Catalog=ICD;Integrated Security=True;Pooling=False;Max Pool Size=200;MultipleActiveResultSets=True" />
			<arg value="${db.aar.connection.string}" />
			<arg value="ALL"/>
		</exec>
		<exec program="ReplaceUtil.exe" failonerror="false">
			<arg value="${ext01.dist.dir}" />
			<arg value="json"/>
			<arg value="Data Source=rpt-servername;Initial Catalog=RPT;Integrated Security=True;Pooling=False;Max Pool Size=200;MultipleActiveResultSets=True" />
			<arg value="${db.rpt.connection.string}" />
			<arg value="ALL"/>
		</exec>
		<exec program="ReplaceUtil.exe" failonerror="false">
			<arg value="${ext01.dist.dir}" />
			<arg value="json"/>
			<arg value="Data Source=raas-server;Initial Catalog=ICD;Integrated Security=True;Pooling=False;Max Pool Size=200;MultipleActiveResultSets=True" />
			<arg value="${db.aar.connection.string}" />
			<arg value="ALL"/>
		</exec>
		
		<if test="${DNAMatchFeature=='true'}">	
			<exec program="ReplaceUtil.exe" failonerror="false">
				<arg value="${app01.dnamatch.service.json}" />
				<arg value="json"/>
				<arg value="database=CCD; server=db-server; Integrated Security=true; Min Pool Size=1; Max Pool Size=100; Connect Timeout=120;" />
				<arg value="${db.ccd.connection.string}" />
				<arg value="ALL"/>
			</exec>
			<exec program="ReplaceUtil.exe" failonerror="false">
				<arg value="${app01.dnamatch.service.json}" />
				<arg value="json"/>
				<arg value="database=DNA; server=db-server; Integrated Security=true; Min Pool Size=1; Max Pool Size=100; Connect Timeout=120;" />
				<arg value="${db.dna.connection.string}" />
				<arg value="ALL"/>
			</exec>
			<exec program="ReplaceUtil.exe" failonerror="false">
				<arg value="${app01.dnamatch.service.json}" />
				<arg value="json"/>
				<arg value="database=DET; server=insight-db-server; Integrated Security=true; Min Pool Size=1; Max Pool Size=100; Connect Timeout=120;" />
				<arg value="${db.det.connection.string}" />
				<arg value="ALL"/>
			</exec>
		</if>
		
		<!-- Replace Tableau Domain -->
		<exec program="fnr-CPS.bat" failonerror="true" >
		<arg value="${dist.dir}" />
		<arg value="${tableau.domain}" />
		<arg value="${ContraFlowMaxCount}" />
		<arg value="${TXPValidatorEnabled}" />
		<arg value="${StorageBasePath}" />
		<arg value="${DMSerilogLoggingLevel}" />
		<arg value="${retainedFileCountLimit}" />
		<arg value="${IncludeDeviceDataEventsInCsv}" />
		<arg value="${SendPlatesToVRG}" />	
		<arg value="${RequestUri}" />	
		<arg value="${RestrictedToMinimumLevel}" />	
		<arg value="${DMBufferedPathFormat}" />	
		<arg value="${IMSBufferedPathFormat}" />	
		<arg value="${CFTSBufferedPathFormat}" />	
		<arg value="${cps.ims.keyexpiration}" />	
		<arg value="${ims.dist.dir}" />
		<arg value="${DetectionMessageTimeToLive}" />	
		<arg value="${DNAMatchingMessageTimeToLive}" />	
		<arg value="${VRGMessageTimeToLive}" />			
		<arg value="${DMLogDirectory}" />	
		<arg value="${DMNFSStorageBasePath}" />	
		<arg value="${DMDetectionMessagesLogPath}" />	
		<arg value="${DMInvalidDetectionMsgLogPath}" />	
		<arg value="${DMInvalidDetectionCSVLogPath}" />	
		<arg value="${DMDetectionFilesMaximumSize}" />	
		<arg value="${DMDetectionCSVFilesLocation}" />	
		<arg value="${DMCSVFileNameSuffix}" />	
		<arg value="${DMFailedCsvStorageDirectory}" />	
		<arg value="${IMSDetectionMessagesLogPath}" />	
		<arg value="${deployment.type}" />	
		<arg value="${ctfs.log.dir}" />	
		<arg value="${DMVWorkerFeature}" />	
		<arg value="${MirTablePendingPollWaitDuration}" />	
		<arg value="${MirTablePendingNoDataPollWaitDuration}" />	
		</exec>
		
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${dist.dir}" />
			<arg value="json"/>
			<arg value="Database=CCD;Server=localhost;Trusted_Connection=true;Min Pool Size=1;Max Pool Size=100;Connect Timeout=120" />
			<arg value="${db.ccd.connection.string}" />
			<arg value="ALL"/>
		</exec>
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${ext01.dist.dir}" />
			<arg value="json"/>
			<arg value="Database=CCD;Server=localhost;Trusted_Connection=true;Min Pool Size=1;Max Pool Size=100;Connect Timeout=120" />
			<arg value="${db.ccd.connection.string}" />
			<arg value="ALL"/>
		</exec>
	
		<!-- WebAPI JSON -->
		<exec program="ReplaceUtil.exe" failonerror="true">
		
			<arg value="${app01.webapi.json}" />
			<arg value="json"/>
			<arg value="Database=CPS;Server=localhost;Trusted_Connection=True;Min Pool Size=1;Max Pool Size=100;Connect Timeout=120" />
			<arg value="${db.connection.string}" />
			<arg value="ALL"/>
		</exec>
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${app01.webapi.json}" />
			<arg value="json"/>
			<arg value="Database=TRIP;Server=rpt-servername;Trusted_Connection=true;TrustServerCertificate=true;Min Pool Size=1;Max Pool Size=100;Connect Timeout=120" />
			<arg value="${db.trip.connection.string}" />
			<arg value="ALL"/>
		</exec>
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${app01.webapi.json}" />
			<arg value="json"/>
			<arg value="Max Pool Size=100;Connect Timeout=120" />
			<arg value="Max Pool Size=100;Connect Timeout=120;TrustServerCertificate=true" />
			<arg value="ALL"/>
		</exec>
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${app01.webapi.json}" />
			<arg value="json"/>
			<arg value="Server=localhost" />
			<arg value="Server=${db.listener}" />
			<arg value="ALL"/>
		</exec>
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${dist.dir}" />
			<arg value="json"/>
			<arg value="rpt-servername" />
			<arg value="${rpt.listener}" />
			<arg value="ALL"/>
		</exec>
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${app01.webapi.json}" />
			<arg value="json"/>
			<arg value="d:\\CPSlogs" />
			<arg value="${log.dir}" />
			<arg value="ALL"/>
		</exec>
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${app01.webapi.json}" />
			<arg value="json"/>
			<arg value="SecretTokenValue" />
			<arg value="${tableau.key}" />
			<arg value="ALL"/>
		</exec>	
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${app01.webapi.json}" />
			<arg value="json"/>
			<arg value="TableauPAT" />
			<arg value="${tableau.PersonalAccessTokenName}" />
			<arg value="ALL"/>
		</exec>		
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${app01.webapi.json}" />
			<arg value="json"/>
			<arg value="CPS-TableauAppUser" />
	 		<arg value="${tableau.TableauUserName}" />
			<arg value="ALL"/>
		</exec>
		<if test="${SSLFeature=='false'}">
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${app01.webapi.json}" />
			<arg value="json"/>
			<arg value="http://txp-servername:7006" />
			<arg value="http://${transportal.web.alias}:7006" />
			<arg value="ALL"/>
		</exec>	
		</if>		
		<if test="${SSLFeature=='true'}">
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${app01.webapi.json}" />
			<arg value="json"/>
			<arg value="http://txp-servername:7006" />
			<arg value="https://${transportal.web.alias}:4436" />
			<arg value="ALL"/>
		</exec>	
		</if>	
		<exec program="fnr-TableauTask-json.bat" failonerror="true">
			<arg value="${app01.webapi.json}" />
			<arg value="${tableau.content.url}" />
		</exec>		
			
		
		<!-- Active Directory json -->
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${app01.activedirectory.json}" />
			<arg value="json"/>
			<arg value="Database=CPS;Server=localhost;Trusted_Connection=True;Min Pool Size=1;Max Pool Size=100;Connect Timeout=120" />
			<arg value="${db.connection.string}" />
			<arg value="ALL"/>
		</exec>
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${app01.activedirectory.json}" />
			<arg value="json"/>
			<arg value="Database=CCD;Server=localhost;Trusted_Connection=True;Min Pool Size=1;Max Pool Size=100;Connect Timeout=120" />
			<arg value="${db.ccd.connection.string}" />
			<arg value="ALL"/>
		</exec>
		
	<if test="${SimulatorFeature=='true'}">	
	<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${app01.image-filter-simulator.json}" />
			<arg value="json"/>
			<arg value="http://localhost" />
			<arg value="http://${web.cluster.alias}" />
			<arg value="ALL"/>
		</exec>
	</if>
	
			
	<!--IMS API-->
	<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${ims.api.json}" />
			<arg value="json"/>
			<arg value="cps-db-server" />
			<arg value="${db.listener}" />
			<arg value="ALL"/>
	</exec>
	<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${ims.api.json}" />
			<arg value="json"/>
			<arg value="cps-rabbitmq-server" />
			<arg value="${rabbitmq.cluster}" />
			<arg value="ALL"/>
	</exec>
	<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${ims.api.json}" />
			<arg value="json"/>
			<arg value="cps-insight-server" />
			<arg value="${insight.web.alias}" />
			<arg value="ALL"/>
	</exec>
	<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${ims.api.json}" />
			<arg value="json"/>
			<arg value="cps-isilon-ims-namespace" />
			<arg value="${cps.isilon.ims.namespace}" />
			<arg value="ALL"/>
	</exec>
	<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${ims.api.json}" />
			<arg value="json"/>
			<arg value="cps-s3-clientid" />
			<arg value="${cps-s3-clientid}" />
			<arg value="ALL"/>
	</exec>
	<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${ims.api.json}" />
			<arg value="json"/>
			<arg value="cps-s3-clientsecret" />
			<arg value="${cps-s3-clientsecret}" />
			<arg value="ALL"/>
	</exec>
	<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${ims.api.json}" />
			<arg value="json"/>
			<arg value="cps-s3-primary-bucket" />
			<arg value="${cps-s3-primary-bucket}" />
			<arg value="ALL"/>
	</exec>
	<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${ims.api.json}" />
			<arg value="json"/>
			<arg value="cps-s3-primary-server" />
			<arg value="${cps-s3-primary-server}" />
			<arg value="ALL"/>
	</exec>
	<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${ims.api.json}" />
			<arg value="json"/>
			<arg value="cps-isilon-type" />
			<arg value="${cps-isilon-type}" />
			<arg value="ALL"/>
	</exec>
	
	<!-- DM API -->
	<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${app01.dm.api.json}" />
			<arg value="json"/>
			<arg value="cps-db-server" />
			<arg value="${db.listener}" />
			<arg value="ALL"/>
	</exec>
	<exec program="ReplaceUtil.exe" failonerror="true">
		<arg value="${app01.dm.api.json}" />
		<arg value="json"/>
		<arg value="Max Pool Size=100;Connect Timeout=120" />
		<arg value="Max Pool Size=100;Connect Timeout=120;TrustServerCertificate=true" />
		<arg value="ALL"/>
	</exec>
	<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${app01.dm.api.json}" />
			<arg value="json"/>
			<arg value="cps-rabbitmq-server" />
			<arg value="${rabbitmq.cluster}" />
			<arg value="ALL"/>
	</exec>
	<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${app01.dm.api.json}" />
			<arg value="json"/>
			<arg value="cps-insight-server" />
			<arg value="${insight.web.alias}" />
			<arg value="ALL"/>
	</exec>
	<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${app01.dm.api.json}" />
			<arg value="json"/>
			<arg value="C:\\Logs\\DM-logs-.log" />
			<arg value="${dm.log.dir}\\DM-logs-.log" />
			<arg value="ALL"/>
	</exec>
	<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${app01.dm.api.json}" />
			<arg value="json"/>
			<arg value="D:\\Logs\\IMS-logs-.log" />
			<arg value="${dm.log.dir}\\DM-logs-.log" />
			<arg value="ALL"/>
	</exec>
	<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${app01.dm.api.json}" />
			<arg value="json"/>
			<arg value="C:\\Logs\\IMS-logs-.log" />
			<arg value="${dm.log.dir}\\DM-logs-.log" />
			<arg value="ALL"/>
	</exec>
	<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${app01.dm.api.json}" />
			<arg value="json"/>
			<arg value="D:\\Logs\\Buffer" />
			<arg value="${dm.log.dir}\\Buffer" />
			<arg value="ALL"/>
	</exec>
	<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${app01.dm.api.json}" />
			<arg value="json"/>
			<arg value="C:\\Logs\\Buffer" />
			<arg value="${dm.log.dir}\\Buffer" />
			<arg value="ALL"/>
	</exec>
	<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${app01.dm.api.json}" />
			<arg value="json"/>
			<arg value="cps-isilon-dm-namespace" />
			<arg value="${cps.isilon.dm.namespace}" />
			<arg value="ALL"/>
	</exec>
	<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${app01.dm.api.json}" />
			<arg value="json"/>
			<arg value="cps-detectioncsv-archive" />
			<arg value="${dm.archive.dir}\\DMArchive" />
			<arg value="ALL"/>
	</exec>
	<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${app01.dm.api.json}" />
			<arg value="json"/>
			<arg value="cps-ignore-moms-messages" />
			<arg value="${cps.ignore.moms.messages}" />
			<arg value="ALL"/>
	</exec>
	<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${app01.dm.api.json}" />
			<arg value="json"/>
			<arg value="C:\\Logs\\DM\\" />
			<arg value="${DMDetectionMessagesLogPath}" />
			<arg value="ALL"/>
	</exec>
	
	
	<!-- VRG Service -->
	<if test="${VRGFeature=='true'}">	
		<exec program="ReplaceUtil.exe" failonerror="true">
				<arg value="${app01.vrg.service.json}" />
				<arg value="json"/>
				<arg value="cps-db-server" />
				<arg value="${db.listener}" />
				<arg value="ALL"/>
		</exec>
		<exec program="ReplaceUtil.exe" failonerror="true">
				<arg value="${app01.vrg.service.json}" />
				<arg value="json"/>
				<arg value="cps-rabbitmq-server" />
				<arg value="${rabbitmq.cluster}" />
				<arg value="ALL"/>
		</exec>
		<exec program="ReplaceUtil.exe" failonerror="true">
				<arg value="${app01.vrg.service.json}" />
				<arg value="json"/>
				<arg value="insight-server" />
				<arg value="http://${insight.web.alias}:5002" />
				<arg value="ALL"/>
		</exec>
		<exec program="ReplaceUtil.exe" failonerror="true">
				<arg value="${app01.vrg.service.json}" />
				<arg value="json"/>
				<arg value="C:\\Logs\\VRG-logs-.log" />
				<arg value="${vrg.log.dir}\\VRG-logs-.log" />
				<arg value="ALL"/>
		</exec>
	</if>
		
	<!-- CPS DNAMatch Service -->
	<if test="${DNAMatchFeature=='true'}">	
	    <if test="${DNAMatchCPSVersion=='true'}">	
			<exec program="ReplaceUtil.exe" failonerror="true">
					<arg value="${app01.dnamatch.service.json}" />
					<arg value="json"/>
					<arg value="cps-db-server" />
					<arg value="${db.listener}" />
					<arg value="ALL"/>
			</exec>
			<exec program="ReplaceUtil.exe" failonerror="true">
					<arg value="${app01.dnamatch.service.json}" />
					<arg value="json"/>
					<arg value="cps-rabbitmq-server" />
					<arg value="${rabbitmq.cluster}" />
					<arg value="ALL"/>
			</exec>
			<exec program="ReplaceUtil.exe" failonerror="true">
					<arg value="${app01.dnamatch.service.json}" />
					<arg value="json"/>
					<arg value="C:\\Logs\\DNAMatch-logs-.log" />
					<arg value="${dnamatch.log.dir}\\DNAMatch-logs-.log" />
					<arg value="ALL"/>
			</exec>
		</if>
	</if>
	
	<!-- Common DNAMatch Service -->
	<if test="${DNAMatchFeature=='true'}">	
	    <if test="${DNAMatchCommonVersion=='true'}">	
			<exec program="ReplaceUtil.exe" failonerror="true">
					<arg value="${app01.dnamatch.service.json}" />
					<arg value="json"/>
					<arg value="=db-server" />
					<arg value="=${db.listener}" />
					<arg value="ALL"/>
			</exec>
			<exec program="ReplaceUtil.exe" failonerror="true">
					<arg value="${app01.dnamatch.service.json}" />
					<arg value="json"/>
					<arg value="rabbitmq-server" />
					<arg value="${rabbitmq.cluster}" />
					<arg value="ALL"/>
			</exec>
			<exec program="ReplaceUtil.exe" failonerror="true">
				<arg value="${app01.dnamatch.service.json}" />
				<arg value="json"/>
				<arg value="insight-server" />
				<arg value="http://${insight.web.alias}:5002" />
				<arg value="ALL"/>
		    </exec>
			
			<exec program="ReplaceUtil.exe" failonerror="true">
					<arg value="${app01.dnamatch.service.json}" />
					<arg value="json"/>
					<arg value="D:\\Logs\\DNAMatch_.log" />
					<arg value="${dnamatch.log.dir}\\DNAMatch_.log" />
					<arg value="ALL"/>
			</exec>
		</if>
	</if>
		
	<!-- CFTS Service -->
	<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${app01.cfts.service.json}" />
			<arg value="json"/>
			<arg value="cps-cfts-archive-path" />
			<arg value="${cfts.archive.dir}" />
			<arg value="ALL"/>
	</exec>
	<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${app01.cfts.service.json}" />
			<arg value="json"/>
			<arg value="cps-insight-server" />
			<arg value="${insight.web.alias}" />
			<arg value="ALL"/>
	</exec>
	<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${app01.cfts.service.json}" />
			<arg value="json"/>
			<arg value="cps-db-server" />
			<arg value="${db.listener}" />
			<arg value="ALL"/>
	</exec>
	
	<!-- TransComService Service -->
	<if test="${TransComServiceFeature=='true'}">	
		<exec program="ReplaceUtil.exe" failonerror="true">
				<arg value="${app01.transcom.service.json}" />
				<arg value="json"/>
				<arg value="cps-db-server" />
				<arg value="${db.listener}" />
				<arg value="ALL"/>
		</exec>
		<exec program="ReplaceUtil.exe" failonerror="true">
				<arg value="${app01.transcom.service.json}" />
				<arg value="json"/>
				<arg value="cps-rabbitmq-server" />
				<arg value="${rabbitmq.cluster}" />
				<arg value="ALL"/>
		</exec>
		<exec program="ReplaceUtil.exe" failonerror="true">
				<arg value="${app01.transcom.service.json}" />
				<arg value="json"/>
				<arg value="D:\\Logs\\TransComService.log" />
				<arg value="${transcom.log.dir}\\TransComService.log" />
				<arg value="ALL"/>
		</exec>	
	</if>
		
		<!-- CPS Email notiifcation json -->
		<exec program="ReplaceUtil.exe" failonerror="false">
			<arg value="${app01.cpsemailnotification.json}" />
			<arg value="json"/>
			<arg value="Database=CCD;Server=localhost;Trusted_Connection=True;Min Pool Size=1;Max Pool Size=100;Connect Timeout=120" />
			<arg value="${db.ccd.connection.string}" />
			<arg value="ALL"/>
		</exec>
		<exec program="ReplaceUtil.exe" failonerror="false">
			<arg value="${app01.cpsemailnotification.json}" />
			<arg value="json"/>
			<arg value="d:\\CPSlogs\\CPSEmailNotification.log" />
			<arg value="${log.dir}\\CPSEmailNotification.log" />
			<arg value="ALL"/>
		</exec>
		
		<!-- DMVLookup Task-->
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${dist.dir}" />
			<arg value="json"/>
			<arg value="DMV-Working-Directory" />
			<arg value="${dmv.working.directory}" />
			<arg value="ALL"/>
		</exec>
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${dist.dir}" />
			<arg value="json"/>
			<arg value="DMV-Archive-Directory" />
			<arg value="${dmv.archive.directory}" />
			<arg value="ALL"/>
		</exec>
		
		<!-- Repleace in ALL JSONS -->
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${dist.dir}" />
			<arg value="json"/>
			<arg value="suppress-insight-messages" />
			<arg value="${suppress-insight-messages}" />
			<arg value="ALL"/>
		</exec>
		
		<exec program="ReplaceUtil.exe" failonerror="true">
				<arg value="${dist.dir}\EXT01\CPS" />
				<arg value="json"/>
				<arg value="=db-server" />
				<arg value="=${db.listener}" />
				<arg value="ALL"/>
			</exec>
		
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${dist.dir}" />
			<arg value="json"/>
			<arg value="rabbitmq-user" />
			<arg value="admin" />
			<arg value="ALL"/>
		</exec>
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${dist.dir}" />
			<arg value="json"/>
			<arg value="rabbitmq-pass" />
			<arg value="admin" />
			<arg value="ALL"/>
		</exec>
		
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${dist.dir}" />
			<arg value="json"/>
			<arg value="insight-user" />
			<arg value="UTSTest" />
			<arg value="ALL"/>
		</exec>
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${dist.dir}" />
			<arg value="json"/>
			<arg value="insight-pass" />
			<arg value="UT3Te3t" />
			<arg value="ALL"/>
		</exec>
		
		<!--AAR Website & NYCDOT (EXT Server), both use the same replacement value -->
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${dist.dir}" />
			<arg value="json"/>
			<arg value="Data Source=localhost;Initial Catalog=ICD;Integrated Security=True;Pooling=False;Max Pool Size=200;MultipleActiveResultSets=True" />
			<arg value="${db.aar.connection.string}" />
			<arg value="ALL"/>
		</exec>
		
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${dist.dir}" />
			<arg value="json"/>
			<arg value="Database=ICD;Server=localhost;Trusted_Connection=True;Min Pool Size=1;Max Pool Size=100;Connect Timeout=120" />
			<arg value="${db.aar.connection.string}" />
			<arg value="ALL"/>
		</exec>
		
		<!--AAR Website (EXT Server)-->
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${dist.dir}" />
			<arg value="json"/>
			<arg value="Data Source=localhost;Initial Catalog=CCD;Integrated Security=True;Pooling=False;Max Pool Size=200;MultipleActiveResultSets=True" />
			<arg value="${db.ccd.connection.string}" />
			<arg value="ALL"/>
		</exec>
		
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${dist.dir}" />
			<arg value="json"/>
			<arg value="insight-db-server" />
			<arg value="${insight.db.server}" />
			<arg value="ALL"/>
		</exec>
		
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${dist.dir}" />
			<arg value="json"/>
			<arg value="c:\\logs" />
			<arg value="${log.dir}\\logs" />
			<arg value="ALL"/>
		</exec>
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${dist.dir}" />
			<arg value="json"/>
			<arg value="d:\\logs" />
			<arg value="${log.dir}\\logs" />
			<arg value="ALL"/>
		</exec>
		
		<!-- TPI -->
		<if test="${TPIFeature=='true'}">
			<exec program="ReplaceUtil.exe" failonerror="true">
				<arg value="${ext01.dist.dir}\CPS\TPI02" />
				<arg value="json"/>
				<arg value="${log.dir}\\logs\\TPI_WebAPI_.log" />
				<arg value="${log.dir}\\logs\\TPI02_WebAPI_.log" />
				<arg value="ALL"/>
			</exec>
		</if>
		
		<!-- Workflow -->
		<if test="${WorkflowFeature=='true'}">
			<if test="${SSLFeature=='false'}">
				<exec program="ReplaceUtil.exe" failonerror="true">
					<arg value="${ext01.dist.dir}\CPS\Services\Workflow" />
					<arg value="json"/>
					<arg value="cps-rabbitmq-server" />
					<arg value="http://${rabbitmq.cluster}:15672/" />
					<arg value="ALL"/>
				</exec>	
			</if>		
			<if test="${SSLFeature=='true'}">
				<exec program="ReplaceUtil.exe" failonerror="true">
					<arg value="${ext01.dist.dir}\CPS\Services\Workflow" />
					<arg value="json"/>
					<arg value="cps-rabbitmq-server" />
					<arg value="https://${rabbitmq.cluster}:15672/" />
					<arg value="ALL"/>
				</exec>	
			</if>
			<exec program="ReplaceUtil.exe" failonerror="true">
					<arg value="${ext01.dist.dir}\CPS\Services\Workflow" />
					<arg value="json"/>
					<arg value="cps-db-server" />
					<arg value="${db.listener}" />
					<arg value="ALL"/>
			</exec>	
			<exec program="ReplaceUtil.exe" failonerror="true">
					<arg value="${ext01.dist.dir}\CPS\Services\Workflow" />
					<arg value="json"/>
					<arg value="http://ims-api:4500/api/v1/" />
					<arg value="${ims.api.workflow}" />
					<arg value="ALL"/>
			</exec>	
			<exec program="ReplaceUtil.exe" failonerror="true">
					<arg value="${ext01.dist.dir}\CPS\Services\Workflow" />
					<arg value="json"/>
					<arg value="cps-insight-server" />
					<arg value="${insight.server.workflow}" />
					<arg value="ALL"/>
			</exec>	
			<exec program="ReplaceUtil.exe" failonerror="true">
					<arg value="${ext01.dist.dir}\CPS\Services\Workflow" />
					<arg value="json"/>
					<arg value="cps-storage-base" />
					<arg value="${cps.storage.base}" />
					<arg value="ALL"/>
			</exec>	
			<exec program="ReplaceUtil.exe" failonerror="true">
					<arg value="${ext01.dist.dir}\CPS\Services\Workflow" />
					<arg value="json"/>
					<arg value="cps-raasURL" />
					<arg value="${cps.raasURL}" />
					<arg value="ALL"/>
			</exec>
		</if>
		
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${dist.dir}" />
			<arg value="json"/>
			<arg value="cps-insight-server" />
			<arg value="${insight.web.alias}" />
			<arg value="ALL"/>
		</exec>
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${dist.dir}" />
			<arg value="json"/>
			<arg value="raas-server" />
			<arg value="${db.listener}" />
			<arg value="ALL"/>
		</exec>
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${dist.dir}" />
			<arg value="config"/>
			<arg value="Development" />
			<arg value="Production" />
			<arg value="ALL"/>
		</exec>
		
		
		<!-- Generical ext01 replacements -->
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${dist.dir}" />
			<arg value="json"/>
			<arg value="Data Source=localhost;Initial Catalog=UVID;Integrated Security=True;Pooling=False;Max Pool Size=200;MultipleActiveResultSets=True" />
			<arg value="${db.uvid.connection.string}" />
			<arg value="ALL"/>
		</exec>
			<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${dist.dir}" />
			<arg value="json"/>
			<arg value="Database=UVID;Server=localhost;Trusted_Connection=True;Min Pool Size=1;Max Pool Size=100;Connect Timeout=120" />
			<arg value="${db.uvid.connection.string}" />
			<arg value="ALL"/>
		</exec>
	
		<!-- generic json replacements -->
		<exec program="ReplaceUtil.exe" failonerror="true">
				<arg value="${dist.dir}" />
				<arg value="json"/>
				<arg value="inference-server" />
				<arg value="${inference.url}" />
				<arg value="ALL"/>
		    </exec>
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${dist.dir}" />
			<arg value="json"/>
			<arg value="Database=CCD;Server=localhost;Trusted_Connection=True;Min Pool Size=1;Max Pool Size=100;Connect Timeout=120" />
			<arg value="${db.ccd.connection.string}" />
			<arg value="ALL"/>
		</exec>
	
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${dist.dir}" />
			<arg value="json"/>
			<arg value="C:\\Temp" />
			<arg value="${temp.dir}" />
			<arg value="ALL"/>
		</exec>
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${dist.dir}" />
			<arg value="json"/>
			<arg value="c:\\temp" />
			<arg value="${temp.dir}" />
			<arg value="ALL"/>
		</exec>
		<!--ftp -->
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${dist.dir}" />
			<arg value="json"/>
			<arg value="ftp-host" />
			<arg value="${ftp-host}" />
			<arg value="ALL"/>
		</exec>
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${dist.dir}" />
			<arg value="json"/>
			<arg value="ftp-user" />
			<arg value="${ftp-user}" />
			<arg value="ALL"/>
		</exec>
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${dist.dir}" />
			<arg value="json"/>
			<arg value="ftp-pw" />
			<arg value="${ftp-pwd}" />
			<arg value="ALL"/>
		</exec>
		
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${dist.dir}" />
			<arg value="json"/>
			<arg value="http://tableau-servername:8000/TableauContentUrlValue" />
			<arg value="${tableau.advanced.url}" />
			<arg value="ALL"/>
		</exec>
		
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${dist.dir}" />
			<arg value="json"/>
			<arg value="http://tableau-servername:8000/" />
			<arg value="${tableau.alias.url}" />
			<arg value="ALL"/>
		</exec>
				
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${dist.dir}" />
			<arg value="json"/>
			<arg value="TableauUserNameValue" />
			<arg value="${tableau.TableauUserName}" />
			<arg value="ALL"/>
		</exec>
		
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${dist.dir}" />
			<arg value="json"/>
			<arg value="TableauPasswordValue" />
			<arg value="${tableau.TableauPassword}" />
			<arg value="ALL"/>
		</exec>
		
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${dist.dir}" />
			<arg value="json"/>
			<arg value="TableauProjectNameValue" />
			<arg value="${tableau.ProjectName}" />
			<arg value="ALL"/>
		</exec>
		
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${dist.dir}" />
			<arg value="json"/>
			<arg value="TableauPATName" />
			<arg value="${tableau.PersonalAccessTokenName}" />
			<arg value="ALL"/>
		</exec>
		
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${dist.dir}" />
			<arg value="json"/>
			<arg value="app_param_stored_proc" />
			<arg value="${tableau.stored.proc}" />
			<arg value="ALL"/>
		</exec>
		
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${dist.dir}" />
			<arg value="json"/>
			<arg value="Database=DET;Server=localhost;Trusted_Connection=True;Min Pool Size=1;Max Pool Size=100;Connect Timeout=120" />
			<arg value="Database=DET;Server=${db.listener};Trusted_Connection=True;Min Pool Size=1;Max Pool Size=100;Connect Timeout=120" />
			<arg value="ALL"/>
		</exec>
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${dist.dir}" />
			<arg value="json"/>
			<arg value="cps-insight-server" />
			<arg value="${insight.web.alias}" />
			<arg value="ALL"/>
		</exec>
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${dist.dir}" />
			<arg value="json"/>
			<arg value="cps-db-server" />
			<arg value="${db.listener}" />
			<arg value="ALL"/>
		</exec>
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${dist.dir}" />
			<arg value="json"/>
			<arg value="C:\\Logs" />
			<arg value="${log.dir}" />
			<arg value="ALL"/>
		</exec>	
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${dist.dir}" />
			<arg value="json"/>
			<arg value="http://cps-inference-server" />
			<arg value="${inference.url}" />
			<arg value="ALL"/>
		</exec>
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${dist.dir}" />
			<arg value="json"/>
			<arg value="ims-base-url" />
			<arg value="${ims.base.url}" />
			<arg value="ALL"/>
		</exec>
		<if test="${SSLFeature=='false'}">
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${dist.dir}" />
			<arg value="json"/>
			<arg value="http://ims-api:4500" />
			<arg value="http://${ims.cluster.alias}:4500" />
			<arg value="ALL"/>
		</exec>
		<exec program="ReplaceUtil.exe" failonerror="true">
				<arg value="${dist.dir}" />
				<arg value="json"/>
				<arg value="http://dvas-api" />
				<arg value="http://${dvas.api.alias}" />
				<arg value="ALL"/>
			</exec>
			<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${dist.dir}" />
			<arg value="json"/>
			<arg value="http://dmv-server:8004" />
			<arg value="http://${ext.alias}:8004" />
			<arg value="ALL"/>
		</exec>
		</if>		
		<if test="${SSLFeature=='true'}">
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${dist.dir}" />
			<arg value="json"/>
			<arg value="http://ims-api:4500" />
			<arg value="https://${ims.cluster.alias}:5443" />
			<arg value="ALL"/>
		</exec>
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${dist.dir}" />
			<arg value="json"/>
			<arg value="http://dmv-server:8004" />
			<arg value="https://${ext.alias}:8004" />
			<arg value="ALL"/>
		</exec>
		<exec program="ReplaceUtil.exe" failonerror="true">
				<arg value="${dist.dir}" />
				<arg value="json"/>
				<arg value="http://dvas-api" />
				<arg value="https://${dvas.api.alias}" />
				<arg value="ALL"/>
			</exec>
		</if>
		<if test="${SSLFeature=='false'}">
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${dist.dir}" />
			<arg value="json"/>
			<arg value="http://app-servername:4200" />
			<arg value="http://${app.server.cluster}:4200" />
			<arg value="ALL"/>
		</exec>
		</if>		
		<if test="${SSLFeature=='true'}">
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${dist.dir}" />
			<arg value="json"/>
			<arg value="http://app-servername:4200" />
			<arg value="https://${app.server.cluster}:2443" />
			<arg value="ALL"/>
		</exec>
		</if>
		<if test="${SSLFeature=='false'}">
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${dist.dir}" />
			<arg value="json"/>
			<arg value="http://cps-api:4000" />
			<arg value="http://${app.server.cluster}:4000" />
			<arg value="ALL"/>
		</exec>
		</if>		
		<if test="${SSLFeature=='true'}">
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${dist.dir}" />
			<arg value="json"/>
			<arg value="http://cps-api:4000" />
			<arg value="https://${app.server.cluster}:4443" />
			<arg value="ALL"/>
		</exec>
		</if>
		<if test="${SSLFeature=='false'}">
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${dist.dir}" />
			<arg value="json"/>
			<arg value="http://cps-IMS-server:4500" />
			<arg value="http://${ims.cluster.alias}:4500" />
			<arg value="ALL"/>
		</exec>
		</if>		
		<if test="${SSLFeature=='true'}">
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${dist.dir}" />
			<arg value="json"/>
			<arg value="http://cps-IMS-server:4500" />
			<arg value="https://${ims.cluster.alias}:5443" />
			<arg value="ALL"/>
		</exec>
		</if>
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${dist.dir}" />
			<arg value="json"/>
			<arg value="Database=DET;Server=localhost;Trusted_Connection=True;Min Pool Size=1;Max Pool Size=100;Connect Timeout=120" />
			<arg value="Database=DET;Server=${db.listener};Trusted_Connection=True;Min Pool Size=1;Max Pool Size=100;Connect Timeout=120" />
			<arg value="ALL"/>
		</exec>
		
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${dist.dir}" />
			<arg value="json"/>
			<arg value="%CTS_BASEDIR%\\Logs\\CFTS.log" />
			<arg value="${ctfs.log.dir}\\CFTS.log" />
			<arg value="ALL"/>
		</exec>
		<if test="${SSLFeature=='false'}">
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${dist.dir}" />
			<arg value="json"/>
			<arg value="http://cps-mir-server:5001" />
			<arg value="http://${mir.trans.cluster}:5001" />
			<arg value="ALL"/>
		</exec>					
		</if>
		<if test="${SSLFeature=='true'}">
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${dist.dir}" />
			<arg value="json"/>
			<arg value="http://cps-mir-server:5001" />
			<arg value="https://${mir.trans.cluster}:4443" />
			<arg value="ALL"/>
		</exec>
		</if>
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${dist.dir}" />
			<arg value="json"/>
			<arg value="cps-isilon-baseurl" />
			<arg value="${cps.isilon.baseurl}" />
			<arg value="ALL"/>
		</exec>
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${dist.dir}" />
			<arg value="json"/>
			<arg value="cps-isilon-username" />
			<arg value="${cps.isilon.username}" />
			<arg value="ALL"/>
		</exec>
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${dist.dir}" />
			<arg value="json"/>
			<arg value="cps-isilon-password" />
			<arg value="${cps.isilon.password}" />
			<arg value="ALL"/>
		</exec>
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${dist.dir}" />
			<arg value="json"/>
			<arg value="cps-dmdb-sharedfolder" />
			<arg value="${cps.dmdb.sharedfolder}" />
			<arg value="ALL"/>
		</exec>
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${dist.dir}" />
			<arg value="json"/>
			<arg value="C:\\Logs" />
			<arg value="${log.dir}" />
			<arg value="ALL"/>
		</exec>
	<!-- web config CORS -->
		<if test="${SSLFeature=='true'}">
			<exec program="ReplaceUtil.exe" failonerror="true">
				<arg value="${web01.web.config}" />
				<arg value="config"/>
				<arg value="http://*.tcore.com" />
				<arg value="https://*.${domain}" />
				<arg value="ALL"/>
			</exec>
		</if>
		<if test="${SSLFeature=='false'}">
			<exec program="ReplaceUtil.exe" failonerror="true">
				<arg value="${web01.web.config}" />
				<arg value="config"/>
				<arg value="http://*.tcore.com" />
				<arg value="http://*.${domain}" />
				<arg value="ALL"/>
			</exec>
		</if>
		<if test="${BOSExternalWebFeature=='true'}">
			<if test="${SSLFeature=='true'}">
			<exec program="ReplaceUtil.exe" failonerror="true">
				<arg value="${web01.extweb.config}" />
				<arg value="config"/>
				<arg value="http://*.tcore.com" />
				<arg value="https://*.${domain}:9000" />
				<arg value="ALL"/>
			</exec>
			</if>
		<if test="${SSLFeature=='false'}">
			<exec program="ReplaceUtil.exe" failonerror="true">
				<arg value="${web01.extweb.config}" />
				<arg value="config"/>
				<arg value="http://*.tcore.com" />
				<!--temporary-->
				<arg value="https://*.cbdtp.net:9000" />
				<arg value="ALL"/>
			</exec>
		</if>
		<!--
		<if test="${SSLFeature=='true'}">
			<exec program="ReplaceUtil.exe" failonerror="true">
				<arg value="${web01.extweb.config}" />
				<arg value="config"/>
				<arg value="https://*.${domain}" />
				<arg value="https://*.${domain}:9000" />
				<arg value="ALL"/>
			</exec>
		</if>
		<if test="${SSLFeature=='false'}">
			<exec program="ReplaceUtil.exe" failonerror="true">
				<arg value="${web01.extweb.config}" />
				<arg value="config"/>
				<arg value="http://*.${domain}" />
				<arg value="http://*.${domain}:9000" />
				<arg value="ALL"/>
			</exec>
		</if>
		-->
		</if>
			
	
	<!-- Third Party Scheduled Tasks (EXT01) -->
	<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${dist.dir}" />
			<arg value="json"/>
			<arg value="Database=DMV;Server=localhost;Trusted_Connection=True;Min Pool Size=1;Max Pool Size=100;Connect Timeout=120" />
			<arg value="${db.dmv.connection.string}" />
			<arg value="ALL"/>
	</exec>
	<if test="${ZPIProcessTask=='true'}">
		<exec program="ReplaceUtil.exe" failonerror="true">
				<arg value="${ext01.dist.dir}" />
				<arg value="json"/>
				<arg value="Database=CCD;Server=localhost;Trusted_Connection=True;Min Pool Size=1;Max Pool Size=100;Connect Timeout=120" />
				<arg value="${db.ccd.connection.string}" />
				<arg value="ALL"/>
		</exec>
		<exec program="ReplaceUtil.exe" failonerror="true">
				<arg value="${ext01.dist.dir}" />
				<arg value="json"/>
				<arg value="TcoreCli" />
				<arg value="${zpi-User}" />
				<arg value="ALL"/>
		</exec>
		<exec program="ReplaceUtil.exe" failonerror="true">
				<arg value="${ext01.dist.dir}" />
				<arg value="json"/>
				<arg value="uKCmq8zN" />
				<arg value="${zpi-pwd}" />
				<arg value="ALL"/>
		</exec>
		<exec program="ReplaceUtil.exe" failonerror="true">
				<arg value="${ext01.dist.dir}" />
				<arg value="json"/>
				<arg value="http://************:8099/SOAP" />
				<arg value="${zpi-thirdparty-serviceurl}" />
				<arg value="ALL"/>
		</exec>
	</if>
	
	<!-- WebReportViewer Web Config poke -->
	<xmlpoke failonerror="true" file="${web01.webreportviewer.config.file}" xpath="/configuration/appSettings/add[@key = 'serilog:write-to:RollingFile.pathFormat']/@value" value="${log.dir}\\WebReportViewer_{Date}.log" />	
	<xmlpoke failonerror="true" file="${web01.webreportviewer.config.file}" xpath="/configuration/connectionStrings/add[@name='CCD']/@connectionString" value="${db.ccd.connection.string}" />
	
	</target>
	
	
	<!-- Filter Report variables -->
	<!--<target name="filter-report-config-file" description="Filter environment specific variables like servers, db, etc">		
		<xmlpoke  file="${reports.deployer.appconfig.file}" xpath="/configuration/appSettings/add[@key = 'reportServiceUrl']/@value" value="${report.server.URL}/ReportService2010.asmx" />	
		<xmlpoke  file="${reports.deployer.appconfig.file}" xpath="/configuration/appSettings/add[@key = 'deployReportsFromDir']/@value" value="${deploy.Reports.From.Dir}" />		
		<xmlpoke  file="${reports.deployer.appconfig.file}" xpath="/configuration/appSettings/add[@key = 'reportsPath']/@value" value="${deploy.Reports.Reports.Path}" />	
		<xmlpoke  file="${reports.deployer.appconfig.file}" xpath="/configuration/appSettings/add[@key = 'reportsFolderName']/@value" value="SSOReports" />	
		<xmlpoke  file="${reports.deployer.appconfig.file}" xpath="/configuration/appSettings/add[@key = 'overwriteReports']/@value" value="${deploy.Reports.Reports.Overwrite}" />		
		<xmlpoke  file="${reports.deployer.appconfig.file}" xpath="/configuration/appSettings/add[@key = 'overwriteDataSets']/@value" value="${deploy.Reports.DataSets.Overwrite}" />	
		<xmlpoke  file="${reports.deployer.appconfig.file}" xpath="/configuration/appSettings/add[@key = 'sharedDataSourceFolderName']/@value" value="${deploy.Reports.Shared.DataSource.FolderName}" />	
		<xmlpoke  file="${reports.deployer.appconfig.file}" xpath="/configuration/appSettings/add[@key = 'sharedDataSetFolderName']/@value" value="${deploy.Reports.Shared.DataSet.FolderName}" />			
		<xmlpoke  file="${reports.deployer.appconfig.file}" xpath="/configuration/appSettings/add[@key = 'dataSetDataSourcePath']/@value" value="${deploy.Reports.DataSet.DataSource.Path}" />				

		<xmlpoke  file="${reports.deployer.appconfig.file}" xpath="/configuration/dataSources/SSOLive/add[@key = 'Name']/@value" value="SSOLive" />
		<xmlpoke  file="${reports.deployer.appconfig.file}" xpath="/configuration/dataSources/SSOLive/add[@key = 'Ext']/@value" value="SQL" />
		<xmlpoke  file="${reports.deployer.appconfig.file}" xpath="/configuration/dataSources/SSOLive/add[@key = 'ConnectionString']/@value" value="${report.datasource.sso}" />
		<xmlpoke  file="${reports.deployer.appconfig.file}" xpath="/configuration/dataSources/SSOLive/add[@key = 'userID']/@value" value="${report.datasource.userid}" />
		<xmlpoke  file="${reports.deployer.appconfig.file}" xpath="/configuration/dataSources/SSOLive/add[@key = 'pass']/@value" value="${report.datasource.pass}" />	
	</target> -->
 <target name="filter-data-analytics" description="Filter environment specific variables like servers, db, etc">
 <!-- DataAnalytics -->
		<exec program="ReplaceUtil.exe" failonerror="false">
			<arg value="${app01.scheduledtasks.txt}" />
			<arg value="txt"/>
			<arg value="smtp-server" />
			<arg value="${smtp-server}" />
			<arg value="ALL"/>
		</exec>
		<exec program="ReplaceUtil.exe" failonerror="false">
			<arg value="${app01.scheduledtasks.txt}" />
			<arg value="txt"/>
			<arg value="PORT=25" />
			<arg value="PORT=${smtp-port}" />
			<arg value="ALL"/>
		</exec>
		<exec program="ReplaceUtil.exe" failonerror="false">
			<arg value="${app01.scheduledtasks.txt}" />
			<arg value="txt"/>
			<arg value="cps-db-server" />
			<arg value="${db.listener}" />
			<arg value="ALL"/>
		</exec>
		<exec program="ReplaceUtil.exe" failonerror="false">
			<arg value="${app01.scheduledtasks.txt}" />
			<arg value="txt"/>
			<arg value="<EMAIL>" />
			<arg value="${sender-email}" />
			<arg value="ALL"/>
		</exec>
 </target>
 <target name="filter-key" description="Filter key">
		<if test="${new.symmetric.key=='true'}">	
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${dist.dir}" />
			<arg value="json"/>
			<arg value="${old.key}" />
			<arg value="${new.key}" />
			<arg value="ALL"/>
		</exec>
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${dist.dir}" />
			<arg value="config"/>
			<arg value="${old.key}" />
			<arg value="${new.key}" />
			<arg value="ALL"/>
		</exec>
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${dist.dir}" />
			<arg value="js"/>
			<arg value="${old.key}" />
			<arg value="${new.key}" />
			<arg value="ALL"/>
		</exec>
		</if>
	</target>	
	
</project>