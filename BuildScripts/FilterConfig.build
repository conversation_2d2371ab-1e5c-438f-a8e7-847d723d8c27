<?xml version="1.0" encoding="utf-8" ?>

<project name="Image Review" >


    <!-- GET target deployment environment from the user -->
	<include buildfile="UserInput.include"/>
	<include buildfile="BUILD.include"/>
	<include buildfile="ENVIRONMENT.include"/>
    <include buildfile="..\Common\KEY.include"/>
	<property name="env" value="${deployment.environment}"/>
	
  	
		<!-- Include environment specific properties -->
	<echo message="****** Deployment Target is Image Review ${env} *******" />	

	<!-- Filters all environment specific variables like servers, db, etc -->
	<target name="filter-config-file" description="Filters all environment specific variables like servers, db, etc">
		<property name="MailLogger.failure.subject" value="Image Review  ${deployment.environment} ${deployment.type} - Filter step failed" />
		<property name="MailLogger.success.subject" value="Image Review  ${deployment.environment} ${deployment.type} - Step 3 Configuration Successfully Completed." />
			<if test="${FingerprintFeature=='true'}"> <call target="filter-ImageReview-FingerPrint"/> </if>
			<if test="${HOCPFeature=='true'}"> <call target="filter-ImageReview-HOCP"/> </if>
			<if test="${PreHOCPFeature=='true'}"> <call target="filter-ImageReview-PreHOCP"/> </if>
			<if test="${ImageClientServiceFeature=='true'}"> <call target="filter-ImageReview-Client-Task"/> </if>
			<if test="${IRRFeature=='true'}">  <call target="filter-ImageReview-IRR"/> </if>
			<if test="${QFreePluginFeature=='true'}">  <call target="filter-External-Transactions-API"/> </if>
			<if test="${ADPollingTask=='true'}">  <call target="filter-ImageReview-ActiveDirectory"/> </if>
			<if test="${VOTTQueueTaskFeature=='true'}"> <call target="filter-VOTTQueue-Task"/> </if>
			<if test="${MIREmailNotificationTaskFeature=='true'}"> <call target="filter-MIREmailNotification-Task"/> </if>
			<if test="${VehicleDetectionTaskFeature=='true'}"> <call target="filter-VehicleDetection-Task"/> </if>
			<if test="${ImgReviewSendTransTaskFeature=='true'}"> <call target="filter-ImgReviewSendTransTaskFeature"/> </if>

			<if test="${deployment.environment=='CBDTP'}"> <if test="${deployment.type=='PROD'}"> <call target="filter-CBDTPLogging"/> </if> </if>
			
			<if test="${ManualResultsFeature=='true'}"> <call target="filter-ImageReview-MIR-ManualResultsTask"/> </if>
			<if test="${TransAPIandMIRIntServiceFeature=='true'}"> <call target="filter-ImageReview-TRANS"/> </if>
			<if test="${GenerateROITaskFeature=='true'}"> <call target="filter-GenerateROITaskFeature"/> </if>
			<call target="filter-ImageReview-Main"/>
			<call target="filter-report-config-file" />
			<call target="filter-key" />
			
			
			<if test="${HumanReadabilityFeature=='true'}"> <call target="filter-HumanReadability"/> </if>
			<if test="${ClusterFeature=='true'}">	
				<copy todir="${base.dist.dir}\APP02">
					<fileset basedir="${base.dist.dir}\APP01" />
				</copy>	
				<copy todir="${base.dist.dir}\WEB02">
					<fileset basedir="${base.dist.dir}\WEB01" />
				</copy>	
				<if test="${IRRFeature=='true'}"> 
					<copy todir="${base.dist.dir}\IRR02">
						<fileset basedir="${base.dist.dir}\IRR01" />
					</copy>	
				</if>
				<if test="${HOCPFeature=='true'}"> 
					<copy todir="${base.dist.dir}\HOCP02">
						<fileset basedir="${base.dist.dir}\HOCP01" />
					</copy>	
				</if>
				<if test="${PreHOCPFeature=='true'}"> 
					<copy todir="${base.dist.dir}\PREHOCP02">
						<fileset basedir="${base.dist.dir}\PREHOCP01" />
					</copy>	
				</if>
				<if test="${TransAPIandMIRIntServiceFeature=='true'}">
					<copy todir="${base.dist.dir}\TRANS02">
						<fileset basedir="${base.dist.dir}\TRANS01" />
					</copy>	
				</if>
				
				<exec program="ReplaceUtil.exe" failonerror="true">
						<arg value="${base.dist.dir}/APP02" />
						<arg value="config"/>
						<arg value="${imagereview.app.server}" />
						<arg value="${imagereview.app02.server}" />
						<arg value="ALL"/>
				</exec>
				<if test="${TransAPIandMIRIntServiceFeature=='true'}"> 
					<exec program="ReplaceUtil.exe" failonerror="true">
							<arg value="${base.dist.dir}/TRANS02" />
							<arg value="config"/>
							<arg value="${imagereview.trans.server}" />
							<arg value="${imagereview.trans02.server}" />
							<arg value="ALL"/>
					</exec>
				</if>
				<if test="${IRRFeature=='true'}"> 
				<exec program="ReplaceUtil.exe" failonerror="true">
						<arg value="${base.dist.dir}/IRR02" />
						<arg value="config"/>
						<arg value="${imagereview.irr01.server}" />
						<arg value="${imagereview.irr02.server}" />
						<arg value="ALL"/>
				</exec>
				</if>
				<if test="${HOCPFeature=='true'}"> 
				<exec program="ReplaceUtil.exe" failonerror="true">
						<arg value="${base.dist.dir}/HOCP02" />
						<arg value="config"/>
						<arg value="${imagereview.hocp01.server}" />
						<arg value="${imagereview.hocp02.server}" />
						<arg value="ALL"/>
				</exec>
				</if>
				<if test="${PreHOCPFeature=='true'}"> 
				<exec program="ReplaceUtil.exe" failonerror="true">
						<arg value="${base.dist.dir}/PREHOCP02" />
						<arg value="config"/>
						<arg value="${imagereview.prehocp01.server}" />
						<arg value="${imagereview.prehocp02.server}" />
						<arg value="ALL"/>
				</exec>
				</if>
				<if test="${FingerprintFeature=='true'}">
					<copy todir="${base.dist.dir}\FP02">
					<fileset basedir="${base.dist.dir}\FP01" />
					</copy>	
					<exec program="ReplaceUtil.exe" failonerror="true">
						<arg value="${base.dist.dir}/FP02" />
						<arg value="config"/>
						<arg value="${imagereview.app.server}" />
						<arg value="${imagereview.app02.server}" />
						<arg value="ALL"/>
					</exec>
					<exec program="ReplaceUtil.exe" failonerror="true">
						<arg value="${base.dist.dir}/FP02" />
						<arg value="config"/>
						<arg value="${imagereview.fingerprint.server}" />
						<arg value="${imagereview.fingerprint02.server}" />
						<arg value="ALL"/>
					</exec>
				</if>
			</if>		
	</target>
	
	
	<!-- Filter Report variables -->
	<target name="filter-report-config-file" description="Filter environment specific variables like servers, db, etc">		
		<xmlpoke  file="${reports.deployer.appconfig.file}" xpath="/configuration/appSettings/add[@key = 'reportServiceUrl']/@value" value="${report.service}" />	
		<xmlpoke  file="${reports.deployer.appconfig.file}" xpath="/configuration/appSettings/add[@key = 'deployReportsFromDir']/@value" value="${deploy.Reports.From.Dir}" />		
		<xmlpoke  file="${reports.deployer.appconfig.file}" xpath="/configuration/appSettings/add[@key = 'reportsPath']/@value" value="${deploy.Reports.Reports.Path}" />	
		<xmlpoke  file="${reports.deployer.appconfig.file}" xpath="/configuration/appSettings/add[@key = 'reportsFolderName']/@value" value="ImageReviewReports" />	
		<xmlpoke  file="${reports.deployer.appconfig.file}" xpath="/configuration/appSettings/add[@key = 'overwriteReports']/@value" value="${deploy.Reports.Reports.Overwrite}" />		
		<xmlpoke  file="${reports.deployer.appconfig.file}" xpath="/configuration/appSettings/add[@key = 'overwriteDataSets']/@value" value="${deploy.Reports.DataSets.Overwrite}" />	
		<xmlpoke  file="${reports.deployer.appconfig.file}" xpath="/configuration/appSettings/add[@key = 'sharedDataSourceFolderName']/@value" value="${deploy.Reports.Shared.DataSource.FolderName}" />	
		<xmlpoke  file="${reports.deployer.appconfig.file}" xpath="/configuration/appSettings/add[@key = 'sharedDataSetFolderName']/@value" value="${deploy.Reports.Shared.DataSet.FolderName}" />			
		<xmlpoke  file="${reports.deployer.appconfig.file}" xpath="/configuration/appSettings/add[@key = 'dataSetDataSourcePath']/@value" value="${deploy.Reports.DataSet.DataSource.Path}" />				
		<!-- Reports data sources -->
		<xmlpoke  file="${reports.deployer.appconfig.file}" xpath="/configuration/dataSources/IPSLive/add[@key = 'Name']/@value" value="IPSLive" />
		<xmlpoke  file="${reports.deployer.appconfig.file}" xpath="/configuration/dataSources/IPSLive/add[@key = 'Ext']/@value" value="SQL" />
		<xmlpoke  file="${reports.deployer.appconfig.file}" xpath="/configuration/dataSources/IPSLive/add[@key = 'ConnectionString']/@value" value="${report.datasource.sso}" />
		<xmlpoke  file="${reports.deployer.appconfig.file}" xpath="/configuration/dataSources/IPSLive/add[@key = 'userID']/@value" value="${report.datasource.userid}" />
		<xmlpoke  file="${reports.deployer.appconfig.file}" xpath="/configuration/dataSources/IPSLive/add[@key = 'pass']/@value" value="${report.datasource.pass}" />	
	</target>

	
	<!-- Filter the Image Review Fingerprint environment specific strings -->
	<target name="filter-ImageReview-FingerPrint" description="Filter environment specific variables like servers, db, etc">	

		<!-- FPM Service Host -->
		<xmlpoke file="${imagereview.fpm.service.host.appconfig.file}" xpath="/configuration/connectionStrings/add[@name='IPSDBConnectionString']/@connectionString" value="${ips.fingerprint.connection.string}" />
		<xmlpoke file="${imagereview.fpm.service.host.appconfig.file}" xpath="/configuration/connectionStrings/add[@name='FPMDBConnectionString']/@connectionString" value="${fpm.connection.string}" />	
		<xmlpoke file="${imagereview.fpm.service.host.appconfig.file}" xpath="/configuration/connectionStrings/add[@name='MOMSAlarmConnectionString']/@connectionString" value="${ips.connection.string}" />		
		<xmlpoke file="${imagereview.fpm.service.host.appconfig.file}" xpath="/configuration/system.serviceModel/services/service/host/baseAddresses/add[@baseAddress = 'net.tcp://localhost:8900']/@baseAddress" value="${fpm.nettcp.base.address}" />
		<xmlpoke file="${imagereview.fpm.service.host.appconfig.file}" xpath="/configuration/system.serviceModel/services/service/host/baseAddresses/add[@baseAddress = 'http://localhost:8901']/@baseAddress" value="${fpm.http.base.address}" />
		<xmlpoke file="${imagereview.fpm.service.host.appconfig.file}" xpath="/configuration/log4net/root/level[@value = 'ALL']/@value" value="${log.error.level}" />
		<xmlpoke file="${imagereview.fpm.service.host.appconfig.file}" xpath="/configuration/log4net/appender/file/@value" value="${imagereview.log.dir}/FPMLogs/FPMInternalServiceHost.log" />	
		<xmlpoke file="${imagereview.fpm.service.host.appconfig.file}" xpath="/configuration/appSettings/add[@key = 'DefaultDBTimeout']/@value" value="${fpm.DefaultDBTimeout}" />
		
		<!-- FPP Service Host -->
		<xmlpoke file="${imagereview.fpp.service.host.appconfig.file}" xpath="/configuration/connectionStrings/add[@name='IPSDBConnectionString']/@connectionString" value="${ips.fingerprint.connection.string}" />		
		<xmlpoke file="${imagereview.fpp.service.host.appconfig.file}" xpath="/configuration/connectionStrings/add[@name='MOMSAlarmConnectionString']/@connectionString" value="${ips.connection.string}" />		
		<xmlpoke file="${imagereview.fpp.service.host.appconfig.file}" xpath="/configuration/log4net/root/level[@value = 'ALL']/@value" value="${log.error.level}" />
		<xmlpoke file="${imagereview.fpp.service.host.appconfig.file}" xpath="/configuration/log4net/appender/file/@value" value="${imagereview.log.dir}/FPPLogs/FPPInternalServiceHost.log" />	

		<!--FPP Service Client Service -->
		<!-- FPP Service Host -->
		<xmlpoke file="${imagereview.fpp.service.client.appconfig.file}" xpath="/configuration/connectionStrings/add[@name='IPSDBConnectionString']/@connectionString" value="${ips.fingerprint.connection.string}" />		
		<xmlpoke file="${imagereview.fpp.service.client.appconfig.file}" xpath="/configuration/connectionStrings/add[@name='MOMSAlarmConnectionString']/@connectionString" value="${ips.connection.string}" />		
		<xmlpoke file="${imagereview.fpp.service.client.appconfig.file}" xpath="/configuration/log4net/root/level[@value = 'ALL']/@value" value="${log.error.level}" />
		<xmlpoke file="${imagereview.fpp.service.client.appconfig.file}" xpath="/configuration/log4net/appender/file/@value" value="${imagereview.log.dir}/FPPLogs/FPPInternalServiceClient.log" />	
		<xmlpoke file="${imagereview.fpp.service.client.appconfig.file}" xpath="/configuration/appSettings/add[@key = 'IntradaCompanyName']/@value" value="${fpp.IntradaCompanyName}" />	
		<xmlpoke file="${imagereview.fpp.service.client.appconfig.file}" xpath="/configuration/appSettings/add[@key = 'IntradaLicenseKey']/@value" value="${fpp.IntradaLicenseKey}" />	

		<!-- Scheduled Task: FPPMatchPoolTask -->
		<xmlpoke file="${imagereview.fpp.task.fppmatchpooltask.config.file}" xpath="/configuration/log4net/root/level[@value = 'ALL']/@value" value="${log.error.level}" />
		<xmlpoke file="${imagereview.fpp.task.fppmatchpooltask.config.file}" xpath="/configuration/log4net/appender/file/@value" value="${imagereview.log.dir}\FPPLogs\FPPMatchPoolTask.log" />
		<xmlpoke file="${imagereview.fpp.task.fppmatchpooltask.config.file}" xpath="/configuration/connectionStrings/add[@name='IPSDBConnectionString']/@connectionString" value="${ips.fingerprint.connection.string}" />
		<xmlpoke file="${imagereview.fpp.task.fppmatchpooltask.config.file}" xpath="/configuration/connectionStrings/add[@name='FPMDBConnectionString']/@connectionString" value="${fpm.connection.string}" />	
		<xmlpoke file="${imagereview.fpp.task.fppmatchpooltask.config.file}" xpath="/configuration/connectionStrings/add[@name='MOMSAlarmConnectionString']/@connectionString" value="${ips.connection.string}" />		
		
		
		<!-- Auth Web Api Config -->
		<xmlpoke file="${imagereview.fpm.webapi.config.file}" xpath="/configuration/system.serviceModel/client/endpoint[@address = 'net.tcp://localhost:8900/FPMService']/@address" value="${ImageReview.fpm.endpoint}" />
		<xmlpoke file="${imagereview.fpm.webapi.config.file}" xpath="/configuration/system.serviceModel/client/endpoint/identity/servicePrincipalName/@value" value="${imagereview.fingerprint.cluster}" />
		<xmlpoke file="${imagereview.fpm.webapi.config.file}" xpath="/configuration/log4net/appender/file/@value" value="${imagereview.log.dir}\FPMLogs\FPMWebAPI.log" />	
		<xmlpoke file="${imagereview.fpm.webapi.config.file}" xpath="/configuration/elmah/errorLog[@logPath = 'D:\\FPM_Log\\Elmah_WebAPI']/@logPath" value="${imagereview.log.dir}\\FPMLogs\\Elmah_WebAPI" />
		<xmlpoke file="${imagereview.fpm.webapi.config.file}" xpath="/configuration/log4net/root/level[@value = 'ALL']/@value" value="${log.error.level}" />	

		<!-- Scheduled Task: FPMSendResults -->
		<xmlpoke file="${imagereview.fpm.task.sendresults.config.file}" xpath="/configuration/log4net/root/level[@value = 'ALL']/@value" value="${log.error.level}" />
		<xmlpoke file="${imagereview.fpm.task.sendresults.config.file}" xpath="/configuration/log4net/appender/file/@value" value="${imagereview.log.dir}\FPMLogs\FPMSendResultsProcessTask.log" />		
		<xmlpoke file="${imagereview.fpm.task.sendresults.config.file}" xpath="/configuration/connectionStrings/add[@name='IPSDBConnectionString']/@connectionString" value="${ips.fingerprint.connection.string}" />
		<xmlpoke file="${imagereview.fpm.task.sendresults.config.file}" xpath="/configuration/connectionStrings/add[@name='MOMSAlarmConnectionString']/@connectionString" value="${ips.connection.string}" />		
		
		<if test="${SSLFeature=='false'}">
			<xmlpoke file="${imagereview.fpm.task.sendresults.config.file}" xpath="/configuration/appSettings/add[@key = 'HIGH_OCR_CONFIDENCE_TRANSACTIONS_URL']/@value" value="http://${imagereview.hocp.cluster}:9091/api/HOCP/transactions" />
		</if>
		<if test="${SSLFeature=='true'}">
			<xmlpoke file="${imagereview.fpm.task.sendresults.config.file}" xpath="/configuration/appSettings/add[@key = 'HIGH_OCR_CONFIDENCE_TRANSACTIONS_URL']/@value" value="https://${imagereview.hocp.cluster}:8443/api/HOCP/transactions" />		
		</if>
		
		<!-- Scheduled Task: AuditReviewResultsTask -->
		<xmlpoke file="${imagereview.mir.mrt.task.auditreviewresults.config.file}" xpath="/configuration/log4net/root/level[@value = 'ALL']/@value" value="${log.error.level}" />
		<xmlpoke file="${imagereview.mir.mrt.task.auditreviewresults.config.file}" xpath="/configuration/log4net/appender/file/@value" value="${imagereview.log.dir}\MIRLogs\AuditReviewResultsTask.log" />		
		<xmlpoke file="${imagereview.mir.mrt.task.auditreviewresults.config.file}" xpath="/configuration/connectionStrings/add[@name='IMGREVDBConnectionString']/@connectionString" value="${ips.connection.string}" />
		<if test="${SSLFeature=='false'}"> 
			<xmlpoke file="${imagereview.mir.mrt.task.auditreviewresults.config.file}" xpath="/configuration/appSettings/add[@key = 'FPMImageResultUrl']/@value" value="http://${imagereview.fingerprint.cluster}:9090/api/fpm/ImageReviewResult" />
		</if>
		
		<if test="${SSLFeature=='true'}"> 
			<xmlpoke file="${imagereview.mir.mrt.task.auditreviewresults.config.file}" xpath="/configuration/appSettings/add[@key = 'FPMImageResultUrl']/@value" value="https://${imagereview.fingerprint.cluster}:9443/api/fpm/ImageReviewResult" />
		</if>
	</target>
	
	<!-- Filter the IMage Review HOCP environment specific strings -->
	<target name="filter-ImageReview-HOCP" description="Filter environment specific variables like servers, db, etc">	

		<!-- hocp Service Host -->
		<xmlpoke file="${imagereview.hocp.service.host.appconfig.file}" xpath="/configuration/connectionStrings/add[@name='HOCDBConnectionString']/@connectionString" value="${hocp.connection.string}" />
		<xmlpoke file="${imagereview.hocp.service.host.appconfig.file}" xpath="/configuration/connectionStrings/add[@name='MIRDBConnectionString']/@connectionString" value="${fpm.connection.string}" />		
		<xmlpoke file="${imagereview.hocp.service.host.appconfig.file}" xpath="/configuration/system.serviceModel/services/service/host/baseAddresses/add[@baseAddress = 'net.tcp://localhost:8950']/@baseAddress" value="${hocp.nettcp.base.address}" />
		<xmlpoke file="${imagereview.hocp.service.host.appconfig.file}" xpath="/configuration/system.serviceModel/services/service/host/baseAddresses/add[@baseAddress = 'http://localhost:8951']/@baseAddress" value="${hocp.http.base.address}" />
		<xmlpoke file="${imagereview.hocp.service.host.appconfig.file}" xpath="/configuration/log4net/root/level[@value = 'ALL']/@value" value="${log.error.level}" />
		<xmlpoke file="${imagereview.hocp.service.host.appconfig.file}" xpath="/configuration/log4net/appender/file/@value" value="${imagereview.log.dir}/HOCPLogs/HOCPInternalServiceHost.log" />	
		<xmlpoke file="${imagereview.hocp.service.host.appconfig.file}" xpath="/configuration/appSettings/add[@key = 'DefaultDBTimeout']/@value" value="${hocp.DefaultDBTimeout}" />
		<xmlpoke file="${imagereview.hocp.service.host.appconfig.file}" xpath="/configuration/connectionStrings/add[@name='MOMSAlarmConnectionString']/@connectionString" value="${ips.connection.string}" />		
		
			<!--  Web Api Config -->
		<xmlpoke file="${imagereview.hocp.webapi.config.file}" xpath="/configuration/system.serviceModel/client/endpoint[@address = 'net.tcp://localhost:8950/HOCPService']/@address" value="${ImageReview.hocp.endpoint}" />
		<xmlpoke file="${imagereview.hocp.webapi.config.file}" xpath="/configuration/system.serviceModel/client/endpoint/identity/servicePrincipalName/@value" value="${imagereview.hocp.cluster}" />
		<xmlpoke file="${imagereview.hocp.webapi.config.file}" xpath="/configuration/log4net/appender/file/@value" value="${imagereview.log.dir}\hocpLogs\hocpWebAPI.log" />	
		<xmlpoke file="${imagereview.hocp.webapi.config.file}" xpath="/configuration/elmah/errorLog[@logPath = 'D:\\FPM_Log\\Elmah_WebAPI']/@logPath" value="${imagereview.log.dir}\\HOCPLogs\\Elmah_WebAPI" />
		<xmlpoke file="${imagereview.hocp.webapi.config.file}" xpath="/configuration/log4net/root/level[@value = 'ALL']/@value" value="${log.error.level}" />	

		<!-- Scheduled Task: HOCPProcessor-->
		<xmlpoke file="${imagereview.hocp.task.processor.config.file}" xpath="/configuration/connectionStrings/add[@name='HOCDBConnectionString']/@connectionString" value="${hocp.connection.string}" />
		<xmlpoke file="${imagereview.hocp.task.processor.config.file}" xpath="/configuration/connectionStrings/add[@name='MIRDBConnectionString']/@connectionString" value="${ips.connection.string}" />	
		<xmlpoke file="${imagereview.hocp.task.processor.config.file}" xpath="/configuration/connectionStrings/add[@name='MOMSAlarmConnectionString']/@connectionString" value="${ips.connection.string}" />				
		<xmlpoke file="${imagereview.hocp.task.processor.config.file}" xpath="/configuration/log4net/root/level[@value = 'ALL']/@value" value="${log.error.level}" />
		<xmlpoke file="${imagereview.hocp.task.processor.config.file}" xpath="/configuration/log4net/appender/file/@value" value="${imagereview.log.dir}\HOCPLogs\HOCPProcessorTask.log" />		
		
		
		<!-- Scheduled Task: HOCPSendResults -->
		<xmlpoke file="${imagereview.hocp.task.sendresults.config.file}" xpath="/configuration/log4net/root/level[@value = 'ALL']/@value" value="${log.error.level}" />
		<xmlpoke file="${imagereview.hocp.task.sendresults.config.file}" xpath="/configuration/log4net/appender/file/@value" value="${imagereview.log.dir}\HOCPLogs\HOCPSendResultsProcessTask.log" />		
		<xmlpoke file="${imagereview.hocp.task.sendresults.config.file}" xpath="/configuration/connectionStrings/add[@name='HOCDBConnectionString']/@connectionString" value="${hocp.connection.string}" />
		<xmlpoke file="${imagereview.hocp.task.sendresults.config.file}" xpath="/configuration/connectionStrings/add[@name='MIRDBConnectionString']/@connectionString" value="${ips.connection.string}" />	
		<xmlpoke file="${imagereview.hocp.task.sendresults.config.file}" xpath="/configuration/connectionStrings/add[@name='MOMSAlarmConnectionString']/@connectionString" value="${ips.connection.string}" />		
		
		<if test="${SSLFeature=='false'}">
			<xmlpoke file="${imagereview.hocp.task.sendresults.config.file}" xpath="/configuration/appSettings/add[@key = 'ManualImageReviewUrl']/@value" value="http://${imagereview.app.alias}:5001/api/ManualImageReview/transactions" />	
			<xmlpoke file="${imagereview.hocp.task.sendresults.config.file}" xpath="/configuration/appSettings/add[@key = 'ImageResultUrl']/@value" value="http://${imagereview.irr.alias}:5005/api/TransactionResults/AddTransactionResult" />	
		</if>
		
		<if test="${SSLFeature=='true'}">
			<xmlpoke file="${imagereview.hocp.task.sendresults.config.file}" xpath="/configuration/appSettings/add[@key = 'ManualImageReviewUrl']/@value" value="https://${imagereview.app.alias}:4443/api/ManualImageReview/transactions" />	
			<xmlpoke file="${imagereview.hocp.task.sendresults.config.file}" xpath="/configuration/appSettings/add[@key = 'ImageResultUrl']/@value" value="https://${imagereview.irr.alias}:5443/api/TransactionResults/AddTransactionResult" />		
		</if>
		
	</target>
	
		<!-- Filter the IMage Review PreHOCP environment specific strings -->
	<target name="filter-ImageReview-PreHOCP" description="Filter environment specific variables like servers, db, etc">	

		<!-- hocp Service Host -->
		<xmlpoke file="${imagereview.prehocp.service.host.appconfig.file}" xpath="/configuration/connectionStrings/add[@name='HOCDBConnectionString']/@connectionString" value="${prehocp.connection.string}" />
		<xmlpoke file="${imagereview.prehocp.service.host.appconfig.file}" xpath="/configuration/connectionStrings/add[@name='MIRDBConnectionString']/@connectionString" value="${fpm.connection.string}" />		
		<xmlpoke file="${imagereview.prehocp.service.host.appconfig.file}" xpath="/configuration/system.serviceModel/services/service/host/baseAddresses/add[@baseAddress = 'net.tcp://localhost:8950']/@baseAddress" value="${prehocp.nettcp.base.address}" />
		<xmlpoke file="${imagereview.prehocp.service.host.appconfig.file}" xpath="/configuration/system.serviceModel/services/service/host/baseAddresses/add[@baseAddress = 'http://localhost:8951']/@baseAddress" value="${prehocp.http.base.address}" />
		<xmlpoke file="${imagereview.prehocp.service.host.appconfig.file}" xpath="/configuration/log4net/root/level[@value = 'ALL']/@value" value="${log.error.level}" />
		<xmlpoke file="${imagereview.prehocp.service.host.appconfig.file}" xpath="/configuration/log4net/appender/file/@value" value="${imagereview.log.dir}/PreHOCPLogs/PReHOCPInternalServiceHost.log" />	
		<xmlpoke file="${imagereview.prehocp.service.host.appconfig.file}" xpath="/configuration/appSettings/add[@key = 'DefaultDBTimeout']/@value" value="${prehocp.DefaultDBTimeout}" />
		<xmlpoke file="${imagereview.prehocp.service.host.appconfig.file}" xpath="/configuration/connectionStrings/add[@name='MOMSAlarmConnectionString']/@connectionString" value="${ips.connection.string}" />		
		
			<!--  Web Api Config -->
		<xmlpoke file="${imagereview.prehocp.webapi.config.file}" xpath="/configuration/system.serviceModel/client/endpoint[@address = 'net.tcp://localhost:8950/HOCPService']/@address" value="${ImageReview.prehocp.endpoint}" />
		<xmlpoke file="${imagereview.prehocp.webapi.config.file}" xpath="/configuration/system.serviceModel/client/endpoint/identity/servicePrincipalName/@value" value="${imagereview.prehocp.cluster}" />
		<xmlpoke file="${imagereview.prehocp.webapi.config.file}" xpath="/configuration/log4net/appender/file/@value" value="${imagereview.log.dir}\PreHOCPLogs\PreHOCPWebAPI.log" />	
		<xmlpoke file="${imagereview.prehocp.webapi.config.file}" xpath="/configuration/elmah/errorLog[@logPath = 'D:\\FPM_Log\\Elmah_WebAPI']/@logPath" value="${imagereview.log.dir}\\PreHOCPLogs\\Elmah_WebAPI" />
		<xmlpoke file="${imagereview.prehocp.webapi.config.file}" xpath="/configuration/log4net/root/level[@value = 'ALL']/@value" value="${log.error.level}" />	

		<!-- Scheduled Task: PreHOCPProcessor-->
		<xmlpoke file="${imagereview.prehocp.task.processor.config.file}" xpath="/configuration/connectionStrings/add[@name='HOCDBConnectionString']/@connectionString" value="${prehocp.connection.string}" />
		<xmlpoke file="${imagereview.prehocp.task.processor.config.file}" xpath="/configuration/connectionStrings/add[@name='MIRDBConnectionString']/@connectionString" value="${ips.connection.string}" />	
		<xmlpoke file="${imagereview.prehocp.task.processor.config.file}" xpath="/configuration/connectionStrings/add[@name='MOMSAlarmConnectionString']/@connectionString" value="${ips.connection.string}" />				
		<xmlpoke file="${imagereview.prehocp.task.processor.config.file}" xpath="/configuration/log4net/root/level[@value = 'ALL']/@value" value="${log.error.level}" />
		<xmlpoke file="${imagereview.prehocp.task.processor.config.file}" xpath="/configuration/log4net/appender/file/@value" value="${imagereview.log.dir}\PreHOCPLogs\PreHOCPProcessorTask.log" />	
		<xmlpoke file="${imagereview.prehocp.task.processor.config.file}" xpath="/configuration/appSettings/add[@key = 'TaskName']/@value" value="PreHOCProcessorTask" />				
		
		
		<!-- Scheduled Task: PreHOCPSendResults -->
		<xmlpoke file="${imagereview.prehocp.task.sendresults.config.file}" xpath="/configuration/log4net/root/level[@value = 'ALL']/@value" value="${log.error.level}" />
		<xmlpoke file="${imagereview.prehocp.task.sendresults.config.file}" xpath="/configuration/log4net/appender/file/@value" value="${imagereview.log.dir}\PreHOCPLogs\PreHOCPSendResultsProcessTask.log" />		
		<xmlpoke file="${imagereview.prehocp.task.sendresults.config.file}" xpath="/configuration/connectionStrings/add[@name='HOCDBConnectionString']/@connectionString" value="${prehocp.connection.string}" />
		<xmlpoke file="${imagereview.prehocp.task.sendresults.config.file}" xpath="/configuration/connectionStrings/add[@name='MIRDBConnectionString']/@connectionString" value="${ips.connection.string}" />	
		<xmlpoke file="${imagereview.prehocp.task.sendresults.config.file}" xpath="/configuration/connectionStrings/add[@name='MOMSAlarmConnectionString']/@connectionString" value="${ips.connection.string}" />	
		<xmlpoke file="${imagereview.prehocp.task.sendresults.config.file}" xpath="/configuration/appSettings/add[@key = 'TaskName']/@value" value="PreHOCSendResultsTask" />			
		
		<if test="${SSLFeature=='false'}">
			<xmlpoke file="${imagereview.prehocp.task.sendresults.config.file}" xpath="/configuration/appSettings/add[@key = 'ManualImageReviewUrl']/@value" value="http://${imagereview.fpm.alias}:9090/transactions" />	
			<xmlpoke file="${imagereview.prehocp.task.sendresults.config.file}" xpath="/configuration/appSettings/add[@key = 'ImageResultUrl']/@value" value="http://${imagereview.irr.alias}:5005/api/TransactionResults/AddTransactionResult" />	
		</if>
		
		<if test="${SSLFeature=='true'}">
			<xmlpoke file="${imagereview.prehocp.task.sendresults.config.file}" xpath="/configuration/appSettings/add[@key = 'ManualImageReviewUrl']/@value" value="https://${imagereview.fpm.alias}:9443/transactions" />	
			<xmlpoke file="${imagereview.prehocp.task.sendresults.config.file}" xpath="/configuration/appSettings/add[@key = 'ImageResultUrl']/@value" value="https://${imagereview.irr.alias}:5443/api/TransactionResults/AddTransactionResult" />		
		</if>
		
	</target>
	
	<!-- Filter the IMage Review IRR environment specific strings -->
	<target name="filter-ImageReview-IRR" description="Filter environment specific variables like servers, db, etc">	

		<!-- irr Service Host -->
		<xmlpoke file="${imagereview.irr.service.host.appconfig.file}" xpath="/configuration/connectionStrings/add[@name='IRRDBConnectionString']/@connectionString" value="${irr.connection.string}" />	
		<xmlpoke file="${imagereview.irr.service.host.appconfig.file}" xpath="/configuration/system.serviceModel/services/service/host/baseAddresses/add[@baseAddress = 'net.tcp://localhost:8970']/@baseAddress" value="${irr.nettcp.base.address}" />
		<xmlpoke file="${imagereview.irr.service.host.appconfig.file}" xpath="/configuration/system.serviceModel/services/service/host/baseAddresses/add[@baseAddress = 'http://localhost:8971']/@baseAddress" value="${irr.http.base.address}" />
		<xmlpoke file="${imagereview.irr.service.host.appconfig.file}" xpath="/configuration/log4net/root/level[@value = 'ALL']/@value" value="${log.error.level}" />
		<xmlpoke file="${imagereview.irr.service.host.appconfig.file}" xpath="/configuration/log4net/appender/file/@value" value="${imagereview.log.dir}/IRRLogs/IRR_IRRInternalServiceHost.log" />	
		<xmlpoke file="${imagereview.irr.service.host.appconfig.file}" xpath="/configuration/appSettings/add[@key = 'DefaultDBTimeout']/@value" value="${irr.DefaultDBTimeout}" />
		
		<!--  Web Api Config -->
		<xmlpoke file="${imagereview.irr.webapi.config.file}" xpath="/configuration/system.serviceModel/client/endpoint[@address = 'net.tcp://localhost:8970/IRRService']/@address" value="${ImageReview.irr.endpoint}" />
		<xmlpoke file="${imagereview.irr.webapi.config.file}" xpath="/configuration/system.serviceModel/client/endpoint/identity/servicePrincipalName/@value" value="${imagereview.irr.cluster}" />
		<xmlpoke file="${imagereview.irr.webapi.config.file}" xpath="/configuration/log4net/appender/file/@value" value="${imagereview.irr.webapi.log.file}" />	
		<xmlpoke file="${imagereview.irr.webapi.config.file}" xpath="/configuration/elmah/errorLog[@logPath = 'D:\\IRR_Log\\Elmah_WebAPI']/@logPath" value="${imagereview.log.dir}\\IRRLogs\\Elmah_WebAPI" />
		<xmlpoke file="${imagereview.irr.webapi.config.file}" xpath="/configuration/log4net/root/level[@value = 'ALL']/@value" value="${log.error.level}" />	

		
		<!-- Scheduled Task: IRRSendResults -->
		<xmlpoke file="${imagereview.irr.task.sendresults.config.file}" xpath="/configuration/log4net/root/level[@value = 'ALL']/@value" value="${log.error.level}" />
		<xmlpoke file="${imagereview.irr.task.sendresults.config.file}" xpath="/configuration/log4net/appender/file/@value" value="${imagereview.log.dir}\IRRLogs\IRRSendResultsTask.log" />		
		<xmlpoke file="${imagereview.irr.task.sendresults.config.file}" xpath="/configuration/connectionStrings/add[@name='IRRDBConnectionString']/@connectionString" value="${irr.connection.string}" />		
		<xmlpoke file="${imagereview.irr.task.sendresults.config.file}" xpath="/configuration/appSettings/add[@key = 'GenerateToken']/@value" value="${irr.task.GenerateToken}" />	
		<xmlpoke file="${imagereview.irr.task.sendresults.config.file}" xpath="/configuration/appSettings/add[@key = 'JWTClientId']/@value" value="${irr.task.JWTClientId}" />	
		<xmlpoke file="${imagereview.irr.task.sendresults.config.file}" xpath="/configuration/appSettings/add[@key = 'UserName']/@value" value="${irr.task.UserName}" />	
		<xmlpoke file="${imagereview.irr.task.sendresults.config.file}" xpath="/configuration/appSettings/add[@key = 'PassWord']/@value" value="${irr.task.PassWord}" />
		<xmlpoke file="${imagereview.irr.task.sendresults.config.file}" xpath="/configuration/appSettings/add[@key = 'SendImageResultUrl']/@value" value="${irr.task.SendImageResultUrl}" />	
		<xmlpoke file="${imagereview.irr.task.sendresults.config.file}" xpath="/configuration/appSettings/add[@key = 'SendImageResultTokenUrl']/@value" value="${irr.task.SendImageResultTokenUrl}" />		
		<xmlpoke failonerror="false" file="${imagereview.irr.task.sendresults.config.file}"   xpath="/configuration/appSettings/add[@key = 'UseNewTransactions']/@value" value="true" />					
		<if test="${SSLFeature=='false'}">
			<xmlpoke file="${imagereview.irr.task.sendresults.config.file}" failonerror="false"  xpath="/configuration/appSettings/add[@key = 'FPMImageResultUrl']/@value" value="http://${imagereview.fingerprint.cluster}:9090/api/fpm/ImageReviewResult" />	
		</if>		
		<if test="${SSLFeature=='true'}">
			<xmlpoke file="${imagereview.irr.task.sendresults.config.file}" xpath="/configuration/appSettings/add[@key = 'FPMImageResultUrl']/@value" value="https://${imagereview.fingerprint.cluster}:9443/api/fpm/ImageReviewResult" />			
		</if>
		
		
			<!-- Scheduled Task: IRRSendResults -->
		<xmlpoke file="${imagereview.irr.task.sendresultsretries.config.file}" xpath="/configuration/log4net/root/level[@value = 'ALL']/@value" value="${log.error.level}" />
		<xmlpoke file="${imagereview.irr.task.sendresultsretries.config.file}" xpath="/configuration/log4net/appender/file/@value" value="${imagereview.log.dir}\IRRLogs\IRRSendResultsTask.log" />		
		<xmlpoke file="${imagereview.irr.task.sendresultsretries.config.file}" xpath="/configuration/connectionStrings/add[@name='IRRDBConnectionString']/@connectionString" value="${irr.connection.string}" />		
		<xmlpoke file="${imagereview.irr.task.sendresultsretries.config.file}" xpath="/configuration/appSettings/add[@key = 'GenerateToken']/@value" value="${irr.task.GenerateToken}" />	
		<xmlpoke file="${imagereview.irr.task.sendresultsretries.config.file}" xpath="/configuration/appSettings/add[@key = 'JWTClientId']/@value" value="${irr.task.JWTClientId}" />	
		<xmlpoke file="${imagereview.irr.task.sendresultsretries.config.file}" xpath="/configuration/appSettings/add[@key = 'UserName']/@value" value="${irr.task.UserName}" />	
		<xmlpoke file="${imagereview.irr.task.sendresultsretries.config.file}" xpath="/configuration/appSettings/add[@key = 'PassWord']/@value" value="${irr.task.PassWord}" />
		<xmlpoke file="${imagereview.irr.task.sendresultsretries.config.file}" xpath="/configuration/appSettings/add[@key = 'SendImageResultUrl']/@value" value="${irr.task.SendImageResultUrl}" />	
		<xmlpoke file="${imagereview.irr.task.sendresultsretries.config.file}" xpath="/configuration/appSettings/add[@key = 'SendImageResultTokenUrl']/@value" value="${irr.task.SendImageResultTokenUrl}" />		
		<xmlpoke failonerror="false" file="${imagereview.irr.task.sendresultsretries.config.file}" xpath="/configuration/appSettings/add[@key = 'UseNewTransactions']/@value" value="false" />					
		<if test="${SSLFeature=='false'}">
			<xmlpoke file="${imagereview.irr.task.sendresultsretries.config.file}" xpath="/configuration/appSettings/add[@key = 'FPMImageResultUrl']/@value" value="http://${imagereview.fingerprint.cluster}:9090/api/fpm/ImageReviewResult" />	
		</if>		
		<if test="${SSLFeature=='true'}">
			<xmlpoke file="${imagereview.irr.task.sendresultsretries.config.file}" xpath="/configuration/appSettings/add[@key = 'FPMImageResultUrl']/@value" value="https://${imagereview.fingerprint.cluster}:9443/api/fpm/ImageReviewResult" />			
		</if>
		
	
		
	</target>
	<target name="filter-ImageReview-ActiveDirectory" description="Filter environment specific variables like servers, db, etc">	
	<!-- Active Directory polling task -->
		<xmlpoke file="${imagereview.activedirectory.task.config.file}" xpath="/configuration/appSettings/add[@key = 'ServiceUser']/@value" value="${ServiceUser}" />
		<xmlpoke file="${imagereview.activedirectory.task.config.file}" xpath="/configuration/appSettings/add[@key = 'ServicePassword']/@value" value="${ServicePassword}" />
		<xmlpoke file="${imagereview.activedirectory.task.config.file}" xpath="/configuration/appSettings/add[@key = 'DefaultDomain']/@value" value="${DefaultDomain}" />
		<xmlpoke file="${imagereview.activedirectory.task.config.file}" xpath="/configuration/appSettings/add[@key = 'GroupDefaultOU']/@value" value="${GroupDefaultOU}" />
		<xmlpoke file="${imagereview.activedirectory.task.config.file}" xpath="/configuration/appSettings/add[@key = 'UserDefaultOU']/@value" value="${UserDefaultOU}" />
		<xmlpoke file="${imagereview.activedirectory.task.config.file}" xpath="/configuration/appSettings/add[@key = 'DefaultRootOU']/@value" value="${DefaultRootOU}" />
		<xmlpoke file="${imagereview.activedirectory.task.config.file}" xpath="/configuration/connectionStrings/add[@name='ADPDBConnectionString']/@connectionString" value="${ips.connection.string}" />	
		<xmlpoke file="${imagereview.activedirectory.task.config.file}" xpath="/configuration/log4net/appender/file/@value" value="${imagereview.log.dir}\IRRLogs\ActiveDirectoryTask.log" />	
	</target>
	<target name="filter-ImageReview-MIR-ManualResultsTask" description="Filter environment specific variables like servers, db, etc">	
	<!-- Scheduled Task: ManualImageResultsTask -->
		<xmlpoke file="${imagereview.mir.mrt.task.manualresults.config.file}" xpath="/configuration/log4net/root/level[@value = 'ALL']/@value" value="${log.error.level}" />
		<xmlpoke file="${imagereview.mir.mrt.task.manualresults.config.file}" xpath="/configuration/log4net/appender/file/@value" value="${imagereview.log.dir}\MIRLogs\ImgRevManualResults.log" />		
		<xmlpoke file="${imagereview.mir.mrt.task.manualresults.config.file}" xpath="/configuration/connectionStrings/add[@name='IMGREVDBConnectionString']/@connectionString" value="${ips.connection.string}" />
		
		<xmlpoke file="${imagereview.mir.mrt.task.manualresults.config.file}" xpath="/configuration/appSettings/add[@key = 'ImageReviewResultsApiUrlExtn']/@value" value="${mir.task.ImageReviewResultsApiUrlExtn}" />	
	
		<if test="${SSLFeature=='false'}"> 
			<xmlpoke file="${imagereview.mir.mrt.task.manualresults.config.file}" xpath="/configuration/appSettings/add[@key = 'ImageReviewResultsApiUrl']/@value" value="http://${imagereview.irr.alias}:5005" />
		</if>
		
		<if test="${SSLFeature=='true'}"> 
			<xmlpoke file="${imagereview.mir.mrt.task.manualresults.config.file}" xpath="/configuration/appSettings/add[@key = 'ImageReviewResultsApiUrl']/@value" value="https://${imagereview.irr.alias}:5443" />
		</if>
	
	</target>
	
	
	
	<!-- Filter the Image Review Transaction Server environment specific strings -->
	<target name="filter-ImageReview-TRANS" description="Filter environment specific variables like servers, db, etc">	

		<!-- MIR Transactions Server Service Host MIRInternalHost-->
		<xmlpoke file="${imagereview.trans.service.host.appconfig.file}" xpath="/configuration/connectionStrings/add[@name='MIRDBConnectionString']/@connectionString" value="${ips.connection.string}" />	
		<xmlpoke file="${imagereview.trans.service.host.appconfig.file}" xpath="/configuration/connectionStrings/add[@name='RPTDBConnectionString']/@connectionString" value="${rpt.connection.string}" />	
		<xmlpoke file="${imagereview.trans.service.host.appconfig.file}" xpath="/configuration/system.serviceModel/services/service/host/baseAddresses/add[@baseAddress = 'net.tcp://localhost:8200']/@baseAddress" value="${trans.nettcp.base.address}" />
		<xmlpoke file="${imagereview.trans.service.host.appconfig.file}" xpath="/configuration/system.serviceModel/services/service/host/baseAddresses/add[@baseAddress = 'http://localhost:8201']/@baseAddress" value="${trans.http.base.address}" />
		<xmlpoke file="${imagereview.trans.service.host.appconfig.file}" xpath="/configuration/log4net/root/level[@value = 'ALL']/@value" value="${log.error.level}" />
		<xmlpoke file="${imagereview.trans.service.host.appconfig.file}" xpath="/configuration/log4net/appender/file/@value" value="${imagereview.log.dir}/MIRLogs/mir_MIRInternalServiceHost.log" />	
		<xmlpoke file="${imagereview.trans.service.host.appconfig.file}" xpath="/configuration/appSettings/add[@key = 'DefaultDBTimeout']/@value" value="${trans.DefaultDBTimeout}" />
		<xmlpoke file="${imagereview.trans.service.host.appconfig.file}" xpath="/configuration/appSettings/add[@key = 'ServiceUser']/@value" value="${ServiceUser}" />
		<xmlpoke file="${imagereview.trans.service.host.appconfig.file}" xpath="/configuration/appSettings/add[@key = 'ServicePassword']/@value" value="${ServicePassword}" />
		<xmlpoke file="${imagereview.trans.service.host.appconfig.file}" xpath="/configuration/appSettings/add[@key = 'DefaultDomain']/@value" value="${DefaultDomain}" />
		<xmlpoke file="${imagereview.trans.service.host.appconfig.file}" xpath="/configuration/appSettings/add[@key = 'GroupDefaultOU']/@value" value="${GroupDefaultOU}" />
		<xmlpoke file="${imagereview.trans.service.host.appconfig.file}" xpath="/configuration/appSettings/add[@key = 'UserDefaultOU']/@value" value="${UserDefaultOU}" />
		<xmlpoke file="${imagereview.trans.service.host.appconfig.file}" xpath="/configuration/appSettings/add[@key = 'DefaultRootOU']/@value" value="${DefaultRootOU}" />
		<xmlpoke file="${imagereview.trans.service.host.appconfig.file}" xpath="/configuration/appSettings/add[@key = 'TransactionTimeZone']/@value" value="${TransactionTimeZone}" />
		
		<!--  Web Api Config -->
		<xmlpoke file="${imagereview.trans.webapi.config.file}" xpath="/configuration/system.serviceModel/client/endpoint/identity/servicePrincipalName/@value" value="${imagereview.trans.cluster}" />
		<xmlpoke file="${imagereview.trans.webapi.config.file}" xpath="/configuration/system.serviceModel/client/endpoint[@address = 'net.tcp://localhost:8200/MIRService']/@address" value="${ImageReview.trans.endpoint}" />
		<xmlpoke file="${imagereview.trans.webapi.config.file}" xpath="/configuration/log4net/appender/file/@value" value="${imagereview.trans.webapi.log.file}" />	
		<xmlpoke file="${imagereview.trans.webapi.config.file}" xpath="/configuration/log4net/root/level[@value = 'ALL']/@value" value="${log.error.level}" />	
	
	</target>
	
	<target name="filter-ImageReview-Client-Task" description="Filter environment specific variables like servers, db, etc">

		<!-- ImageClientServiceTask_RetrieveTransactions -->
		<xmlpoke file="${imagereview.mir.task.imageclient.retrievetrans.config.file}" xpath="/configuration/log4net/root/level/@value" value="${log.error.level}" />
		<xmlpoke failonerror="false" file="${imagereview.mir.task.imageclient.retrievetrans.config.file}" xpath="/configuration/connectionStrings/add[@name='IPSDBConnectionString']/@connectionString" value="${ips.connection.string}" />
		<xmlpoke file="${imagereview.mir.task.imageclient.retrievetrans.config.file}" xpath="/configuration/log4net/appender/file/@value" value="${imagereview.log.dir}/MIRLogs/tcore.ImageReview.ImageClientServiceHost_RetrieveTransactions.log" />	
		<xmlpoke failonerror="false" file="${imagereview.mir.task.imageclient.retrievetrans.config.file}" xpath="/configuration/system.serviceModel/client/endpoint[@address = 'net.tcp://localhost:9400/ImageReviewService']/@address" value="${ServiceAddress}" />
		<xmlpoke failonerror="false" file="${imagereview.mir.task.imageclient.retrievetrans.config.file}" xpath="/configuration/system.serviceModel/client/endpoint/identity/servicePrincipalName/@value" value="${imagereview.app.cluster}" />
		<xmlpoke failonerror="false" file="${imagereview.mir.task.imageclient.retrievetrans.config.file}" xpath="/configuration/system.serviceModel/client/endpoint[@address = 'http://#{QFreeISSHost}/ManualReview.asmx']/@address" value="${imageclientservice.qfree.endpoint}" />
		<xmlpoke file="${imagereview.mir.task.imageclient.retrievetrans.config.file}" xpath="/configuration/appSettings/add[@key = 'RequestNewImageTransactions']/@value" value="true" />	
		<xmlpoke file="${imagereview.mir.task.imageclient.retrievetrans.config.file}" xpath="/configuration/appSettings/add[@key = 'AcknowledgeImageTransactions']/@value" value="true" />	
		<xmlpoke file="${imagereview.mir.task.imageclient.retrievetrans.config.file}" xpath="/configuration/appSettings/add[@key = 'SendManualReviewResults']/@value" value="false" />	
		<xmlpoke file="${imagereview.mir.task.imageclient.retrievetrans.config.file}" xpath="/configuration/appSettings/add[@key = 'SleepTimeBetweenImageDownloads']/@value" value="${SleepTimeBetweenImageDownloads}" />	

		<!-- Only replace if the project/env = SBCTA PROD -->		
		<if test="${deployment.environment=='SBCTA'}"> 
			<if test="${deployment.type=='PROD'}"> 
				<xmlpoke file="${imagereview.mir.task.imageclient.retrievetrans.config.file}" xpath="/configuration/appSettings/add[@key = 'PluginUserName']/@value" value="${PluginUserName}" />	
				<xmlpoke file="${imagereview.mir.task.imageclient.retrievetrans.config.file}" xpath="/configuration/appSettings/add[@key = 'PluginPassword']/@value" value="${PluginPassword}" />
				<xmlpoke failonerror="false" file="${imagereview.mir.task.imageclient.retrievetrans.config.file}" xpath="/configuration/connectionStrings/add[@name='RPTDBConnectionString']/@connectionString" value="${ips.connection.string}" />
			</if>	
		</if>		
		
		<if test="${SSLFeature=='false'}"> 
		<xmlpoke file="${imagereview.mir.task.imageclient.retrievetrans.config.file}" xpath="/configuration/appSettings/add[@key = 'ImageTransactionService']/@value" value="http://${imagereview.web.alias}:6500" />	
		</if>
		
		<if test="${SSLFeature=='true'}"> 
		<xmlpoke file="${imagereview.mir.task.imageclient.retrievetrans.config.file}" xpath="/configuration/appSettings/add[@key = 'ImageTransactionService']/@value" value="https://${imagereview.web.alias}:7443" />	
		</if>
		
		<!-- ImageClientServiceTask_SendResponse -->
		<xmlpoke file="${imagereview.mir.task.imageclient.sendresponse.config.file}" xpath="/configuration/log4net/root/level/@value" value="${log.error.level}" />
		<xmlpoke failonerror="false" file="${imagereview.mir.task.imageclient.sendresponse.config.file}" xpath="/configuration/connectionStrings/add[@name='IPSDBConnectionString']/@connectionString" value="${ips.connection.string}" />
		<xmlpoke file="${imagereview.mir.task.imageclient.sendresponse.config.file}" xpath="/configuration/log4net/appender/file/@value" value="${imagereview.log.dir}/MIRLogs/tcore.ImageReview.ImageClientServiceHost_SendResponse.log" />	
		<xmlpoke failonerror="false" file="${imagereview.mir.task.imageclient.sendresponse.config.file}" xpath="/configuration/system.serviceModel/client/endpoint[@address = 'net.tcp://localhost:9400/ImageReviewService']/@address" value="${ServiceAddress}" />
		<xmlpoke failonerror="false" file="${imagereview.mir.task.imageclient.sendresponse.config.file}" xpath="/configuration/system.serviceModel/client/endpoint/identity/servicePrincipalName/@value" value="${imagereview.app.cluster}" />
		<xmlpoke failonerror="false" file="${imagereview.mir.task.imageclient.sendresponse.config.file}" xpath="/configuration/system.serviceModel/client/endpoint[@address = 'http://#{QFreeISSHost}/ManualReview.asmx']/@address" value="${imageclientservice.qfree.endpoint}" />
		<xmlpoke file="${imagereview.mir.task.imageclient.sendresponse.config.file}" xpath="/configuration/appSettings/add[@key = 'RequestNewImageTransactions']/@value" value="false" />	
		<xmlpoke file="${imagereview.mir.task.imageclient.sendresponse.config.file}" xpath="/configuration/appSettings/add[@key = 'AcknowledgeImageTransactions']/@value" value="true" />	
		<xmlpoke file="${imagereview.mir.task.imageclient.sendresponse.config.file}" xpath="/configuration/appSettings/add[@key = 'SendManualReviewResults']/@value" value="true" />	
		<xmlpoke file="${imagereview.mir.task.imageclient.sendresponse.config.file}" xpath="/configuration/appSettings/add[@key = 'SleepTimeBetweenImageDownloads']/@value" value="${SleepTimeBetweenImageDownloads}" />	

		<!-- Only replace if the project/env = SBCTA PROD -->		
		<if test="${deployment.environment=='SBCTA'}"> 
			<if test="${deployment.type=='PROD'}"> 
				<xmlpoke file="${imagereview.mir.task.imageclient.sendresponse.config.file}" xpath="/configuration/appSettings/add[@key = 'PluginUserName']/@value" value="${PluginUserName}" />	
				<xmlpoke file="${imagereview.mir.task.imageclient.sendresponse.config.file}" xpath="/configuration/appSettings/add[@key = 'PluginPassword']/@value" value="${PluginPassword}" />	
			</if>	
		</if>	
		
		<if test="${SSLFeature=='false'}"> 
		<xmlpoke file="${imagereview.mir.task.imageclient.sendresponse.config.file}" xpath="/configuration/appSettings/add[@key = 'ImageTransactionService']/@value" value="http://${imagereview.web.alias}:6500" />	
		</if>
		
		<if test="${SSLFeature=='true'}"> 
		<xmlpoke file="${imagereview.mir.task.imageclient.sendresponse.config.file}" xpath="/configuration/appSettings/add[@key = 'ImageTransactionService']/@value" value="https://${imagereview.web.alias}:7443" />	
		</if>
		
	</target>
	
	<target name="filter-VOTTQueue-Task" description="Filter environment specific variables like servers, db, etc">
		<!-- ImageClientServiceTask_RetrieveTransactions -->
		<xmlpoke file="${imagereview.mir.task.vottqueuetask.config.file}" xpath="/configuration/log4net/root/level/@value" value="${log.error.level}" />
		<xmlpoke file="${imagereview.mir.task.vottqueuetask.config.file}" xpath="/configuration/appSettings/add[@key = 'VOTTImageUrl']/@value" value="${VOTTImageUrl}" />	
		<xmlpoke file="${imagereview.mir.task.vottqueuetask.config.file}" xpath="/configuration/log4net/appender/file/@value" value="${imagereview.log.dir}/MIRLogs/VOTTQueueTask.log" />	
		<xmlpoke file="${imagereview.mir.task.vottqueuetask.config.file}" xpath="/configuration/connectionStrings/add[@name='IPSDBConnectionString']/@connectionString" value="${ips.connection.string}" />	
		<xmlpoke failonerror="false" file="${imagereview.mir.task.vottqueuetask.config.file}" xpath="/configuration/connectionStrings/add[@name='RPTDBConnectionString']/@connectionString" value="${rpt.connection.string}" />		
	</target>
	
	<target name="filter-MIREmailNotification-Task" description="Filter environment specific variables like servers, db, etc">
		<xmlpoke file="${imagereview.mir.task.miremailnotificationtask.config.file}" xpath="/configuration/log4net/root/level/@value" value="${log.error.level}" />	
		<xmlpoke file="${imagereview.mir.task.miremailnotificationtask.config.file}" xpath="/configuration/log4net/appender/file/@value" value="${imagereview.log.dir}/MIRLogs/MIREmailNotificationTask.log" />	
		<xmlpoke file="${imagereview.mir.task.miremailnotificationtask.config.file}" xpath="/configuration/connectionStrings/add[@name='IPSDBConnectionString']/@connectionString" value="${ips.connection.string}" />
		<xmlpoke failonerror="false" file="${imagereview.mir.task.miremailnotificationtask.config.file}" xpath="/configuration/connectionStrings/add[@name='RPTDBConnectionString']/@connectionString" value="${rpt.connection.string}" />
		<xmlpoke file="${imagereview.mir.task.miremailnotificationtask.config.file}" xpath="/configuration/appSettings/add[@key = 'SMTPServer']/@value" value="${SMTPServer}" />		
		<xmlpoke file="${imagereview.mir.task.miremailnotificationtask.config.file}" xpath="/configuration/appSettings/add[@key = 'useEmailDefaultCredentials']/@value" value="${useEmailDefaultCredentials}" />			
		<xmlpoke file="${imagereview.mir.task.miremailnotificationtask.config.file}" xpath="/configuration/appSettings/add[@key = 'emailUserName']/@value" value="${emailUserName}" />			
		<xmlpoke file="${imagereview.mir.task.miremailnotificationtask.config.file}" xpath="/configuration/appSettings/add[@key = 'emailPassword']/@value" value="${emailPassword}" />			
		<xmlpoke file="${imagereview.mir.task.miremailnotificationtask.config.file}" xpath="/configuration/appSettings/add[@key = 'emailDomainName']/@value" value="${emailDomainName}" />			
		<xmlpoke file="${imagereview.mir.task.miremailnotificationtask.config.file}" xpath="/configuration/appSettings/add[@key = 'SMTPPort']/@value" value="${SMTPPort}" />					
	</target>
	
	
	<target name="filter-ImageReview-Main" description="Filter environment specific variables like servers, db, etc">	

		<!-- Image Review -->
		<xmlpoke file="${imagereview.mir.service.host.ir.appconfig.file}" xpath="/configuration/log4net/root/level/@value" value="${log.error.level}" />
		<xmlpoke file="${imagereview.mir.service.host.ir.appconfig.file}" xpath="/configuration/log4net/appender/file/@value" value="${imagereview.log.dir}/MIRLogs/tcore.ImageReview.ImageReviewServiceHost.log" />	
		<xmlpoke file="${imagereview.mir.service.host.ir.appconfig.file}" xpath="/configuration/connectionStrings/add[@name='IPSDBConnectionString']/@connectionString" value="${ips.connection.string}" />
		<xmlpoke failonerror="false" file="${imagereview.mir.service.host.ir.appconfig.file}" xpath="/configuration/connectionStrings/add[@name='RPTDBConnectionString']/@connectionString" value="${rpt.connection.string}" />
		<xmlpoke file="${imagereview.mir.service.host.ir.appconfig.file}" xpath="/configuration/appSettings/add[@key = 'PostOcrConfidenceUrl']/@value" value="${PostOcrConfidenceUrl}" />
		<xmlpoke file="${imagereview.mir.service.host.ir.appconfig.file}" xpath="/configuration/system.serviceModel/services/service/host/baseAddresses/add[@baseAddress = 'net.tcp://localhost:9400']/@baseAddress" value="net.tcp://${imagereview.app.server}:9400" />
		<xmlpoke file="${imagereview.mir.service.host.ir.appconfig.file}" xpath="/configuration/system.serviceModel/services/service/host/baseAddresses/add[@baseAddress = 'http://localhost:9401']/@baseAddress" value="http://${imagereview.app.server}:9401" />
		<xmlpoke file="${imagereview.mir.service.host.ir.appconfig.file}" xpath="/configuration/appSettings/add[@key = 'ServiceUser']/@value" value="${ServiceUser}" />
		<xmlpoke file="${imagereview.mir.service.host.ir.appconfig.file}" xpath="/configuration/appSettings/add[@key = 'ServicePassword']/@value" value="${ServicePassword}" />
		<xmlpoke file="${imagereview.mir.service.host.ir.appconfig.file}" xpath="/configuration/appSettings/add[@key = 'DefaultDomain']/@value" value="${DefaultDomain}" />
		<xmlpoke file="${imagereview.mir.service.host.ir.appconfig.file}" xpath="/configuration/appSettings/add[@key = 'GroupDefaultOU']/@value" value="${GroupDefaultOU}" />
		<xmlpoke file="${imagereview.mir.service.host.ir.appconfig.file}" xpath="/configuration/appSettings/add[@key = 'UserDefaultOU']/@value" value="${UserDefaultOU}" />
		<xmlpoke file="${imagereview.mir.service.host.ir.appconfig.file}" xpath="/configuration/appSettings/add[@key = 'DefaultRootOU']/@value" value="${DefaultRootOU}" />
		<xmlpoke file="${imagereview.mir.service.host.ir.appconfig.file}" xpath="/configuration/appSettings/add[@key = 'DefaultStateId']/@value" value="${DefaultStateId}" />
		<if test="${HOCPFeature=='true'}"> 
			<xmlpoke file="${imagereview.mir.service.host.ir.appconfig.file}" xpath="/configuration/appSettings/add[@key = 'PostOcrConfidence']/@value" value="true" />	
		</if>
		<if test="${HOCPFeature=='false'}"> 
			<xmlpoke file="${imagereview.mir.service.host.ir.appconfig.file}" xpath="/configuration/appSettings/add[@key = 'PostOcrConfidence']/@value" value="false" />	
		</if>		
		
		<!-- License Plate Manager
		<xmlpoke file="${imagereview.mir.service.host.licenseplatemanager.appconfig.file}" xpath="/configuration/applicationConfiguration/@logLevel" value="${log.error.level}" />
		<xmlpoke file="${imagereview.mir.service.host.licenseplatemanager.appconfig.file}" xpath="/configuration/applicationConfiguration/@logPath" value="${imagereview.log.dir}/MIRLogs/tcore.ImageReview.LicensePlateManagerServiceHost.log" />	
		-->
		<!-- Transaction Qing -->
		<if test="${TransactionQueueServiceFeature=='true'}"> 
			<xmlpoke file="${imagereview.mir.service.host.transactionqing.appconfig.file}" xpath="/configuration/applicationConfiguration/@logLevel" value="${log.error.level}" />
			<xmlpoke file="${imagereview.mir.service.host.transactionqing.appconfig.file}" xpath="/configuration/applicationConfiguration/@logPath" value="${imagereview.log.dir}/MIRLogs/tcore.ImageReview.TransactionQingHost.log" />	
			<xmlpoke file="${imagereview.mir.service.host.transactionqing.appconfig.file}" xpath="/configuration/appSettings/add[@key = 'ServiceAddress']/@value" value="${ServiceAddress}" />
			<xmlpoke file="${imagereview.mir.service.host.transactionqing.appconfig.file}" xpath="/configuration/system.serviceModel/client/endpoint[@address = 'net.tcp://localhost:9400/ImageReviewService']/@address" value="${ServiceAddress}" />
			<xmlpoke file="${imagereview.mir.service.host.transactionqing.appconfig.file}" xpath="/configuration/system.serviceModel/client/endpoint/identity/servicePrincipalName/@value" value="${imagereview.app.cluster}" />
		</if>
	
		<!-- Web API -->
		<xmlpoke file="${imagereview.mir.webapi.config.file}" xpath="/configuration/log4net/root/level/@value" value="${log.error.level}" />
		<xmlpoke file="${imagereview.mir.webapi.config.file}" xpath="/configuration/log4net/appender/file[@value = 'D:\MIRLogs\ImageReviewWebApi.log']/@value" value="${imagereview.log.dir}/MIRLogs/ImageReviewWebApi.log" />
		<xmlpoke failonerror="false" file="${imagereview.mir.webapi.config.file}" xpath="/configuration/log4net/appender/file[@value = 'D:\MIRLogs\TimeTakenNAS.log']/@value" value="${imagereview.log.dir}/MIRLogs/TimeTakenNAS.log" />
		<xmlpoke file="${imagereview.mir.webapi.config.file}" xpath="/configuration/appSettings/add[@key = 'reportServerURL']/@value" value="${report.server.URL}" />
		<xmlpoke file="${imagereview.mir.webapi.config.file}" xpath="/configuration/appSettings/add[@key = 'ServiceAddress']/@value" value="${ServiceAddress}" />
		<xmlpoke failonerror="false" file="${imagereview.mir.webapi.config.file}" xpath="/configuration/appSettings/add[@key = 'GoToAppServerDirectly']/@value" value="${GoToAppServerDirectly}" />
		
		<xmlpoke  file="${imagereview.mir.webapi.config.file}" xpath="/configuration/system.serviceModel/client/endpoint[@address = 'net.tcp://localhost:9400/ImageReviewService']/@address" value="${ServiceAddress}" />
		<xmlpoke failonerror="false" file="${imagereview.mir.webapi.config.file}" xpath="/configuration/appSettings/add[@key = 'TableauUrl']/@value" value="${imagereview.tableau.server.url}" />
		<xmlpoke failonerror="false" file="${imagereview.mir.webapi.config.file}" xpath="/configuration/appSettings/add[@key = 'TableauAdvancedUrl']/@value" value="${imagereview.tableau.server.url}" />
		<xmlpoke failonerror="false" file="${imagereview.mir.webapi.config.file}" xpath="/configuration/appSettings/add[@key = 'TableauContentUrl']/@value" value="${imagereview.tableau.content.url}" />
		<xmlpoke failonerror="false" file="${imagereview.mir.webapi.config.file}" xpath="/configuration/appSettings/add[@key = 'TableauProjectName']/@value" value="${imagereview.tableau.project.name}" />
		
		<!--<xmlpoke  file="${imagereview.mir.webapi.config.file}" xpath="/configuration/system.serviceModel/client/endpoint/identity/servicePrincipalName/[@value = 'localhost']/@value" value="${imagereview.app.cluster}" />-->

		<!-- new tcp service -->
			
			<exec program="ReplaceUtil.exe" failonerror="true">
				<arg value="${imagereview.mir.web.dist.WebAPI.dir}" />
				<arg value="config"/>
				<arg value="localhostService:9200/InternalService" />
				<arg value="${ifx.app.alias}${ifx.service.endpoint.port.name}" />
				<arg value="ALL"/>
			</exec>
			
			<exec program="ReplaceUtil.exe" failonerror="true">
				<arg value="${imagereview.mir.web.dist.WebAPI.dir}" />
				<arg value="config"/>
				<arg value="localhostService" />
				<arg value="${ifx.app.alias}" />
				<arg value="ALL"/>
			</exec>
			
			<exec program="ReplaceUtil.exe" failonerror="true">
				<arg value="${imagereview.mir.web.dist.WebAPI.dir}" />
				<arg value="config"/>
				<arg value="localhost" />
				<arg value="${imagereview.app.cluster}" />
				<arg value="ALL"/>
			</exec>
		
		<!-- Trunk - BAIFA/VTA3 TCP address needs to be highly customized only applies to Trunk and old IFX/AMS -->
		<xmlpoke failonerror="false" file="${imagereview.mir.webapi.config.file}" xpath="/configuration/appSettings/add[@key = 'InvokeWcfForNas']/@value" value="${InvokeWcfForNasValue}" />
			<exec program="ReplaceUtil.exe" failonerror="true">
					<arg value="${imagereview.mir.web.dist.WebAPI.dir}" />
					<arg value="config"/>
					<arg value="endpointAddressValue" />
					<arg value="net.tcp://${ifx.app.alias}${ifx.service.endpoint.port.name}" />
					<arg value="ALL"/>
			</exec>
			<exec program="ReplaceUtil.exe" failonerror="true">
					<arg value="${imagereview.mir.web.dist.WebAPI.dir}" />
					<arg value="config"/>
					<arg value="NASHostService" />
					<arg value="${ifx.app.alias}" />
					<arg value="ALL"/>
			</exec>
		<!-- AMS / BAIFA -->
		<if test="${InvokeWcfForNasValue=='1'}"> 
		
			<exec program="ReplaceUtil.exe" failonerror="true">
					<arg value="${imagereview.mir.web.dist.WebAPI.dir}" />
					<arg value="config"/>
					<arg value="endpointContractValue" />
					<arg value="IInternalService" />
					<arg value="ALL"/>
			</exec>	
		</if>
		<!-- VTA3 / IFX 1.0 -->
		<if test="${InvokeWcfForNasValue=='2'}"> 
			<exec program="ReplaceUtil.exe" failonerror="true">
				<arg value="${imagereview.mir.web.dist.WebAPI.dir}" />
				<arg value="config"/>
				<arg value="endpointContractValue" />
				<arg value="IIFXService" />
				<arg value="ALL"/>
			</exec>
		</if>
			
		<!-- Workflows -->
		<xmlpoke  failonerror="false" file="${imagereview.mir.worklow.policyruleengine.config.file}" xpath="/configuration/connectionStrings/add[@name='RuleSetStoreConnectionString']/@connectionString" value="${ips.connection.string}" />
		
		
		
		<if test="${SSLFeature=='false'}"> 
			<xmlpoke file="${imagereview.mir.webapi.config.file}" xpath="/configuration/appSettings/add[@key = 'WebSiteUrl']/@value" value="http://${WebSiteUrl}" />
			<xmlpoke file="${imagereview.mir.webapi.config.file}" xpath="/configuration/appSettings/add[@key = 'issuer']/@value" value="http://${imagereview.web.alias}:6555/" />
			<xmlpoke file="${imagereview.mir.webapi.config.file}" xpath="/configuration/appSettings/add[@key = 'PostImageReviewCompletionUrl']/@value" value="http://${ifx.web.alias}:9090/api/ManualImageReview/UpdateImageReviewCompletion/" />
		</if>
		<if test="${SSLFeature=='true'}"> 
			<xmlpoke file="${imagereview.mir.webapi.config.file}" xpath="/configuration/appSettings/add[@key = 'WebSiteUrl']/@value" value="https://${WebSiteUrl}" />
			<xmlpoke file="${imagereview.mir.webapi.config.file}" xpath="/configuration/appSettings/add[@key = 'issuer']/@value" value="https://${imagereview.web.alias}:6443/" />
			<xmlpoke  file="${imagereview.mir.webapi.config.file}" xpath="/configuration/appSettings/add[@key = 'PostImageReviewCompletionUrl']/@value" value="https://${ifx.web.alias}:9443/api/ManualImageReview/UpdateImageReviewCompletion/" />
			
		</if>
		
		<!-- Angular old WebUI -->
			<if test="${Angular9WebSiteFeature=='false'}">	
				<xmlpoke file="${imagereview.mir.web.config.file}" xpath="/configuration/appSettings/add[@key = 'ReportServerUrl']/@value" value="${report.server.URL}" />
				<xmlpoke file="${imagereview.mir.web.config.file}" xpath="/configuration/appSettings/add[@key = 'SsoWebApiUrl']/@value" value="${web.Transportal.webapi}" />
				<xmlpoke file="${imagereview.mir.web.config.file}" xpath="/configuration/appSettings/add[@key = 'LogOffRedirectUrl']/@value" value="${web.Transportal.website}" />
				<xmlpoke failonerror="false"  file="${imagereview.mir.web.config.file}" xpath="/configuration/appSettings/add[@key = 'ReportServerUserName']/@value" value="${reportuser}" />
				<xmlpoke failonerror="false"  file="${imagereview.mir.web.config.file}" xpath="/configuration/appSettings/add[@key = 'ReportServerPassword']/@value" value="${reportpassword}" />
				<if test="${SSLFeature=='false'}"> 
					<xmlpoke file="${imagereview.mir.web.config.file}" xpath="/configuration/appSettings/add[@key = 'TripApiUrl']/@value" value="http://${imagereview.app.alias}:5000" />
					<xmlpoke file="${imagereview.mir.web.config.file}" xpath="/configuration/appSettings/add[@key = 'TripRequestApiUrl']/@value" value="http://${imagereview.app.alias}:5001" />
					<xmlpoke file="${imagereview.mir.web.config.file}" xpath="/configuration/appSettings/add[@key = 'TripQueueApiUrl']/@value" value="http://${imagereview.app.alias}:5002" />
					<xmlpoke file="${imagereview.mir.web.config.file}" xpath="/configuration/appSettings/add[@key = 'WebServiceUrl']/@value" value="http://${imagereview.web.alias}:6555/" />
				</if>
				<if test="${SSLFeature=='true'}"> 
					<xmlpoke file="${imagereview.mir.web.config.file}" xpath="/configuration/appSettings/add[@key = 'TripApiUrl']/@value" value="https://${imagereview.app.alias}:4443" />
					<xmlpoke file="${imagereview.mir.web.config.file}" xpath="/configuration/appSettings/add[@key = 'TripRequestApiUrl']/@value" value="https://${imagereview.app.alias}:???" />
					<xmlpoke file="${imagereview.mir.web.config.file}" xpath="/configuration/appSettings/add[@key = 'TripQueueApiUrl']/@value" value="https://${imagereview.app.alias}:???" />
					<xmlpoke file="${imagereview.mir.web.config.file}" xpath="/configuration/appSettings/add[@key = 'WebServiceUrl']/@value" value="https://${imagereview.web.alias}:6443/" />
				</if>
			</if>
			
			<if test="${Angular9WebSiteFeature=='true'}">	
				<xmlpoke file="${imagereview.mir.reports.web.config.file}" xpath="/configuration/appSettings/add[@key = 'reportServerURL']/@value" value="${report.server.URL}" />	
				<xmlpoke file="${imagereview.mir.webapi.config.file}" xpath="/configuration/appSettings/add[@key = 'ImageDLStreamOptimization']/@value" value="true" />	
			</if>
		

	</target>
	
	<target name="filter-External-Transactions-API" description="Filter environment for External Transactions API">
	<!-- Transactions Web Server Site -->
		<xmlpoke file="${imagereview.mir.web.trans.config.file}" xpath="/configuration/appSettings/add[@key = 'ServiceAddress']/@value" value="${ServiceAddress}" />
		<xmlpoke file="${imagereview.mir.web.trans.config.file}" xpath="/configuration/connectionStrings/add[@name='IPSTransactionsEntities']/@connectionString" value="${ips.trans.connection.string}" />
		<if test="${SSLFeature=='false'}"> 
		<xmlpoke file="${imagereview.mir.web.trans.config.file}" xpath="/configuration/appSettings/add[@key = 'issuer']/@value" value="http://${imagereview.web.alias}:6555" />	
		<xmlpoke failonerror="false" file="${imagereview.mir.qfree.plugin.config.file}" xpath="/configuration/system.serviceModel/client/endpoint[@address = 'http://#{QFreeISSHost}/ManualReview.asmx']/@address" value="${imageclientservice.qfree.endpoint}" />	
		</if>
		<if test="${SSLFeature=='true'}"> 
		<xmlpoke file="${imagereview.mir.web.trans.config.file}" xpath="/configuration/appSettings/add[@key = 'issuer']/@value" value="https://${imagereview.web.alias}:6443" />
		<xmlpoke failonerror="false" file="${imagereview.mir.qfree.plugin.config.file}" xpath="/configuration/system.serviceModel/client/endpoint[@address = 'http://#{QFreeISSHost}/ManualReview.asmx']/@address" value="${imageclientservice.qfree.endpoint}" />	
		
		</if>
	</target>

	<target name="filter-CBDTPLogging" description="Filter environment for CBDTP Logging in Production">
	<!-- Transactions Web Server Site -->
		<xmlpoke file="${imagereview.mir.service.host.ir.appconfig.file}" xpath="/configuration/log4net/appender/maxSizeRollBackups/@value" value="9000" />	
		<xmlpoke file="${imagereview.trans.webapi.config.file}" xpath="/configuration/log4net/appender/maxSizeRollBackups/@value" value="250" />	
		<xmlpoke file="${imagereview.trans.service.host.appconfig.file}" xpath="/configuration/log4net/appender/maxSizeRollBackups/@value" value="1500" />
		<xmlpoke file="${imagereview.mir.webapi.config.file}" xpath="/configuration/log4net/appender/maxSizeRollBackups/@value" value="4500" />		
	</target>
	
	
	<target name="filter-VehicleDetection-Task" description="Filter environment for VehicleDetection-Task">
	<!-- Transactions Web Server Site -->
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${base.dist.dir}\VehicleDetectionTask" />
			<arg value="json"/>
			<arg value="mir-db-server" />
			<arg value="${imagereview.db.server}" />
			<arg value="ALL"/>
		</exec>
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${base.dist.dir}\VehicleDetectionTask" />
			<arg value="json"/>
			<arg value="customer-db-server" />
			<arg value="${thirdparty.detection.db.server}" />
			<arg value="ALL"/>
		</exec>
		<if test="${SSLFeature=='false'}"> 
			<exec program="ReplaceUtil.exe" failonerror="true">
				<arg value="${base.dist.dir}\VehicleDetectionTask" />
				<arg value="json"/>
				<arg value="http://transactions-api-server:5001" />
				<arg value="http://${imagereview.trans.alias}:5001" />
				<arg value="ALL"/>
			</exec>
		</if>
		<if test="${SSLFeature=='true'}"> 
			<exec program="ReplaceUtil.exe" failonerror="true">
				<arg value="${base.dist.dir}\VehicleDetectionTask" />
				<arg value="json"/>
				<arg value="http://transactions-api-server:5001" />
				<arg value="https://${imagereview.trans.alias}:4443" />
				<arg value="ALL"/>
			</exec>
		</if>
	</target>
	
	<target name="filter-ImgReviewSendTransTaskFeature" description="Filter environment for ImgRevSendTrans-Task">
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${base.dist.dir}" />
			<arg value="json"/>
			<arg value="ifx-db-listener" />
			<arg value="${txn.db.listener}" />
			<arg value="ALL"/>
		</exec>
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${base.dist.dir}" />
			<arg value="json"/>
			<arg value="Database=ifx-db-name" />
			<arg value="Database=ICD_MW" />
			<arg value="ALL"/>
		</exec>
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${base.dist.dir}" />
			<arg value="json"/>
			<arg value="http://mir-txn-api:9090/api/ManualImageReview/transactions" />
			<arg value="${mir.txn.alias}" />
			<arg value="ALL"/>
		</exec>
		
		<!-- Replace Retries value -->
		<exec program="fnr-retries.bat" failonerror="true" >
		<arg value="${base.dist.dir}\ImgReviewSendTransTaskRetries" />
		<arg value="ImgRevSendTrans" />
		</exec>
	</target>
	
	
	<target name="filter-GenerateROITaskFeature" description="Filter environment for GenerateROI-Task">
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${base.dist.dir}" />
			<arg value="json"/>
			<arg value="ifx-db-listener" />
			<arg value="${txn.db.listener}" />
			<arg value="ALL"/>
		</exec>
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${base.dist.dir}" />
			<arg value="json"/>
			<arg value="SplitStringValue" />
			<arg value="${split.string}" />
			<arg value="ALL"/>
		</exec>
			<!-- Replace Retries value -->
		<exec program="fnr-retries.bat" failonerror="true" >
			<arg value="${base.dist.dir}\GenerateROITaskRetries" />
			<arg value="GenerateROICreateUrl" />
		</exec>
	</target>	
	
	
	<target name="filter-HumanReadability" description="Filter environment for filter-HumanReadability">
		<!-- TODO:  Copy WebLayer, App layers, AD Polling if set to True,  -->
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${base.dist.dir}" />
			<arg value="json"/>
			<arg value="Database=ifx-db-name" />
			<arg value="Database=ICD_HR" />
			<arg value="ALL"/>
		</exec>
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${base.dist.dir}" />
			<arg value="json"/>
			<arg value="ifx-db-listener" />
			<arg value="${txn.db.listener}" />
			<arg value="ALL"/>
		</exec>
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${base.dist.dir}" />
			<arg value="json"/>
			<arg value="hr-db-listener" />
			<arg value="${imagereview.humanread.server}" />
			<arg value="ALL"/>
		</exec>
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${base.dist.dir}" />
			<arg value="json"/>
			<arg value="http://mir-txn-api:9090/api/ManualImageReview/transactions" />
			<arg value="${mir.txn.alias}" />
			<arg value="ALL"/>
		</exec>
	
		<!-- Replace Retries value -->
		<exec program="fnr-retries.bat" failonerror="true" >
			<arg value="${base.dist.dir}\ImgReviewSendTransTaskRetries" />
			<arg value="ImgRevSendTrans" />
		</exec>
		<!-- ALL THE CONFIG REPLACEMENTS -->
				<copy todir="${base.dist.dir}\HR01\MIR\ImageReview">
				<fileset basedir="${base.dist.dir}\APP01\MIR\ImageReview" >
					<exclude name="${base.dist.dir}/APP01/MIR/ImageReview/ScheduledTasks/**/*"/>
				</fileset>
				</copy>	
				<!--<copy todir="${base.dist.dir}\HR01\MIR\ScheduledTasks\ActiveDirectoryPollingTask">
				<fileset basedir="${base.dist.dir}\ActiveDirectoryPollingTask" />
				</copy>	-->
				<copy todir="${base.dist.dir}\HR01\RPT01">
				<fileset basedir="${base.dist.dir}\RPT01" />
				</copy>	
				<copy todir="${base.dist.dir}\HR01\MIR">
				<fileset basedir="${base.dist.dir}\WEB01\MIR" />
				</copy>	
					<copy todir="${base.dist.dir}\HR01\MIR">
				<fileset basedir="${base.dist.dir}\TRANS01\MIR" />
				</copy>	
		<exec program="ReplaceUtil.exe" failonerror="true">
				<arg value="${base.dist.dir}\HR01" />
				<arg value="config"/>
				<arg value="${imagereview.app.server}" />
				<arg value="${imagereview.humanread.server}" />
				<arg value="ALL"/>
		</exec>
		<exec program="ReplaceUtil.exe" failonerror="true">
				<arg value="${base.dist.dir}\HR01" />
				<arg value="config"/>
				<arg value="${imagereview.trans.server}" />
				<arg value="${imagereview.humanread.server}" />
				<arg value="ALL"/>
		</exec>
		<exec program="ReplaceUtil.exe" failonerror="true">
				<arg value="${base.dist.dir}\HR01" />
				<arg value="config"/>
				<arg value="${imagereview.web.server}" />
				<arg value="${imagereview.humanread.server}" />
				<arg value="ALL"/>
		</exec>
		<exec program="ReplaceUtil.exe" failonerror="true">
				<arg value="${base.dist.dir}\HR01" />
				<arg value="config"/>
				<arg value="${imagereview.fingerprint.server}" />
				<arg value="${imagereview.humanread.server}" />
				<arg value="ALL"/>
		</exec>
		<exec program="ReplaceUtil.exe" failonerror="true">
				<arg value="${base.dist.dir}\HR01" />
				<arg value="config"/>
				<arg value="${imagereview.db.server}" />
				<arg value="${imagereview.humanread.server}" />
				<arg value="ALL"/>
		</exec>
		<exec program="ReplaceUtil.exe" failonerror="true">
				<arg value="${base.dist.dir}\HR01" />
				<arg value="config"/>
				<arg value="${imagereview.report.server}" />
				<arg value="${imagereview.humanread.server}" />
				<arg value="ALL"/>
		</exec>
		<exec program="ReplaceUtil.exe" failonerror="true">
				<arg value="${base.dist.dir}\HR01" />
				<arg value="config"/>
				<arg value="${imagereview.app.cluster}" />
				<arg value="${imagereview.humanread.server}" />
				<arg value="ALL"/>
		</exec>
		<exec program="ReplaceUtil.exe" failonerror="true">
				<arg value="${base.dist.dir}\HR01" />
				<arg value="config"/>
				<arg value="${imagereview.trans.cluster}" />
				<arg value="${imagereview.humanread.server}" />
				<arg value="ALL"/>
		</exec>
		<exec program="ReplaceUtil.exe" failonerror="true">
				<arg value="${base.dist.dir}\HR01" />
				<arg value="config"/>
				<arg value="${imagereview.fingerprint.cluster}" />
				<arg value="${imagereview.humanread.server}" />
				<arg value="ALL"/>
		</exec>
		<exec program="ReplaceUtil.exe" failonerror="true">
				<arg value="${base.dist.dir}\HR01" />
				<arg value="config"/>
				<arg value="${imagereview.web.alias}" />
				<arg value="${imagereview.humanread.alias}" />
				<arg value="ALL"/>
		</exec>
		<exec program="ReplaceUtil.exe" failonerror="true">
				<arg value="${base.dist.dir}\HR01" />
				<arg value="config"/>
				<arg value="${imagereview.app.alias}" />
				<arg value="${imagereview.humanread.alias}" />
				<arg value="ALL"/>
		</exec>
		<exec program="ReplaceUtil.exe" failonerror="true">
				<arg value="${base.dist.dir}\HR01" />
				<arg value="config"/>
				<arg value="${imagereview.trans.alias}" />
				<arg value="${imagereview.humanread.alias}" />
				<arg value="ALL"/>
		</exec>
		<exec program="ReplaceUtil.exe" failonerror="true">
				<arg value="${base.dist.dir}\HR01" />
				<arg value="config"/>
				<arg value="${imagereview.app02.server}" />
				<arg value="${imagereview.humanread.server}" />
				<arg value="ALL"/>
		</exec>
		<exec program="ReplaceUtil.exe" failonerror="true">
				<arg value="${base.dist.dir}\HR01" />
				<arg value="config"/>
				<arg value="${imagereview.trans02.server}" />
				<arg value="${imagereview.humanread.server}" />
				<arg value="ALL"/>
		</exec>
		<exec program="ReplaceUtil.exe" failonerror="true">
				<arg value="${base.dist.dir}\HR01" />
				<arg value="config"/>
				<arg value="${imagereview.web02.server}" />
				<arg value="${imagereview.humanread.server}" />
				<arg value="ALL"/>
		</exec>
		<exec program="ReplaceUtil.exe" failonerror="true">
				<arg value="${base.dist.dir}\HR01" />
				<arg value="config"/>
				<arg value="${imagereview.fingerprint02.server}" />
				<arg value="${imagereview.humanread.server}" />
				<arg value="ALL"/>
		</exec>
		<exec program="ReplaceUtil.exe" failonerror="true">
				<arg value="${base.dist.dir}\HR01" />
				<arg value="config"/>
				<arg value="${imagereview.rpt.db.listener}" />
				<arg value="${imagereview.humanread.server}" />
				<arg value="ALL"/>
		</exec>
	</target>
	<target name="filter-key" description="Filter key">
		<if test="${new.symmetric.key=='true'}">	
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${base.dist.dir}" />
			<arg value="json"/>
			<arg value="${old.key}" />
			<arg value="${new.key}" />
			<arg value="ALL"/>
		</exec>
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${base.dist.dir}" />
			<arg value="config"/>
			<arg value="${old.key}" />
			<arg value="${new.key}" />
			<arg value="ALL"/>
		</exec>
		</if>
	</target>	
	
</project>