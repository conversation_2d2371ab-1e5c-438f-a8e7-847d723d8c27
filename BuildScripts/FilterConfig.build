<?xml version="1.0" encoding="utf-8" ?>

<project name="MOMS" >

    <!-- GET target deployment environment from the user -->
	<include buildfile="UserInput.include"/>
	<include buildfile="BUILD.include"/>
	<include buildfile="ENVIRONMENT.include"/>
    <include buildfile="..\Common\KEY.include"/>
	<property name="env" value="${deployment.environment}"/>
		
		<!-- Include environment specific properties -->
	<echo message="****** Deployment Target is ${env} *******" />	

	<!-- Filters all environment specific variables like servers, db, etc -->
	<target name="filter-config-file" description="Filters all environment specific variables like servers, db, etc">
		<property name="MailLogger.failure.subject" value="MOMS ${deployment.environment} ${deployment.type}  - Filter step failed" />
		<property name="MailLogger.success.subject" value="MOMS ${deployment.environment} ${deployment.type}  - Step 3 Configuration Successfully Completed." />
		<call target="filter-all-primary-config-file" />
		<if test="${deployment.environment=='CBDTP'}"> <if test="${deployment.type=='PROD'}"> 
			<call target="filter-CBDTPLogging"/> 
		</if> </if>		
		<call target="filter-report-config-file"/>	
		<call target="filter-ApplParams" />
		<call target="filter-database-directory" />		
		<if test="${MobileFeature=='true'}">		
			<call target="filter-MOMSMobile" />		
		</if>
		<call target="filter-key" />
	</target>
	
	<!-- Filter environment variables -->
	<target name="filter-all-primary-config-file" description="Filters all environment specific variables like servers, db, etc">
		
		<!-- WCF Services -->
		<call target="filter-MOMS-service-config-file" />	
		
		
		<if test="${GPSFeature=='true'}">
		<call target="filter-MOMS-api-gps-config-file" />
		</if>
		
		<if test="${InfinityFeature=='true'}">			
			<call target="filter-Infinity-service-config-file" />			
		</if>
					
		<if test="${IntegrityFeature=='true'}">			
			<call target="filter-Integrity-service-config-file" />			
		</if>
		
		<if test="${TransSuiteFeature=='true'}">			
			<call target="filter-TransSuite-service-config-file" />			
		</if>
		
		<!-- Scheduled Tasks -->
		<call target="filter-MOMS-workOrderMessaging-task-config-file" />
		<call target="filter-MOMS-failureAnalysis-task-config-file" />
		<call target="filter-MOMS-inventoryLevelNotification-task-config-file" />
		<call target="filter-MOMS-Preventive-Maintenance-task-config-file" />
		<call target="filter-MOMS-Predictive-Maintenance-task-config-file" />
		<call target="filter-MOMS-notificationEscalation-task-config-file" />
		<if test="${MOMSEventLoader=='true'}">
			<call target="filter-MOMS-eventLoader-task-config-file" />
		</if>
		<if test="${ImageReviewEventLoader=='true'}">
			<call target="filter-ImageReview-MOMS-eventLoader-task-config-file" />
		</if>
		<if test="${IntegrityEventLoader=='true'}">
			<call target="filter-Integrity-MOMS-eventLoader-task-config-file" />
		</if>
		<if test="${TransPortalEventLoader=='true'}">
			<call target="filter-TransPortal-MOMS-eventLoader-task-config-file" />
		</if>
		
		<if test="${CPSEventLoader=='true'}">
			<call target="filter-CPS-MOMS-eventLoader-task-config-file" />
		</if>
		
		<if test="${VOTTEventLoader=='true'}">
			<call target="filter-VOTT-MOMS-eventLoader-task-config-file" />
		</if>
		
		<if test="${property::exists('GFIEventLoader')}">
			<call target="filter-MOMS-GFI-eventLoader-task-config-file" />
		</if>		
		<if test="${MOMSExternalNotifier=='true'}">
		<call target="filter-MOMS-ExternalNotifier-task-config-file" />
		</if>	
		<if test="${TrafficLoaderTask=='true'}">
		<call target="filter-MOMS-MOMSTrafficDataLoader-task-config-file" />
		</if>
		
		<if test="${SolarWindsFeature=='true'}">
		<call target="filter-SolarWinds"/>
		</if>
		
		<if test="${WhatsUpFeature=='true'}">
		<call target="filter-WhatsUp"/>
		</if>	
		
		<call target="filter-Insight"/>
		
		<if test="${ActiveDirectoryFeature=='true'}">
		<call target="filter-ActiveDirectory" />
		</if>
		<if test="${IntegratedSecurityIFXLoader=='true'}">
		<call target="filter-IntegratedSecurityIFXLoader" />
		</if>
		
		<if test="${deployment.type=='BOS'}">
		<call target="filter-logo"/>
		</if>	
		
		<if test="${InsightMobileFeature=='true'}">
		<call target="filter-for-InsightMobile"/>
		</if>	
		
		<if test="${IntegratedSecurity=='true'}">
		<call target="filter-for-SSPI"/>
		</if>	
		
		<if test="${OfflineMapFeature=='true'}">
		<call target="filter-for-OfflineMaps"/>
		</if>	
		
	</target>
	
	<target name="filter-CBDTPLogging" description="Filter environment for CBDTP Logging in Production">
	<!-- Transactions Web Server Site -->
		<xmlpoke file="${moms.external.service.host.appconfig.file}" xpath="/configuration/log4net/appender/rollingStyle/@value" value="Date" />
		<xmlpoke file="${moms.external.service.host.appconfig.file}" xpath="/configuration/log4net/appender/maxSizeRollBackups/@value" value="7" />		
		<xmlpoke file="${moms.internal.service.host.appconfig.file}" xpath="/configuration/log4net/appender/rollingStyle/@value" value="Date" />
		<xmlpoke file="${moms.internal.service.host.appconfig.file}" xpath="/configuration/log4net/appender/maxSizeRollBackups/@value" value="7" />
		<xmlpoke file="${notificationEscalation.task.moms.config.file}" xpath="/configuration/log4net/appender/rollingStyle/@value" value="Date" />
		<xmlpoke file="${notificationEscalation.task.moms.config.file}" xpath="/configuration/log4net/appender/maxSizeRollBackups/@value" value="7" />	
		<xmlpoke file="${workOrderMessaging.task.moms.config.file}" xpath="/configuration/log4net/appender/rollingStyle/@value" value="Date" />
		<xmlpoke file="${workOrderMessaging.task.moms.config.file}" xpath="/configuration/log4net/appender/maxSizeRollBackups/@value" value="7" />	
		<exec program="FNR-Filter-CBDTPPRODLogging.bat" failonerror="true" >
			<arg value="${dist.dir}" />			
		</exec>		
	</target>
	

	<!-- Filter the environment specific strings for NotificationEscalationTask-->
	<target name="filter-MOMS-notificationEscalation-task-config-file" description="Filter environment specific variables like servers, db, etc">	
		<xmlpoke  file="${notificationEscalation.task.moms.config.file}" xpath="/configuration/connectionStrings/add[@connectionString = 'Database=MOMS;Server=localhost;UID=MOMSAppUser;PWD=*******;Min Pool Size=1;Max Pool Size=100;Connect Timeout=120;']/@connectionString" value="${moms.connection.string}" />
		<xmlpoke  file="${notificationEscalation.task.moms.config.file}" xpath="/configuration/appSettings/add[@key = 'notificationUserID']/@value" value="${notificationUserID}" />
		<xmlpoke  file="${notificationEscalation.task.moms.config.file}" xpath="/configuration/appSettings/add[@key = 'ClientSettingsProvider.ServiceUri']/@value" value="${ClientSettingsProvider.ServiceUri}" />
		<xmlpoke  file="${notificationEscalation.task.moms.config.file}" xpath="/configuration/appSettings/add[@key = 'saveSentEmail']/@value" value="${saveSentEmail}" />	
		<xmlpoke  file="${notificationEscalation.task.moms.config.file}" xpath="/configuration/appSettings/add[@key = 'workOrderEscalationMinutesAssigned']/@value" value="${workOrderEscalationMinutesAssigned}" />	
		<xmlpoke  file="${notificationEscalation.task.moms.config.file}" xpath="/configuration/log4net/appender/file[@value = 'D:/MOMSLogs/moms_NotificationEscalationTask.log']/@value" value="${notificationEscalation.task.log.file}" />	
		<xmlpoke  file="${notificationEscalation.task.moms.config.file}" xpath="/configuration/log4net/root/level[@value = 'ALL']/@value" value="${log.error.level}" />
	</target>
	
	<!-- Filter the environment specific strings for MOMS EventLoader Task -->
	<target name="filter-MOMS-eventLoader-task-config-file" description="Filter environment specific variables like servers, db, etc">
		<xmlpoke  file="${MOMSEventLoader.task.moms.config.file}" xpath="/configuration/connectionStrings/add[@name='MOMSDBConnectionString']/@connectionString" value="${moms.connection.string}" />
		<xmlpoke  file="${MOMSEventLoader.task.moms.config.file}" xpath="/configuration/connectionStrings/add[@name='EventLoaderDBConnectionString']/@connectionString" value="${eventloader.connection.string}" />
		<xmlpoke  file="${MOMSEventLoader.task.moms.config.file}" xpath="/configuration/appSettings/add[@key = 'workOrderEscalationMinutesAssigned']/@value" value="${workOrderEscalationMinutesAssigned}" />			
		<xmlpoke  file="${MOMSEventLoader.task.moms.config.file}" xpath="/configuration/appSettings/add[@key = 'ClientSettingsProvider.ServiceUri']/@value" value="${ClientSettingsProvider.ServiceUri}" />
		<xmlpoke  file="${MOMSEventLoader.task.moms.config.file}" xpath="/configuration/appSettings/add[@key = 'saveSentEmail']/@value" value="${saveSentEmail}" />	
		<xmlpoke  file="${MOMSEventLoader.task.moms.config.file}" xpath="/configuration/system.serviceModel/client/endpoint[@address = 'http://localhost:5002/MOMSWCFExternalService']/@address" value="${wcf.external.http.client.endpoint}" />
		<xmlpoke  file="${MOMSEventLoader.task.moms.config.file}" xpath="/configuration/log4net/appender/file[@value = 'D:/MOMSLogs/moms_EventLoaderTask.log']/@value" value="${event.loader.task.log.file}" />	
		<xmlpoke  file="${MOMSEventLoader.task.moms.config.file}" xpath="/configuration/log4net/root/level[@value = 'ALL']/@value" value="${log.error.level}" />
		<xmlpoke  file="${MOMSEventLoader.task.moms.config.file}" xpath="/configuration/appSettings/add[@key = 'TaskName']/@value" value="${env} ${deployment.type} MOMS Event Loader" />
		<xmlpoke  file="${MOMSEventLoader.task.moms.config.file}" xpath="/configuration/system.serviceModel/client/endpoint/identity/servicePrincipalName/@value" value="${endpoint.servicePrincipalName}" />
		<if test="${IntegratedSecurityMOMSEventLoader=='true'}">
		<xmlpoke  file="${MOMSEventLoader.task.moms.config.file}" xpath="/configuration/connectionStrings/add[@name='EventLoaderDBConnectionString']/@connectionString" value="${eventloader.secure.connection.string}" />	
		</if>
	 </target>
	 
	 <!-- Filter the environment specific strings for MOMS EventLoader Task -->
	<target name="filter-ImageReview-MOMS-eventLoader-task-config-file" description="Filter environment specific variables like servers, db, etc">
		<xmlpoke  file="${ImageReview.MOMSEventLoader.task.moms.config.file}" xpath="/configuration/connectionStrings/add[@name='MOMSDBConnectionString']/@connectionString" value="${moms.connection.string}" />
		<xmlpoke  file="${ImageReview.MOMSEventLoader.task.moms.config.file}" xpath="/configuration/connectionStrings/add[@name='EventLoaderDBConnectionString']/@connectionString" value="${imagereview.eventloader.connection.string}" />
		<xmlpoke  file="${ImageReview.MOMSEventLoader.task.moms.config.file}" xpath="/configuration/appSettings/add[@key = 'workOrderEscalationMinutesAssigned']/@value" value="${workOrderEscalationMinutesAssigned}" />			
		<xmlpoke  file="${ImageReview.MOMSEventLoader.task.moms.config.file}" xpath="/configuration/appSettings/add[@key = 'ClientSettingsProvider.ServiceUri']/@value" value="${ClientSettingsProvider.ServiceUri}" />
		<xmlpoke  file="${ImageReview.MOMSEventLoader.task.moms.config.file}" xpath="/configuration/appSettings/add[@key = 'saveSentEmail']/@value" value="${saveSentEmail}" />	
		<xmlpoke  file="${ImageReview.MOMSEventLoader.task.moms.config.file}" xpath="/configuration/system.serviceModel/client/endpoint[@address = 'http://localhost:5002/MOMSWCFExternalService']/@address" value="${wcf.external.http.client.endpoint}" />
		<xmlpoke  file="${ImageReview.MOMSEventLoader.task.moms.config.file}" xpath="/configuration/log4net/appender/file[@value = 'D:/MOMSLogs/moms_EventLoaderTask.log']/@value" value="${ImageReview.event.loader.task.log.file}" />	
		<xmlpoke  file="${ImageReview.MOMSEventLoader.task.moms.config.file}" xpath="/configuration/log4net/root/level[@value = 'ALL']/@value" value="${log.error.level}" />
		<xmlpoke  file="${ImageReview.MOMSEventLoader.task.moms.config.file}" xpath="/configuration/appSettings/add[@key = 'TaskName']/@value" value="${env} ${deployment.type} Image Review MOMS Event Loader" />
		<xmlpoke  file="${ImageReview.MOMSEventLoader.task.moms.config.file}" xpath="/configuration/system.serviceModel/client/endpoint/identity/servicePrincipalName/@value" value="${endpoint.servicePrincipalName}" />	
		<xmlpoke  file="${ImageReview.MOMSEventLoader.task.moms.config.file}" xpath="/configuration/appSettings/add[@key = 'ExternalIDConcat']/@value" value="666" />
		<if test="${IntegratedSecurityImageReviewLoader=='true'}">
		<xmlpoke  file="${ImageReview.MOMSEventLoader.task.moms.config.file}" xpath="/configuration/connectionStrings/add[@name='EventLoaderDBConnectionString']/@connectionString" value="${imagereview.eventloader.secure.connection.string}" />	
		</if>
	 </target>
	 
	  <!-- Filter the environment specific strings for MOMS EventLoader Task -->
	<target name="filter-TransPortal-MOMS-eventLoader-task-config-file" description="Filter environment specific variables like servers, db, etc">
		<xmlpoke  file="${TransPortal.MOMSEventLoader.task.moms.config.file}" xpath="/configuration/connectionStrings/add[@name='MOMSDBConnectionString']/@connectionString" value="${moms.connection.string}" />
		<xmlpoke  file="${TransPortal.MOMSEventLoader.task.moms.config.file}" xpath="/configuration/connectionStrings/add[@name='EventLoaderDBConnectionString']/@connectionString" value="${transportal.eventloader.connection.string}" />
		<xmlpoke  file="${TransPortal.MOMSEventLoader.task.moms.config.file}" xpath="/configuration/appSettings/add[@key = 'workOrderEscalationMinutesAssigned']/@value" value="${workOrderEscalationMinutesAssigned}" />			
		<xmlpoke  file="${TransPortal.MOMSEventLoader.task.moms.config.file}" xpath="/configuration/appSettings/add[@key = 'ClientSettingsProvider.ServiceUri']/@value" value="${ClientSettingsProvider.ServiceUri}" />
		<xmlpoke  file="${TransPortal.MOMSEventLoader.task.moms.config.file}" xpath="/configuration/appSettings/add[@key = 'saveSentEmail']/@value" value="${saveSentEmail}" />	
		<xmlpoke  file="${TransPortal.MOMSEventLoader.task.moms.config.file}" xpath="/configuration/system.serviceModel/client/endpoint[@address = 'http://localhost:5002/MOMSWCFExternalService']/@address" value="${wcf.external.http.client.endpoint}" />
		<xmlpoke  file="${TransPortal.MOMSEventLoader.task.moms.config.file}" xpath="/configuration/log4net/appender/file[@value = 'D:/MOMSLogs/moms_EventLoaderTask.log']/@value" value="${TransPortal.event.loader.task.log.file}" />	
		<xmlpoke  file="${TransPortal.MOMSEventLoader.task.moms.config.file}" xpath="/configuration/log4net/root/level[@value = 'ALL']/@value" value="${log.error.level}" />
		<xmlpoke  file="${TransPortal.MOMSEventLoader.task.moms.config.file}" xpath="/configuration/appSettings/add[@key = 'TaskName']/@value" value="${env} ${deployment.type} TransPortal MOMS Event Loader" />
		<xmlpoke  file="${TransPortal.MOMSEventLoader.task.moms.config.file}" xpath="/configuration/system.serviceModel/client/endpoint/identity/servicePrincipalName/@value" value="${endpoint.servicePrincipalName}" />	
		<xmlpoke  file="${TransPortal.MOMSEventLoader.task.moms.config.file}" xpath="/configuration/appSettings/add[@key = 'ExternalIDConcat']/@value" value="888" />
	 </target>
	 
	  <!-- Filter the environment specific strings for CPS EventLoader Task -->
	<target name="filter-CPS-MOMS-eventLoader-task-config-file" description="Filter environment specific variables like servers, db, etc">
		<xmlpoke  file="${CPS.MOMSEventLoader.task.moms.config.file}" xpath="/configuration/connectionStrings/add[@name='MOMSDBConnectionString']/@connectionString" value="${moms.connection.string}" />
		<xmlpoke  file="${CPS.MOMSEventLoader.task.moms.config.file}" xpath="/configuration/connectionStrings/add[@name='EventLoaderDBConnectionString']/@connectionString" value="${cps.eventloader.connection.string}" />
		<xmlpoke  file="${CPS.MOMSEventLoader.task.moms.config.file}" xpath="/configuration/appSettings/add[@key = 'workOrderEscalationMinutesAssigned']/@value" value="${workOrderEscalationMinutesAssigned}" />			
		<xmlpoke  file="${CPS.MOMSEventLoader.task.moms.config.file}" xpath="/configuration/appSettings/add[@key = 'ClientSettingsProvider.ServiceUri']/@value" value="${ClientSettingsProvider.ServiceUri}" />
		<xmlpoke  file="${CPS.MOMSEventLoader.task.moms.config.file}" xpath="/configuration/appSettings/add[@key = 'saveSentEmail']/@value" value="${saveSentEmail}" />	
		<xmlpoke  file="${CPS.MOMSEventLoader.task.moms.config.file}" xpath="/configuration/system.serviceModel/client/endpoint[@address = 'http://localhost:5002/MOMSWCFExternalService']/@address" value="${wcf.external.http.client.endpoint}" />
		<xmlpoke  file="${CPS.MOMSEventLoader.task.moms.config.file}" xpath="/configuration/log4net/appender/file[@value = 'D:/MOMSLogs/moms_EventLoaderTask.log']/@value" value="${CPS.event.loader.task.log.file}" />	
		<xmlpoke  file="${CPS.MOMSEventLoader.task.moms.config.file}" xpath="/configuration/log4net/root/level[@value = 'ALL']/@value" value="${log.error.level}" />
		<xmlpoke  file="${CPS.MOMSEventLoader.task.moms.config.file}" xpath="/configuration/appSettings/add[@key = 'TaskName']/@value" value="${env} ${deployment.type} CPS MOMS Event Loader" />
		<xmlpoke  file="${CPS.MOMSEventLoader.task.moms.config.file}" xpath="/configuration/system.serviceModel/client/endpoint/identity/servicePrincipalName/@value" value="${endpoint.servicePrincipalName}" />	
		<xmlpoke  file="${CPS.MOMSEventLoader.task.moms.config.file}" xpath="/configuration/appSettings/add[@key = 'ExternalIDConcat']/@value" value="999" />
		<if test="${IntegratedSecurityCPSLoader=='true'}">
		<xmlpoke  file="${CPS.MOMSEventLoader.task.moms.config.file}" xpath="/configuration/connectionStrings/add[@name='EventLoaderDBConnectionString']/@connectionString" value="${cps.eventloader.secure.connection.string}" />	
		</if>
	 </target>
	 
	   <!-- Filter the environment specific strings for CPS EventLoader Task -->
	<target name="filter-VOTT-MOMS-eventLoader-task-config-file" description="Filter environment specific variables like servers, db, etc">
		<xmlpoke  file="${VOTT.MOMSEventLoader.task.moms.config.file}" xpath="/configuration/connectionStrings/add[@name='MOMSDBConnectionString']/@connectionString" value="${moms.connection.string}" />
		<xmlpoke  file="${VOTT.MOMSEventLoader.task.moms.config.file}" xpath="/configuration/connectionStrings/add[@name='EventLoaderDBConnectionString']/@connectionString" value="${vott.eventloader.connection.string}" />
		<xmlpoke  file="${VOTT.MOMSEventLoader.task.moms.config.file}" xpath="/configuration/appSettings/add[@key = 'workOrderEscalationMinutesAssigned']/@value" value="${workOrderEscalationMinutesAssigned}" />			
		<xmlpoke  file="${VOTT.MOMSEventLoader.task.moms.config.file}" xpath="/configuration/appSettings/add[@key = 'ClientSettingsProvider.ServiceUri']/@value" value="${ClientSettingsProvider.ServiceUri}" />
		<xmlpoke  file="${VOTT.MOMSEventLoader.task.moms.config.file}" xpath="/configuration/appSettings/add[@key = 'saveSentEmail']/@value" value="${saveSentEmail}" />	
		<xmlpoke  file="${VOTT.MOMSEventLoader.task.moms.config.file}" xpath="/configuration/system.serviceModel/client/endpoint[@address = 'http://localhost:5002/MOMSWCFExternalService']/@address" value="${wcf.external.http.client.endpoint}" />
		<xmlpoke  file="${VOTT.MOMSEventLoader.task.moms.config.file}" xpath="/configuration/log4net/appender/file[@value = 'D:/MOMSLogs/moms_EventLoaderTask.log']/@value" value="${VOTT.event.loader.task.log.file}" />	
		<xmlpoke  file="${VOTT.MOMSEventLoader.task.moms.config.file}" xpath="/configuration/log4net/root/level[@value = 'ALL']/@value" value="${log.error.level}" />
		<xmlpoke  file="${VOTT.MOMSEventLoader.task.moms.config.file}" xpath="/configuration/appSettings/add[@key = 'TaskName']/@value" value="${env} ${deployment.type} VOTT MOMS Event Loader" />
		<xmlpoke  file="${VOTT.MOMSEventLoader.task.moms.config.file}" xpath="/configuration/system.serviceModel/client/endpoint/identity/servicePrincipalName/@value" value="${endpoint.servicePrincipalName}" />	
		<xmlpoke  file="${VOTT.MOMSEventLoader.task.moms.config.file}" xpath="/configuration/appSettings/add[@key = 'ExternalIDConcat']/@value" value="222" />
		<if test="${IntegratedSecurityVOTTLoader=='true'}">
		<xmlpoke  file="${VOTT.MOMSEventLoader.task.moms.config.file}" xpath="/configuration/connectionStrings/add[@name='EventLoaderDBConnectionString']/@connectionString" value="${vott.eventloader.secure.connection.string}" />	
		</if>
	 </target>
	 
	 <!-- Filter the environment specific strings for MOMS EventLoader Task -->
	<target name="filter-Integrity-MOMS-eventLoader-task-config-file" description="Filter environment specific variables like servers, db, etc">
		<xmlpoke  file="${Integrity.MOMSEventLoader.task.moms.config.file}" xpath="/configuration/connectionStrings/add[@name='MOMSDBConnectionString']/@connectionString" value="${moms.connection.string}" />
		<xmlpoke  file="${Integrity.MOMSEventLoader.task.moms.config.file}" xpath="/configuration/connectionStrings/add[@name='EventLoaderDBConnectionString']/@connectionString" value="${integrity.eventloader.connection.string}" />
		<xmlpoke  file="${Integrity.MOMSEventLoader.task.moms.config.file}" xpath="/configuration/appSettings/add[@key = 'workOrderEscalationMinutesAssigned']/@value" value="${workOrderEscalationMinutesAssigned}" />			
		<xmlpoke  file="${Integrity.MOMSEventLoader.task.moms.config.file}" xpath="/configuration/appSettings/add[@key = 'ClientSettingsProvider.ServiceUri']/@value" value="${ClientSettingsProvider.ServiceUri}" />
		<xmlpoke  file="${Integrity.MOMSEventLoader.task.moms.config.file}" xpath="/configuration/appSettings/add[@key = 'saveSentEmail']/@value" value="${saveSentEmail}" />	
		<xmlpoke  file="${Integrity.MOMSEventLoader.task.moms.config.file}" xpath="/configuration/system.serviceModel/client/endpoint[@address = 'http://localhost:5002/MOMSWCFExternalService']/@address" value="${wcf.external.http.client.endpoint}" />
		<xmlpoke  file="${Integrity.MOMSEventLoader.task.moms.config.file}" xpath="/configuration/log4net/appender/file[@value = 'D:/MOMSLogs/moms_EventLoaderTask.log']/@value" value="${integrity.eventloader.log.dir}\Integrity_MOMSEventLoader.log" />	
		<xmlpoke  file="${Integrity.MOMSEventLoader.task.moms.config.file}" xpath="/configuration/log4net/root/level[@value = 'ALL']/@value" value="${log.error.level}" />
		<xmlpoke  file="${Integrity.MOMSEventLoader.task.moms.config.file}" xpath="/configuration/appSettings/add[@key = 'TaskName']/@value" value="${env} ${deployment.type} Integrity MOMS Event Loader" />
		<xmlpoke  file="${Integrity.MOMSEventLoader.task.moms.config.file}" xpath="/configuration/system.serviceModel/client/endpoint/identity/servicePrincipalName/@value" value="${endpoint.servicePrincipalName}" />	
		<xmlpoke  file="${Integrity.MOMSEventLoader.task.moms.config.file}" xpath="/configuration/appSettings/add[@key = 'ExternalIDConcat']/@value" value="777" />
		<if test="${IntegratedSecurityIntegrityLoader=='true'}">
		<xmlpoke  file="${Integrity.MOMSEventLoader.task.moms.config.file}" xpath="/configuration/connectionStrings/add[@name='EventLoaderDBConnectionString']/@connectionString" value="${integrity.eventloader.secure.connection.string}" />	
		</if>
	 </target>
	 
	    
	<!-- Filter the environment specific strings for GFI EventLoader Task -->
	<target name="filter-MOMS-GFI-eventLoader-task-config-file" description="Filter environment specific variables like servers, db, etc">
		<xmlpoke  file="${GFI.MOMSEventLoader.task.moms.config.file}" xpath="/configuration/connectionStrings/add[@name='MOMSDBConnectionString']/@connectionString" value="${moms.connection.string}" />
		<xmlpoke  file="${GFI.MOMSEventLoader.task.moms.config.file}" xpath="/configuration/connectionStrings/add[@name='EventLoaderDBConnectionString']/@connectionString" value="${gfi.connection.string}" />
		<xmlpoke  file="${GFI.MOMSEventLoader.task.moms.config.file}" xpath="/configuration/appSettings/add[@key = 'workOrderEscalationMinutesAssigned']/@value" value="${workOrderEscalationMinutesAssigned}" />				
		<xmlpoke  file="${GFI.MOMSEventLoader.task.moms.config.file}" xpath="/configuration/appSettings/add[@key = 'ClientSettingsProvider.ServiceUri']/@value" value="${ClientSettingsProvider.ServiceUri}" />
		<xmlpoke  file="${GFI.MOMSEventLoader.task.moms.config.file}" xpath="/configuration/appSettings/add[@key = 'saveSentEmail']/@value" value="${saveSentEmail}" />	
		<xmlpoke  file="${GFI.MOMSEventLoader.task.moms.config.file}" xpath="/configuration/system.serviceModel/client/endpoint[@address = 'http://localhost:5002/MOMSWCFExternalService']/@address" value="${wcf.external.http.client.endpoint}" />
		<xmlpoke  file="${GFI.MOMSEventLoader.task.moms.config.file}" xpath="/configuration/log4net/appender/file[@value = 'D:/MOMSLogs/moms_EventLoaderTask.log']/@value" value="${GFI.event.loader.task.log.file}" />	
		<xmlpoke  file="${GFI.MOMSEventLoader.task.moms.config.file}" xpath="/configuration/log4net/root/level[@value = 'ALL']/@value" value="${log.error.level}" />
		<xmlpoke  file="${GFI.MOMSEventLoader.task.moms.config.file}" xpath="/configuration/appSettings/add[@key = 'TaskName']/@value" value="${env} GFI Event Loader" />
		<xmlpoke  file="${GFI.MOMSEventLoader.task.moms.config.file}" xpath="/configuration/system.serviceModel/client/endpoint/identity/servicePrincipalName/@value" value="${endpoint.servicePrincipalName}" />
		<xmlpoke  file="${moms.internal.service.host.appconfig.file}" xpath="/configuration/connectionStrings/add[@name='NSMDBConnectionString']/@connectionString" value="${gfi.connection.string}" />	
		<xmlpoke  file="${moms.external.service.host.appconfig.file}" xpath="/configuration/connectionStrings/add[@name='NSMDBConnectionString']/@connectionString" value="${gfi.connection.string}" />			
	</target>

	<!-- Filter the environment specific strings for WorkOrderMessaging Taslk -->
	<target name="filter-MOMS-workOrderMessaging-task-config-file" description="Filter environment specific variables like servers, db, etc">
		<xmlpoke  file="${workOrderMessaging.task.moms.config.file}" xpath="/configuration/connectionStrings/add[@name='MOMSDBConnectionString']/@connectionString" value="${moms.connection.string}" />	
		<xmlpoke  file="${workOrderMessaging.task.moms.config.file}" xpath="/configuration/appSettings/add[@key = 'ClientSettingsProvider.ServiceUri']/@value" value="${ClientSettingsProvider.ServiceUri}" />
		<xmlpoke  file="${workOrderMessaging.task.moms.config.file}" xpath="/configuration/log4net/appender/file[@value = 'D:/MOMSLogs/moms_WorkOrderMessageTask.log']/@value" value="${workorder.messaging.task.log.file}" />
		<xmlpoke  file="${workOrderMessaging.task.moms.config.file}" xpath="/configuration/log4net/root/level[@value = 'ALL']/@value" value="${log.error.level}" />
		<xmlpoke  file="${workOrderMessaging.task.moms.config.file}" xpath="/configuration/appSettings/add[@key = 'TaskName1']/@value" value="${env} ${deployment.type} Work Order Messaging"  />
		<xmlpoke  file="${workOrderMessaging.task.moms.config.file}" xpath="/configuration/appSettings/add[@key = 'imapPort']/@value" value="${imapPort}"  />
		<xmlpoke  failonerror="false" file="${workOrderMessaging.task.moms.config.file}" xpath="/configuration/appSettings/add[@key = 'imapUseSsl']/@value" value="${imapUseSsl}"  />
		<xmlpoke  failonerror="false" file="${workOrderMessaging.task.moms.config.file}" xpath="/configuration/appSettings/add[@key = 'imapValidateCertificate']/@value" value="${imapValidateCertificate}"  />
	</target>

	<!-- Filter the environment specific strings for Failure Analysis Task -->
	<target name="filter-MOMS-failureAnalysis-task-config-file" description="Filter environment specific variables like servers, db, etc">
		<xmlpoke  file="${MOMSFailureAnalysisTaskConsole.task.moms.config.file}" xpath="/configuration/connectionStrings/add[@name='MOMSDBConnectionString']/@connectionString" value="${moms.connection.string}" />
		<xmlpoke  file="${MOMSFailureAnalysisTaskConsole.task.moms.config.file}" xpath="/configuration/appSettings/add[@key = 'ClientSettingsProvider.ServiceUri']/@value" value="${ClientSettingsProvider.ServiceUri}" />
		<xmlpoke  file="${MOMSFailureAnalysisTaskConsole.task.moms.config.file}" xpath="/configuration/log4net/appender/file[@value = 'D:/MOMSLogs/moms_MOMSFailureAnalysisTask.log']/@value" value="${failure.analysis.task.log.file}" />
		<xmlpoke  file="${MOMSFailureAnalysisTaskConsole.task.moms.config.file}" xpath="/configuration/log4net/root/level[@value = 'ALL']/@value" value="${log.error.level}" />
		<xmlpoke  file="${MOMSFailureAnalysisTaskConsole.task.moms.config.file}" xpath="/configuration/appSettings/add[@key = 'TaskName']/@value" value="${env} ${deployment.type} Failure Analysis" />
	</target>
  
	<!-- Filter the environment specific strings for Inventory Level Notification Task -->
	<target name="filter-MOMS-inventoryLevelNotification-task-config-file" description="Filter environment specific variables like servers, db, etc">
		<xmlpoke  file="${InventoryLevelNotification.moms.config.file}" xpath="/configuration/connectionStrings/add[@name='MOMSDBConnectionString']/@connectionString" value="${moms.connection.string}" />
		<xmlpoke  file="${InventoryLevelNotification.moms.config.file}" xpath="/configuration/appSettings/add[@key = 'notificationUserID']/@value" value="${notificationUserID}" />    
		<xmlpoke  file="${InventoryLevelNotification.moms.config.file}" xpath="/configuration/log4net/appender/file[@value = 'D:/MOMSLogs/moms_InventoryLevel.log']/@value" value="${inventory.level.task.log.file}" />
		<xmlpoke  file="${InventoryLevelNotification.moms.config.file}" xpath="/configuration/log4net/root/level[@value = 'ALL']/@value" value="${log.error.level}" />
		<xmlpoke  file="${InventoryLevelNotification.moms.config.file}" xpath="/configuration/appSettings/add[@key = 'ClientSettingsProvider.ServiceUri']/@value" value="${ClientSettingsProvider.ServiceUri}" />
		<xmlpoke  file="${InventoryLevelNotification.moms.config.file}" xpath="/configuration/appSettings/add[@key = 'TaskName']/@value" value="${env} ${deployment.type} Inventory Level Notification" /> 
	</target>

	<!-- Filter the environment specific strings for Predictive Maintenance Task -->
	<target name="filter-MOMS-Predictive-Maintenance-task-config-file" description="Filter environment specific variables like servers, db, etc">
		<xmlpoke  file="${MOMSPredictiveMaintenanceNotificationTaskConsole.task.moms.config.file}" xpath="/configuration/connectionStrings/add[@name='MOMSDBConnectionString']/@connectionString" value="${moms.connection.string}" />
		<xmlpoke  file="${MOMSPredictiveMaintenanceNotificationTaskConsole.task.moms.config.file}" xpath="/configuration/appSettings/add[@key = 'ClientSettingsProvider.ServiceUri']/@value" value="${ClientSettingsProvider.ServiceUri}" />
		<xmlpoke  file="${MOMSPredictiveMaintenanceNotificationTaskConsole.task.moms.config.file}" xpath="/configuration/log4net/appender/file[@value = 'D:/MOMSLogs/moms_PredictiveMaintenanceNotificationTask.log']/@value" value="${predictive.maint.task.log.file}" />
		<xmlpoke  file="${MOMSPredictiveMaintenanceNotificationTaskConsole.task.moms.config.file}" xpath="/configuration/log4net/root/level[@value = 'ALL']/@value" value="${log.error.level}" />	
	</target>

    <!-- Filter the environment specific strings for DEV Preventive Maintenance Host -->
	<target name="filter-MOMS-Preventive-Maintenance-task-config-file" description="Filter environment specific variables like servers, db, etc">
		<xmlpoke  file="${MOMSPreventiveMaintenanceTask.moms.config.file}" xpath="/configuration/connectionStrings/add[@name='MOMSDBConnectionString']/@connectionString" value="${moms.connection.string}" />
		<xmlpoke  file="${MOMSPreventiveMaintenanceTask.moms.config.file}" xpath="/configuration/appSettings/add[@key = 'ClientSettingsProvider.ServiceUri']/@value" value="${ClientSettingsProvider.ServiceUri}" />
		<xmlpoke  file="${MOMSPreventiveMaintenanceTask.moms.config.file}" xpath="/configuration/log4net/root/level[@value = 'ALL']/@value" value="${log.error.level}" />
		<xmlpoke  file="${MOMSPreventiveMaintenanceTask.moms.config.file}" xpath="/configuration/log4net/appender/file[@value = 'D:/MOMSLogs/moms_PreventiveMaintenanceTask.log']/@value" value="${preventive.maint.task.log.file}" />
		<xmlpoke  file="${MOMSPreventiveMaintenanceTask.moms.config.file}" xpath="/configuration/appSettings/add[@key = 'TaskName']/@value" value="${env} ${deployment.type} Preventive Maintenance Task" />		
	</target>
	 
	<!-- Filter the environment specific strings for External Notifier Task -->
	<target name="filter-MOMS-ExternalNotifier-task-config-file" description="Filter environment specific variables like servers, db, etc">
		<xmlpoke  file="${MOMSExternalNotifier.task.moms.config.file}" xpath="/configuration/connectionStrings/add[@name='MOMSDBConnectionString']/@connectionString" value="${moms.connection.string}" />
		<xmlpoke  file="${MOMSExternalNotifier.task.moms.config.file}" xpath="/configuration/log4net/appender/file[@value = 'D:/MOMSLogs/moms_ExternalNotifierTask.log']/@value" value="${Externalnotifier.task.log.file}" />	
		<xmlpoke  file="${MOMSExternalNotifier.task.moms.config.file}" xpath="/configuration/log4net/root/level/@value" value="${log.error.level}" />		
		<xmlpoke  file="${MOMSExternalNotifier.task.moms.config.file}" xpath="/configuration/appSettings/add[@key = 'TaskName']/@value" value="${env} ${deployment.type} External Notifier" />
		<xmlpoke  file="${MOMSExternalNotifier.task.moms.config.file}" xpath="/configuration/system.serviceModel/client/endpoint[@address = 'net.tcp://localhost:9034/MOMSExternalService']/@address" value="${NotifierTaskServiceConnectionAddress}" />
		<xmlpoke  file="${MOMSExternalNotifier.task.moms.config.file}" xpath="/configuration/system.serviceModel/client/endpoint/identity/servicePrincipalName/@value" value="${NotifierTaskServiceConnectionServer}" />
	 </target>
	 
	 <!-- Filter MOMSDataTrafficLoader Task -->	 
	<target name="filter-MOMS-MOMSTrafficDataLoader-task-config-file" description="Filter environment specific variables like servers, db, etc">
		<xmlpoke  file="${MOMSTrafficDataLoader.task.moms.config.file}" xpath="/configuration/connectionStrings/add[@name='MOMSDBConnectionString']/@connectionString" value="${moms.connection.string}" />
		<xmlpoke  file="${MOMSTrafficDataLoader.task.moms.config.file}" xpath="/configuration/log4net/root/level[@value = 'ALL']/@value" value="${log.error.level}" />	
		<xmlpoke  file="${MOMSTrafficDataLoader.task.moms.config.file}" xpath="/configuration/log4net/appender/file[@value = 'D:/MOMSLogs/moms_TrafficDataLoader.log']/@value" value="${MOMSTrafficDataLoader.task.log.file}" />	
	</target>
	

	
	<!-- Filter the environment specific strings for API -->
	<target name="filter-MOMS-api-gps-config-file" description="Filter environment specific variables like servers, db, etc">
		<xmlpoke  file="${moms.gps.locator.service.appconfig.file}" xpath="/configuration/system.serviceModel/services/service/host/baseAddresses/add[@baseAddress = 'http://localhost:5005']/@baseAddress" value="${moms.gps.service.host.base.address}" />
		<xmlpoke  file="${moms.gps.locator.service.appconfig.file}" xpath="/configuration/connectionStrings/add[@name='MOMSDBConnectionString']/@connectionString" value="${moms.connection.string}" />
		<xmlpoke  file="${moms.gps.locator.service.appconfig.file}" xpath="/configuration/appSettings/add[@key = 'ServiceName']/@value" value="${GPSLocatorServiceName}" />
		<xmlpoke  file="${moms.gps.locator.service.appconfig.file}" xpath="/configuration/appSettings/add[@key = 'ServiceDisplayName']/@value" value="${GPSLocatorServiceDisplayName}" />
		<xmlpoke  file="${moms.gps.locator.service.appconfig.file}" xpath="/configuration/appSettings/add[@key = 'ServiceDescription']/@value" value="${GPSLocatorServiceDescription}" />
		<xmlpoke  file="${moms.gps.locator.service.appconfig.file}" xpath="/configuration/appSettings/add[@key = 'ClientSettingsProvider.ServiceUri']/@value" value="${ClientSettingsProvider.ServiceUri}" />
		<xmlpoke  file="${moms.gps.locator.service.appconfig.file}" xpath="/configuration/log4net/appender/file[@value = 'D:/MOMSLogs/moms_MOMSGPSLocatorServiceHost.log']/@value" value="${gps.locator.app.log.file}" />	
		<xmlpoke  file="${moms.gps.locator.service.appconfig.file}" xpath="/configuration/log4net/root/level[@value = 'ALL']/@value" value="${log.error.level}" />
	</target>

	<!-- Filter the environment specific strings for Intertnal Service Host -->
	<target name="filter-MOMS-service-config-file" description="Filter environment specific variables like servers, db, etc">

		<!-- internal -->
		<xmlpoke  file="${moms.internal.service.host.appconfig.file}" xpath="/configuration/connectionStrings/add[@name='MOMSDBConnectionString']/@connectionString" value="${moms.connection.string}" />
		<if test="${HasDMS=='true'}">
			<xmlpoke failonerror="false" file="${moms.internal.service.host.appconfig.file}" xpath="/configuration/connectionStrings/add[@name='DMSDBConnectionString']/@connectionString" value="${dms.connection.string}" />
		</if>
		<xmlpoke failonerror="false"  file="${moms.internal.service.host.appconfig.file}" xpath="/configuration/appSettings/add[@key = 'DocumentServerLocation']/@value" value="\\${moms.app.server}\" />			
		
		<xmlpoke  file="${moms.internal.service.host.appconfig.file}" xpath="/configuration/appSettings/add[@key = 'ServiceName']/@value" value="${InternalServiceName}" />
		<xmlpoke  file="${moms.internal.service.host.appconfig.file}" xpath="/configuration/appSettings/add[@key = 'ServiceDisplayName']/@value" value="${InternalServiceDisplayName}" />
		<xmlpoke  file="${moms.internal.service.host.appconfig.file}" xpath="/configuration/appSettings/add[@key = 'ServiceDescription']/@value" value="${InternalServiceDescription}" />
		<xmlpoke failonerror="false"   file="${moms.internal.service.host.appconfig.file}" xpath="/configuration/system.serviceModel/services/service/host/baseAddresses/add[@baseAddress = 'net.tcp://localhost:5000']/@baseAddress" value="${wcf.moms.tcp.base.address}" />
		<xmlpoke  file="${moms.internal.service.host.appconfig.file}" xpath="/configuration/system.serviceModel/services/service/host/baseAddresses/add[@baseAddress = 'http://localhost:5001']/@baseAddress" value="${wcf.moms.http.base.address}" />
		<xmlpoke  file="${moms.internal.service.host.appconfig.file}" xpath="/configuration/log4net/appender/file[@value = 'D:/MOMSLogs/moms_MOMSServiceHost.log']/@value" value="${app.log.file}" />	
		<xmlpoke  file="${moms.internal.service.host.appconfig.file}" xpath="/configuration/log4net/root/level[@value = 'ALL']/@value" value="${log.error.level}" />

		<!-- external -->
		<xmlpoke  file="${moms.external.service.host.appconfig.file}" xpath="/configuration/connectionStrings/add[@name='MOMSDBConnectionString']/@connectionString" value="${moms.connection.string}" />
		<xmlpoke  file="${moms.external.service.host.appconfig.file}" xpath="/configuration/appSettings/add[@key = 'ServiceName']/@value" value="${ExternalServiceName}" />
		<xmlpoke  file="${moms.external.service.host.appconfig.file}" xpath="/configuration/appSettings/add[@key = 'ServiceDisplayName']/@value" value="${ExternalServiceDisplayName}" />
		<xmlpoke  file="${moms.external.service.host.appconfig.file}" xpath="/configuration/appSettings/add[@key = 'ServiceDescription']/@value" value="${ExternalServiceDescription}" />
		<xmlpoke  file="${moms.external.service.host.appconfig.file}" xpath="/configuration/appSettings/add[@key = 'AuthenticateUser']/@value" value="${AuthenticateUser}" />
		<xmlpoke  file="${moms.external.service.host.appconfig.file}" xpath="/configuration/appSettings/add[@key = 'AESKey']/@value" value="${AESKey}" />
		<xmlpoke  file="${moms.external.service.host.appconfig.file}" xpath="/configuration/system.serviceModel/services/service/host/baseAddresses/add[@baseAddress = 'http://localhost:5002']/@baseAddress" value="${external.wcf.moms.http.base.address}" />
		<xmlpoke  file="${moms.external.service.host.appconfig.file}" xpath="/configuration/log4net/appender/file[@value = 'D:/MOMSLogs/moms_MOMSExternalServiceHost.log']/@value" value="${external.app.log.file}" />	
		<xmlpoke  file="${moms.external.service.host.appconfig.file}" xpath="/configuration/log4net/root/level[@value = 'ALL']/@value" value="${log.error.level}" />		
		<xmlpoke  file="${moms.external.service.host.appconfig.file}" xpath="/configuration/system.serviceModel/client/endpoint[@address = 'net.tcp://localhost:5000/MOMSWCFService']/@address" value="${wcf.moms.internal.tcp.client.endpoint}" />
		<xmlpoke  file="${moms.external.service.host.appconfig.file}" xpath="/configuration/system.serviceModel/client/endpoint/identity/servicePrincipalName/@value" value="${moms.servicePrincipalName}" />
	</target>
	
	<!-- Filter the environment specific strings for SolarWinds EventLoader Task Host -->
	<target name="filter-SolarWinds" description="Filter environment specific variables like servers, db, etc">
		<xmlpoke failonerror="false" file="${SolarWinds.MOMSEventLoader.task.moms.config.file}" xpath="/configuration/connectionStrings/add[@name='MOMSDBConnectionString']/@connectionString" value="${moms.connection.string}" />
		<xmlpoke failonerror="false" file="${SolarWinds.MOMSEventLoader.task.moms.config.file}" xpath="/configuration/connectionStrings/add[@name='EventLoaderDBConnectionString']/@connectionString" value="${solarwinds.connection.string}" />		
		<xmlpoke failonerror="false" file="${SolarWinds.MOMSEventLoader.task.moms.config.file}" xpath="/configuration/appSettings/add[@key = 'saveSentEmail']/@value" value="${saveSentEmail}" />	
		<xmlpoke failonerror="false" file="${SolarWinds.MOMSEventLoader.task.moms.config.file}" xpath="/configuration/system.serviceModel/client/endpoint[@address = 'http://localhost:5002/MOMSWCFExternalService']/@address" value="${wcf.external.http.client.endpoint}" />
		<xmlpoke failonerror="false" file="${SolarWinds.MOMSEventLoader.task.moms.config.file}" xpath="/configuration/log4net/appender/file[@value = 'D:/MOMSLogs/moms_EventLoaderTask.log']/@value" value="${SolarWinds.event.loader.task.log.file}" />	
		<xmlpoke failonerror="false" file="${SolarWinds.MOMSEventLoader.task.moms.config.file}" xpath="/configuration/log4net/root/level[@value = 'ALL']/@value" value="${log.error.level}" />
		<xmlpoke failonerror="false" file="${SolarWinds.MOMSEventLoader.task.moms.config.file}" xpath="/configuration/appSettings/add[@key = 'TaskName']/@value" value="${env} ${deployment.type} SolarWinds Event Loader" />		
		<xmlpoke failonerror="false" file="${SolarWinds.MOMSEventLoader.task.moms.config.file}" xpath="/configuration/appSettings/add[@key = 'ExternalIDConcat']/@value" value="${SolarWindsIDConcat}" />
		<xmlpoke failonerror="false" file="${SolarWinds.MOMSEventLoader.task.moms.config.file}" xpath="/configuration/system.serviceModel/client/endpoint/identity/servicePrincipalName/@value" value="${endpoint.servicePrincipalName}" />	
		<if test="${IntegratedSecuritySolarWindsLoader=='false'}">
			<xmlpoke failonerror="false" file="${SolarWinds.MOMSEventLoader.task.moms.config.file}" xpath="/configuration/connectionStrings/add[@name='EventLoaderDBConnectionString']/@connectionString" value="${solarwinds.connection.string}" />	
		</if>
		<if test="${IntegratedSecuritySolarWindsLoader=='true'}">
			<xmlpoke failonerror="false" file="${SolarWinds.MOMSEventLoader.task.moms.config.file}" xpath="/configuration/connectionStrings/add[@name='EventLoaderDBConnectionString']/@connectionString" value="${solarwinds.eventloader.secure.connection.string}" />	
		</if>
		<if test="${IntegratedSecuritySolarWindsLoader=='false'}">
			<xmlpoke  file="${moms.internal.service.host.appconfig.file}" xpath="/configuration/connectionStrings/add[@name='SolarWindsDBConnectionString']/@connectionString" value="${solarwinds.connection.string}" />
		</if>
		<if test="${IntegratedSecuritySolarWindsLoader=='true'}">
			<xmlpoke  file="${moms.internal.service.host.appconfig.file}" xpath="/configuration/connectionStrings/add[@name='SolarWindsDBConnectionString']/@connectionString" value="${solarwinds.eventloader.secure.connection.string}" />
		</if>
		<if test="${IntegratedSecuritySolarWindsLoader=='false'}">
			<xmlpoke  file="${moms.external.service.host.appconfig.file}" xpath="/configuration/connectionStrings/add[@name='SolarWindsDBConnectionString']/@connectionString" value="${solarwinds.connection.string}" />
		</if>
		<if test="${IntegratedSecuritySolarWindsLoader=='true'}">
			<xmlpoke  file="${moms.external.service.host.appconfig.file}" xpath="/configuration/connectionStrings/add[@name='SolarWindsDBConnectionString']/@connectionString" value="${solarwinds.eventloader.secure.connection.string}" />
		</if>
		
		<xmlpoke failonerror="false" file="${NewSolarWinds.MOMSEventLoader.task.moms.config.file}" xpath="/configuration/connectionStrings/add[@name='MOMSDBConnectionString']/@connectionString" value="${moms.connection.string}" />
		<xmlpoke failonerror="false" file="${NewSolarWinds.MOMSEventLoader.task.moms.config.file}" xpath="/configuration/connectionStrings/add[@name='EventLoaderDBConnectionString']/@connectionString" value="${solarwinds.connection.string}" />		
		<xmlpoke failonerror="false" file="${NewSolarWinds.MOMSEventLoader.task.moms.config.file}" xpath="/configuration/appSettings/add[@key = 'saveSentEmail']/@value" value="${saveSentEmail}" />	
		<xmlpoke failonerror="false" file="${NewSolarWinds.MOMSEventLoader.task.moms.config.file}" xpath="/configuration/system.serviceModel/client/endpoint[@address = 'http://localhost:5002/MOMSWCFExternalService']/@address" value="${wcf.external.http.client.endpoint}" />
		<xmlpoke failonerror="false" file="${NewSolarWinds.MOMSEventLoader.task.moms.config.file}" xpath="/configuration/log4net/appender/file[@value = 'D:/MOMSLogs/moms_EventLoaderTask.log']/@value" value="${SolarWinds.event.loader.task.log.file}" />	
		<xmlpoke failonerror="false" file="${NewSolarWinds.MOMSEventLoader.task.moms.config.file}" xpath="/configuration/log4net/root/level[@value = 'ALL']/@value" value="${log.error.level}" />
		<xmlpoke failonerror="false" file="${NewSolarWinds.MOMSEventLoader.task.moms.config.file}" xpath="/configuration/appSettings/add[@key = 'TaskName']/@value" value="${env} ${deployment.type} SolarWinds Event Loader" />		
		<xmlpoke failonerror="false" file="${NewSolarWinds.MOMSEventLoader.task.moms.config.file}" xpath="/configuration/appSettings/add[@key = 'ExternalIDConcat']/@value" value="${SolarWindsIDConcat}" />
		<xmlpoke failonerror="false" file="${NewSolarWinds.MOMSEventLoader.task.moms.config.file}" xpath="/configuration/system.serviceModel/client/endpoint/identity/servicePrincipalName/@value" value="${endpoint.servicePrincipalName}" />	
		<if test="${IntegratedSecuritySolarWindsLoader=='false'}">
			<xmlpoke failonerror="false" file="${NewSolarWinds.MOMSEventLoader.task.moms.config.file}" xpath="/configuration/connectionStrings/add[@name='EventLoaderDBConnectionString']/@connectionString" value="${solarwinds.connection.string}" />	
		</if>
		<if test="${IntegratedSecuritySolarWindsLoader=='true'}">
			<xmlpoke failonerror="false" file="${NewSolarWinds.MOMSEventLoader.task.moms.config.file}" xpath="/configuration/connectionStrings/add[@name='EventLoaderDBConnectionString']/@connectionString" value="${solarwinds.eventloader.secure.connection.string}" />	
		</if>
		
	</target>
	
	<!-- Filter the environment specific strings for WhatsUp EventLoader Task Host -->
	<target name="filter-WhatsUp" description="Filter environment specific variables like servers, db, etc">
		<xmlpoke  file="${WhatsUp.MOMSEventLoader.task.moms.config.file}" xpath="/configuration/connectionStrings/add[@name='MOMSDBConnectionString']/@connectionString" value="${moms.connection.string}" />
		<xmlpoke  file="${WhatsUp.MOMSEventLoader.task.moms.config.file}" xpath="/configuration/connectionStrings/add[@name='EventLoaderDBConnectionString']/@connectionString" value="${whatsup.connection.string}" />		
		<xmlpoke  file="${WhatsUp.MOMSEventLoader.task.moms.config.file}" xpath="/configuration/appSettings/add[@key = 'saveSentEmail']/@value" value="${saveSentEmail}" />	
		<xmlpoke  file="${WhatsUp.MOMSEventLoader.task.moms.config.file}" xpath="/configuration/system.serviceModel/client/endpoint[@address = 'http://localhost:5002/MOMSWCFExternalService']/@address" value="${wcf.external.http.client.endpoint}" />
		<xmlpoke  file="${WhatsUp.MOMSEventLoader.task.moms.config.file}" xpath="/configuration/log4net/appender/file[@value = 'D:/MOMSLogs/moms_EventLoaderTask.log']/@value" value="${WhatsUp.event.loader.task.log.file}" />	
		<xmlpoke  file="${WhatsUp.MOMSEventLoader.task.moms.config.file}" xpath="/configuration/log4net/root/level[@value = 'ALL']/@value" value="${log.error.level}" />
		<xmlpoke  file="${WhatsUp.MOMSEventLoader.task.moms.config.file}" xpath="/configuration/appSettings/add[@key = 'TaskName']/@value" value="${env} WhatsUp Event Loader" />		
		<if test="${IntegratedSecurityWhatsUpLoader=='false'}">
			<xmlpoke  file="${moms.internal.service.host.appconfig.file}" xpath="/configuration/connectionStrings/add[@name='WhatsUpDBConnectionString']/@connectionString" value="${whatsup.connection.string}" />
		</if>	
		<if test="${IntegratedSecurityWhatsUpLoader=='true'}">
			<xmlpoke  file="${moms.internal.service.host.appconfig.file}" xpath="/configuration/connectionStrings/add[@name='WhatsUpDBConnectionString']/@connectionString" value="${whatsup.eventloader.secure.connection.string}" />	
		</if>
		<if test="${IntegratedSecurityWhatsUpLoader=='false'}">
			<xmlpoke  file="${moms.external.service.host.appconfig.file}" xpath="/configuration/connectionStrings/add[@name='WhatsUpDBConnectionString']/@connectionString" value="${whatsup.connection.string}" />
		</if>
		<if test="${IntegratedSecurityWhatsUpLoader=='true'}">
			<xmlpoke  file="${moms.external.service.host.appconfig.file}" xpath="/configuration/connectionStrings/add[@name='WhatsUpDBConnectionString']/@connectionString" value="${whatsup.eventloader.secure.connection.string}" />	
		</if>
		<xmlpoke  file="${WhatsUp.MOMSEventLoader.task.moms.config.file}" xpath="/configuration/appSettings/add[@key = 'ExternalIDConcat']/@value" value="${WhatsUpIDConcat}" />
		<xmlpoke  file="${WhatsUp.MOMSEventLoader.task.moms.config.file}" xpath="/configuration/system.serviceModel/client/endpoint/identity/servicePrincipalName/@value" value="${endpoint.servicePrincipalName}" />	
		<if test="${IntegratedSecurityWhatsUpLoader=='false'}">
			<xmlpoke  file="${WhatsUp.MOMSEventLoader.task.moms.config.file}" xpath="/configuration/connectionStrings/add[@name='EventLoaderDBConnectionString']/@connectionString" value="${whatsup.connection.string}" />	
		</if>
		<if test="${IntegratedSecurityWhatsUpLoader=='true'}">
			<xmlpoke  file="${WhatsUp.MOMSEventLoader.task.moms.config.file}" xpath="/configuration/connectionStrings/add[@name='EventLoaderDBConnectionString']/@connectionString" value="${whatsup.eventloader.secure.connection.string}" />	
		</if>

	</target>
	
	<!-- Filter the MOMSMobile environment specific strings for WebAPI -->
	<target name="filter-MOMSMobile" description="Filter environment specific variables like servers, db, etc">		
		<xmlpoke  file="${momsmobile.webapi.config.file}" xpath="/configuration/appSettings/add[@key = 'PreserveLoginUrl']/@value" value="${PreserveLoginUrl}" />
		<xmlpoke  file="${momsmobile.webapi.config.file}" xpath="/configuration/appSettings/add[@key = 'ClientValidationEnabled']/@value" value="${ClientValidationEnabled}" />
		<xmlpoke  file="${momsmobile.webapi.config.file}" xpath="/configuration/appSettings/add[@key = 'UnobtrusiveJavaScriptEnabled']/@value" value="${UnobtrusiveJavaScriptEnabled}" />
		<xmlpoke  file="${momsmobile.webapi.config.file}" xpath="/configuration/appSettings/add[@key = 'AESKey']/@value" value="${AESKey}" />		
		<xmlpoke  file="${momsmobile.webapi.config.file}" xpath="/configuration/system.serviceModel/client/endpoint[@address = 'net.tcp://localhost:988/MomsMobileWcfService']/@address" value="${wcf.momsmobile.internal.tcp.client.endpoint}" />		
		<xmlpoke  file="${momsmobile.webapi.config.file}" xpath="/configuration/log4net/appender/file[@value = 'D:\MomsMobileLogs\MomsMobileWebApi.log']/@value" value="${webapi.log.file}" />		
		<xmlpoke  file="${momsmobile.webapi.config.file}" xpath="/configuration/log4net/root/level[@value = 'ALL']/@value" value="${log.error.level}" />	
		<xmlpoke  file="${momsmobile.webapi.config.file}" xpath="/configuration/system.serviceModel/client/endpoint/identity/servicePrincipalName/@value" value="${momsmobile.servicePrincipalName}" />

		
	<!-- Filter the MOMSMobile environment specific strings for MOMSMobile Internal Service Host -->
		<xmlpoke  file="${momsmobile.internal.service.host.appconfig.file}" xpath="/configuration/connectionStrings/add[@connectionString = 'Database=MOMS;Server=localhost;UID=MOMSAppUser;PWD=*******;Min Pool Size=1;Max Pool Size=100;Connect Timeout=120;']/@connectionString" value="${momsmobile.connection.string}" />		
		<xmlpoke  file="${momsmobile.internal.service.host.appconfig.file}" xpath="/configuration/appSettings/add[@key = 'AESKey']/@value" value="${AESKey}" />	
        <xmlpoke  file="${momsmobile.internal.service.host.appconfig.file}" xpath="/configuration/system.serviceModel/services/service/host/baseAddresses/add[@baseAddress = 'net.tcp://localhost:988']/@baseAddress" value="${wcf.momsmobile.tcp.base.address}" />		
		<xmlpoke  file="${momsmobile.internal.service.host.appconfig.file}" xpath="/configuration/system.serviceModel/services/service/host/baseAddresses/add[@baseAddress = 'http://localhost:989']/@baseAddress" value="${wcf.momsmobile.http.base.address}" />
	    <xmlpoke  file="${momsmobile.internal.service.host.appconfig.file}" xpath="/configuration/system.serviceModel/client/endpoint[@address = 'net.tcp://localhost:5000/MOMSWCFService']/@address" value="${wcf.moms.internal.tcp.client.endpoint}" />	
		<xmlpoke  file="${momsmobile.internal.service.host.appconfig.file}" xpath="/configuration/log4net/appender/file[@value = 'D:\MomsMobileLogs\MomsMobileWcfInternalHost.log']/@value" value="${mobile.app.log.file}" />		
		<xmlpoke  file="${momsmobile.internal.service.host.appconfig.file}" xpath="/configuration/log4net/root/level[@value = 'ALL']/@value" value="${log.error.level}" />		
		<xmlpoke  file="${momsmobile.internal.service.host.appconfig.file}" xpath="/configuration/system.serviceModel/client/endpoint/identity/servicePrincipalName/@value" value="${momsmobile.servicePrincipalName}" />
	
	<!-- Replace MOMS Mobile js strings -->	
		<if test="${deployment.environment=='PRODUCT'}">
			<exec program="ReplaceUtil.exe" failonerror="true">
				<arg value="${momsmobile.dist.dir.js}" />
				<arg value="js"/>
				<arg value="http://127.0.0.1:7777/MomsMobileWebApi/api/" />
				<arg value="/MomsMobileWebApi/api/" />
			</exec>
			<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${momsmobile.dist.dir.js}" />
			<arg value="js"/>
			<arg value="http://**************/Inventory" />
			<arg value="${momsmobile.js.url}/Inventory" />
			<arg value="ALL"/>
		</exec>
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${momsmobile.dist.dir.js}" />
			<arg value="js"/>
			<arg value="http://127.0.0.1:7777/Inventory" />
			<arg value="${momsmobile.js.url}/Inventory" />
			<arg value="ALL"/>
		</exec>
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${momsmobile.dist.dir.js}" />
			<arg value="js"/>
			<arg value="homeURL = 'http://127.0.0.1:7777'" />
			<arg value="homeURL = '${momsmobile.js.url}'" />
		</exec>			
		</if>
		<exec program="ReplaceUtil.exe" failonerror="true">
				<arg value="${momsmobile.dist.dir.js}" />
				<arg value="js"/>
				<arg value="http://127.0.0.1:7777" />
				<arg value="${momsmobile.js.url}" />
				<arg value="ALL" />
		</exec>
		
		
		<if test="${ActiveDirectoryFeature=='true'}">
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${momsmobile.dist.dir.js}" />
			<arg value="js"/>
			<arg value="_SSOEnabled = false" />
			<arg value="_SSOEnabled = true" />
		</exec>
		</if>	
		<if test="${TransPortalFeature=='true'}">
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${momsmobile.dist.dir.js}" />
			<arg value="js"/>
			<arg value="_SSOEnabled = false" />
			<arg value="_SSOEnabled = true" />
		</exec>
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${web.momsmobile.dist.dir}" />
			<arg value="html"/>
			<arg value=" display " />
			<arg value=" display:none " />
			<arg value="ALL"/>
		</exec>
		</if>
		<if test="${deployment.environment!='PRODUCT'}">
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${momsmobile.dist.dir.js}" />
			<arg value="js"/>
			<arg value="http://127.0.0.1:7777/MomsMobileWebApi/api/" />
			<arg value="${momsmobile.js.url}/MomsMobileWebApi/api/" />
		</exec> 
		</if>		
	</target>

	
	<!-- Filter Infinity Service Host config -->	
	<target name="filter-Infinity-service-config-file" description="Filter Infinity Service Host Config">
		<xmlpoke  file="${Infinity.service.host.appconfig.file}" xpath="/configuration/system.serviceModel/services/service/host/baseAddresses/add[@baseAddress = 'http://localhost:5006']/@baseAddress" value="${wcf.Infinity.http.base.address}" />
		<xmlpoke  file="${Infinity.service.host.appconfig.file}" xpath="/configuration/system.serviceModel/client/endpoint[@address = 'http://localhost:5002/MOMSWCFExternalService']/@address" value="${wcf.Infinity.http.client.endpoint}" />
		<xmlpoke  file="${Infinity.service.host.appconfig.file}" xpath="/configuration/log4net/appender/file[@value = 'D:/MOMSLogs/infinity_InfinityMOMSServiceHost.log']/@value" value="${log.dir}/infinity_InfinityMOMSServiceHost.log" />	
		<xmlpoke  file="${Infinity.service.host.appconfig.file}" xpath="/configuration/system.serviceModel/client/endpoint/identity/servicePrincipalName/@value" value="${endpoint.servicePrincipalName}" />
		<!--<if test="${property::exists('InfinitySecurityMode')}">
		<if test="${InfinitySecurityMode=='None'}">
			<exec program="FilterInfinitySecurity.bat" failonerror="true" >
			<arg value="${base.dir}" />
			</exec>
			</if> 
		</if> -->
	</target>
	
	<!-- Filter Integrity Service Host config -->
	<target name="filter-Integrity-service-config-file" description="Filter Integrity Service Host Config">
		<xmlpoke  file="${Integrity.service.host.appconfig.file}" xpath="/configuration/system.serviceModel/services/service/host/baseAddresses/add[@baseAddress = 'http://localhost:5009']/@baseAddress" value="${wcf.Integrity.http.base.address}" />
		<xmlpoke  file="${Integrity.service.host.appconfig.file}" xpath="/configuration/system.serviceModel/client/endpoint[@address = 'http://localhost:5002/MOMSWCFExternalService']/@address" value="${wcf.Integrity.http.client.endpoint}" />
		<xmlpoke  file="${Integrity.service.host.appconfig.file}" xpath="/configuration/log4net/appender/file[@value = 'D:/MOMSLogs/Integrity_IntegrityMOMSServiceHost.log']/@value" value="${log.dir}/integrity_IntegrityMOMSServiceHost.log" />	
		<xmlpoke  file="${Integrity.service.host.appconfig.file}" xpath="/configuration/system.serviceModel/client/endpoint/identity/servicePrincipalName/@value" value="${endpoint.servicePrincipalName}" />	
	</target>
	
	<!-- Filter TransSuite Service Host config -->
	<target name="filter-TransSuite-service-config-file" description="Filter TransSuite Service Host Config">
		<xmlpoke  file="${TransSuite.service.host.appconfig.file}" xpath="/configuration/system.serviceModel/services/service/host/baseAddresses/add[@baseAddress = 'http://localhost:5007']/@baseAddress" value="${wcf.TransSuite.http.base.address}" />
		<xmlpoke  file="${TransSuite.service.host.appconfig.file}" xpath="/configuration/system.serviceModel/client/endpoint[@address = 'http://localhost:5002/MOMSWCFExternalService']/@address" value="${wcf.TransSuite.http.client.endpoint}" />
		<xmlpoke  file="${TransSuite.service.host.appconfig.file}" xpath="/configuration/log4net/appender/file[@value = 'D:/MOMSLogs/TransSuite_MOMServiceHost.log']/@value" value="${log.dir}/TransSuite_MOMServiceHost.log" />	
		<xmlpoke  file="${TransSuite.service.host.appconfig.file}" xpath="/configuration/system.serviceModel/client/endpoint/identity/servicePrincipalName/@value" value="${endpoint.servicePrincipalName}" />	
	</target>
	
	<!-- Filter Report variables -->
	<target name="filter-report-config-file" description="Filter environment specific variables like servers, db, etc">		
		<xmlpoke  file="${reports.deployer.appconfig.file}" xpath="/configuration/appSettings/add[@key = 'reportServiceUrl']/@value" value="${report.service}" />	
		<xmlpoke  file="${reports.deployer.appconfig.file}" xpath="/configuration/appSettings/add[@key = 'deployReportsFromDir']/@value" value="${deploy.Reports.From.Dir}" />		
		<xmlpoke  file="${reports.deployer.appconfig.file}" xpath="/configuration/appSettings/add[@key = 'reportsPath']/@value" value="${deploy.Reports.Reports.Path}" />	
		<xmlpoke  file="${reports.deployer.appconfig.file}" xpath="/configuration/appSettings/add[@key = 'reportsFolderName']/@value" value="MOMSReports" />	
		<xmlpoke  file="${reports.deployer.appconfig.file}" xpath="/configuration/appSettings/add[@key = 'overwriteReports']/@value" value="${deploy.Reports.Reports.Overwrite}" />		
		<xmlpoke  file="${reports.deployer.appconfig.file}" xpath="/configuration/appSettings/add[@key = 'overwriteDataSets']/@value" value="${deploy.Reports.DataSets.Overwrite}" />	
		<xmlpoke  file="${reports.deployer.appconfig.file}" xpath="/configuration/appSettings/add[@key = 'sharedDataSourceFolderName']/@value" value="${deploy.Reports.Shared.DataSource.FolderName}" />	
		<xmlpoke  file="${reports.deployer.appconfig.file}" xpath="/configuration/appSettings/add[@key = 'sharedDataSetFolderName']/@value" value="${deploy.Reports.Shared.DataSet.FolderName}" />			
		<xmlpoke  file="${reports.deployer.appconfig.file}" xpath="/configuration/appSettings/add[@key = 'dataSetDataSourcePath']/@value" value="${deploy.Reports.DataSet.DataSource.Path}" />				
		<!-- Reports data sources -->
		<xmlpoke  file="${reports.deployer.appconfig.file}" xpath="/configuration/dataSources/MOMSLive/add[@key = 'Name']/@value" value="MOMSLive" />
		<xmlpoke  file="${reports.deployer.appconfig.file}" xpath="/configuration/dataSources/MOMSLive/add[@key = 'Ext']/@value" value="SQL" />
		<xmlpoke  file="${reports.deployer.appconfig.file}" xpath="/configuration/dataSources/MOMSLive/add[@key = 'ConnectionString']/@value" value="${report.datasource.moms}" />
		<xmlpoke  file="${reports.deployer.appconfig.file}" xpath="/configuration/dataSources/MOMSLive/add[@key = 'userID']/@value" value="${report.datasource.userid}" />
		<xmlpoke  file="${reports.deployer.appconfig.file}" xpath="/configuration/dataSources/MOMSLive/add[@key = 'pass']/@value" value="${report.datasource.pass}" />	
	</target>



	<target name="filter-ActiveDirectory" description="Filter  ActiveDirectory">
		<xmlpoke  file="${insight.webapi.config.file}" xpath="/configuration/appSettings/add[@key = 'EnableActiveDirectory']/@value" value="true"  />
		<if test="${MobileFeature=='true'}">		
			<xmlpoke  file="${momsmobile.webapi.config.file}" xpath="/configuration/appSettings/add[@key = 'ProviderName']/@value" value="MOMSADMembershipProvider" />	
			<xmlpoke  file="${momsmobile.internal.service.host.appconfig.file}" xpath="/configuration/appSettings/add[@key = 'ProviderName']/@value" value="MOMSADMembershipProvider" />	
		</if>
		<xmlpoke  file="${moms.internal.service.host.appconfig.file}" xpath="/configuration/appSettings/add[@key = 'ServiceUser']/@value" value="${ServiceUser}" />
		<xmlpoke  file="${moms.internal.service.host.appconfig.file}" xpath="/configuration/appSettings/add[@key = 'ServicePassword']/@value" value="${ServicePassword}" />
		<if test="${ADPollingTask=='true'}">	
			<xmlpoke  file="${dist.dir}\ScheduledTasks\MOMSActiveDirectoryPolling\tcore.MOMSActiveDirectoryPolling.exe.config" xpath="/configuration/log4net/appender/file[@value = 'D:/MOMSLogs/moms_MOMSActiveDirectoryPollingTask.log']/@value" value="${ActiveDirectoryPollingTask.task.log.file}" />	
			<xmlpoke  file="${dist.dir}\ScheduledTasks\MOMSActiveDirectoryPolling\tcore.MOMSActiveDirectoryPolling.exe.config" xpath="/configuration/appSettings/add[@key = 'ServiceUser']/@value" value="${ServiceUser}" />
			<xmlpoke  file="${dist.dir}\ScheduledTasks\MOMSActiveDirectoryPolling\tcore.MOMSActiveDirectoryPolling.exe.config" xpath="/configuration/appSettings/add[@key = 'ServicePassword']/@value" value="${ServicePassword}" />
			<xmlpoke  file="${dist.dir}\ScheduledTasks\MOMSActiveDirectoryPolling\tcore.MOMSActiveDirectoryPolling.exe.config" xpath="/configuration/connectionStrings/add[@name='MOMSDBConnectionString']/@connectionString" value="${moms.connection.string}" />
		</if>
	</target>
	
		
	<!-- Filter the Insight environment specific strings -->
	<target name="filter-Insight" description="Filter environment specific variables like servers, db, etc">			
		<xmlpoke  file="${insight.webapi.config.file}" xpath="/configuration/system.serviceModel/client/endpoint[@address = 'net.tcp://localhost:5000/MOMSWCFService']/@address" value="${wcf.internal.tcp.client.endpoint}" />
		<xmlpoke  file="${insight.webapi.config.file}" xpath="/configuration/log4net/appender/file[@value = 'D:\InsightLogs\InsightWebApi.log']/@value" value="${insight.webapi.log.file}" />		
		<xmlpoke  file="${insight.webapi.config.file}" xpath="/configuration/log4net/root/level[@value = 'ALL']/@value" value="${log.error.level}" />	
		<xmlpoke  file="${insight.webapi.config.file}" xpath="/configuration/appSettings/add[@key = 'DocumentServerLocation']/@value" value="${document.server.location}" />
		<xmlpoke  file="${insight.webapi.config.file}" xpath="/configuration/appSettings/add[@key = 'IFXWebAPIService']/@value" value="${IFXWebAPIService}" />
		<xmlpoke failonerror="false" file="${insight.webapi.config.file}" xpath="/configuration/appSettings/add[@key = 'EnableSingleSignOn']/@value" value="${enableSingleSignOn}" />
		<xmlpoke  file="${insight.webapi.config.file}" xpath="/configuration/elmah/errorLog[@logPath = 'D:\InsightLogs\Elmah_InsightWebAPI']/@logPath" value="${insight.elmah.webapi.log.dir}" />
		<xmlpoke  file="${insight.webapi.keys.config.file}" xpath="/appSettings/add[@key = 'webSiteURL']/@value" value="http://${insight.api.server}/"  />		
		<if test="${SSLFeature=='false'}">
			<xmlpoke failonerror="false" file="${insight.webapi.keys.config.file}" xpath="/appSettings/add[@key = 'authServerJWTDomain']/@value" value="http://${transportal.web.alias}:7006"  />	
		</if>
        <if test="${SSLFeature=='true'}">
			<xmlpoke failonerror="false" file="${insight.webapi.keys.config.file}" xpath="/appSettings/add[@key = 'authServerJWTDomain']/@value" value="https://${transportal.web.alias}:4436"  />	
		</if>
		<xmlpoke  file="${insight.webapi.config.file}" xpath="/configuration/appSettings/add[@key = 'AuthorizeAttributeFilter']/@value" value="${insight.AuthorizeAttributeFilter}"  />
		<xmlpoke  file="${insight.webapi.config.file}" xpath="/configuration/system.serviceModel/client/endpoint/identity/servicePrincipalName/@value" value="${moms.servicePrincipalName}" />
		<xmlpoke  file="${insight.webapi.config.file}" xpath="/configuration/appSettings/add[@key = 'ReportServiceURL']/@value" value="${report.service}"  />
		<xmlpoke  file="${insight.rpts.config.file}" xpath="/configuration/appSettings/add[@key = 'reportServerURL']/@value" value="${report.server.URL}"  />
		<xmlpoke  file="${insight.webapi.config.file}" xpath="/configuration/appSettings/add[@key = 'EnableActiveDirectory']/@value" value="${ActiveDirectoryFeature}"  />
		<xmlpoke  file="${insight.webapi.config.file}" xpath="/configuration/appSettings/add[@key = 'IsRTM']/@value" value="${map.rtmMap}"  />
		<xmlpoke  failonerror="false"  file="${insight.webapi.config.file}" xpath="/configuration/appSettings/add[@key = 'EnableExternalEquipmentEvents']/@value" value="${enable.external.equipment.events}"  />
		
		<!-- Web Config -->
		<xmlpoke failonerror="true"  file="${insight.web.config.file}" xpath="/configuration/system.webServer/rewrite/rules/rule[@name = 'SSL']/@enabled" value="${SSLFeature}" />
		<if test="${SSLTermination=='true'}">
			<xmlpoke failonerror="true"  file="${insight.web.config.file}" xpath="/configuration/system.webServer/rewrite/rules/rule[@name = 'SSL']/@enabled" value="false" />
		</if>
		<!-- Insight replace logo with project logo -->
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${insight.dist.dir}\webLayer\InsightWeb\dist\css" />
			<arg value="css"/>
			<arg value="SanJose_login_Hero.jpg" />
			<arg value="${insight.project.logo.name}" />
			<arg value="ALL"/>
		</exec>
		<!-- Insight replace logo with project logo - 2 -->
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${insight.dist.dir}\webLayer\InsightWeb\dist\css" />
			<arg value="css"/>
			<arg value="mrachina.jpg" />
			<arg value="${insight.project.logo.name}" />
			<arg value="ALL"/>
		</exec>
		<!-- Insight replace logo with project logo - 2 
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${insight.dist.dir}\webLayer\InsightWeb\dist\css" />
			<arg value="css"/>
			<arg value="login-background.png" />
			<arg value="${insight.project.logo.name}" />
			<arg value="ALL"/>
		</exec>-->
		
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${insight.dist.dir.js}" />
			<arg value="js"/>
			<arg value="https://infinity-servername/" />
			<arg value="${infinity.map.url.server}/" />
			<arg value="ALL"/>
		</exec>	
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${insight.dist.dir.js}" />
			<arg value="js"/>
			<arg value="http://infinity-servername/" />
			<arg value="${infinity.map.url.server}/" />
			<arg value="ALL"/>
		</exec>	
				
		<if test="${SSLFeature=='false'}">
			<exec program="ReplaceUtil.exe" failonerror="true">
				<arg value="${insight.dist.dir.js}" />
				<arg value="js"/>
				<arg value="http://localhost:889/" />
				<arg value="http://${insight.api.server}:889/" />
				<arg value="ALL"/>
			</exec>
			<exec program="ReplaceUtil.exe" failonerror="true">
				<arg value="${insight.dist.dir.js}" />
				<arg value="js"/>
				<arg value="http://localhost:888/" />
				<arg value="http://${insight.web.url}/" />
				<arg value="ALL"/>
			</exec>
			<exec program="ReplaceUtil.exe" failonerror="true">
				<arg value="${insight.dist.dir.js}" />
				<arg value="js"/>
				<arg value="http://localhost:7006/" />
				<arg value="http://${transportal.web.alias}:7006/" />
				<arg value="ALL"/>
			</exec>
			<exec program="ReplaceUtil.exe" failonerror="true">
				<arg value="${insight.dist.dir.js}" />
				<arg value="js"/>
				<arg value="http://txp-servername:7006/" />
				<arg value="http://${transportal.web.alias}:7006/" />
				<arg value="ALL"/>
			</exec>
			<exec program="ReplaceUtil.exe" failonerror="true">
				<arg value="${insight.dist.dir.js}" />
				<arg value="js"/>
				<arg value="http://localhost:7007" />
				<arg value="http://${transportal.web.alias}" />
				<arg value="ALL"/>
			</exec>
			<exec program="ReplaceUtil.exe" failonerror="true">
				<arg value="${insight.dist.dir.js}" />
				<arg value="js"/>
				<arg value="http://txp-servername:7007" />
				<arg value="http://${transportal.web.alias}" />
				<arg value="ALL"/>
			</exec>
			<exec program="ReplaceUtil.exe" failonerror="true">
				<arg value="${web.insight.dist.dir}" />
				<arg value="html"/>
				<arg value="http://localhost:889/" />
				<arg value="http://${insight.api.server}:889/" />
				<arg value="ALL"/>
			</exec>
			<exec program="ReplaceUtil.exe" failonerror="true">
				<arg value="${insight.dist.dir.js}" />
				<arg value="js"/>
				<arg value="http://localhost/TransSuite.TIS" />
				<arg value="${transsuite.map.url.server}/TransSuite.TIS" />
				<arg value="ALL"/>
			</exec>
			<exec program="ReplaceUtil.exe" failonerror="true">
				<arg value="${insight.dist.dir.js}" />
				<arg value="js"/>
				<arg value="http://transsuite-servername/TransSuite.TIS" />
				<arg value="${transsuite.map.url.server}/TransSuite.TIS" />
				<arg value="ALL"/>
			</exec>
			<exec program="ReplaceUtil.exe" failonerror="true">
				<arg value="${insight.dist.dir.js}" />
				<arg value="js"/>
				<arg value="http://localhost/TransSuite.FMS" />
				<arg value="${transsuite.map.url.server}/TransSuite.FMS" />
				<arg value="ALL"/>
			</exec>
			<exec program="ReplaceUtil.exe" failonerror="true">
				<arg value="${insight.dist.dir.js}" />
				<arg value="js"/>
				<arg value="http://transsuite-servername/TransSuite.FMS" />
				<arg value="${transsuite.map.url.server}/TransSuite.FMS" />
				<arg value="ALL"/>
			</exec>
		
			<exec program="ReplaceUtil.exe" failonerror="true">
				<arg value="${insight.dist.dir.js}" />
				<arg value="js"/>
				<arg value="http://localhost/" />
				<arg value="http://${transportal.web.alias}" />
				<arg value="ALL"/>
			</exec>
			
			<exec program="ReplaceUtil.exe" failonerror="true">
				<arg value="${insight.dist.dir.js}" />
				<arg value="js"/>
				<arg value="http://ifx-servername/" />
				<arg value="http://${ifx.web.alias}/" />
				<arg value="commonconstants.js"/>
			</exec>
		</if>
		
		<if test="${SSLFeature=='true'}">
		
			<xmlpoke  file="${insight.webapi.keys.config.file}" xpath="/appSettings/add[@key = 'webSiteURL']/@value" value="https://${insight.api.server}/"  />
			<exec program="ReplaceUtil.exe" failonerror="true">
				<arg value="${insight.dist.dir.js}" />
				<arg value="js"/>
				<arg value="http://localhost:889/" />
				<arg value="https://${insight.api.server}:8443/" />
				<arg value="ALL"/>
			</exec>
			<exec program="ReplaceUtil.exe" failonerror="true">
				<arg value="${insight.dist.dir.js}" />
				<arg value="js"/>
				<arg value="http://localhost:888/" />
				<arg value="https://${insight.api.server}/" />
				<arg value="ALL"/>
			</exec>
			<exec program="ReplaceUtil.exe" failonerror="true">
				<arg value="${insight.dist.dir.js}" />
				<arg value="js"/>
				<arg value="http://localhost:7006/" />
				<arg value="https://${transportal.web.alias}:4436/" />
				<arg value="ALL"/>
			</exec>
			<exec program="ReplaceUtil.exe" failonerror="true">
				<arg value="${insight.dist.dir.js}" />
				<arg value="js"/>
				<arg value="http://txp-servername:7006/" />
				<arg value="https://${transportal.web.alias}:4436/" />
				<arg value="ALL"/>
			</exec>
			<exec program="ReplaceUtil.exe" failonerror="true">
				<arg value="${insight.dist.dir.js}" />
				<arg value="js"/>
				<arg value="http://localhost:7007" />
				<arg value="https://${transportal.web.alias}" />
				<arg value="ALL"/>
			</exec>
			<exec program="ReplaceUtil.exe" failonerror="true">
				<arg value="${insight.dist.dir.js}" />
				<arg value="js"/>
				<arg value="http://txp-servername:7007" />
				<arg value="https://${transportal.web.alias}" />
				<arg value="ALL"/>
			</exec>
			<exec program="ReplaceUtil.exe" failonerror="true">
				<arg value="${web.insight.dist.dir}" />
				<arg value="html"/>
				<arg value="http://localhost:889/" />
				<arg value="https://${insight.api.server}:8443/" />
				<arg value="ALL"/>
			</exec>
			<exec program="ReplaceUtil.exe" failonerror="true">
				<arg value="${insight.dist.dir.js}" />
				<arg value="js"/>
				<arg value="http://localhost/TransSuite.TIS" />
				<arg value="${transsuite.map.url.server}/TransSuite.TIS" />
				<arg value="ALL"/>
			</exec>
			<exec program="ReplaceUtil.exe" failonerror="true">
				<arg value="${insight.dist.dir.js}" />
				<arg value="js"/>
				<arg value="http://transsuite-servername/TransSuite.TIS" />
				<arg value="${transsuite.map.url.server}/TransSuite.TIS" />
				<arg value="ALL"/>
			</exec>
			<exec program="ReplaceUtil.exe" failonerror="true">
				<arg value="${insight.dist.dir.js}" />
				<arg value="js"/>
				<arg value="http://localhost/TransSuite.FMS" />
				<arg value="${transsuite.map.url.server}/TransSuite.FMS" />
				<arg value="ALL"/>
			</exec>
			<exec program="ReplaceUtil.exe" failonerror="true">
				<arg value="${insight.dist.dir.js}" />
				<arg value="js"/>
				<arg value="http://transsuite-servername/TransSuite.FMS" />
				<arg value="${transsuite.map.url.server}/TransSuite.FMS" />
				<arg value="ALL"/>
			</exec>
			
			<exec program="ReplaceUtil.exe" failonerror="true">
				<arg value="${insight.dist.dir.js}" />
				<arg value="js"/>
				<arg value="http://localhost/" />
				<arg value="https://${transportal.web.alias}" />
				<arg value="ALL"/>
			</exec>
			<exec program="ReplaceUtil.exe" failonerror="true">
				<arg value="${insight.dist.dir.js}" />
				<arg value="js"/>
				<arg value="ws://" />
				<arg value="wss://" />
				<arg value="ALL"/>
			</exec>
			
			<exec program="ReplaceUtil.exe" failonerror="true">
				<arg value="${insight.dist.dir.js}" />
				<arg value="js"/>
				<arg value="http://ifx-servername/" />
				<arg value="https://${ifx.web.alias}/" />
				<arg value="commonconstants.js"/>
			</exec>
		</if>
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${insight.dist.dir.js}" />
			<arg value="js"/>
			<arg value="http://ifx-api/" />
			<arg value="${express.map.api.url.server}/" />
			<arg value="ALL"/>
		</exec>	
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${insight.dist.dir.js}" />
			<arg value="js"/>
			<arg value="http://ifx-api/" />
			<arg value="${express.map.api.url.server}/" />
			<arg value="ALL"/>
		</exec>	
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${insight.dist.dir.js}" />
			<arg value="js"/>
			<arg value="http://ifx-servername/" />
			<arg value="${express.map.url.server}/" />
			<arg value="ALL"/>
		</exec>	
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${insight.dist.dir.js}" />
			<arg value="js"/>
			<arg value="http://incidentlog-servername/" />
			<arg value="${express.map.url.server}/" />
			<arg value="ALL"/>
		</exec>
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${insight.dist.dir.js}" />
			<arg value="js"/>
			<arg value="http://vtms-servername/" />
			<arg value="${express.map.url.server}/" />
			<arg value="ALL"/>
		</exec>
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${insight.dist.dir.js}" />
			<arg value="js"/>
			<arg value="http://cctv-servername/videoinsight/" />
			<arg value="${cctv.servername}/videoinsight/" />
			<arg value="ALL"/>
		</exec>
		
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${insight.dist.dir.js}" />
			<arg value="js"/>
			<arg value="http://cps-app-alias:4000" />
			<arg value="${rtm.exceptions.url}" />
			<arg value="ALL"/>
		</exec>
		
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${insight.dist.dir.js}" />
			<arg value="js"/>
			<arg value="showInfinityOption: false" />
			<arg value="showInfinityOption: ${map.showInfinityOption}" />
		</exec>
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${insight.dist.dir.js}" />
			<arg value="js"/>
			<arg value="rtmMap: false" />
			<arg value="rtmMap: ${map.rtmMap}" />
		</exec>
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${insight.dist.dir.js}" />
			<arg value="js"/>
			<arg value="rtmMap: true" />
			<arg value="rtmMap: ${map.rtmMap}" />
		</exec>
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${insight.dist.dir.js}" />
			<arg value="js"/>
			<arg value="onlineMap: true" />
			<arg value="onlineMap: ${online.maps}" />
		</exec>
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${insight.dist.dir.js}" />
			<arg value="js"/>
			<arg value="onlineMap: false" />
			<arg value="onlineMap: ${online.maps}" />
		</exec>
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${insight.dist.dir.js}" />
			<arg value="js"/>
			<arg value="offlineMap: true" />
			<arg value="offlineMap: ${offline.maps}" />
		</exec>
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${insight.dist.dir.js}" />
			<arg value="js"/>
			<arg value="offlineMap: false" />
			<arg value="offlineMap: ${offline.maps}" />
		</exec>
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${insight.dist.dir.js}" />
			<arg value="js"/>
			<arg value="laneCommands: false" />
			<arg value="laneCommands: ${map.laneCommands}" />
		</exec>	
		
		<!-- showRtmLayers -->		
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${insight.dist.dir.js}" />
			<arg value="js"/>
			<arg value="speed: true" />
			<arg value="speed: ${showRtmLayers.speed}" />
			<arg value="commonconstants.js"/>
		</exec>
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${insight.dist.dir.js}" />
			<arg value="js"/>
			<arg value="cctv: true" />
			<arg value="cctv: ${showRtmLayers.cctv}" />
			<arg value="commonconstants.js"/>
		</exec>
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${insight.dist.dir.js}" />
			<arg value="js"/>
			<arg value="tollZone: true" />
			<arg value="tollZone: ${showRtmLayers.tollZone}" />
			<arg value="commonconstants.js"/>
		</exec>
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${insight.dist.dir.js}" />
			<arg value="js"/>
			<arg value="transactions: true" />
			<arg value="transactions: ${showRtmLayers.transactions}" />
			<arg value="commonconstants.js"/>
		</exec>
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${insight.dist.dir.js}" />
			<arg value="js"/>
			<arg value="vtms: true" />
			<arg value="vtms: ${showRtmLayers.vtms}" />
			<arg value="commonconstants.js"/>
		</exec>
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${insight.dist.dir.js}" />
			<arg value="js"/>
			<arg value="laneMode: false" />
			<arg value="laneMode: ${showRtmLayers.laneMode}" />
			<arg value="commonconstants.js"/>
		</exec>
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${insight.dist.dir.js}" />
			<arg value="js"/>
			<arg value="laneOperations: false" />
			<arg value="laneOperations: ${showRtmLayers.laneOperations}" />
			<arg value="commonconstants.js"/>
		</exec>
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${insight.dist.dir.js}" />
			<arg value="js"/>
			<arg value="laneCommand: false" />
			<arg value="laneCommand: ${showRtmLayers.laneCommand}" />
			<arg value="commonconstants.js"/>
		</exec>
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${insight.dist.dir.js}" />
			<arg value="js"/>
			<arg value="pricingMode: false" />
			<arg value="pricingMode: ${showRtmLayers.pricingMode}" />
			<arg value="commonconstants.js"/>
		</exec>
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${insight.dist.dir.js}" />
			<arg value="js"/>
			<arg value="travelTimes: false" />
			<arg value="travelTimes: ${showRtmLayers.travelTimes}" />
			<arg value="commonconstants.js"/>
		</exec>	
		
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${insight.dist.dir.js}" />
			<arg value="js"/>
			<arg value="iconHeightPx: 30" />
			<arg value="iconHeightPx: ${mapSetting.iconHeightPx}" />
			<arg value="commonconstants.js"/>
		</exec>		
		
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${insight.dist.dir.js}" />
			<arg value="js"/>
			<arg value="iconWidthPx: 30" />
			<arg value="iconWidthPx: ${mapSetting.iconWidthPx}" />
			<arg value="commonconstants.js"/>
		</exec>		


		<!-- showMapLayers -->
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${insight.dist.dir.js}" />
			<arg value="js"/>
			<arg value="mspeed: false" />
			<arg value="mspeed: ${showMapLayers.speed}" />
			<arg value="commonconstants.js"/>
		</exec>
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${insight.dist.dir.js}" />
			<arg value="js"/>
			<arg value="mcctv: false" />
			<arg value="mcctv: ${showMapLayers.cctv}" />
			<arg value="commonconstants.js"/>
		</exec>
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${insight.dist.dir.js}" />
			<arg value="js"/>
			<arg value="mtollZone: false" />
			<arg value="mtollZone: ${showMapLayers.tollZone}" />
			<arg value="commonconstants.js"/>
		</exec>
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${insight.dist.dir.js}" />
			<arg value="js"/>
			<arg value="mtransactions: false" />
			<arg value="mtransactions: ${showMapLayers.transactions}" />
			<arg value="commonconstants.js"/>
		</exec>
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${insight.dist.dir.js}" />
			<arg value="js"/>
			<arg value="mvtms: false" />
			<arg value="mvtms: ${showMapLayers.vtms}" />
			<arg value="commonconstants.js"/>
		</exec>
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${insight.dist.dir.js}" />
			<arg value="js"/>
			<arg value="mlaneMode: false" />
			<arg value="mlaneMode: ${showMapLayers.laneMode}" />
			<arg value="commonconstants.js"/>
		</exec>
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${insight.dist.dir.js}" />
			<arg value="js"/>
			<arg value="mpricingMode: false" />
			<arg value="mpricingMode: ${showMapLayers.pricingMode}" />
			<arg value="commonconstants.js"/>
		</exec>
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${insight.dist.dir.js}" />
			<arg value="js"/>
			<arg value="mspeed: true" />
			<arg value="mspeed: ${showMapLayers.speed}" />
			<arg value="commonconstants.js"/>
		</exec>
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${insight.dist.dir.js}" />
			<arg value="js"/>
			<arg value="mcctv: true" />
			<arg value="mcctv: ${showMapLayers.cctv}" />
			<arg value="commonconstants.js"/>
		</exec>
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${insight.dist.dir.js}" />
			<arg value="js"/>
			<arg value="mtollZone: true" />
			<arg value="mtollZone: ${showMapLayers.tollZone}" />
			<arg value="commonconstants.js"/>
		</exec>
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${insight.dist.dir.js}" />
			<arg value="js"/>
			<arg value="mtransactions: true" />
			<arg value="mtransactions: ${showMapLayers.transactions}" />
			<arg value="commonconstants.js"/>
		</exec>
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${insight.dist.dir.js}" />
			<arg value="js"/>
			<arg value="mvtms: true" />
			<arg value="mvtms: ${showMapLayers.vtms}" />
			<arg value="commonconstants.js"/>
		</exec>
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${insight.dist.dir.js}" />
			<arg value="js"/>
			<arg value="mlaneMode: true" />
			<arg value="mlaneMode: ${showMapLayers.laneMode}" />
			<arg value="commonconstants.js"/>
		</exec>
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${insight.dist.dir.js}" />
			<arg value="js"/>
			<arg value="mpricingMode: true" />
			<arg value="mpricingMode: ${showMapLayers.pricingMode}" />
			<arg value="commonconstants.js"/>
		</exec>
		
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${insight.dist.dir.js}" />
			<arg value="js"/>
			<arg value="enableSingleSignOn: false" />
			<arg value="enableSingleSignOn: ${enableSingleSignOn}" />
			<arg value="commonconstants.js"/>
		</exec>	
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${insight.dist.dir.js}" />
			<arg value="js"/>
			<arg value="enableSingleSignOn: true" />
			<arg value="enableSingleSignOn: ${enableSingleSignOn}" />
			<arg value="commonconstants.js"/>
		</exec>	
		
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${insight.dist.dir.js}" />
			<arg value="js"/>
			<arg value="vtmsDisplayValue" />
			<arg value="${vtmsDisplayValue}" />
			<arg value="commonconstants.js"/>
		</exec>	
		
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${insight.dist.dir.js}" />
			<arg value="js"/>
			<arg value="showContextMenu: false" />
			<arg value="showContextMenu: ${showContextMenu}" />
			<arg value="commonconstants.js"/>
		</exec>	
		
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${insight.dist.dir.js}" />
			<arg value="js"/>
			<arg value="liveVideoShowIFrame: false" />
			<arg value="liveVideoShowIFrame: ${liveVideoShowIFrame}" />
		</exec>
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${insight.dist.dir.js}" />
			<arg value="js"/>
			<arg value="liveTrafficForDetectionPoints: false" />
			<arg value="liveTrafficForDetectionPoints: ${liveTrafficForDetectionPoints}" />
		</exec>
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${insight.dist.dir.js}" />
			<arg value="js"/>
			<arg value="10/06/2017" />
			<arg value="${dated.deployment.dir}" />
			<arg value="ALL"/>
		</exec>
		<if test="${MapFeature=='false'}">
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${web.insight.dist.dir}\app\components\login\controllers" />
			<arg value="js"/>
			<arg value="home.map" />
			<arg value="home.workorder.main.open" />
			<arg value="indexController.js"/>
			<arg value="All"/>
		</exec>	
		<exec program="ReplaceUtil.exe" failonerror="true">
				<arg value="${insight.dist.dir.js}" />
				<arg value="js"/>
				<arg value="mapFeature: true" />
			<arg value="mapFeature: ${MapFeature}" />
			</exec>
		<exec program="FNR-NoMap.bat" failonerror="true" >
		</exec>		
		</if>
		
		<!-- change Google API URL to license versionj for Production systems -->
		<if test="${deployment.type=='PROD'}">
			<exec program="FNR-GoogleMapApi.bat" failonerror="true" >
			<arg value="${base.dir}" />
			</exec>
		</if>
		
		<!-- Report URL -->
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${insight.dist.dir.js}" />
			<arg value="js"/>
			<arg value="http://localhost:8080/" />
			<arg value="http://${moms.report.server}:8080/" />
			<arg value="ALL"/>
		</exec>
		 <!-- Release Version -->
		 <exec program="ReplaceUtil.exe" failonerror="true">
				<arg value="${insight.dist.dir.js}" />
				<arg value="js"/>
				<arg value="1.0.0" />
				<arg value="${release.version}" />
				<arg value="commonconstants.js"/>
				<arg value="ALL"/>
		</exec>	
			
		<!-- mapSetting.openMapTileUrl -->
		<if test="${SSLFeature=='false'}">
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${insight.dist.dir.js}" />
			<arg value="js"/>
			<arg value="http://localhost:890/" />
				<arg value="http://${insight.api.server}:890/" />
				<arg value="ALL"/>
		</exec>
		</if>
		<if test="${SSLFeature=='true'}">
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${insight.dist.dir.js}" />
			<arg value="js"/>
			<arg value="http://localhost:890/" />
			<arg value="https://${insight.api.server}:8444/" />
			<arg value="ALL"/>
		</exec>
		</if>
		
		<if test="${OfflineMapFeature=='true'}">
			<if test="${SSLFeature=='false'}">
			<exec program="ReplaceUtil.exe" failonerror="true">
				<arg value="${insight.dist.dir}\webLayer\InsightOfflineMaps" />
				<arg value="js"/>
				<arg value="http://localhost:890/" />
					<arg value="http://${insight.api.server}:890/" />
					<arg value="ALL"/>
			</exec>
			</if>
			<if test="${SSLFeature=='true'}">
			<exec program="ReplaceUtil.exe" failonerror="true">
				<arg value="${insight.dist.dir}\webLayer\InsightOfflineMaps" />
				<arg value="js"/>
				<arg value="http://localhost:890/" />
				<arg value="https://${insight.api.server}:8444/" />
				<arg value="ALL"/>
			</exec>
			</if>
			<exec program="ReplaceUtil.exe" failonerror="true">
				<arg value="${insight.dist.dir}\webLayer\InsightOfflineMaps" />
				<arg value="js"/>
				<arg value="2017-07-03_california_san-francisco-bay" />
				<arg value="${offline.maps.tile.url}" />
				<arg value="ALL"/>
			</exec>
		</if>
				
		<!-- Filter Insight Transaction Columns in ConfigConstants.js -->
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${insight.dist.dir.js}" />
			<arg value="js"/>
			<arg value="useDeviceStatusForSiteMarker: false" />
			<arg value="useDeviceStatusForSiteMarker: ${useDeviceStatusForSiteMarker}" />
		</exec>
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${insight.dist.dir.js}" />
			<arg value="js"/>
			<arg value="TransDateTime: true" />
			<arg value="TransDateTime: ${Trans.TransDateTime}" />
		</exec>
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${insight.dist.dir.js}" />
			<arg value="js"/>
			<arg value="LocationName: false" />
			<arg value="LocationName: ${Trans.LocationName}" />
		</exec>
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${insight.dist.dir.js}" />
			<arg value="js"/>
			<arg value="Speed: true" />
			<arg value="Speed: ${Trans.Speed}" />
		</exec>
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${insight.dist.dir.js}" />
			<arg value="js"/>
			<arg value="NumIndicatedAxle: false" />
			<arg value="NumIndicatedAxle: ${Trans.NumIndicatedAxle}" />
		</exec>
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${insight.dist.dir.js}" />
			<arg value="js"/>
			<arg value="NumActFwdAxle: false" />
			<arg value="NumActFwdAxle: ${Trans.NumActFwdAxle}" />
		</exec>
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${insight.dist.dir.js}" />
			<arg value="js"/>
			<arg value="RevenueExpected: false" />
			<arg value="RevenueExpected: ${Trans.RevenueExpected}" />
		</exec>
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${insight.dist.dir.js}" />
			<arg value="js"/>
			<arg value="RevenueCollected: false" />
			<arg value="RevenueCollected: ${Trans.RevenueCollected}" />
		</exec>
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${insight.dist.dir.js}" />
			<arg value="js"/>
			<arg value="PaymentTypeLabel: true" />
			<arg value="PaymentTypeLabel: ${Trans.PaymentTypeLabel}" />
		</exec>
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${insight.dist.dir.js}" />
			<arg value="js"/>
			<arg value="TagNumber: true" />
			<arg value="TagNumber: ${Trans.TagNumber}" />
		</exec>
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${insight.dist.dir.js}" />
			<arg value="js"/>
			<arg value="TagStatusDescription: true" />
			<arg value="TagStatusDescription: ${Trans.TagStatusDescription}" />
		</exec>
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${insight.dist.dir.js}" />
			<arg value="js"/>
			<arg value="UODescription: false" />
			<arg value="UODescription: ${Trans.UODescription}" />
		</exec>
		<!-- filter liveVideoType for CPS -->
		<if test="${CPSEventLoader=='true'}">
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${insight.dist.dir.js}" />
			<arg value="js"/>
			<arg value="liveVideoType: 'tolling'" />
			<arg value="liveVideoType: 'cps'" />
		</exec>
		</if>
		<!-- Filter Insight app.css file -->
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${insight.dist.dir.css}" />
			<arg value="css"/>
			<arg value="background-color: #0c7cd5; /* $tcore-site-title-default" />
			<arg value="background-color: ${tcore-site-title-default-color} /* $tcore-site-title-default" />
			<arg value="ALL"/>
		</exec>
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${insight.dist.dir.css}" />
			<arg value="css"/>
			<arg value="background-color: #FF0000; /* $tcore-site-title-high" />
			<arg value="background-color: ${tcore-site-title-high-color} /* $tcore-site-title-high" />
			<arg value="ALL"/>
		</exec>
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${insight.dist.dir.css}" />
			<arg value="css"/>
			<arg value="background-color: #FF931F; /* $tcore-site-title-medium" />
			<arg value="background-color: ${tcore-site-title-medium-color} /* $tcore-site-title-medium" />
			<arg value="ALL"/>
		</exec>
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${insight.dist.dir.css}" />
			<arg value="css"/>
			<arg value="background-color: #FFF200; /* $tcore-site-title-low" />
			<arg value="background-color: ${tcore-site-title-low-color} /* $tcore-site-title-low" />
			<arg value="ALL"/>
		</exec>
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${insight.dist.dir.css}" />
			<arg value="css"/>
			<arg value="background-color: gray; /* $tcore-site-title-inactive" />
			<arg value="background-color: ${tcore-site-title-inactive-color} /* $tcore-site-title-inactive" />
			<arg value="ALL"/>
		</exec>
		
		<!-- Filter Insight scss file -->
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${insight.dist.dir.scss}" />
			<arg value="scss"/>
			<arg value="tcore-site-title-default: #0c7cd5" />
			<arg value="tcore-site-title-default: ${tcore-site-title-default-color}" />
			<arg value="ALL"/>
		</exec>
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${insight.dist.dir.scss}" />
			<arg value="scss"/>
			<arg value="tcore-site-title-high: #FF0000" />
			<arg value="tcore-site-title-high: ${tcore-site-title-high-color}" />
			<arg value="ALL"/>
		</exec>
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${insight.dist.dir.scss}" />
			<arg value="scss"/>
			<arg value="tcore-site-title-medium: #FF931F" />
			<arg value="tcore-site-title-medium: ${tcore-site-title-medium-color}" />
			<arg value="ALL"/>
		</exec>
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${insight.dist.dir.scss}" />
			<arg value="scss"/>
			<arg value="tcore-site-title-low: #FFF200" />
			<arg value="tcore-site-title-low: ${tcore-site-title-low-color}" />
			<arg value="ALL"/>
		</exec>
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${insight.dist.dir.scss}" />
			<arg value="scss"/>
			<arg value="tcore-site-title-inactive: gray" />
			<arg value="tcore-site-title-inactive: ${tcore-site-title-inactive-color}" />
			<arg value="ALL"/>
		</exec>
		
		<!-- Filter MOMS DB paths -->
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${build.dir}\Database\DBScripts" />
			<arg value="sql"/>
			<arg value="D:\Program Files\Microsoft SQL Server\MSSQL12.MSSQLSERVER\MSSQL\Data"/>
			<arg value="${sqlserver.data.path}"/>
			<arg value="ALL"/>
		</exec>
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${build.dir}\Database\DBScripts" />
			<arg value="sql"/>
			<arg value="D:\Program Files\Microsoft SQL Server\MSSQL12.MSSQLSERVER\MSSQL\LOG"/>
			<arg value="${sqlserver.log.path}" />
			<arg value="ALL"/>
		</exec>
		
		<!-- Change Security group from FMMS to SD-IFXDEV -->
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${build.dir}\Database\Data" />
			<arg value="sql"/>
			<arg value="FMMS" />
			<arg value="${SYSADMIN.securitygroup}" />
			<arg value="dbo.stbRole.data.sql"/>
			<arg value="ALL"/>
		</exec>
		<!-- Change AD values in Appl Param -->
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${build.dir}\Database\Data" />
			<arg value="sql"/>
			<arg value="'tcore.com'" />
			<arg value="'${DefaultDomain}'" />
			<arg value="dbo.tbApplParam.data.sql"/>
			<arg value="All"/>
		</exec>
		<!--<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${build.dir}\Database\Data" />
			<arg value="sql"/>
			<arg value="'OU=San Diego 335,OU=San Diego,DC=tcore,DC=com'" />
			<arg value="'${UserDefaultOU}'" />
			<arg value="dbo.tbApplParam.data.sql"/>
			<arg value="All"/>
		</exec>-->
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${build.dir}\Database\Data" />
			<arg value="sql"/>
			<arg value="'OU=Security Groups,OU=San Diego 335,OU=San Diego,DC=tcore,DC=com'" />
			<arg value="'${GroupDefaultOU}'" />
			<arg value="dbo.tbApplParam.data.sql"/>
			<arg value="All"/>
		</exec>	
		<!--<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${build.dir}\Database\Data" />
			<arg value="sql"/>
			<arg value="'OU=San Diego,DC=tcore,DC=com'" />
			<arg value="'${PlazaDefaultOU}'" />
			<arg value="dbo.tbApplParam.data.sql"/>
			<arg value="All"/>
		</exec>	-->	
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${build.dir}\Database\Data" />
			<arg value="sql"/>
			<arg value="'DC=tcore,DC=com'" />
			<arg value="'${DefaultRootOU}'" />
			<arg value="dbo.tbApplParam.data.sql"/>
			<arg value="All"/>		
		</exec>
		
		<!-- If TransPortal feature is built then filter SSO for TransPortal-->		
			<exec program="ReplaceUtil.exe" failonerror="true">
				<arg value="${insight.dist.dir.js}" />
				<arg value="js"/>
				<arg value="enableNonTransportalSSO: false" />
			<arg value="enableNonTransportalSSO: ${NonTransPortalSSOFeature}" />
			</exec>
			<exec program="ReplaceUtil.exe" failonerror="true">
				<arg value="${insight.dist.dir.js}" />
				<arg value="js"/>
				<arg value="enableNonTransportalSSO: true" />
				<arg value="enableNonTransportalSSO: ${NonTransPortalSSOFeature}" />
			</exec>
		<if test="${TransPortalFeature=='true'}">
			<exec program="ReplaceUtil.exe" failonerror="true">
				<arg value="${build.dir}\Database\StoredProcedures" />
				<arg value="sql"/>
				<arg value="--AND siFunctionalAreaResourceID != 5006" />
				<arg value="AND siFunctionalAreaResourceID != 5006" />
				<arg value="dbo.uspUtlAssignResourecsToRole.sql"/>
			</exec>
			<exec program="ReplaceUtil.exe" failonerror="true">
				<arg value="${build.dir}\Database\StoredProcedures" />
				<arg value="sql"/>
				<arg value=",5006" />
				<arg value="--,5006" />
				<arg value="dbo.uspUtlAssignResourecsToRole.sql"/>
			</exec>
		</if>
			
	</target>
	
	<!-- Filter the Insight MOBILE environment specific strings -->
	<target name="filter-for-InsightMobile" description="Filter for INsight Mobile external webapi">
	<if test="${SSLFeature=='false'}"> 
		<xmlpoke  file="${insight.mobile.webapi.config.file}" xpath="/configuration/appSettings/add[@key = 'InsightWebAPI']/@value" value="http://${insight.api.server}:889" />	
		</if>
		<xmlpoke  file="${insight.mobile.webapi.config.file}" xpath="/configuration/log4net/appender/file[@value = 'D:\MOMSLogs\InsightExternalWebApi2.log']/@value" value="${insight.external.webapi.log.file}" />		
			<if test="${SSLFeature=='true'}"> 
			<xmlpoke  file="${insight.mobile.webapi.config.file}" xpath="/configuration/appSettings/add[@key = 'InsightWebAPI']/@value" value="https://${insight.api.server}:8443" />	
			</if>		
	</target>
	
		<!-- Filter the Email-related APplication Parameters environment specific strings -->
	<target name="filter-ApplParams" description="Filter environment specific variables for the application parameters">	
	     <exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${build.dir}\Database\Data" />
			<arg value="sql"/>
			<arg value="<EMAIL>" />
			<arg value="${emailUserName}" />
			<arg value="dbo.tbApplParam.data.sql"/>
			<arg value="All"/>
		</exec>
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${build.dir}\Database\Data" />
			<arg value="sql"/>
			<arg value="<EMAIL>" />
			<arg value="${emailUserName}" />
			<arg value="dbo.tbApplParam.data.sql"/>
			<arg value="All"/>
		</exec>
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${build.dir}\Database\Data" />
			<arg value="sql"/>
			<arg value="S@ndieg0" />
			<arg value="${emailPassword}" />
			<arg value="dbo.tbApplParam.data.sql"/>
			<arg value="All"/>
		</exec>
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${build.dir}\Database\Data" />
			<arg value="sql"/>
			<arg value="mail.tcsdhosting.com" />
			<arg value="${SMTPServer}" />
			<arg value="dbo.tbApplParam.data.sql"/>			
		</exec>
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${build.dir}\Database\Data" />
			<arg value="sql"/>
			<arg value="mail.tcsdhosting.com" />
			<arg value="${IMAPMailServer}" />
			<arg value="dbo.tbApplParam.data.sql"/>			
		</exec>
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${build.dir}\Database\Data" />
			<arg value="sql"/>
			<arg value="tcore'" />
			<arg value="${emailDomainName}'" />
			<arg value="dbo.tbApplParam.data.sql"/>
			<arg value="All"/>
		</exec>
	</target>

	<target name="filter-database-directory" description="Replace the SQL server datadase path if necessary (for example if installed on a different drive)">
	<if test="${property::exists('database.directory')}">	
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${build.dir}\Database\DBScripts" />
			<arg value="sql"/>
			<arg value="D:\Program Files\Microsoft SQL Server\MSSQL13.MSSQLSERVER" />
			<arg value="${database.directory}" />
			<arg value="CreateDatabaseMOMS.sql"/>
			<arg value="All"/>
		</exec>
	</if>
	</target>
	
	<target name="filter-IntegratedSecurityIFXLoader" description="Filter for Integrated Security for IFX Database Connections" >
		<if test="${MOMSEventLoader=='true'}">
		<xmlpoke  file="${MOMSEventLoader.task.moms.config.file}" xpath="/configuration/connectionStrings/add[@name='EventLoaderDBConnectionString']/@connectionString" value="${eventloader.secure.connection.string}" />	
		</if>
		<if test="${TrafficLoaderTask=='true'}">
		<xmlpoke  file="${moms.traffic.loader.service.appconfig.file}" xpath="/configuration/connectionStrings/add[@name='DMSDBConnectionString']/@connectionString" value="${eventloader.secure.connection.string}" />			
        </if>
	</target>
	
	<target name="filter-logo" description="Switch log for demo">	
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${web.insight.dist.dir}\dist\css" />
			<arg value="css"/>
			<arg value="login-background.png" />
			<arg value="Charlotte_login_Hero.jpg" />
			<arg value="ALL"/>
		</exec>
	</target>
	
	
	
	<target name="filter-for-SSPI" description="Switch strings for Integrated Security">	
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${base.dir}\Distribution" />
			<arg value="config"/>
			<arg value="${moms.connection.string}" />
			<arg value="${moms.secure.connection.string}" />
			<arg value="ALL"/>
		</exec>
		<if test="${MobileFeature=='true'}">
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${base.dir}\Distribution" />
			<arg value="config"/>
			<arg value="${momsmobile.connection.string}" />
			<arg value="${momsmobile.secure.connection.string}" />
			<arg value="ALL"/>
		</exec>
		</if>
		<if test="${TransPortalEventLoader=='true'}">
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${base.dir}\Distribution" />
			<arg value="config"/>
			<arg value="${transportal.eventloader.connection.string}" />
			<arg value="${transportal.eventloader.secure.connection.string}" />
			<arg value="ALL"/>
		</exec>
		</if>
	</target>
	
	
	<target name="filter-for-OfflineMaps" description="Switch strings for OfflineMaps">	
		<if test="${SSLFeature=='false'}">
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${offlinemaps.insight.dist.dir}" />
			<arg value="json"/>
			<arg value="http://localhost:890/" />
				<arg value="http://${insight.api.server}:890/" />
				<arg value="ALL"/>
		</exec>
		</if>
		<if test="${SSLFeature=='true'}">
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${offlinemaps.insight.dist.dir}" />
			<arg value="json"/>
			<arg value="http://localhost:890/" />
			<arg value="https://${insight.api.server}:8444/" />
			<arg value="ALL"/>
		</exec>
		</if>
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${offlinemaps.insight.dist.dir}" />
			<arg value="json"/>
			<arg value="2017-07-03_california_san-francisco-bay" />
			<arg value="${offline.maps.tile.url}" />
			<arg value="ALL"/>
		</exec>
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${offlinemaps.insight.dist.dir}" />
			<arg value="php"/>
			<arg value="C:/MapData" />
			<arg value="${offline.maps.tilelocation.php}" />
			<arg value="tileserver.php"/>			
		</exec>		
	</target>
	
	 <target name="filter-key" description="Filter key">
		<if test="${new.symmetric.key=='true'}">	
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${base.dir}\Distribution" />
			<arg value="json"/>
			<arg value="${old.key}" />
			<arg value="${new.key}" />
			<arg value="ALL"/>
		</exec>
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${base.dir}\Distribution" />
			<arg value="config"/>
			<arg value="${old.key}" />
			<arg value="${new.key}" />
			<arg value="ALL"/>
		</exec>
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${base.dir}\Distribution" />
			<arg value="js"/>
			<arg value="${old.key}" />
			<arg value="${new.key}" />
			<arg value="ALL"/>
		</exec>
		</if>
	</target>	
	
</project>