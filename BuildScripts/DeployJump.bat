@echo off
rem set variables
Set CURRENTDIR=%CD%

echo ------- Your current directory is:  %CURRENTDIR% ---------

set NOW=%date:~10,4%-%date:~4,2%-%date:~7,2%
echo Deployment beginning %NOW%
echo -----------------------------------
echo Connect to Server
echo -----------------------------------

SET APP01SERVER=%2
SET USER=%3
SET PASSWORD=%4
SET DEPLOYMENTDRIVE=%5
SET DEPLOYMENTDIR=%6
SET CLUSTER=%7
SET APP02SERVER=%8
SET WEB01SERVER=%9
SHIFT
SHIFT
SHIFT
SHIFT
SHIFT
SHIFT
SHIFT
SHIFT
SHIFT
SET WEB02SERVER=%1
SET JUMPDRIVE=%2
SET EXT01SERVER=%3
SET APP03SERVER=%4
SET ENVTYPE=%5
SET BASEDIR=%JUMPDRIVE%:\Staging-CPS\%DEPLOYMENTDIR%
net use m: /delete
net use n: /delete
net use l: /delete
net use p: /delete
net use q: /delete
net use r: /delete
net use s: /delete


ECHO %APP01SERVER%
ECHO %DEPLOYMENTDRIVE%
ECHO %DEPLOYMENTDIR%
ECHO %USER%
ECHO Cluster? %CLUSTER%
ECHO App02 server is %APP02SERVER%

echo ------- map drive ----------
net use m: \\%APP01SERVER%\%DEPLOYMENTDRIVE%$ %PASSWORD% /user:%USER%
net use n: \\%WEB01SERVER%\%DEPLOYMENTDRIVE%$ %PASSWORD% /user:%USER%
net use r: \\%EXT01SERVER%\%DEPLOYMENTDRIVE%$ %PASSWORD% /user:%USER%


if %ERRORLEVEL% NEQ 0 GOTO :error 
echo ------- destination cleanup -----------
if exist m:\Deployment-CPS\%DEPLOYMENTDIR%\ (rmdir /s /q m:\Deployment-CPS\%DEPLOYMENTDIR%) 
if %ERRORLEVEL% NEQ 0 GOTO :error 
if exist n:\Deployment-CPS\%DEPLOYMENTDIR%\ (rmdir /s /q n:\Deployment-CPS\%DEPLOYMENTDIR%)
if %ERRORLEVEL% NEQ 0 GOTO :error 
if exist r:\Deployment-CPS\%DEPLOYMENTDIR%\ (rmdir /s /q r:\Deployment-CPS\%DEPLOYMENTDIR%) 
if %ERRORLEVEL% NEQ 0 GOTO :error 



if not exist m:\Deployment-CPS\%DEPLOYMENTDIR%\ (mkdir m:\Deployment-CPS\%DEPLOYMENTDIR%) 
if %ERRORLEVEL% NEQ 0 GOTO :error 
if not exist n:\Deployment-CPS\%DEPLOYMENTDIR%\ (mkdir n:\Deployment-CPS\%DEPLOYMENTDIR%)
if %ERRORLEVEL% NEQ 0 GOTO :error 
if not exist r:\Deployment-CPS\%DEPLOYMENTDIR%\ (mkdir r:\Deployment-CPS\%DEPLOYMENTDIR%) 
if %ERRORLEVEL% NEQ 0 GOTO :error 


:deploy


echo ------------------------------------
echo Copy CPS....
echo ------------------------------------
set STEP="Deploy APP01.7z"
for /F %%i in ('dir /b "%BASEDIR%\APP01.7z"') do (
xcopy /R /Y %BASEDIR%\APP01.7z m:\Deployment-CPS\%DEPLOYMENTDIR%\  
)

set STEP="Deploy WEB01.zip"
for /F %%i in ('dir /b "%BASEDIR%\WEB01.zip"') do (
xcopy /R /Y %BASEDIR%\WEB01.zip n:\Deployment-CPS\%DEPLOYMENTDIR%\  
)

set STEP="Deploy EXT01.zip"
for /F %%i in ('dir /b "%BASEDIR%\EXT01.zip"') do (
xcopy /R /Y %BASEDIR%\EXT01.zip r:\Deployment-CPS\%DEPLOYMENTDIR%\  
)



if %ERRORLEVEL% NEQ 0 GOTO :error 
echo %STEP% Deployment complete 
net use m: /delete
net use n: /delete
net use q: /delete
net use r: /delete


CD %CURRENTDIR%

IF NOT "%ENVTYPE%"=="PROD" (GOTO CHECKCLUSTER)
set STEP="Deploy APP03.7z"
net use s: /delete
net use s: \\%APP03SERVER%\%DEPLOYMENTDRIVE%$ %PASSWORD% /user:%USER%
if exist s:\Deployment-CPS\%DEPLOYMENTDIR%\ (rmdir /s /q s:\Deployment-CPS\%DEPLOYMENTDIR%) 
if %ERRORLEVEL% NEQ 0 GOTO :error 
if not exist s:\Deployment-CPS\%DEPLOYMENTDIR%\ (mkdir s:\Deployment-CPS\%DEPLOYMENTDIR%) 
if %ERRORLEVEL% NEQ 0 GOTO :error 
for /F %%i in ('dir /b "%BASEDIR%\APP03.7z"') do (
xcopy /R /Y %BASEDIR%\APP03.7z s:\Deployment-CPS\%DEPLOYMENTDIR%\  
)
if %ERRORLEVEL% NEQ 0 GOTO :error 
net use s: /delete

:CHECKCLUSTER 
echo ---------------------------------
echo if Cluster is True deploy to APP02
echo ---------------------------------
if %CLUSTER%==true GOTO deploy02

echo --------------------------------------------------------------------
echo Deployment Completed...
echo --------------------------------------------------------------------

echo DEPLOYMENT COMPLETE 
GOTO END


:deploy02
set STEP="Deploy Cluster2"
net use l: /delete
net use p: /delete
net use r: /delete
echo ------- map drive ----------
net use l: \\%APP02SERVER%\%DEPLOYMENTDRIVE%$ %PASSWORD% /user:%USER%
net use p: \\%WEB02SERVER%\%DEPLOYMENTDRIVE%$ %PASSWORD% /user:%USER%
if %ERRORLEVEL% NEQ 0 GOTO :error 
echo ------- destination cleanup -----------
echo temp for testing - once we have cluster env set up, unrem

rem if exist o:\Deployment-CPS\%DEPLOYMENTDIR% (rmdir /s /q o:\Deployment-CPS\%DEPLOYMENTDIR%) 
rem if %ERRORLEVEL% NEQ 0 GOTO :error 
rem if exist p:\Deployment-CPS\%DEPLOYMENTDIR% (rmdir /s /q p:\Deployment-CPS\%DEPLOYMENTDIR%) 
rem if %ERRORLEVEL% NEQ 0 GOTO :error 
rem if exist r:\Deployment-CPS\%DEPLOYMENTDIR% (rmdir /s /q r:\Deployment-CPS\%DEPLOYMENTDIR%) 
rem if %ERRORLEVEL% NEQ 0 GOTO :error
rem mkdir o:\Deployment-CPS\%DEPLOYMENTDIR%
rem if not %APP02SERVER%==%WEB02SERVER% (mkdir p:\Deployment-CPS\%DEPLOYMENTDIR%)
rem if not %APP02SERVER%==%IMS02SERVER% (mkdir r:\Deployment-CPS\%DEPLOYMENTDIR%)

if %ERRORLEVEL% NEQ 0 GOTO :error 

echo ------------------------------------
echo Copy CPS to APP02
echo ------------------------------------
set STEP="Deploy APP02.7z"
for /F %%i in ('dir /b "%BASEDIR%\APP02.7z"') do (
xcopy /R /Y %BASEDIR%\APP02.7z l:\Deployment-CPS\%DEPLOYMENTDIR%\
)

echo ------------------------------------
echo Copy CPS to WEB02
echo ------------------------------------
set STEP="Deploy WEB02.zip"
for /F %%i in ('dir /b "%BASEDIR%\WEB02.zip"') do (
xcopy  /R /Y %BASEDIR%\WEB02.zip p:\Deployment-CPS\%DEPLOYMENTDIR%\
)

echo --------------------------------------------------------------------
echo Deployment Cluster 2 Completed...
echo --------------------------------------------------------------------
echo DEPLOYMENT COMPLETE 
net use l: /delete
net use p: /delete
net use r: /delete
GOTO END

:newdir
echo ------- Create dated deployment directory -----------------
mkdir m:\Deployment-CPS\%DEPLOYMENTDIR%
if %ERRORLEVEL% NEQ 0 GOTO :error 
goto deploy


:error
echo ------- AN ERROR OCCURED DURING THIS STEP: %STEP% ------
net use m: /delete
net use l: /delete
net use p: /delete
net use n: /delete
net use q: /delete
net use r: /delete
net use s: /delete
exit /b 1

:END