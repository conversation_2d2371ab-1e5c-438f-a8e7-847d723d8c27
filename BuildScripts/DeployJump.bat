@echo off
rem set variables
Set CURRENTDIR=%CD%

echo ------- Your current directory is:  %CURRENTDIR% ---------

set NOW=%date:~10,4%-%date:~4,2%-%date:~7,2%
echo Deployment beginning %NOW%
echo -----------------------------------
echo Connect to Server
echo -----------------------------------
SET BASEDIR=%1
SET FPSERVER=%2
SET APPSERVER=%3
SET USER=%4
SET PASSWORD=%5
SET DEPLOYMENTDRIVE=%6
SET DEPLOYMENTDIR=%7
SET WEBSERVER=%8
SET DBSERVER=%9
SHIFT
SHIFT
SHIFT
SHIFT
SHIFT
SHIFT
SHIFT
SHIFT
SHIFT
SET DEPLOYMENTSERVER=%1
SET FP02SERVER=%2
SET APP02SERVER=%3
SET WEB02SERVER=%4
SET CLUSTER=%5
SET JUMPDRIVE=%6
SET TRANSSERVER=%7
SET TRANS02SERVER=%8
SET IFXSERVER=%9
SHIFT
SHIFT
SHIFT
SHIFT
SHIFT
SHIFT
SHIFT
SHIFT
SHIFT
SET IFX02SERVER=%1
SET HUMANREADSERVER=%2
SET HRFEATURE=%3
SET HOCP01SERVER=%4
SET HOCP02SERVER=%5
SET IRR01SERVER=%6
SET IRR02SERVER=%7
SET HOCPFEATURE=%8
SET IRRFEATURE=%9
SHIFT
SHIFT
SHIFT
SHIFT
SHIFT
SHIFT
SHIFT
SHIFT
SHIFT
SET PREHOCPFEATURE=%1
SET PREHOCP01SERVER=%2
SET PREHOCP02SERVER=%3

SET BASEDIR=%JUMPDRIVE%:\Staging-ImageReview\%DEPLOYMENTDIR%


:deletemappeddrives
Set STEP="delete mapped drives"
net use j: /delete
net use o: /delete
net use k: /delete
net use p: /delete
net use n: /delete

:mapdrives
Set STEP="Map drives"
echo ------- map drive ----------
echo map fpp: %FPSERVER%
net use j: \\%FPSERVER%\%DEPLOYMENTDRIVE%$ %PASSWORD% /user:%USER%
if %ERRORLEVEL% NEQ 0 GOTO :error 
echo map app:  %APPSERVER%
net use o: \\%APPSERVER%\%DEPLOYMENTDRIVE%$ %PASSWORD% /user:%USER%
if %ERRORLEVEL% NEQ 0 GOTO :error 
echo map web:  %WEBSERVER%
net use k: \\%WEBSERVER%\%DEPLOYMENTDRIVE%$ %PASSWORD% /user:%USER%
if %ERRORLEVEL% NEQ 0 GOTO :error 
echo map db:  %DBSERVER%
net use p: \\%DBSERVER%\%DEPLOYMENTDRIVE%$ %PASSWORD% /user:%USER%
if %ERRORLEVEL% NEQ 0 GOTO :error 
net use n: \\%TRANSSERVER%\%DEPLOYMENTDRIVE%$ %PASSWORD% /user:%USER%
if %ERRORLEVEL% NEQ 0 GOTO :error 


:destinationcleanup
echo ------- destination cleanup -----------
Set STEP="destination cleanup"
if exist j:\Deployment-ImageReview\%DEPLOYMENTDIR% (rmdir /s /q j:\Deployment-ImageReview\%DEPLOYMENTDIR%) 
if exist o:\Deployment-ImageReview\%DEPLOYMENTDIR% (rmdir /s /q o:\Deployment-ImageReview\%DEPLOYMENTDIR%) 
if exist k:\Deployment-ImageReview\%DEPLOYMENTDIR% (rmdir /s /q k:\Deployment-ImageReview\%DEPLOYMENTDIR%) 
if exist p:\Deployment-ImageReview\%DEPLOYMENTDIR% (rmdir /s /q p:\Deployment-ImageReview\%DEPLOYMENTDIR%) 
if exist n:\Deployment-ImageReview\%DEPLOYMENTDIR% (rmdir /s /q n:\Deployment-ImageReview\%DEPLOYMENTDIR%) 


:newdir
echo ------- Create dated deployment directory -----------------
Set STEP="Create Deployment Directories on target servers"
if not exist j:\Deployment-ImageReview\%DEPLOYMENTDIR% (mkdir j:\Deployment-ImageReview\%DEPLOYMENTDIR%)
if not exist o:\Deployment-ImageReview\%DEPLOYMENTDIR% (mkdir o:\Deployment-ImageReview\%DEPLOYMENTDIR%)
if not exist k:\Deployment-ImageReview\%DEPLOYMENTDIR% (mkdir k:\Deployment-ImageReview\%DEPLOYMENTDIR%)
if not exist p:\Deployment-ImageReview\%DEPLOYMENTDIR% (mkdir p:\Deployment-ImageReview\%DEPLOYMENTDIR%)
if not exist n:\Deployment-ImageReview\%DEPLOYMENTDIR% (mkdir n:\Deployment-ImageReview\%DEPLOYMENTDIR%)
if %ERRORLEVEL% NEQ 0 GOTO :error 

:deploybuildscripts
set STEP="Deploy Build Zip"
echo -----------------------------------
echo Deploy Build Scripts zip file to ImageReview Server (MIR task server)...
echo -----------------------------------
if exist o:\Deployment-ImageReview\BuildScripts.Zip (del /s /q o:\Deployment-ImageReview\BuildScripts.zip)
if %ERRORLEVEL% NEQ 0 GOTO :error 
if exist o:\Deployment-ImageReview\BuildScripts (del /s /q o:\Deployment-ImageReview\BuildScripts\*)
if %ERRORLEVEL% NEQ 0 GOTO :error 
rem xcopy /R "..\BuildScripts.zip" "o:\Deployment-ImageReview\*"
if %ERRORLEVEL% NEQ 0 GOTO :error 
rem unzip -q o:\Deployment-ImageReview\BuildScripts.zip -d  o:\Deployment-ImageReview\
if %ERRORLEVEL% NEQ 0 GOTO :error 
echo %STEP% Deployment complete 


if exist %BASEDIR%\HR01.zip ( robocopy /R:3 %BASEDIR%\  \\%HUMANREADSERVER%\%DEPLOYMENTDRIVE%$\Deployment-ImageReview\%DEPLOYMENTDIR% HR01.zip 
)
if %ERRORLEVEL% GEQ 8  GOTO :error 
if "%HRFEATURE%" == "true" ( xcopy /R /Y %BASEDIR%\AES.zip  \\%HUMANREADSERVER%\%DEPLOYMENTDRIVE%$\Deployment-ImageReview\%DEPLOYMENTDIR%)
if %ERRORLEVEL% NEQ 0 GOTO :error 
if "%HRFEATURE%" == "true" GOTO cleanup

:deploypackages
echo ------------------------------------
echo Copy ImageReview....
echo ------------------------------------
set STEP="Deploy ImageReview FPM/FPP"
if exist %BASEDIR%\FP01.zip  (  robocopy /R:3 %BASEDIR%\ j:\Deployment-ImageReview\%DEPLOYMENTDIR% FP01.zip
)
if %ERRORLEVEL% GEQ 8  GOTO :error 

set STEP="Deploy APP"
if exist %BASEDIR%\APP01.zip ( robocopy /R:3 %BASEDIR%\ o:\Deployment-ImageReview\%DEPLOYMENTDIR% APP01.zip 
)
if %ERRORLEVEL% GEQ 8  GOTO :error 

set STEP="Deploy WEB"
if exist %BASEDIR%\WEB01.zip ( robocopy /R:3 %BASEDIR%\ k:\Deployment-ImageReview\%DEPLOYMENTDIR% WEB01.zip 
)
if %ERRORLEVEL% GEQ 8  GOTO :error 

set STEP="Deploy ImageReview Reports"
if exist %BASEDIR%\RPT01.zip ( robocopy /R:3 %BASEDIR%\  p:\Deployment-ImageReview\%DEPLOYMENTDIR% RPT01.zip
)
if %ERRORLEVEL% GEQ 8  GOTO :error 

set STEP="Deploy TRANS"
if exist %BASEDIR%\TRANS01.zip ( robocopy /R:3 %BASEDIR%\ n:\Deployment-ImageReview\%DEPLOYMENTDIR% TRANS01.zip 
)
if %ERRORLEVEL% GEQ 8  GOTO :error 




echo ------------------------------------
echo Copy AES scripts
echo ------------------------------------
set STEP="Deploy AES Scripts"
if not exist j:\Deployment-ImageReview\%DEPLOYMENTDIR%\AES.zip (xcopy /R /Y %BASEDIR%\AES.zip j:\Deployment-ImageReview\%DEPLOYMENTDIR% 
)
if %ERRORLEVEL% NEQ 0 GOTO :error 
if not exist o:\Deployment-ImageReview\%DEPLOYMENTDIR%\AES.zip (xcopy /R /Y %BASEDIR%\AES.zip o:\Deployment-ImageReview\%DEPLOYMENTDIR% 
)
if %ERRORLEVEL% NEQ 0 GOTO :error 
if not exist k:\Deployment-ImageReview\%DEPLOYMENTDIR%\AES.zip (xcopy /R /Y %BASEDIR%\AES.zip k:\Deployment-ImageReview\%DEPLOYMENTDIR%  
)
if %ERRORLEVEL% NEQ 0 GOTO :error 

if not exist n:\Deployment-ImageReview\%DEPLOYMENTDIR%\AES.zip (xcopy /R /Y %BASEDIR%\AES.zip n:\Deployment-ImageReview\%DEPLOYMENTDIR%  
)

if not "%HOCPFEATURE%"=="true" GOTO SKIPPEDHOCP
:deployhocp
set STEP="Delete Mapped Drive to prep for HOCP deploy"
echo ------------------------------------
echo Copy HOCP 
echo ------------------------------------
Set STEP="delete mapped drives"
net use j: /delete
net use o: /delete
net use k: /delete
net use p: /delete
net use n: /delete
set STEP="Map HOCP"
echo -------------------------
echo Map HOCP
echo --------------------------
net use j: \\%HOCP01SERVER%\%DEPLOYMENTDRIVE%$ %PASSWORD% /user:%USER%
if %ERRORLEVEL% NEQ 0 GOTO :error 

set STEP="Clean up HOCP"
echo -------------------------
echo Clean up HOCP
echo --------------------------
if exist j:\Deployment-ImageReview\%DEPLOYMENTDIR%\HOCP01 (rmdir /s /q j:\Deployment-ImageReview\%DEPLOYMENTDIR%\HOCP01) 
if exist j:\Deployment-ImageReview\%DEPLOYMENTDIR%\HOCP01.zip (del /s /q j:\Deployment-ImageReview\%DEPLOYMENTDIR%\HOCP01.zip) 

echo ------------------------------------
echo Copy HOCP 
echo ------------------------------------
set STEP="Deploy ImageReview HOCP"
if exist %BASEDIR%\HOCP01.zip  (  robocopy /R:3 %BASEDIR%\ j:\Deployment-ImageReview\%DEPLOYMENTDIR% HOCP01.zip
)
if %ERRORLEVEL% GEQ 8  GOTO :error 
VERIFY > nul

echo ------------------------------------
echo Copy AES scripts
echo ------------------------------------
set STEP="Deploy HOCP AES Scripts"
if exist %BASEDIR%\HOCP01.zip if not exist j:\Deployment-ImageReview\%DEPLOYMENTDIR%\AES.zip (xcopy /R /Y %BASEDIR%\AES.zip j:\Deployment-ImageReview\%DEPLOYMENTDIR% 
)
if %ERRORLEVEL% NEQ 0 GOTO :error 

net use j: /delete

echo %STEP% Deployment complete 
:SKIPPEDHOCP
if not "%IRRFEATURE%"=="true" GOTO SKIPPEDIRR
:deployirr
ECHO DELETE MAPPED IRR DRIVES
Set STEP="delete mapped irr drives"
net use j: /delete
net use o: /delete
net use k: /delete
net use p: /delete
net use n: /delete
echo map app:  %IRR01SERVER%
echo MAP IRR
net use o: \\%IRR01SERVER%\%DEPLOYMENTDRIVE%$ %PASSWORD% /user:%USER%
if %ERRORLEVEL% NEQ 0 GOTO :error
set STEP="Clean IRR"
echo -------------------------
echo Clean up IRR
echo --------------------------
if exist o:\Deployment-ImageReview\%DEPLOYMENTDIR%\IRR01 (rmdir /s /q o:\Deployment-ImageReview\%DEPLOYMENTDIR%\IRR01) 
if %ERRORLEVEL% NEQ 0 GOTO :error
if exist o:\Deployment-ImageReview\%DEPLOYMENTDIR%\IRR01.zip (del /s /q o:\Deployment-ImageReview\%DEPLOYMENTDIR%\IRR01.zip) 
if %ERRORLEVEL% NEQ 0 GOTO :error
set STEP="Deploy IRR"
echo ------------------------------------
echo Copy IRR
echo ------------------------------------
echo directory is %BASEDIR%
if exist %BASEDIR%\IRR01.zip ( robocopy /R:3 %BASEDIR%\ o:\Deployment-ImageReview\%DEPLOYMENTDIR% IRR01.zip
)
if %ERRORLEVEL% GEQ 8  GOTO :error
VERIFY > nul 
if exist %BASEDIR%\IRR01.zip if not exist o:\Deployment-ImageReview\%DEPLOYMENTDIR%\AES.zip (xcopy /R /Y %BASEDIR%\AES.zip o:\Deployment-ImageReview\%DEPLOYMENTDIR% 
)
if %ERRORLEVEL% NEQ 0 GOTO :error 
:SKIPPEDIRR

if not "%PREHOCPFEATURE%"=="true" GOTO SKIPPEDPREHOCP
:deployprehocp
set STEP="Delete Mapped Drive to prep for PREHOCP deploy"
echo ------------------------------------
echo Copy PREHOCP 
echo ------------------------------------
Set STEP="delete mapped drives"
net use j: /delete
net use o: /delete
net use k: /delete
net use p: /delete
net use n: /delete
set STEP="Map PREHOCP"
echo -------------------------
echo Map PREHOCP
echo --------------------------
net use j: \\%PREHOCP01SERVER%\%DEPLOYMENTDRIVE%$ %PASSWORD% /user:%USER%
if %ERRORLEVEL% NEQ 0 GOTO :error 

set STEP="Clean up PREHOCP"
echo -------------------------
echo Clean up PREHOCP
echo --------------------------
if exist j:\Deployment-ImageReview\%DEPLOYMENTDIR%\PREHOCP01 (rmdir /s /q j:\Deployment-ImageReview\%DEPLOYMENTDIR%\PREHOCP01) 
if exist j:\Deployment-ImageReview\%DEPLOYMENTDIR%\PREHOCP01.zip (del /s /q j:\Deployment-ImageReview\%DEPLOYMENTDIR%\PREHOCP01.zip) 

echo ------------------------------------
echo Copy PREHOCP 
echo ------------------------------------
set STEP="Deploy ImageReview PREHOCP"
if exist %BASEDIR%\PREHOCP01.zip  (  robocopy /R:3 %BASEDIR%\ j:\Deployment-ImageReview\%DEPLOYMENTDIR% PREHOCP01.zip
)
if %ERRORLEVEL% GEQ 8  GOTO :error 
VERIFY > nul

echo ------------------------------------
echo Copy AES scripts
echo ------------------------------------
set STEP="Deploy PREHOCP AES Scripts"
if exist %BASEDIR%\PREHOCP01.zip if not exist j:\Deployment-ImageReview\%DEPLOYMENTDIR%\AES.zip (xcopy /R /Y %BASEDIR%\AES.zip j:\Deployment-ImageReview\%DEPLOYMENTDIR% 
)
if %ERRORLEVEL% NEQ 0 GOTO :error 

net use j: /delete

echo %STEP% Deployment complete 
:SKIPPEDPREHOCP

CD %CURRENTDIR%
echo ---------------------------------
echo if Cluster is True deploy to Node 2
echo ---------------------------------
if "%CLUSTER%"=="true" GOTO deploy02

GOTO finish

:deploy02
:delete02mappeddrives
Set STEP="delete mapped drives"
net use j: /delete
net use o: /delete
net use k: /delete
net use p: /delete
net use n: /delete

:map02drives
Set STEP="Map 02 drives"
echo ------- map drive ----------
net use j: \\%FP02SERVER%\%DEPLOYMENTDRIVE%$ %PASSWORD% /user:%USER%
if %ERRORLEVEL% NEQ 0 GOTO :error 
net use o: \\%APP02SERVER%\%DEPLOYMENTDRIVE%$ %PASSWORD% /user:%USER%
if %ERRORLEVEL% NEQ 0 GOTO :error 
net use k: \\%WEB02SERVER%\%DEPLOYMENTDRIVE%$ %PASSWORD% /user:%USER%
if %ERRORLEVEL% NEQ 0 GOTO :error 
net use n: \\%TRANS02SERVER%\%DEPLOYMENTDRIVE%$ %PASSWORD% /user:%USER%
if %ERRORLEVEL% NEQ 0 GOTO :error



:destination02cleanup
echo ------- destination cleanup -----------
Set STEP="destination cleanup"
if exist j:\Deployment-ImageReview\%DEPLOYMENTDIR% (rmdir /s /q j:\Deployment-ImageReview\%DEPLOYMENTDIR%) 
if exist o:\Deployment-ImageReview\%DEPLOYMENTDIR% (rmdir /s /q o:\Deployment-ImageReview\%DEPLOYMENTDIR%) 
if exist k:\Deployment-ImageReview\%DEPLOYMENTDIR% (rmdir /s /q k:\Deployment-ImageReview\%DEPLOYMENTDIR%) 
if exist n:\Deployment-ImageReview\%DEPLOYMENTDIR% (rmdir /s /q n:\Deployment-ImageReview\%DEPLOYMENTDIR%) 


:newdir02
echo ------- Create dated deployment directory -----------------
Set STEP="Create Deployment Directories on target servers"
if not exist j:\Deployment-ImageReview\%DEPLOYMENTDIR% (mkdir j:\Deployment-ImageReview\%DEPLOYMENTDIR%)
if not exist o:\Deployment-ImageReview\%DEPLOYMENTDIR% (mkdir o:\Deployment-ImageReview\%DEPLOYMENTDIR%)
if not exist k:\Deployment-ImageReview\%DEPLOYMENTDIR% (mkdir k:\Deployment-ImageReview\%DEPLOYMENTDIR%)
if not exist n:\Deployment-ImageReview\%DEPLOYMENTDIR% (mkdir n:\Deployment-ImageReview\%DEPLOYMENTDIR%)
if %ERRORLEVEL% NEQ 0 GOTO :error 

:deploypackages02
echo ------------------------------------
echo Copy ImageReview....
echo ------------------------------------
set STEP="Deploy ImageReview FPM/FPP"
if exist %BASEDIR%\FP02.zip  (
xcopy %BASEDIR%\FP02.zip j:\Deployment-ImageReview\%DEPLOYMENTDIR% 
)
if %ERRORLEVEL% NEQ 0 GOTO :error 

set STEP="Deploy APP"
if exist %BASEDIR%\APP02.zip ( xcopy %BASEDIR%\APP02.zip o:\Deployment-ImageReview\%DEPLOYMENTDIR%  
)
if %ERRORLEVEL% NEQ 0 GOTO :error 

set STEP="Deploy TRANS"
if exist %BASEDIR%\TRANS02.zip ( xcopy %BASEDIR%\TRANS02.zip n:\Deployment-ImageReview\%DEPLOYMENTDIR%  
)
if %ERRORLEVEL% NEQ 0 GOTO :error 

set STEP="Deploy WEB"
if exist %BASEDIR%\WEB02.zip ( xcopy %BASEDIR%\WEB02.zip k:\Deployment-ImageReview\%DEPLOYMENTDIR%  
)
if %ERRORLEVEL% NEQ 0 GOTO :error 


echo ------------------------------------
echo Copy AES scripts
echo ------------------------------------
set STEP="Deploy AES Scripts"
if not exist j:\Deployment-ImageReview\%DEPLOYMENTDIR%\AES.zip (xcopy /R /Y %BASEDIR%\AES.zip j:\Deployment-ImageReview\%DEPLOYMENTDIR% 
)
if %ERRORLEVEL% NEQ 0 GOTO :error 
if not exist o:\Deployment-ImageReview\%DEPLOYMENTDIR%\AES.zip (xcopy /R /Y %BASEDIR%\AES.zip o:\Deployment-ImageReview\%DEPLOYMENTDIR% 
)
if %ERRORLEVEL% NEQ 0 GOTO :error 
if not exist k:\Deployment-ImageReview\%DEPLOYMENTDIR%\AES.zip (xcopy /R /Y %BASEDIR%\AES.zip k:\Deployment-ImageReview\%DEPLOYMENTDIR%  
)
if %ERRORLEVEL% NEQ 0 GOTO :error 
if not exist n:\Deployment-ImageReview\%DEPLOYMENTDIR%\AES.zip (xcopy /R /Y %BASEDIR%\AES.zip n:\Deployment-ImageReview\%DEPLOYMENTDIR%  
)
if %ERRORLEVEL% NEQ 0 GOTO :error 

if not "%HOCPFEATURE%"=="true" GOTO SKIPPEDHOCP02
:deployhocp02
set STEP="Delete Mapped Drive to prep for HOCP02 deploy"
echo ------------------------------------
echo Copy HOCP 
echo ------------------------------------
Set STEP="delete mapped drives"
net use j: /delete
net use o: /delete
net use k: /delete
net use p: /delete
net use n: /delete
set STEP="Map HOCP"
echo -------------------------
echo Map HOCP
echo --------------------------
net use j: \\%HOCP02SERVER%\%DEPLOYMENTDRIVE%$ %PASSWORD% /user:%USER%
if %ERRORLEVEL% NEQ 0 GOTO :error 

set STEP="Clean up HOCP02"
echo -------------------------
echo Clean up HOCP02
echo --------------------------
if exist j:\Deployment-ImageReview\%DEPLOYMENTDIR%\HOCP2 (rmdir /s /q j:\Deployment-ImageReview\%DEPLOYMENTDIR%\HOCP2) 
if exist j:\Deployment-ImageReview\%DEPLOYMENTDIR%\HOCP2.zip (del /s /q j:\Deployment-ImageReview\%DEPLOYMENTDIR%\HOCP2.zip) 

echo ------------------------------------
echo Copy HOCP 
echo ------------------------------------
set STEP="Deploy ImageReview HOCP"
if exist %BASEDIR%\HOCP02.zip  (  robocopy /R:3 %BASEDIR%\ j:\Deployment-ImageReview\%DEPLOYMENTDIR% HOCP02.zip
)
if %ERRORLEVEL% GEQ 8  GOTO :error 
VERIFY > nul

echo ------------------------------------
echo Copy AES scripts
echo ------------------------------------
set STEP="Deploy HOCP AES Scripts"
if exist %BASEDIR%\HOCP02.zip if not exist j:\Deployment-ImageReview\%DEPLOYMENTDIR%\AES.zip (xcopy /R /Y %BASEDIR%\AES.zip j:\Deployment-ImageReview\%DEPLOYMENTDIR% 
)
if %ERRORLEVEL% NEQ 0 GOTO :error 

net use j: /delete

echo %STEP% Deployment complete 
:SKIPPEDHOCP02
if not "%IRRFEATURE%"=="true" GOTO SKIPPEDIRR02
:deployirr02
Set STEP="delete mapped irr drives"
net use j: /delete
net use o: /delete
net use k: /delete
net use p: /delete
net use n: /delete
echo map app:  %IRR02SERVER%
net use o: \\%IRR02SERVER%\%DEPLOYMENTDRIVE%$ %PASSWORD% /user:%USER%
if %ERRORLEVEL% NEQ 0 GOTO :error
set STEP="Clean IRR02"
if exist o:\Deployment-ImageReview\%DEPLOYMENTDIR%\IRR02 (rmdir /s /q o:\Deployment-ImageReview\%DEPLOYMENTDIR%\IRR02) 
if exist o:\Deployment-ImageReview\%DEPLOYMENTDIR%\IRR02.zip (rmdir /s /q o:\Deployment-ImageReview\%DEPLOYMENTDIR%\IRR02.zip) 
set STEP="Deploy IRR02"
if exist %BASEDIR%\IRR02.zip ( robocopy /R:3 %BASEDIR%\ o:\Deployment-ImageReview\%DEPLOYMENTDIR% IRR02.zip 
)
if %ERRORLEVEL% GEQ 8  GOTO :error 
VERIFY > nul
if exist %BASEDIR%\IRR02.zip if not exist o:\Deployment-ImageReview\%DEPLOYMENTDIR%\AES.zip (xcopy /R /Y %BASEDIR%\AES.zip o:\Deployment-ImageReview\%DEPLOYMENTDIR% 
)
if %ERRORLEVEL% NEQ 0 GOTO :error 
:SKIPPEDIRR02

if not "%PREHOCPFEATURE%"=="true" GOTO SKIPPEDPREHOCP02
:deployprehocp02
set STEP="Delete Mapped Drive to prep for PREHOCP deploy"
echo ------------------------------------
echo Copy PREHOCP02
echo ------------------------------------
Set STEP="delete mapped drives"
net use j: /delete
net use o: /delete
net use k: /delete
net use p: /delete
net use n: /delete
set STEP="Map PREHOCP02"
echo -------------------------
echo Map PREHOCP
echo --------------------------
net use j: \\%PREHOCP02SERVER%\%DEPLOYMENTDRIVE%$ %PASSWORD% /user:%USER%
if %ERRORLEVEL% NEQ 0 GOTO :error 

set STEP="Clean up PREHOCP"
echo -------------------------
echo Clean up PREHOCP
echo --------------------------
if exist j:\Deployment-ImageReview\%DEPLOYMENTDIR%\PREHOCP02 (rmdir /s /q j:\Deployment-ImageReview\%DEPLOYMENTDIR%\PREHOCP02) 
if exist j:\Deployment-ImageReview\%DEPLOYMENTDIR%\PREHOCP02.zip (del /s /q j:\Deployment-ImageReview\%DEPLOYMENTDIR%\PREHOCP02.zip) 

echo ------------------------------------
echo Copy PREHOCP 
echo ------------------------------------
set STEP="Deploy ImageReview PREHOCP"
if exist %BASEDIR%\PREHOCP02.zip  (  robocopy /R:3 %BASEDIR%\ j:\Deployment-ImageReview\%DEPLOYMENTDIR% PREHOCP02.zip
)
if %ERRORLEVEL% GEQ 8  GOTO :error 
VERIFY > nul

echo ------------------------------------
echo Copy AES scripts
echo ------------------------------------
set STEP="Deploy PREHOCP AES Scripts"
if exist %BASEDIR%\PREHOCP02.zip if not exist j:\Deployment-ImageReview\%DEPLOYMENTDIR%\AES.zip (xcopy /R /Y %BASEDIR%\AES.zip j:\Deployment-ImageReview\%DEPLOYMENTDIR% 
)
if %ERRORLEVEL% NEQ 0 GOTO :error 

net use j: /delete

echo %STEP% Deployment complete 
:SKIPPEDPREHOCP02

:cleanup
echo %STEP% Deployment complete 

Set STEP="delete mapped drives"
net use j: /delete
net use o: /delete
net use k: /delete
net use p: /delete
net use n: /delete


:finish
CD %CURRENTDIR%
echo --------------------------------------------------------------------
echo Deployment Completed...
echo --------------------------------------------------------------------
echo DEPLOYMENT COMPLETE 
GOTO END


:error
echo ------- AN ERROR OCCURED DURING THIS STEP: %STEP% ------
exit /b 1

:END