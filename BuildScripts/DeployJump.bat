@echo off
rem set variables
Set CURRENTDIR=%CD%

echo ------- Your current directory is:  %CURRENTDIR% ---------

set NOW=%date:~10,4%-%date:~4,2%-%date:~7,2%


SET MOMSSERVER=%2
SET USER=%3
SET PASSWORD=%4
SET DEPLOYMENTDRIVE=%5
SET DEPLOYMENTDIR=%6
SET JUMPDRIVE=%7
SET BASEDIR=%JUMPDRIVE%:\Staging-Insight\%DEPLOYMENTDIR%
SET DBSERVER=%8
SET INSIGHTMOBILEFEATURE=%9
SHIFT
SHIFT
SHIFT
SHIFT
SHIFT
SHIFT
SHIFT
SHIFT
SHIFT
SET INSIGHTMOBILESERVER=%1
SET REPORTSERVER=%2
SET RPTDRIVE=%3
SET DBDRIVE=%4
SET CREATEDB=%5


echo %DEPLOYMENTDIR%


SET STEP="Start Connection"

echo -----------------------------------
echo Connect to Server
echo -----------------------------------

echo Deployment beginning %DEPLOYMENTDIR%

net use w: /delete
net use y: /delete
net use m: /delete


echo ------- map drive ----------
net use w: \\%MOMSSERVER%\%DEPLOYMENTDRIVE%$ %PASSWORD% /user:%USER%
net use m: \\%REPORTSERVER%\%DEPLOYMENTDRIVE%$ %PASSWORD% /user:%USER%
if %ERRORLEVEL% NEQ 0 GOTO :error 

echo ------- destination cleanup -----------
rem if exist x:\Deployment\%DEPLOYMENTDIR%\nul goto deldir
SET STEP="delete dir if exist"
rem psexec -accepteula -h -u %USER% -p %PASSWORD%  \\%MOMSSERVER% cmd /c if exist %DEPLOYMENTDRIVE%:\Deployment\%DEPLOYMENTDIR%\ (rmdir /s /q %DEPLOYMENTDRIVE%:\Deployment\%DEPLOYMENTDIR%)
if exist w:\Deployment\%DEPLOYMENTDIR% (rmdir /s /q w:\Deployment\%DEPLOYMENTDIR%)
if exist m:\Deployment\%DEPLOYMENTDIR% (rmdir /s /q m:\Deployment\%DEPLOYMENTDIR%)
if %ERRORLEVEL% NEQ 0 GOTO :error 
echo ------- Create dated deployment directory -----------------
echo create dated deployment directory
SET STEP="create deploymentdir"
echo create directory %DEPLOYMENTDIR%
if not exist w:\Deployment\%DEPLOYMENTDIR%  (mkdir w:\Deployment\%DEPLOYMENTDIR%)
if not exist m:\Deployment\%DEPLOYMENTDIR%  (mkdir m:\Deployment\%DEPLOYMENTDIR%)
rem echo psexec -accepteula -h -u %USER% -p %PASSWORD% \\%MOMSSERVER% cmd /c  "mkdir %DEPLOYMENTDRIVE%:\Deployment\%DEPLOYMENTDIR%"
rem psexec -accepteula -h -u %USER% -p %PASSWORD% \\%MOMSSERVER% cmd /c  "mkdir %DEPLOYMENTDRIVE%:\Deployment\%DEPLOYMENTDIR%"
echo successfully created deployment directory
if %ERRORLEVEL% NEQ 0 GOTO :error 
echo ------- create MOMSAttachment Folder -------
SET STEP="create MOMSAttachment Folder if needed"
if exist w:\MOMSAttachment\WikiPageDocument GOTO :deploy
if NOT exist w:\MOMSAttachment\WikiPageDocument (mkdir w:\MOMSAttachment\WikiPageDocument)
echo ------- make MOMSAttachment\WikiPageDocument a Shared Folder -------
psexec -accepteula \\%MOMSSERVER% cmd /c if exist D:\MOMSAttachment\WikiPageDocument\ (net share WikiPageDocument=D:\MOMSAttachment\WikiPageDocument /GRANT:Everyone,FULL)

:deploy

echo -----------------------------------
echo Deploy Build Scripts zip file to MOMS Server....
echo -----------------------------------
SET STEP="Deploy BuildScripts.zip"
if exist w:\Deployment\BuildScripts.Zip (del /s /q w:\Deployment\BuildScripts.zip)
if %ERRORLEVEL% NEQ 0 GOTO :error 
echo remove build scripts dir
rem if exist x:\Staging-Insight\BuildScripts (rmdir /s /q x:\Staging-Insight\BuildScripts)
echo remove build scripts dir complete
if %ERRORLEVEL% NEQ 0 GOTO :error 
echo copy build scripts zip
xcopy /R "..\BuildScripts.zip" "w:\Deployment\*" 
echo copy build scripts zip done
echo error level is %ERRORLEVEL%
if %ERRORLEVEL% NEQ 0 GOTO :error 
echo unzip build scripts
unzip -q -ou w:\Deployment\BuildScripts.zip -d  w:\Deployment\
if %ERRORLEVEL% NEQ 0 GOTO :error 
echo unzip complete
echo %STEP% Deployment complete 



echo -----------------------------------
echo Deploy Distribution zip file to MOMS Server....
echo -----------------------------------
SET STEP="Deploy Distribution.zip"
rem ROBOCOPY %BASEDIR% x:\Deployment\%NOW% Distribution.zip > DEPLOY.log
echo start xcopy
rem echo xcopy /Z "..\Staging\Distribution.zip" "x:\Deployment\%DEPLOYMENTDIR%\*"
xcopy /Z "%BASEDIR%\Distribution.zip" "w:\Deployment\%DEPLOYMENTDIR%\*" 
echo stop xcopy
rem ErrorLevel is set at %ERRORLEVEL%
if %ERRORLEVEL% NEQ 0 GOTO :error 
echo stop xcopy 
rem ErrorLevel is set at %ERRORLEVEL%
if %ERRORLEVEL% NEQ 0 GOTO :error 
echo %STEP% Deployment complete 

echo -----------------------------------
echo Deploy resources to the report server....
echo -----------------------------------
SET STEP="Deploy Report Resources.zip"
xcopy /Y /R %BASEDIR%\Resources.zip m:\Deployment\%DEPLOYMENTDIR%\*  
if %ERRORLEVEL% NEQ 0 GOTO :error
echo %STEP% Deployment complete 

echo -----------------------------------
echo Copy reports to the report server....
echo -----------------------------------
SET STEP="Deploy MOMSReports.zip"
xcopy /Y /R %BASEDIR%\MOMSReports.zip m:\Deployment\%DEPLOYMENTDIR% 
if %ERRORLEVEL% NEQ 0 GOTO :error 
echo %STEP% Deployment complete 


echo ------------------------------------
echo Copy Scheduled Tasks creation scripts
echo ------------------------------------
set STEP="Deploy Scheduled Tasks Scripts"
xcopy /Y /R %BASEDIR%\ScheduledTasks.zip w:\Deployment\%DEPLOYMENTDIR%
if %ERRORLEVEL% NEQ 0 GOTO :error 
echo %STEP% Deployment complete 


if "%CREATEDB%"=="N" GOTO :COPYREPORTFILES
:COPYDBFILES
set STEP="Deploy DB files"
net use y: \\%DBSERVER%\%DEPLOYMENTDRIVE%$ %PASSWORD% /user:%USER%
if exist y:\Deployment\%DEPLOYMENTDIR%\AlterScripts.zip (rmdir /s /q y:\Deployment\%DEPLOYMENTDIR%\AlterScripts.zip)
if exist y:\Deployment\%DEPLOYMENTDIR%\DBScripts.zip (rmdir /s /q y:\Deployment\%DEPLOYMENTDIR%\DBScripts.zip)
if exist y:\Deployment\%DEPLOYMENTDIR%\MOMSDatabase.zip (rmdir /s /q y:\Deployment\%DEPLOYMENTDIR%\MOMSDatabase.zip)
if exist y:\Deployment\%DEPLOYMENTDIR%\Database (rmdir /s /q y:\Deployment\%DEPLOYMENTDIR%\Database)
if exist y:\Deployment\%DEPLOYMENTDIR%\LocalDBScripts (rmdir /s /q y:\Deployment\%DEPLOYMENTDIR%\LocalDBScripts)

if not exist y:\Deployment\%DEPLOYMENTDIR%  (mkdir y:\Deployment\%DEPLOYMENTDIR%)

echo ------------------------------------
echo Copy database alter scripts to DB server....
echo ------------------------------------
set STEP="Deploy AlterScripts.zip"
for /F %%i in ('dir /b "%BASEDIR%\AlterScripts.zip"') do (
xcopy /Y /R "%BASEDIR%\AlterScripts.zip" y:\Deployment\%DEPLOYMENTDIR%\* 
)
if %ERRORLEVEL% NEQ 0 GOTO :error 
echo %STEP% Deployment complete 

echo ------------------------------------
echo Copy db scripts to server for DB creation
echo ------------------------------------
set STEP="Deploy DB Creation Scripts"
xcopy /Y /R %BASEDIR%\DBScripts.zip y:\Deployment\%DEPLOYMENTDIR% 
if %ERRORLEVEL% NEQ 0 GOTO :error 
echo %STEP% Deployment complete 

echo ------------------------------------
echo Copy db source to server for DB creation
echo ------------------------------------
set STEP="Deploy DB Creation Scripts"
xcopy /Y /R %BASEDIR%\MOMSDatabase.zip y:\Deployment\%DEPLOYMENTDIR% 
if %ERRORLEVEL% NEQ 0 GOTO :error 
echo %STEP% Deployment complete 


:COPYREPORTFILES
echo ------------------------
echo copy report files
echo -----------------------
xcopy /Y /R %BASEDIR%\ReportsDeployer.exe m:\Deployment\%DEPLOYMENTDIR%\*
xcopy /Y /R %BASEDIR%\ReportsDeployer.exe.config m:\Deployment\%DEPLOYMENTDIR%\*

if "%INSIGHTMOBILEFEATURE%"=="false" GOTO END
:INSIGHTMOBILEDEPLOY
set STEP="Deploy Insight Mobile files"
net use q: /delete
net use q: \\%INSIGHTMOBILESERVER%\%DEPLOYMENTDRIVE%$ %PASSWORD% /user:%USER%
if %ERRORLEVEL% NEQ 0 GOTO :error 
if exist q:\Deployment-InsightMobile\%DEPLOYMENTDIR% (rmdir /s /q q:\Deployment-InsightMobile\%DEPLOYMENTDIR%)
if %ERRORLEVEL% NEQ 0 GOTO :error 
if exist q:\Deployment-InsightMobile\%DEPLOYMENTDIR% (rmdir /s /q q:\Deployment-InsightMobile\%DEPLOYMENTDIR%)
if %ERRORLEVEL% NEQ 0 GOTO :error 
if "%INSIGHTMOBILEFEATURE%"=="true" mkdir q:\Deployment-InsightMobile\%DEPLOYMENTDIR%
if exist "%BASEDIR%\InsightMobile.zip" (xcopy /Z "%BASEDIR%\InsightMobile.zip" "q:\Deployment-InsightMobile\%DEPLOYMENTDIR%\*"  )
if %ERRORLEVEL% NEQ 0 GOTO :error 
net use q: /delete


CD %CURRENTDIR%
net use w: /delete
net use y: /delete
net use m: /delete


echo --------------------------------------------------------------------
echo Deployment Completed...
echo --------------------------------------------------------------------
echo DEPLOYMENT COMPLETE 
GOTO END


:error
echo ------- AN ERROR OCCURED DURING THIS STEP: %STEP% ------
exit /b 1

:END