﻿<?xml version="1.0" encoding="UTF-8"?>
<project name="MOMS 2.0" default="build" basedir=".">

  <include buildfile="UserInput.include"/>
  <include buildfile="BUILD.include"/>
  <include buildfile="ENVIRONMENT.include"/>
  <property name="env" value="${deployment.environment}"/>

	<!-- Echo Environment Target -->
	<echo message="****** Deployment Target is ${env} ${deployment.type} *******" />	
	<echo message="Building the following for ${deployment.environment} ${deployment.type} = ${build_id}" />
	
	<!-- Remove all debris from any prior builds -->
	<target name="clean" description="Remove temporary folders">
		<property name="MailLogger.failure.subject" value="MOMS - SVN cleanup step failed." />
		<attrib readonly="false">
			<fileset>
				<include name="${build.dir}/**/*.*" />
				<include name="${base.dir}/*.*" />
				<include name="${app.moms.internal.dist.dir}/**/*.*" />
				<include name="${app.moms.external.dist.dir}/**/*.*" />
				<include name="${app.moms.traffic.loader.dist.dir}/**/*.*" />	
				<include name="${moms.resources.dir}/**/*.*" />
				<include name="${insight.build.dir}/**/*.*" />			
				
				<include name="${moms.release.directory}/**/*.*" />
				<include name="${momsmobile.release.directory}/**/*.*" />
				<include name="${infinity.release.directory}/**/*.*" />
			    <include name="${integrity.release.directory}/**/*.*" />
				<include name="${transsuite.release.directory}/**/*.*" />
				<include name="${interfaces.release.directory}/**/*.*" />
				<include name="${app.moms.gps.locator.dist.dir}/**/*.*" />
				<include name="${mobile.build.dir}/**/*.*" />
				<include name="${app.momsmobile.internal.dist.dir}/**/*.*" />			
				<include name="${web.momsmobile.dist.dir}/**/*.*" />
				<include name="${interfaces.release.directory}/**/*.*" />	
						

			</fileset>
		</attrib>
		
		
		<if test="${InfinityFeature=='true'}">
			<attrib readonly="false">
			<fileset>
				<include name="${infinity.build.dir}/**/*.*" />
				<include name="${app.Infinity.dist.dir}/**/*.*" />
			</fileset>
			</attrib>		
		</if>
		<if test="${IntegrityFeature=='true'}">
			<attrib readonly="false">
			<fileset>
				<include name="${integrity.build.dir}/**/*.*" />
				<include name="${app.Integrity.dist.dir}/**/*.*" />
			</fileset>
			</attrib>		
		</if>
		<if test="${TransSuiteFeature=='true'}">
			<attrib readonly="false">
			<fileset>
				<include name="${transsuite.build.dir}/**/*.*" />
				<include name="${app.TransSuite.dist.dir}/**/*.*" />
			</fileset>
			</attrib>		
		</if>
		
		<attrib readonly="false" failonerror="false">
		<fileset>
		<include name="${insight.mobile.dist.dir}/**/*.*" />	
		</fileset>
		</attrib>			

		
		<delete dir="${base.dir}/BuildScripts" failonerror="false" />
		<delete dir="${mobile.build.dir}" failonerror="false" />
		<delete dir="${mobile.dist.dir}" failonerror="false" />
		<delete dir="${infinity.build.dir}" failonerror="false" />
		<delete dir="${infinity.dist.dir}" failonerror="false" />
		<delete dir="${integrity.build.dir}" failonerror="false" />
		<delete dir="${integrity.dist.dir}" failonerror="false" />
		<delete dir="${transsuite.build.dir}" failonerror="false" />
		<delete dir="${transsuite.dist.dir}" failonerror="false" />
		<delete dir="${interfaces.dist.dir}" failonerror="false" />		
		<delete dir="${build.dir}" failonerror="false" />		
		<delete dir="${dist.dir}" failonerror="false" />
		<delete dir="${moms.resources.dir}" failonerror="false" />
		<delete dir="${insight.dist.dir}" failonerror="false" />
		<delete dir="${insight.build.dir}" failonerror="false" />
		<delete dir="${insight.mobile.dist.dir}" failonerror="false" />	
		<delete dir="${base.dir}\Source\INS\MOMSInterfaces" failonerror="false" />		
		<delete dir="${base.dir}\Source\INS" failonerror="false" />		

			
		<delete>
		
			<fileset>
			<include name="${moms.release.directory}/**/*.*"/>
			<include name="${moms.release.directory}/*.*"/>
			</fileset>
		</delete>
		

		<delete>
			<fileset>
			<include name="${momsmobile.release.directory}/**/*.*"/>
			<include name="${momsmobile.release.directory}/*.*"/>
			</fileset>
		</delete>

		<!--<delete>
			<fileset>
			<include name="${insight.release.directory}/**/*.*"/>
			<include name="${insight.release.directory}/*.*"/>
			</fileset>
		</delete>-->
		
		<delete>
			<fileset>
			<include name="${interfaces.release.directory}/**/*.*"/>
			<include name="${interfaces.release.directory}/*.*"/>
			</fileset>
		</delete>
		
		<delete dir="${moms.release.directory}" failonerror="false" />
		<delete dir="${momsmobile.release.directory}" failonerror="false" />
		<delete dir="${integrity.release.directory}" failonerror="false" />
		<delete dir="${infinity.release.directory}" failonerror="false" />
		<delete dir="${transsuite.release.directory}" failonerror="false" />
		<delete dir="${interfaces.release.directory}" failonerror="false" />
		<delete dir="${insight.mobile.release.directory}" failonerror="false" />
		<delete>
			<fileset>
			<include name="${base.dir}/*.*"/>
			</fileset>
		</delete>
		
	</target>
	
	<!-- Create all default directories -->
	<target name="init" description="create build folders">
			<!-- remove old folders -->
		<call target="clean" />
		<mkdir dir="${base.dir}\Source\INS"/>
		
		<!-- Third Party / Common Layer dlls -->
		<mkdir dir="${moms.dist.AppLayer.dir}"/>
		<mkdir dir="${moms.dist.CommonLayer.dir}"/>
		<mkdir dir="${moms.dist.ServiceClient.dir}"/>
		<mkdir dir="${moms.dist.ThirdParty.dir}"/>

		
		<!-- app service folders -->
		<mkdir dir="${app.moms.internal.dist.dir}" />
		<mkdir dir="${app.moms.external.dist.dir}" />
		<mkdir dir="${app.moms.traffic.loader.dist.dir}" />
		
		
		<!-- Create GPS Folder if feature exists -->
		<if test="${GPSFeature=='true'}">
		<mkdir dir="${app.moms.gps.locator.dist.dir}" />
		</if>
			
		<!--scheduled tasks folders  -->
		<mkdir dir="${tasks.moms.dist.dir}" />
		
		
		<!-- Create Mobile Folders -->
		<if test="${MobileFeature=='true'}">
			<!--<mkdir dir="${mobile.build.dir}" />-->
			<!-- app folders -->
			<mkdir dir="${app.momsmobile.internal.dist.dir}" />		
			<!-- web & webapi folders -->
			<mkdir dir="${web.momsmobile.dist.dir}" />
		</if>
		
	
		<!-- Create Infinity Service Folders -->
		<if test="${InfinityFeature=='true'}">
	
			<mkdir dir="${app.Infinity.dist.dir}" />
			<mkdir dir="${infinity.dist.AppLayer.dir}"/>
			<mkdir dir="${infinity.dist.CommonLayer.dir}"/>
			<mkdir dir="${infinity.dist.ServiceClient.dir}"/>
			<mkdir dir="${infinity.dist.DataContract.dir}"/>
			<mkdir dir="${interfaces.dist.ThirdParty.dir}"/>
		</if>
		
	    <!-- Create Integrity Service Folders -->
		<if test="${IntegrityFeature=='true'}">

			<mkdir dir="${app.Integrity.dist.dir}" />
			<mkdir dir="${integrity.dist.AppLayer.dir}"/>
			<mkdir dir="${integrity.dist.CommonLayer.dir}"/>
			<mkdir dir="${integrity.dist.ServiceClient.dir}"/>
			<mkdir dir="${integrity.dist.DataContract.dir}"/>
			<mkdir dir="${interfaces.dist.ThirdParty.dir}"/>
		</if>
		
		<!-- Create TransSuite Service Folders -->
		<if test="${TransSuiteFeature=='true'}">

			<mkdir dir="${app.TransSuite.dist.dir}" />
			<mkdir dir="${transsuite.dist.AppLayer.dir}"/>
			<mkdir dir="${transsuite.dist.CommonLayer.dir}"/>
			<mkdir dir="${transsuite.dist.ServiceClient.dir}"/>
			<mkdir dir="${transsuite.dist.DataContract.dir}"/>
			<mkdir dir="${interfaces.dist.ThirdParty.dir}"/>
		</if>
			
		<!-- Create Insight Folders -->

		<mkdir dir="${insight.dist.dir}" />
		<!-- api folders -->
		<mkdir dir="${webapi.insight.dist.dir}" />		
		<!-- web folders -->
		<mkdir dir="${web.insight.dist.dir}" />
		<!-- web rpt folders -->
		<mkdir dir="${rpts.insight.dist.dir}" />
		<!-- Create Insight Mobile folders -->
		<if test="${InsightMobileFeature=='true'}">
			<mkdir dir="${insight.mobile.dist.dir}" />
		</if>
		
		
		
		<mkdir dir="${moms.release.directory}" />
		<mkdir dir="${momsmobile.release.directory}" />
		<mkdir dir="${interfaces.release.directory}" />
		<mkdir dir="${infinity.release.directory}" />
	    <mkdir dir="${integrity.release.directory}" />
		<mkdir dir="${transsuite.release.directory}" />
		
	</target>
	
	<!-- Checkout source from correct repo -->
	<target name="checkout" description="Get current version of source">
	<if test="${devops=='true'}">	
		<call target="checkout-src-devops" failonerror="true"/>	
	</if>
	<if test="${devops=='false'}">	
		<call target="checkout-src" failonerror="true"/>	
	</if>
	</target>
	<!-- Get the source files from DevOps -->
	<target name="checkout-src-devops" description="Get current version of source">
		
		<exec program="devopsfetch.bat" failonerror="true">
			<arg value="${devops.dir}" />
			<arg value="${base.dir}" />
			<arg value="${build_id}" />
			<arg value="${devops.repo}" />
			<arg value="${devops.repo.pds}" />
		</exec>	
			
	</target>
	
	<target name="checkout-src">
			<echo message="Checking out SVN the following for ${deployment.environment} ${deployment.type} = ${build_id}" />
			<property name="MailLogger.failure.subject" value="MOMS Insight ${deployment.environment} ${deployment.type} - checkout step failed." />
		    <exec program="AutobuildCheckout.bat" failonerror="true">
			<arg value="${svn.url-dev-branch}" />
			<arg value="${build.dir}" />
			<arg value="${svn.password}" />
			<arg value="HEAD" />
			<arg value="${svn.user}" />						
			</exec>
			
			<if test="${MobileFeature=='true'}">
			<property name="MailLogger.failure.subject" value="MOMSMobile- ${deployment.environment} ${deployment.type} - checkout step failed." />
		    <exec program="AutobuildCheckout.bat" failonerror="true">
			<arg value="${mobile.svn.url-dev-branch}" />
			<arg value="${mobile.build.dir}" />
			<arg value="${svn.password}" />
			<arg value="HEAD" />
			<arg value="${svn.user}" />						
			</exec>
			</if>
			
			<if test="${InfinityFeature=='true'}">
			<property name="MailLogger.failure.subject" value="Infinity Service- ${deployment.environment} ${deployment.type} - checkout step failed." />
		    <exec program="AutobuildCheckout.bat" failonerror="true">
			<arg value="${infinity.svn.url-dev-branch}" />
			<arg value="${infinity.build.dir}" />
			<arg value="${svn.password}" />
			<arg value="HEAD" />
			<arg value="${svn.user}" />						
			</exec>
			</if>
			
			<if test="${IntegrityFeature=='true'}">
			<property name="MailLogger.failure.subject" value="Integrity Service- ${deployment.environment} ${deployment.type} - checkout step failed." />
		    <exec program="AutobuildCheckout.bat" failonerror="true">
			<arg value="${integrity.svn.url-dev-branch}" />
			<arg value="${integrity.build.dir}" />
			<arg value="${svn.password}" />
			<arg value="HEAD" />
			<arg value="${svn.user}" />						
			</exec>
			</if>
			
			<if test="${TransSuiteFeature=='true'}">
			<property name="MailLogger.failure.subject" value="TransSuite Service- ${deployment.environment} ${deployment.type} - checkout step failed." />
		    <exec program="AutobuildCheckout.bat" failonerror="true">
			<arg value="${transsuite.svn.url-dev-branch}" />
			<arg value="${transsuite.build.dir}" />
			<arg value="${svn.password}" />
			<arg value="HEAD" />
			<arg value="${svn.user}" />						
			</exec>
			</if>
			
			
			<property name="MailLogger.failure.subject" value="Insight Service- ${deployment.environment} ${deployment.type} - checkout step failed." />
		    <exec program="AutobuildCheckout.bat" failonerror="true">
			<arg value="${insight.svn.url-dev-branch}" />
			<arg value="${insight.build.dir}" />
			<arg value="${svn.password}" />
			<arg value="HEAD" />
			<arg value="${svn.user}" />						
			</exec>	
			
			<exec program="git" failonerror="true">
			<arg value="clone" />
			<arg value="${devops.repo.pds}" />
			<arg value="${pds.dir}" />
			</exec>			
		
	</target>
	
	
	<!-- Build the solutions -->
	<target name="build-MOMS" description="Build solution">
		<property name="MailLogger.failure.subject" value="MOMS - Build step failed" />
			<property name="MailLogger.success.subject" value="MOMS ${deployment.environment} ${deployment.type} - Step 1 Build Successfully Completed." />
		
		<copy todir="${build.dir}\MOMSReports\MOMSReports" overwrite="true" failonerror="true">
					<fileset basedir="${build.dir}\MOMSReports\MOMSReports\${env}" />
			</copy>
			
		<call target="build-MOMS-solution" failonerror="true" />
		
		<if test="${MobileFeature=='true'}">
			<property name="MailLogger.failure.subject" value="MOMSMobile - Build step failed" />
			<call target="build-MOMSMobile-solution" failonerror="true" />
		</if>
		
		<if test="${InfinityFeature=='true'}">
			<property name="MailLogger.failure.subject" value="Infinity Service - Build step failed" />
			<call target="build-InfinityService-solution" failonerror="true" />
		</if>
		
		<if test="${IntegrityFeature=='true'}">
			<property name="MailLogger.failure.subject" value="Integrity Service - Build step failed" />
			<call target="build-IntegrityService-solution" failonerror="true" />
		</if>
		
		<if test="${TransSuiteFeature=='true'}">
			<property name="MailLogger.failure.subject" value="TransSuite Service - Build step failed" />
			<call target="build-TransSuiteService-solution" failonerror="true" />
		</if>
		
		<property name="MailLogger.failure.subject" value="Insight - Build step failed" />
		<call target="build-Insight-solution" failonerror="true" />

		
		<if test="${InsightMobileFeature=='true'}">
			<if test="${insight.mobile.ssl=='true'}">
				<exec program="ReplaceUtil.exe" failonerror="true">
				<arg value="${insight.build.dir}\WebLayer\InsightMobile\src\environments" />
				<arg value="ts"/>
				<arg value="http://localhost:889" />
				<arg value="https://${insight.mobile.urlname}" />
				<arg value="ALL"/>
			</exec>
			</if>
			<if test="${insight.mobile.ssl=='false'}">
				<exec program="ReplaceUtil.exe" failonerror="true">
				<arg value="${insight.build.dir}\WebLayer\InsightMobile\src\environments" />
				<arg value="ts"/>
				<arg value="http://localhost:889" />
				<arg value="http://${insight.mobile.urlname}" />
				<arg value="ALL"/>
				</exec>
				</if>
				
			<if test="${ActiveDirectoryFeature=='true'}">
				<exec program="ReplaceUtil.exe" failonerror="true">
				<arg value="${insight.build.dir}\WebLayer\InsightMobile\src\environments" />
				<arg value="ts"/>
				<arg value="isActiveDirectory:false" />
				<arg value="isActiveDirectory:true" />
				<arg value="ALL"/>
				</exec>
			</if>
			<exec program="InsightMobile-IonicBuild.bat" failonerror="true">
			<arg value="${deployment.type}" />
			<arg value="${insight.build.dir}\WebLayer\InsightMobile" />	
			<arg value="${source_drive}" />
		</exec>	
		</if>
		
		
	</target>
	
	<!-- Build the MOMS solution -->
	<target name="build-MOMS-solution" description="Build MOMS solution">
		<property name="MailLogger.failure.subject" value="MOMS - Solution build step failed" />
		<!-- Build the MOMS solution -->
				
		<exec program="C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\MSBuild\Current\Bin\msbuild.exe" failonerror="true">
			<arg value="${solution.file}" />
			<arg value="/p:Configuration=Release" />
		</exec>
		
	</target>
	
	<!-- Build the MOMSMobile solution -->
	<target name="build-MOMSMobile-solution" description="Build MOMSMobile solution">
		<property name="MailLogger.failure.subject" value="MOMSMobile - Solution build step failed" />
		<!-- Build the MOMS Mobile solution -->
				
		<exec program="C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\MSBuild\Current\Bin\msbuild.exe" failonerror="true">
			<arg value="${mobile.solution.file}" />
			<arg value="/p:Configuration=Release;Platform=x86" />
		</exec>	
	</target>
	
	
	<!-- Build the InfinityService Solution -->
	<target name="build-InfinityService-solution" description="Build Infinity Service solution">
		<property name="MailLogger.failure.subject" value="Infinity Service - Solution build step failed" />
		<!-- Build the Infinity Service solution -->
				
		<exec program="C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\MSBuild\Current\Bin\msbuild.exe" failonerror="true">
			<arg value="${infinity.solution.file}" />
			<arg value="/p:Configuration=Release" />
		</exec>	
	</target>
	
	<!-- Build the IntegrityService Solution -->
	<target name="build-IntegrityService-solution" description="Build Integrity Service solution">
		<property name="MailLogger.failure.subject" value="Integrity Service - Solution build step failed" />
		<!-- Build the Integrity Service solution -->
				
		<exec program="C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\MSBuild\Current\Bin\msbuild.exe" failonerror="true">
			<arg value="${integrity.solution.file}" />
			<arg value="/p:Configuration=Release" />
		</exec>	
	</target>
	
	<!-- Build the Transsuite Service Solution -->
	<target name="build-TransSuiteService-solution" description="Build TransSuite Service solution">
		<property name="MailLogger.failure.subject" value="TransSuite Service - Solution build step failed" />
		<!-- Build the Infinity Service solution -->
				
		<exec program="C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\MSBuild\Current\Bin\msbuild.exe" failonerror="true">
			<arg value="${transsuite.solution.file}" />
			<arg value="/p:Configuration=Release" />
		</exec>	
	</target>
	
	
	<!-- Build the Insight Solution -->
	<target name="build-Insight-solution" description="Build Insight solution">
		<property name="MailLogger.failure.subject" value="Insight- Solution build step failed" />
		<!-- Build the Insight Service solution -->
				
		<exec program="C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\MSBuild\Current\Bin\msbuild.exe" failonerror="true">
			<arg value="${insight.solution.file}" />
			<arg value="/p:Configuration=Release" />
		</exec>	
	</target>
	
	
	<!-- Ready the solution for deployment -->
	<target name="distribute" description="Ready the solution for deployment">
		<property name="MailLogger.failure.subject" value="MOMS - Distribution step failed" />
		<property name="MailLogger.success.subject" value="MOMS ${deployment.environment} ${deployment.type} - Step 2 Package Successfully Completed." />
		
		<!-- Copy APPLICATION files from build directory to distribution directory on local machine -->
		<call target="distribute-MOMS-app" />

		<!-- Copy scheduled tasks files from build directory to distribution directory on local machine -->
		<call target="distribute-MOMS-Tasks" />
		
		<if test="${MobileFeature=='true'}">
			<!-- Copy APPLICATION files from build directory to distribution directory on local machine -->
			<call target="distribute-MOMSMobile-app" />
		
			<!-- Copy WEB files from build directory to distribution directory on local machine -->
			<call target="distribute-MOMSMobile-web" />
		</if>
		
		<if test="${InfinityFeature=='true'}">
			<!-- Copy APPLICATION files from build directory to distribution directory on local machine -->
			<call target="distribute-InfinityService-app" />
		</if>
		
		<if test="${IntegrityFeature=='true'}">
			<!-- Copy APPLICATION files from build directory to distribution directory on local machine -->
			<call target="distribute-IntegrityService-app" />
		</if>
		
		<if test="${TransSuiteFeature=='true'}">
			<!-- Copy APPLICATION files from build directory to distribution directory on local machine -->
			<call target="distribute-TransSuiteService-app" />
		</if>
		
		<!-- Copy files from build directory to distribution directory on local machine -->
		<call target="distribute-Insight-web" />
		<!-- Copy Report deployer exe and config -->
		<call target="distribute-Report-Deployer" />
		
		<if test="${InsightMobileFeature=='true'}">
		
			<!-- Copy WEB files from build directory to distribution directory on local machine -->
			<call target="distribute-InsightMobile-web" />
		</if>
		
		<if test="${OfflineMapFeature=='true'}">
		
			<!-- Copy WEB files from build directory to distribution directory on local machine -->
			<call target="distribute-OfflineMaps-web" />
		</if>
		
		<call target="distribute-Data-Scripts" failonerror="true" />
		
	</target>

	
	<target name="distribute-Data-Scripts" description="Distribute Project data script">
		<property name="MailLogger.failure.subject" value="Insight project Data script - Distribute Project data script failed" />	
		<property name="projtarget" value="${pds.dir}/${deployment.environment}"/>
		<copy todir="${build.dir}/Database/Data/INSIGHT_DATA/${deployment.environment}" overwrite="true" if="${directory::exists(projtarget)}" >
			<fileset basedir="${pds.dir}/${deployment.environment}">
				<include name="**/*" />
			</fileset>
		</copy>			
	</target>
	
	<!-- Distribute the internal app folder -->
	<target name="distribute-MOMS-app" description="Ready the solution for deployment of internal application">
		<property name="MailLogger.failure.subject" value="MOMS - Distribution of app layer step failed" />
		
			<copy todir="${app.moms.internal.dist.dir}">
				<fileset basedir="${moms.release.directory}\ServiceHost\${moms.release.subdirectory}">
					<include name="**/*" />
					<exclude name="**/*.pdb" />
				</fileset>
			</copy>
			<copy todir="${moms.dist.AppLayer.dir}">
				<fileset basedir="${moms.release.AppLayer.dir}">
					<include name="**/*" />
					<exclude name="**/*.pdb" />
				</fileset>
			</copy>
			<copy todir="${moms.dist.CommonLayer.dir}">
				<fileset basedir="${moms.release.CommonLayer.dir}">
					<include name="**/*" />
					<exclude name="**/*.pdb" />
				</fileset>
			</copy>
			<copy todir="${moms.dist.ServiceClient.dir}">
				<fileset basedir="${moms.release.ServiceClient.dir}">
					<include name="**/*" />
					<exclude name="**/*.pdb" />
				</fileset>
			</copy>
			<copy todir="${moms.dist.ThirdParty.dir}">
				<fileset basedir="${moms.release.ThirdParty.dir}">
					<include name="**/*" />
					<exclude name="**/*.pdb" />
				</fileset>
			</copy>		
	</target>
	
	<!-- Distribute the Mobile internal app folder -->
	<target name="distribute-MOMSMobile-app" description="Ready the solution for deployment of internal application">
		<property name="MailLogger.failure.subject" value="MOMSMobile - Distribution of app layer step failed" />	
		<copy todir="${app.momsmobile.internal.dist.dir}">
			<fileset basedir="${momsmobile.release.directory}\ServiceHosts\${momsmobile.release.subdirectory}">
				<include name="**/*" />
				<exclude name="**/*.pdb" />
			</fileset>
		</copy>
	</target>
	
	<!-- Ready the solution for deployment of Mobile web & webApi -->
	<target name="distribute-MOMSMobile-web" description="Ready the solution for deployment of  web and webApi files"> 
		<property name="MailLogger.failure.subject" value="MOMSMobile - Distribution of web layer step failed" />
		
		<copy todir="${web.momsmobile.dist.dir}">
			<fileset basedir="${mobile.build.dir}\WebLayer\MomsWindowsPhone\www">
				<include name="**/*" />
				<exclude name="aspnet_client" />
				<exclude name="**/bin/*.cs" />
				<exclude name="**/bin/*.pdb" />
				<exclude name="**/.svn/*.*" />
				<exclude name="**/**/.svn/*.*" />
				<exclude name="**/**/**/.svn/*.*" />
			</fileset>
		</copy>
		
		<copy todir="${webapi.momsmobile.dist.dir}">
			<fileset basedir="${mobile.build.dir}\Services\MomsMobileWebApi">
				<include name="Global.*" />
				<include name="**/bin/*.*" />
			    <include name="Web.config*" />							
			</fileset>
		</copy>
				
		<!-- Cleanup the directory of not needed files -->		
		<delete dir="${web.momsmobile.dist.dir}\obj" />
		<delete dir="${web.momsmobile.dist.dir}\Properties" />
		<delete dir="${webapi.momsmobile.dist.dir}\obj" />
		<delete dir="${webapi.momsmobile.dist.dir}\Properties" />
	</target> 
	
	<!-- Distribute the Infinity Service app folder -->
	<target name="distribute-InfinityService-app" description="Ready the solution for deployment of internal application">
		<property name="MailLogger.failure.subject" value="Infinity Service - Distribution of service step failed" />
		<copy todir="${app.Infinity.dist.dir}">
			<fileset basedir="${infinity.release.directory}\ServiceHost\">
				<include name="**/*" />
				<exclude name="**/*.pdb" />
				<exclude name="**/*.nlp" />
			</fileset>
		</copy>
		<copy todir="${app.Infinity.dist.dir}">
			<fileset basedir="${infinity.build.dir}\InfinityMOMSServiceSolution\InfinityMOMSServiceHost\">
				<include name="**/*.bat" />
				<exclude name="**/*.pdb" />
				<exclude name="**/*.nlp" />
			</fileset>
		</copy>
		<copy todir="${infinity.dist.AppLayer.dir}">
				<fileset basedir="${infinity.release.directory}\AppLayer">
					<include name="**/*" />
					<exclude name="**/*.pdb" />
				</fileset>
			</copy>
			<copy todir="${infinity.dist.CommonLayer.dir}">
				<fileset basedir="${infinity.release.directory}\Common">
					<include name="**/*" />
					<exclude name="**/*.pdb" />
				</fileset>
			</copy>
			<copy todir="${infinity.dist.ServiceClient.dir}">
				<fileset basedir="${infinity.release.directory}\ServiceClient">
					<include name="**/*" />
					<exclude name="**/*.pdb" />
				</fileset>
			</copy>
			<copy todir="${infinity.dist.DataContract.dir}">
				<fileset basedir="${infinity.release.directory}\DataContract">
					<include name="**/*" />
					<exclude name="**/*.pdb" />
				</fileset>
			</copy>
			<copy todir="${interfaces.dist.ThirdParty.dir}">
				<fileset basedir="${interfaces.release.directory}\ThirdParty">
					<include name="**/*" />
					<exclude name="**/*.pdb" />
				</fileset>
			</copy>
	</target>
	
	<!-- Distribute the Integrity Service app folder -->
	<target name="distribute-IntegrityService-app" description="Ready the solution for deployment of internal application">
		<property name="MailLogger.failure.subject" value="Integrity Service - Distribution of service step failed" />
		<copy todir="${app.Integrity.dist.dir}">
			<fileset basedir="${integrity.release.directory}\ServiceHost\">
				<include name="**/*" />
				<exclude name="**/*.pdb" />
				<exclude name="**/*.nlp" />
			</fileset>
		</copy>
		<copy todir="${app.Integrity.dist.dir}">
			<fileset basedir="${integrity.build.dir}\IntegrityMOMSService\IntegrityMOMSServiceHost\">
				<include name="**/*.bat" />
				<exclude name="**/*.pdb" />
				<exclude name="**/*.nlp" />
			</fileset>
		</copy>
		<copy todir="${integrity.dist.AppLayer.dir}">
				<fileset basedir="${integrity.release.directory}\AppLayer">
					<include name="**/*" />
					<exclude name="**/*.pdb" />
				</fileset>
			</copy>
			<copy todir="${integrity.dist.CommonLayer.dir}">
				<fileset basedir="${integrity.release.directory}\Common">
					<include name="**/*" />
					<exclude name="**/*.pdb" />
				</fileset>
			</copy>
			<copy todir="${integrity.dist.ServiceClient.dir}">
				<fileset basedir="${integrity.release.directory}\ServiceClient">
					<include name="**/*" />
					<exclude name="**/*.pdb" />
				</fileset>
			</copy>
			<copy todir="${integrity.dist.DataContract.dir}">
				<fileset basedir="${integrity.release.directory}\DataContract">
					<include name="**/*" />
					<exclude name="**/*.pdb" />
				</fileset>
			</copy>
			<copy todir="${interfaces.dist.ThirdParty.dir}">
				<fileset basedir="${interfaces.release.directory}\ThirdParty">
					<include name="**/*" />
					<exclude name="**/*.pdb" />
				</fileset>
			</copy>
	</target>
	
	<!-- Distribute the TransSuite Service app folder -->
	<target name="distribute-TransSuiteService-app" description="Ready the solution for deployment of internal application">
		<property name="MailLogger.failure.subject" value="TransSuite Service - Distribution of service step failed" />
		<copy todir="${app.TransSuite.dist.dir}">
			<fileset basedir="${transsuite.release.directory}\ServiceHost\">
				<include name="**/*" />
				<exclude name="**/*.pdb" />
				<exclude name="**/*.nlp" />
			</fileset>
		</copy>
		<copy todir="${app.TransSuite.dist.dir}">
			<fileset basedir="${transsuite.build.dir}\TransSuiteMOMSServiceSolution\TransSuiteMOMSServiceHost\">
				<include name="**/*.bat" />
				<exclude name="**/*.pdb" />
				<exclude name="**/*.nlp" />
			</fileset>
		</copy>
		<copy todir="${transsuite.dist.AppLayer.dir}">
				<fileset basedir="${transsuite.release.directory}\AppLayer">
					<include name="**/*" />
					<exclude name="**/*.pdb" />
				</fileset>
			</copy>
			<copy todir="${transsuite.dist.CommonLayer.dir}">
				<fileset basedir="${transsuite.release.directory}\Common">
					<include name="**/*" />
					<exclude name="**/*.pdb" />
				</fileset>
			</copy>
			<copy todir="${transsuite.dist.ServiceClient.dir}">
				<fileset basedir="${transsuite.release.directory}\ServiceClient">
					<include name="**/*" />
					<exclude name="**/*.pdb" />
				</fileset>
			</copy>
			<copy todir="${transsuite.dist.DataContract.dir}">
				<fileset basedir="${transsuite.release.directory}\DataContract">
					<include name="**/*" />
					<exclude name="**/*.pdb" />
				</fileset>
			</copy>
			<copy todir="${interfaces.dist.ThirdParty.dir}">
				<fileset basedir="${interfaces.release.directory}\ThirdParty">
					<include name="**/*" />
					<exclude name="**/*.pdb" />
				</fileset>
			</copy>
	</target>
	
	
	
	<!-- Ready the solution for deployment of Insight web & webApi -->
	<target name="distribute-Insight-web" description="Ready the solution for deployment of  web and webApi files"> 
		<property name="MailLogger.failure.subject" value="Insight - Distribution of web layer step failed" />
		
		<copy todir="${web.insight.dist.dir}">
			<fileset basedir="${insight.build.dir}\WebLayer\InsightWeb">
				<include name="**/*" />
				<exclude name="aspnet_client" />
				<exclude name="**/bin/*.cs" />
				<exclude name="**/bin/*.pdb" />
				<exclude name="**/.svn/*.*" />
				<exclude name="**/**/.svn/*.*" />
				<exclude name="**/**/**/.svn/*.*" />
			</fileset>
		</copy>
		
		<copy todir="${webapi.insight.dist.dir}">
			<fileset basedir="${insight.build.dir}\WebAPI\InsightWebAPI">
				<include name="Global.*" />
				<include name="**/bin/*.*" />
			    <include name="Web.config*" />	
				<include name="keys.config" />					
			</fileset>
		</copy>
		
		<copy todir="${rpts.insight.dist.dir}">
			<fileset basedir="${insight.build.dir}\WebLayer\InsightReports">
				<include name="**/*" />
				<exclude name="aspnet_client" />
				<exclude name="**/bin/*.cs" />
				<exclude name="**/bin/*.pdb" />
				<exclude name="**/.svn/*.*" />
				<exclude name="**/**/.svn/*.*" />
				<exclude name="**/**/**/.svn/*.*" />
			</fileset>
		</copy>
					
		<!-- Cleanup the directory of not needed files -->		
		<delete dir="${web.insight.dist.dir}\obj" />
		<delete dir="${web.insight.dist.dir}\Properties" />
		<delete dir="${webapi.insight.dist.dir}\obj" />
		<delete dir="${webapi.insight.dist.dir}\Properties" />
		<delete dir="${rpts.insight.dist.dir}\obj" />
		<delete dir="${rpts.insight.dist.dir}\Properties" />
	</target> 
	
	<target name="distribute-OfflineMaps-web" description="Ready the solution for deployment of  OfflineMaps files"> 
		<property name="MailLogger.failure.subject" value="Insight - Distribution of OfflineMaps step failed" />
		<copy todir="${offlinemaps.insight.dist.dir}">
			<fileset basedir="${insight.build.dir}\WebLayer\InsightOfflineMaps">
				<include name="**/*" />
			</fileset>
		</copy>
	</target>
	
	<target name="distribute-InsightMobile-web" description="Ready the solution for deployment of  web and webApi files"> 
		<property name="MailLogger.failure.subject" value="Insight - Distribution of InsightMobile web layer step failed" />
		
		<copy todir="${insight.mobile.dist.dir}\WebLayer\www">
			<fileset basedir="${insight.build.dir}\WebLayer\InsightMobile\www">
				<include name="**/*" />
				<exclude name="aspnet_client" />
				<exclude name="**/bin/*.cs" />
				<exclude name="**/bin/*.pdb" />
				<exclude name="**/.svn/*.*" />
				<exclude name="**/**/.svn/*.*" />
				<exclude name="**/**/**/.svn/*.*" />
			</fileset>
		</copy>
		
		<copy todir="${insight.mobile.dist.dir}\WebAPI">
			<fileset basedir="${insight.build.dir}\WebAPI\InsightExternalWebAPI">
				<include name="**/*" />
				<exclude name="**/*.cs" />
				<exclude name="**/bin/*.cs" />
				<exclude name="**/.svn/*.*" />
				<exclude name="**/**/.svn/*.*" />
				<exclude name="**/**/**/.svn/*.*" />
				<exclude name="**/*.cs" />
				<exclude name="**/App_Data/*.*" />
				<exclude name="**/App_Start/*.*" />
				<exclude name="**/Common/*.*" />
				<exclude name="**/Content/*.*" />
				<exclude name="**/Controllers/*.*" />
				<exclude name="**/Models/*.*" />
				<exclude name="**/obj/*.*" />
				<exclude name="**/Properties/*.*" />
				<exclude name="**/Providers/*.*" />
				<exclude name="**/Results/*.*" />
				<exclude name="Web.Debug.config" />
				<exclude name="Web.Release.config" />
				<exclude name="Project_Readme.html" />
				<exclude name="ExternalWebAPI.csproj" />
			</fileset>
		</copy>
		<copy todir="${insight.mobile.dist.dir}\ThirdParty">
			<fileset basedir="${insight.mobile.release.directory}\ThirdParty">
				<include name="**/*" />
				<exclude name="**/*.pdb" />
			</fileset>
		</copy>
		
	</target>
	
	<!-- Distribute the scheduled tasks app folder -->
	<target name="distribute-MOMS-Tasks" description="Ready the solution for deployment of scheduled tasks">
		<property name="MailLogger.failure.subject" value="MOMS - Distribution of scheduled tasks step failed" />

	<!-- ####################  Deploy Scheduled Tasks  #################### -->


	<!-- NotificationEscalation Task  -->
	<copy todir="${notificationEscalation.task.moms.dist.dir}">
      		<fileset basedir="${moms.release.directory}\ScheduledTasks\MOMSNotificationEscalationTask\${moms.release.subdirectory}">
			<include name="**/*" />
			<exclude name="**/*.pdb" />
		</fileset>
	</copy>

	<!-- EventLoader Task (generic)  -->
	<if test="${MOMSEventLoader=='true'}">
    	<copy todir="${MOMSEventLoader.task.moms.dist.dir}">
      		<fileset basedir="${moms.release.directory}\ScheduledTasks\MOMSEventLoader\${moms.release.subdirectory}">
        		<include name="**/*" />
        		<exclude name="**/*.pdb" />
      		</fileset>
	</copy>
	</if>
	
	<!-- Image Review Event Loader -->
	<if test="${ImageReviewEventLoader=='true'}">
    	<copy todir="${ImageReview.MOMSEventLoader.task.moms.dist.dir}">
      		<fileset basedir="${moms.release.directory}\ScheduledTasks\MOMSEventLoader\${moms.release.subdirectory}">
        		<include name="**/*" />
        		<exclude name="**/*.pdb" />
      		</fileset>
	</copy>
	</if>
	
	<!-- Integrity Event Loader -->
	<if test="${IntegrityEventLoader=='true'}">
    	<copy todir="${Integrity.MOMSEventLoader.task.moms.dist.dir}">
      		<fileset basedir="${moms.release.directory}\ScheduledTasks\MOMSEventLoader\${moms.release.subdirectory}">
        		<include name="**/*" />
        		<exclude name="**/*.pdb" />
      		</fileset>
	</copy>
	</if>
	
	<!-- TransPortal Event Loader -->
	<if test="${TransPortalEventLoader=='true'}">
    	<copy todir="${TransPortal.MOMSEventLoader.task.moms.dist.dir}">
      		<fileset basedir="${moms.release.directory}\ScheduledTasks\MOMSEventLoader\${moms.release.subdirectory}">
        		<include name="**/*" />
        		<exclude name="**/*.pdb" />
      		</fileset>
	</copy>
	</if>
	
	<!-- CPS Event Loader -->
	<if test="${CPSEventLoader=='true'}">
    	<copy todir="${CPS.MOMSEventLoader.task.moms.dist.dir}">
      		<fileset basedir="${moms.release.directory}\ScheduledTasks\MOMSCpsEventLoader\${moms.release.subdirectory}">
        		<include name="**/*" />
        		<exclude name="**/*.pdb" />
      		</fileset>
	</copy>
	</if>
	
	<!-- VOTT Event Loader -->
	<if test="${VOTTEventLoader=='true'}">
    	<copy todir="${VOTT.MOMSEventLoader.task.moms.dist.dir}">
      		<fileset basedir="${moms.release.directory}\ScheduledTasks\MOMSEventLoader\${moms.release.subdirectory}">
        		<include name="**/*" />
        		<exclude name="**/*.pdb" />
      		</fileset>
	</copy>
	</if>
	
	<!--GFI EventLoader Task  -->
	<if test="${property::exists('GFIEventLoader')}">
    	<copy todir="${GFI.MOMSEventLoader.task.moms.dist.dir}">
      		<fileset basedir="${moms.release.directory}\ScheduledTasks\MOMSEventLoader\${moms.release.subdirectory}">
        		<include name="**/*" />
        		<exclude name="**/*.pdb" />
      		</fileset>
    	</copy>	
	</if>
	
	<!--SolarWinds EventLoader Task  -->
	<if test="${SolarWindsFeature=='true'}">
	<if test="${directory::exists('C:\MOMS\ScheduledTasks\MOMSSolarWindsEventLoaderConsole')}" >
    	<copy todir="${SolarWinds.MOMSEventLoader.task.moms.dist.dir}">
      		<fileset basedir="C:\MOMS\ScheduledTasks\MOMSSolarWindsEventLoaderConsole">
        		<include name="**/*" />
        		<exclude name="**/*.pdb" />
      		</fileset>
    	</copy>	
	</if>
	<if test="${not directory::exists('C:\MOMS\ScheduledTasks\MOMSSolarWindsEventLoaderConsole')}" >
    	<copy todir="${SolarWinds.MOMSEventLoader.task.moms.dist.dir}">
      		<fileset basedir="${moms.release.directory}\ScheduledTasks\MOMSEventLoader\${moms.release.subdirectory}">
        		<include name="**/*" />
        		<exclude name="**/*.pdb" />
      		</fileset>
    	</copy>	
	</if>
	</if>
	
	
	<!--WhatsUp EventLoader Task  -->
	<if test="${WhatsUpFeature=='true'}">
    	<copy todir="${WhatsUp.MOMSEventLoader.task.moms.dist.dir}">
      		<fileset basedir="${moms.release.directory}\ScheduledTasks\MOMSEventLoader\${moms.release.subdirectory}">
        		<include name="**/*" />
        		<exclude name="**/*.pdb" />
      		</fileset>
    	</copy>	
	</if>

	<!-- Work Order Messaging Task  -->
	<copy todir="${workOrderMessaging.task.moms.dist.dir}">
		<fileset basedir="${moms.release.directory}\ScheduledTasks\MOMSWorkOrderMessagingTask\${moms.release.subdirectory}">
			<include name="**/*" />
			<exclude name="**/*.pdb" />
		</fileset>
	</copy>


	<!-- Predictive Maintenance Messaging Task  -->
	<copy todir="${MOMSPredictiveMaintenanceNotificationTaskConsole.task.moms.dist.dir}">
		<fileset basedir="${moms.release.directory}\ScheduledTasks\MOMSPredictiveMaintenanceNotificationTaskConsole\${moms.release.subdirectory}">
			<include name="**/*" />
			<exclude name="**/*.pdb" />
		</fileset>
	</copy>

	<!-- Preventive Maintenance Task  -->
	<copy todir="${MOMSPreventiveMaintenanceTask.task.moms.dist.dir}">
		<fileset basedir="${moms.release.directory}\ScheduledTasks\MOMSPreventiveMaintenanceTask\${moms.release.subdirectory}">
			<include name="**/*" />
			<exclude name="**/*.pdb" />
		</fileset>
	</copy>

	<!-- Failure Analysis Predictive Maintenance Work Order Task  -->
	<copy todir="${MOMSFailureAnalysisTaskConsole.task.moms.dist.dir}">
		<fileset basedir="${moms.release.directory}\ScheduledTasks\MOMSFailureAnalysisTaskConsole\${moms.release.subdirectory}">
			<include name="**/*" />
			<exclude name="**/*.pdb" />
		</fileset>
    </copy>

	<!-- InventoryLevelNotificationTask  -->
	<copy todir="${InventoryLevelNotification.task.moms.dist.dir}">
      		<fileset basedir="${moms.release.directory}\ScheduledTasks\MOMSInventoryLevelNotification\${moms.release.subdirectory}">
			<include name="**/*" />
			<exclude name="**/*.pdb" />
		</fileset>
	</copy>
	
	
	<!-- ExternalNotificationTask  -->
	<if test="${MOMSExternalNotifier=='true'}">
	<copy todir="${ExternalNotifier.task.moms.dist.dir}">
			<fileset basedir="${moms.release.directory}\ScheduledTasks\MOMSExternalNotifier\${moms.release.subdirectory}">
			<include name="**/*" />
			<exclude name="**/*.pdb" />
		</fileset>
	</copy>
	</if>
	
	<!-- ActiveDirectoryPollingTask  -->
	<if test="${ADPollingTask=='true'}"> 
	<copy todir="${ActiveDirectoryPollingTask.task.moms.dist.dir}">
			<fileset basedir="${moms.release.directory}\ScheduledTasks\MOMSActiveDirectoryPolling\${moms.release.subdirectory}">
			<include name="**/*" />
			<exclude name="**/*.pdb" />
		</fileset>
	</copy>
	</if>	
	
	<!-- MOMSTrafficDataLoader only used for new Product so check if InsightFeature is included  -->
	<if test="${TrafficLoaderTask=='true'}">
	<copy todir="${MOMSTrafficDataLoader.task.moms.dist.dir}">
			<fileset basedir="${moms.release.directory}\ScheduledTasks\MOMSTrafficDataLoader\${moms.release.subdirectory}">
			<include name="**/*" />
			<exclude name="**/*.pdb" />
		</fileset>
	</copy>
	</if>
	</target>

	<!-- Distribute the reports resources -->
	<target name="distribute-reports-resources" description="Copy the resource files to the SQL server bin directory">
		<property name="MailLogger.failure.subject" value="MOMS - Reports resources deployment failed" />
		
		<copy todir="${moms.resources.dir}">
			<fileset basedir="${moms.release.directory}\CommonLayer\{moms.release.subdirectory}">
				<include name="**/*tcore.MOMSResources.resources*" />
				<exclude name="**/*.pdb" />
			</fileset>
		</copy>

	</target>

	
	<!-- Deploy/Copy files and directories to the destination machines -->
	<target name="deploy-MOMS" description="">
		<property name="MailLogger.failure.subject" value="MOMS - Deploy files to the ${env} ${deployment.type} JUMP servers step failed" />	
		<property name="MailLogger.success.subject" value="MOMS ${env} ${deployment.type} - Step 4 Deploy Successfully Completed." />	
		<echo message="****** Distribution Target is ${env} *******" />		
		<exec program="Deploy.bat" failonerror="true" >
		<arg value="${base.dir}" />
		<arg value="${deployment.server}" />
		<arg value="${deployment.user}" />
		<arg value="${deployment.password}" />
		<arg value="${jump.drive}" />
		<arg value="${dated.deployment.dir}" />
		<arg value="${create_db}" />
		</exec>
	</target>
	
	<target name="deploy-Jump" description="">
		<property name="MailLogger.failure.subject" value="MOMS ${env} ${deployment.type}  - Jump Deploy files to the ${env} servers step failed" />	
		<property name="MailLogger.success.subject" value="MOMS ${env} ${deployment.type} - Step 5 Jump Deploy Successfully Completed." />	
		<echo message="****** Distribution Target is ${env} *******" />		
		<exec program="DeployJump.bat" failonerror="true" >
		<arg value="${base.dir}" />
		<arg value="${moms.app.server}" />
		<arg value="${deployment.user}" />
		<arg value="${deployment.password}" />
		<arg value="${deployment.drive}" />
		<arg value="${dated.deployment.dir}" />
		<arg value="${jump.drive}" />
		<arg value="${moms.db.server}" />
		<arg value="${InsightMobileFeature}" />
		<arg value="${insight.mobile.server}" />
		<arg value="${moms.report.server}" />
		<arg value="${sqlserver.reports.drive}" />
		<arg value="${sqlserver.data.drive}" />
		<arg value="${create_db}" />
		</exec>
	</target>
	
	<!-- Distribute the Mobile internal app folder -->
	<target name="distribute-Report-Deployer" description="Copy the Reports Deployer exe and config">
		<property name="MailLogger.failure.subject" value="Reports Deployer files - Distribution of files failed" />	
		<copy todir="${base.dir}">
			<fileset>
				<include name="ReportsDeployer.exe" />
				<include name="ReportsDeployer.exe.config" />
			</fileset>
		</copy>
	</target>
	
	
	<target name="autobuild-error" description="Send failure mail for autobuild">
		<property name="MailLogger.failure.subject" value="MOMS/Insight - ${env} Autobuild failure" />
		<property name="MailLogger.failure.to" value="${autobuild.email.address}" />	
		<property name="MailLogger.from" value="${autobuild.email.address.from}" />	
				<fail>Autobuild failure.  Check the log Autobuild log.</fail>
	</target>
	
	
	<target name="autobuild-complete" description="Send success mail for autobuild">
		<property name="MailLogger.success.subject" value="MOMS/Insight - ${env} Autobuild SUCCESS!!" />
		<property name="MailLogger.success.to" value="${autobuild.email.address}" />
		<property name="MailLogger.from" value="${autobuild.email.address.from}" />	
	</target>
	
		
	<target name="autobuild-execute" description="Execute Autobuild with DB creation">
		<property name="MailLogger.failure.subject" value="ERROR: ${env} ${deployment.type} MOMS/Insight - Autobuild with DB failed in execution" />
		<property name="MailLogger.failure.to" value="${autobuild.email.address}" />	
		<property name="MailLogger.success.subject" value="SUCCESS: ${env} ${deployment.type} MOMS/Insight - Autobuild with DB creation Succeeded" />	
		<property name="MailLogger.success.to" value="${autobuild.email.address}" />	
		<property name="MailLogger.from" value="${autobuild.email.address.from}" />	
		<exec program="Autobuild.bat" failonerror="true">
		<arg value="${moms.app.server}" />
		<arg value="${env}" />
		<arg value="${deployment.type}" />
		<arg value="${dated.deployment.dir}" />
		<arg value="" />
		<arg value="${InfinityFeature}" />
		<arg value="${ARCSFeature}" />
		<arg value="${SolarWindsFeature}" />
		<arg value="${WhatsUpFeature}" />
		</exec>
	</target>
	
	<target name="autobuild-execute-nodb" description="Execute Autobuild with no Database Creation">
		<property name="MailLogger.failure.subject" value="ERROR: ${env} ${deployment.type} MOMS/Insight Autobuild with no DB failed in execution" />	
		<property name="MailLogger.failure.to" value="${autobuild.email.address}" />	
		<property name="MailLogger.success.subject" value="SUCCESS: ${env} ${deployment.type} MOMS/Insight - Autobuild with no DB Succeeded" />	
		<property name="MailLogger.success.to" value="${autobuild.email.address}" />	
		<property name="MailLogger.from" value="${autobuild.email.address.from}" />		
		<exec program="Autobuild.bat" failonerror="true">
		<arg value="${moms.app.server}" />
		<arg value="${env}" />
		<arg value="${deployment.type}" />
		<arg value="${dated.deployment.dir}" />
		<arg value="NODB" />
		<arg value="${InfinityFeature}" />
		<arg value="${ARCSFeature}" />
		<arg value="${IntegrityFeature}" />
		<arg value="${SolarWindsFeature}" />
		<arg value="${WhatsUpFeature}" />
		</exec>
	</target>
	
	 <!-- Stops all services -->
	<target name="Stop-ServiceHost" description="Stops MOMS Services" >
		<property name="MailLogger.failure.subject" value="MOMS ${deployment.environment} - MOMS Services Failed to Stop" />		
			<echo message="-i-: 1-Call servicecontoller" />
		<servicecontroller action="Stop" machine="${moms.app.server}" service="MOMSExternalService" failonerror="${ratout.service.errors}"  timeout="1000000"/>
		<if test="${MobileFeature=='true'}">
		<servicecontroller action="Stop" machine="${moms.app.server}" service="MOMSMobileService" failonerror="${ratout.service.errors}"  timeout="1000000"/>
		</if>
		<servicecontroller action="Stop" machine="${moms.app.server}" service="MOMSService" failonerror="${ratout.service.errors}"  timeout="1000000"/>
		<if test="${TransSuiteFeature=='true'}">
		<servicecontroller action="Stop" machine="${moms.app.server}" service="TransSuiteMOMSService" failonerror="${ratout.service.errors}"  timeout="1000000"/>
		</if>
		<if test="${InfinityFeature=='true'}">
		<servicecontroller action="Stop" machine="${moms.app.server}" service="InfinityMOMSService" failonerror="${ratout.service.errors}"  timeout="1000000"/>
		</if>
		<if test="${IntegrityFeature=='true'}">
		<servicecontroller action="Stop" machine="${moms.app.server}" service="IntegrityMOMSService" failonerror="${ratout.service.errors}"  timeout="1000000"/>
		</if>
		<if test="${GPSFeature=='true'}">
		<servicecontroller action="Stop" machine="${moms.app.server}" service="MOMSGPSLocatorService" failonerror="${ratout.service.errors}"  timeout="1000000"/>
		</if>
			<echo message="-i-: 3-End Stop-ServiceHost" />
	</target>
	
	<!-- Starts the WCF service host on the primary and secondary app servers -->
	<target name="Start-ServiceHost" description="Starts MOMS Services" >
		<property name="MailLogger.failure.subject" value="MOMS ${deployment.environment} - MOMS Services Failed to Start" />
		<servicecontroller action="Start" machine="${moms.app.server}" service="MOMSService" failonerror="${ratout.service.errors}"  timeout="1000000"/>
		<servicecontroller action="Start" machine="${moms.app.server}" service="MOMSExternalService" failonerror="${ratout.service.errors}"  timeout="1000000"/>   
		<if test="${MobileFeature=='true'}">
		<servicecontroller action="Start" machine="${moms.app.server}" service="MOMSMobileService" failonerror="${ratout.service.errors}"  timeout="1000000"/>
		</if>
		<if test="${TransSuiteFeature=='true'}">
		<servicecontroller action="Start" machine="${moms.app.server}" service="TransSuiteMOMSService" failonerror="${ratout.service.errors}"  timeout="1000000"/>
		</if>
		<if test="${InfinityFeature=='true'}">
		<servicecontroller action="Start" machine="${moms.app.server}" service="InfinityMOMSService" failonerror="${ratout.service.errors}"  timeout="1000000"/>
		</if>
		<if test="${IntegrityFeature=='true'}">
		<servicecontroller action="Start" machine="${moms.app.server}" service="IntegrityMOMSService" failonerror="${ratout.service.errors}"  timeout="1000000"/>
		</if>
		<if test="${GPSFeature=='true'}">
		<servicecontroller action="Start" machine="${moms.app.server}" service="MOMSGPSLocatorService" failonerror="${ratout.service.errors}"  timeout="1000000"/>
		</if>
		<servicecontroller action="Start" machine="${moms.app.server}" service="ASP.NET State Service" failonerror="${ratout.service.errors}"  timeout="1000000"/>
	</target>
	
	
	<target name="unzip-packages" description="Unzip packages for official Insight builds">
		<property name="MailLogger.failure.subject" value="ERROR: ${env} ${deployment.type} MOMS/Insight - Unzip failed" />	
		<property name="MailLogger.success.subject" value="SUCCESS: ${env} ${deployment.type} MOMS/Insight - Unzip Succeeded" />	
		<exec program="UnzipPackages.bat" failonerror="true">
		<arg value="${moms.app.server}" />
		<arg value="${env}" />
		<arg value="${deployment.type}" />
		<arg value="${deployment.drive}" />
		<arg value="${dated.deployment.dir}" />
		<arg value="${insight.mobile.server}" />
		<arg value="${InsightMobileFeature}" />
		<arg value="${deployment.user}" />
		<arg value="${deployment.password}" />
		<arg value="${moms.db.server}" />
		<arg value="${moms.report.server}" />
		<arg value="${sqlserver.reports.drive}" />
		<arg value="${sqlserver.data.drive}" />
		<arg value="${create_db}" />		
		</exec>
	</target>
	
	<target name="pre-install-sleep" description="Put the application to sleep (stop tasks/services)">
		<property name="MailLogger.failure.subject" value="ERROR: ${env} ${deployment.type}  MOMS/Insight - Pre-Install Sleep failed" />
		<property name="MailLogger.success.subject" value="SUCCESS: ${env} ${deployment.type}  MOMS/Insight - Pre-Install Sleep Succeeded" />	
		<exec program="PreInstallSleep.bat" failonerror="true">
		<arg value="${moms.app.server}" />
		<arg value="${env}" />
		<arg value="${deployment.type}" />
		<arg value="${IntegrityEventLoader}" />
		<arg value="${integrity.eventloader.exe.server}" />
		<arg value="${insight.mobile.server}" />
		<arg value="${InsightMobileFeature}" />
		<arg value="${ADPollingTask}" />
		<arg value="${WorkOrderMessagingFeature}" />
		<arg value="${ActiveDirectoryFeature}" />
		<arg value="${transportal.web.alias}" />
		</exec>
	</target>
	
	
	<target name="install-build" description="Install for official Insight builds">
		<property name="MailLogger.failure.subject" value="ERROR: ${env} ${deployment.type} MOMS/Insight - Install failed" />
		<property name="MailLogger.success.subject" value="SUCCESS: ${env} ${deployment.type} MOMS/Insight - Install Succeeded" />	
		<exec program="InstallBuild.bat" failonerror="true">
		<arg value="${moms.app.server}" />
		<arg value="${env}" />
		<arg value="${deployment.type}" />
		<arg value="${deployment.drive}" />
		<arg value="${dated.deployment.dir}" />
		<arg value="${sqlserver.reports.drive}" />
		<arg value="${sqlserver.rpt.version}" />
		<arg value="${IntegrityEventLoader}" />
		<arg value="${integrity.eventloader.exe.server}" />
		<arg value="${insight.mobile.server}" />
		<arg value="${InsightMobileFeature}" />
		<arg value="${moms.db.server}" />
		<arg value="${moms.report.server}" />
		</exec>
	</target>
	
	<target name="deploy-reports" description="Install for official Insight builds">
		<property name="MailLogger.failure.subject" value="ERROR: ${env} ${deployment.type} MOMS/Insight - Deploy Reports failed" />
		<property name="MailLogger.success.subject" value="SUCCESS: ${env} ${deployment.type} MOMS/Insight - Deploy Reports Succeeded" />	
		<exec program="DeployReports.bat" failonerror="true">
		<arg value="${moms.report.server}" />
		<arg value="${env}" />
		<arg value="${deployment.type}" />
		<arg value="${deployment.drive}" />
		<arg value="${dated.deployment.dir}" />
		<arg value="${sqlserver.reports.drive}" />
		<arg value="${sqlserver.rpt.version}" />
		<arg value="${moms.report.server}" />		
		</exec>
	</target>
	
	<target name="post-install-startup" description="Run after installation and after DBA finished DB deployment">
		<property name="MailLogger.failure.subject" value="ERROR: ${env} ${deployment.type} MOMS/Insight - Post-Install Startup failed" />
		<property name="MailLogger.success.subject" value="SUCCESS: ${env} ${deployment.type} MOMS/Insight - Post-Install Startup Succeeded" />	
		<exec program="PostInstallStartup.bat" failonerror="true">
		<arg value="${moms.app.server}" />
		<arg value="${env}" />
		<arg value="${deployment.type}" />
		<arg value="${IntegrityEventLoader}" />
		<arg value="${integrity.eventloader.exe.server}" />
		<arg value="${insight.mobile.server}" />
		<arg value="${InsightMobileFeature}" />
		<arg value="${ADPollingTask}" />
		<arg value="${TransPortalFeature}" />
		<arg value="${transportal.db.server}" />
		<arg value="${dated.deployment.dir}" />
		<arg value="${release.version}" />
		<arg value="${deployment.drive}" />
		<arg value="${WorkOrderMessagingFeature}" />
		<arg value="${transportal.db.listener}" />
		<arg value="${moms.db.server}" />
		<arg value="${db.user}" />
		<arg value="${db.password}" />		
		</exec>
	</target>
	
	<target name="create-db" description="DB creation option to run if needed">
		<property name="MailLogger.failure.subject" value="ERROR: ${env} ${deployment.type} MOMS/Insight - DB Creation failed" />
		<property name="MailLogger.success.subject" value="SUCCESS: ${env} ${deployment.type} MOMS/Insight - DB creation Succeeded" />	
		<exec program="CreateDB.bat" failonerror="true">
		<arg value="${moms.db.server}" />
		<arg value="${env}" />
		<arg value="${deployment.type}" />
		<arg value="${deployment.drive}" />
		<arg value="${dated.deployment.dir}" />
		<arg value="${InfinityFeature}" />
		<arg value="${ARCSFeature}" />
		<arg value="${ImageReviewEventLoader}" />
		<arg value="${IFXFeature}" />
		<arg value="${TransSuiteFeature}" />
		<arg value="${SolarWindsFeature}" />
		<arg value="${IntegrityEventLoader}" />
		<arg value="${CPSEventLoader}" />
		<arg value="${VOTTEventLoader}" />
		<arg value="${MDIntegrityFeature}" />
		<arg value="${DBAlwaysOnFeature}" />
		<arg value="${WhatsUpFeature}" />
		<arg value="${moms.db.server}" />
		</exec>
	</target>
	
	
	<target name="stop-web" description="Put the application to sleep (stop tasks/services)">
	<property name="MailLogger.failure.subject" value="ERROR: ${env} ${deployment.type}  Insight  - Stop IIS Web failed" />
	<property name="MailLogger.success.subject" value="SUCCESS: ${env} ${deployment.type} Insight - Stop IIS Web Succeeded" />	
	<servicecontroller action="Stop" machine="${iisserver}" service="World Wide Web Publishing Service" failonerror="${ratout.service.errors}"  timeout="1000000"/>
	<servicecontroller action="Stop" machine="${iisserver}" service="Net.Tcp Listener Adapter" timeout="1000000" failonerror="false" />
	<servicecontroller action="Stop" machine="${iisserver}" service="NetTcpActivator" timeout="1000000" failonerror="false" />
	<servicecontroller action="Stop" machine="${iisserver}" service="Windows Process Activation Service" failonerror="${ratout.service.errors}"  timeout="1000000"/>
	</target>
	
	
	<target name="start-web" description="Put the application to sleep (stop tasks/services)">
		<property name="MailLogger.failure.subject" value="ERROR: ${env} ${deployment.type}  Insight - Start IIS WEB failed" />
		<property name="MailLogger.success.subject" value="SUCCESS: ${env} ${deployment.type}  Insight - IIS WEB Succeeded" />	
		<servicecontroller action="Start" machine="${iisserver}" service="Windows Process Activation Service" failonerror="${ratout.service.errors}"  timeout="1000000"/>
		<servicecontroller action="Start" machine="${iisserver}" service="Net.Tcp Listener Adapter" failonerror="false"  timeout="1000000"/>
		<servicecontroller action="Start" machine="${iisserver}" service="NetTcpActivator" failonerror="false"  timeout="1000000"/>
		<servicecontroller action="Start" machine="${iisserver}" service="World Wide Web Publishing Service" failonerror="${ratout.service.errors}"  timeout="1000000"/>
	</target>
	
	
	<target name="encrypt-configs" description="Encrypt Config files (for PROD)">
		<property name="MailLogger.failure.subject" value="ERROR: ${env} ${deployment.type} MOMS/Insight - Encrypt failed" />
		<property name="MailLogger.success.subject" value="SUCCESS: ${env} ${deployment.type} MOMS/Insight -  Encrypt Succeeded" />	
		<exec program="EncryptConfigs.bat" failonerror="true">
		<arg value="${moms.app.server}" />
		<arg value="${deployment.drive}" />
		</exec>
	</target>
	
	<target name="decrypt-configs" description="Decrypt Config files (for PROD)">
		<property name="MailLogger.failure.subject" value="ERROR: ${env} ${deployment.type} MOMS/Insight - Decrypt failed" />
		<property name="MailLogger.success.subject" value="SUCCESS: ${env} ${deployment.type} MOMS/Insight -  Decrypt Succeeded" />	
		<exec program="DecryptConfigs.bat" failonerror="true">
		<arg value="${moms.app.server}" />
		<arg value="${deployment.drive}" />
		</exec>
	</target>
	
	<target name="cleanup-deployment" description="Encrypt Config files (for PROD)">
		<property name="MailLogger.failure.subject" value="ERROR: ${env} ${deployment.type} Insight - Deployment Cleanup Succeeded" />
		<property name="MailLogger.success.subject" value="SUCCESS: ${env} ${deployment.type} Insight -  Deployment Cleanup Succeeded" />	
		<exec program="CleanUpDeployment.bat" failonerror="true">
		<arg value="${moms.app.server}" />
		<arg value="${deployment.drive}" />
		<arg value="${deployment.user}" />
		<arg value="${deployment.password}" />
		</exec>
	</target>
	
</project>
