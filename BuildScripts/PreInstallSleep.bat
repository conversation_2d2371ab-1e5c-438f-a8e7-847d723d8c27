@ECHO OFF

set NOW=%date:~10,4%-%date:~4,2%-%date:~7,2%
Set CURRENTDIR=%CD%
SET FPSERVER=%1
SET APPSERVER=%2
SET PROJECT=%3
SET ENVTYPE=%4
SET FINGERPRINTFEATURE=%5
SET HOCPFEATURE=%6
SET WEBSERVER=%7
SET CLUSTERNODE=%8
SET IFXFEATURE=%9
SHIFT
SHIFT
SHIFT
SHIFT
SHIFT
SHIFT
SHIFT
SHIFT
SHIFT
SET IFXSERVER=%1
SET ADPOLLINGTASK=%2
SET IRRFEATURE=%3
SET IMAGECLIENTFEATURE=%4
SET VOTTTASKFEATURE=%5
SET EMAILTASKFEATURE=%6
SET TRANSSERVER=%7
SET VEHICLEDETECTIONTASK=%8
SET HUMANREADSERVER=%9
SHIFT
SHIFT
SHIFT
SHIFT
SHIFT
SHIFT
SHIFT
SHIFT
SHIFT
SET HRFEATURE=%1
SET MANUALRESULTSFEATURE=%2
SET HOCPSERVER=%3
SET IRRSERVER=%4

echo FPSERVER %FPSERVER%
echo APPSERVER %APPSERVER%
echo WEBSERVER %WEBSERVER%
echo TRANSSERVER %TRANSSERVER%


echo TransPortal PRe-Install sleep is starting at %NOW%
echo ----------------------------------


if "%HRFEATURE%"=="true" GOTO HRFEATURE
if "%CLUSTERNODE%"=="02" GOTO StopServices
if "%IFXFEATURE%"=="false"  GOTO StopServices

:DisableIFXTasks
if exist \\%IFXSERVER%\C$\IFX\ScheduledTasks\IFXImageReviewSendTrans (C:\Windows\system32\schtasks.exe /S %IFXSERVER% /End /TN  IFXImageReviewSendTrans)
if exist \\%IFXSERVER%\C$\IFX\ScheduledTasks\IFXImageReviewSendTrans (C:\Windows\system32\schtasks.exe /S %IFXSERVER% /Change /TN IFXImageReviewSendTrans /DISABLE)
if exist \\%IFXSERVER%\C$\IFX\ScheduledTasks\IFXImageReviewSendTransRetries (C:\Windows\system32\schtasks.exe /S %IFXSERVER% /End /TN  IFXImageReviewSendTransRetries)
if exist \\%IFXSERVER%\C$\IFX\ScheduledTasks\IFXImageReviewSendTransRetries (C:\Windows\system32\schtasks.exe /S %IFXSERVER% /Change /TN IFXImageReviewSendTransRetries /DISABLE)

rem BAIFA has multiples of this task so lets stop them all
if exist \\%IFXSERVER%\C$\CAMS\ScheduledTasks\IFXImageReviewSendTrans (C:\Windows\system32\schtasks.exe /S %IFXSERVER% /End /TN  IFXImageReviewSendTrans)
if exist \\%IFXSERVER%\C$\CAMS\ScheduledTasks\IFXImageReviewSendTrans (C:\Windows\system32\schtasks.exe /S %IFXSERVER% /Change /TN IFXImageReviewSendTrans /DISABLE)
if exist \\%IFXSERVER%\C$\CAMS\ScheduledTasks\IFXImageReviewSendTrans2 (C:\Windows\system32\schtasks.exe /S %IFXSERVER% /End /TN  IFXImageReviewSendTrans2)
if exist \\%IFXSERVER%\C$\CAMS\ScheduledTasks\IFXImageReviewSendTrans2 (C:\Windows\system32\schtasks.exe /S %IFXSERVER% /Change /TN IFXImageReviewSendTrans2 /DISABLE)
if exist \\%IFXSERVER%\C$\CAMS\ScheduledTasks\IFXImageReviewSendTrans3 (C:\Windows\system32\schtasks.exe /S %IFXSERVER% /End /TN  IFXImageReviewSendTrans3)
if exist \\%IFXSERVER%\C$\CAMS\ScheduledTasks\IFXImageReviewSendTrans3 (C:\Windows\system32\schtasks.exe /S %IFXSERVER% /Change /TN IFXImageReviewSendTrans3 /DISABLE)
if exist \\%IFXSERVER%\C$\CAMS\ScheduledTasks\IFXImageReviewSendTrans4 (C:\Windows\system32\schtasks.exe /S %IFXSERVER% /End /TN  IFXImageReviewSendTrans4)
if exist \\%IFXSERVER%\C$\CAMS\ScheduledTasks\IFXImageReviewSendTrans4 (C:\Windows\system32\schtasks.exe /S %IFXSERVER% /Change /TN IFXImageReviewSendTrans4 /DISABLE)
rem VTA has its own multiples too
if exist \\%IFXSERVER%\C$\IFX\ScheduledTasks\IFXImageReviewSendTrans-1100 (C:\Windows\system32\schtasks.exe /S %IFXSERVER% /End /TN  IFXImageReviewSendTrans-1100)
if exist \\%IFXSERVER%\C$\IFX\ScheduledTasks\IFXImageReviewSendTrans-1100 (C:\Windows\system32\schtasks.exe /S %IFXSERVER% /Change /TN IFXImageReviewSendTrans-1100 /DISABLE)
if exist \\%IFXSERVER%\C$\IFX\ScheduledTasks\IFXImageReviewSendTrans-1200 (C:\Windows\system32\schtasks.exe /S %IFXSERVER% /End /TN  IFXImageReviewSendTrans-1200)
if exist \\%IFXSERVER%\C$\IFX\ScheduledTasks\IFXImageReviewSendTrans-1200 (C:\Windows\system32\schtasks.exe /S %IFXSERVER% /Change /TN IFXImageReviewSendTrans-1200 /DISABLE)
if exist \\%IFXSERVER%\C$\IFX\ScheduledTasks\IFXImageReviewSendTrans-1300 (C:\Windows\system32\schtasks.exe /S %IFXSERVER% /End /TN  IFXImageReviewSendTrans-1300)
if exist \\%IFXSERVER%\C$\IFX\ScheduledTasks\IFXImageReviewSendTrans-1300 (C:\Windows\system32\schtasks.exe /S %IFXSERVER% /Change /TN IFXImageReviewSendTrans-1300 /DISABLE)
if exist \\%IFXSERVER%\C$\IFX\ScheduledTasks\IFXImageReviewSendTrans-1400 (C:\Windows\system32\schtasks.exe /S %IFXSERVER% /End /TN  IFXImageReviewSendTrans-1100)
if exist \\%IFXSERVER%\C$\IFX\ScheduledTasks\IFXImageReviewSendTrans-1400 (C:\Windows\system32\schtasks.exe /S %IFXSERVER% /Change /TN IFXImageReviewSendTrans-1400 /DISABLE)


:StopServices
SET STEP="Stop Services"
echo Step %STEP% commencing...  ---------
if %ERRORLEVEL% NEQ 0 GOTO :error
NAnt.exe -buildfile:ImageReview_build.build -D:iisserver=%WEBSERVER% stop-web

if %ERRORLEVEL% NEQ 0 GOTO :error
NAnt.exe -buildfile:ImageReview_build.build -D:iisserver=%APPSERVER% stop-web



if %ERRORLEVEL% NEQ 0 GOTO :error
if NOT "%APPSERVER%" == "%TRANSSERVER%" ( NAnt.exe -buildfile:ImageReview_build.build -D:iisserver=%TRANSSERVER% stop-web)

if %ERRORLEVEL% NEQ 0 GOTO :error
if "%FINGERPRINTFEATURE%" == "true" ( NAnt.exe -buildfile:ImageReview_build.build -D:iisserver=%FPSERVER% stop-web)



if %ERRORLEVEL% NEQ 0 GOTO :error
if NOT "%HOCPSERVER%" == "%APPSERVER%" ( NAnt.exe -buildfile:ImageReview_build.build -D:iisserver=%TRANSSERVER% stop-web)


if %ERRORLEVEL% NEQ 0 GOTO :error
NAnt.exe -buildfile:ImageReview_build.build -D:iisserver=%IRRSERVER% stop-web

if %ERRORLEVEL% NEQ 0 GOTO :error
NAnt.exe -buildfile:ImageReview_build.build -D:iisserver=%HOCPSERVER% stop-web

if %ERRORLEVEL% NEQ 0 GOTO :error

echo Step %STEP% completed...  ---------


GOTO END


:HRFEATURE
if "%HRFEATURE%" == "true" ( NAnt.exe -buildfile:ImageReview_build.build -D:iisserver=%HUMANREADSERVER% stop-web)
if %ERRORLEVEL% NEQ 0 GOTO :error
GOTO END

:error
echo ------- AN ERROR OCCURED DURING THIS STEP: %STEP% ------
echo ------- AN ERROR OCCURED DURING THIS STEP: %STEP% ------

exit /b 1

:END
echo -- SLEEP of Image Review is complete
echo -- SLEEP of Image Review is complete

exit /b 0
