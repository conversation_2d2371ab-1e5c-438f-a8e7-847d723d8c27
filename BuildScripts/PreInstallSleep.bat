@ECHO OFF

set NOW=%date:~10,4%-%date:~4,2%-%date:~7,2%
Set CURRENTDIR=%CD%
SET MOMSSERVER=%1
SET PROJECT=%2
SET ENVTYPE=%3
SET INTEGRITY=%4
SET INTEGRITYSERVER=%5
SET INSIGHTMOBILESERVER=%6
SET INSIGHTMOBILE=%7
SET ADPOLLINGTASK=%8
SET WOMESSAGING=%9
SHIFT
SHIFT
SHIFT
SHIFT
SHIFT
SHIFT
SHIFT
SHIFT
SHIFT
SET ACTIVEDIRECTORY=%1
SET TRANSPORTALSERVER=%2


echo %MOMSSERVER%
echo ad polling %ADPOLLINGTASK%

echo Insight PRe-Install sleep is starting at %NOW%
echo ----------------------------------



:DisableMOMSTasks
SET STEP="Disable MOMS Tasks"
echo Step %STEP% commencing...  ---------
echo -----------------------------------
echo Disabling MOMS scheduled tasks...
echo -----------------------------------
rem if "%WOMESSAGING%" == "true"  (C:\Windows\system32\schtasks.exe /S %MOMSSERVER% /End /TN  MOMSWorkOrderMessagingTask )
rem if "%WOMESSAGING%" == "true"  (C:\Windows\system32\schtasks.exe /S %MOMSSERVER% /Change /TN MOMSWorkOrderMessagingTask /DISABLE )
rem C:\Windows\system32\schtasks.exe /S %MOMSSERVER% /End /TN  MOMSNotificationEscalation
rem C:\Windows\system32\schtasks.exe /S %MOMSSERVER% /Change /TN MOMSNotificationEscalation /DISABLE
rem C:\Windows\system32\schtasks.exe /S %MOMSSERVER% /End /TN  MOMSPredictiveMaintenanceNotificationTask
rem C:\Windows\system32\schtasks.exe /S %MOMSSERVER% /Change /TN MOMSPredictiveMaintenanceNotificationTask /DISABLE
rem C:\Windows\system32\schtasks.exe /S %MOMSSERVER% /End /TN  MOMSPreventiveMaintenanceTask
rem C:\Windows\system32\schtasks.exe /S %MOMSSERVER% /Change /TN MOMSPreventiveMaintenanceTask   /DISABLE
rem C:\Windows\system32\schtasks.exe /S %MOMSSERVER% /End /TN  MOMSFailureAnalysis
rem C:\Windows\system32\schtasks.exe /S %MOMSSERVER% /Change /TN MOMSFailureAnalysis /DISABLE
rem C:\Windows\system32\schtasks.exe /S %MOMSSERVER% /End /TN  MOMSInventoryLevelNotification
rem C:\Windows\system32\schtasks.exe /S %MOMSSERVER% /Change /TN MOMSInventoryLevelNotification /DISABLE

if "%WOMESSAGING%" == "true"  psexec -accepteula \\%MOMSSERVER% cmd /c if exist C:\MOMS\ScheduledTasks\MOMSWorkOrderMessagingTask\ (C:\Windows\system32\schtasks.exe /S %MOMSSERVER% /End /TN  MOMSWorkOrderMessagingTask)
if "%WOMESSAGING%" == "true"  psexec -accepteula \\%MOMSSERVER% cmd /c if exist C:\MOMS\ScheduledTasks\MOMSWorkOrderMessagingTask\ (C:\Windows\system32\schtasks.exe /S %MOMSSERVER% /Change /TN MOMSWorkOrderMessagingTask /DISABLE)

psexec -accepteula \\%MOMSSERVER% cmd /c if exist C:\MOMS\ScheduledTasks\MOMSFailureAnalysisTaskConsole\ (C:\Windows\system32\schtasks.exe /S %MOMSSERVER% /End /TN  MOMSFailureAnalysis)
psexec -accepteula \\%MOMSSERVER% cmd /c if exist C:\MOMS\ScheduledTasks\MOMSFailureAnalysisTaskConsole\ (C:\Windows\system32\schtasks.exe /S %MOMSSERVER% /Change /TN MOMSFailureAnalysis /DISABLE)

psexec -accepteula \\%MOMSSERVER% cmd /c if exist C:\MOMS\ScheduledTasks\MOMSInventoryLevelNotification\ (C:\Windows\system32\schtasks.exe /S %MOMSSERVER% /End /TN  MOMSInventoryLevelNotification)
psexec -accepteula \\%MOMSSERVER% cmd /c if exist C:\MOMS\ScheduledTasks\MOMSInventoryLevelNotification\ (C:\Windows\system32\schtasks.exe /S %MOMSSERVER% /Change /TN MOMSInventoryLevelNotification /DISABLE)

psexec -accepteula \\%MOMSSERVER% cmd /c if exist C:\MOMS\ScheduledTasks\MOMSNotificationEscalationTask\ (C:\Windows\system32\schtasks.exe /S %MOMSSERVER% /End /TN  MOMSNotificationEscalation)
psexec -accepteula \\%MOMSSERVER% cmd /c if exist C:\MOMS\ScheduledTasks\MOMSNotificationEscalationTask\ (C:\Windows\system32\schtasks.exe /S %MOMSSERVER% /Change /TN MOMSNotificationEscalation /DISABLE)

psexec -accepteula \\%MOMSSERVER% cmd /c if exist C:\MOMS\ScheduledTasks\MOMSPredictiveMaintenanceNotificationTaskConsole\ (C:\Windows\system32\schtasks.exe /S %MOMSSERVER% /End /TN  MOMSPredictiveMaintenanceNotificationTask)
psexec -accepteula \\%MOMSSERVER% cmd /c if exist C:\MOMS\ScheduledTasks\MOMSPredictiveMaintenanceNotificationTaskConsole\ (C:\Windows\system32\schtasks.exe /S %MOMSSERVER% /Change /TN MOMSPredictiveMaintenanceNotificationTask /DISABLE)

psexec -accepteula \\%MOMSSERVER% cmd /c if exist C:\MOMS\ScheduledTasks\MOMSPreventiveMaintenanceTask\ (C:\Windows\system32\schtasks.exe /S %MOMSSERVER% /End /TN  MOMSPreventiveMaintenanceTask)
psexec -accepteula \\%MOMSSERVER% cmd /c if exist C:\MOMS\ScheduledTasks\MOMSPreventiveMaintenanceTask\ (C:\Windows\system32\schtasks.exe /S %MOMSSERVER% /Change /TN MOMSPreventiveMaintenanceTask /DISABLE)

psexec -accepteula \\%MOMSSERVER% cmd /c if exist C:\MOMS\ScheduledTasks\TransPortal_MOMSEventLoader\ (C:\Windows\system32\schtasks.exe /S %MOMSSERVER% /End /TN  TransPortal_MOMSEventLoader)
psexec -accepteula \\%MOMSSERVER% cmd /c if exist C:\MOMS\ScheduledTasks\TransPortal_MOMSEventLoader\ (C:\Windows\system32\schtasks.exe /S %MOMSSERVER% /Change /TN TransPortal_MOMSEventLoader /DISABLE)
psexec -accepteula \\%MOMSSERVER% cmd /c if exist C:\MOMS\ScheduledTasks\CPS_MOMSEventLoader\ (C:\Windows\system32\schtasks.exe /S %MOMSSERVER% /End /TN  CPS_MOMSEventLoader)
psexec -accepteula \\%MOMSSERVER% cmd /c if exist C:\MOMS\ScheduledTasks\CPS_MOMSEventLoader\ (C:\Windows\system32\schtasks.exe /S %MOMSSERVER% /Change /TN CPS_MOMSEventLoader /DISABLE)
psexec -accepteula \\%MOMSSERVER% cmd /c if exist C:\MOMS\ScheduledTasks\VOTT_MOMSEventLoader\ (C:\Windows\system32\schtasks.exe /S %MOMSSERVER% /End /TN  VOTT_MOMSEventLoader)
psexec -accepteula \\%MOMSSERVER% cmd /c if exist C:\MOMS\ScheduledTasks\VOTT_MOMSEventLoader\ (C:\Windows\system32\schtasks.exe /S %MOMSSERVER% /Change /TN VOTT_MOMSEventLoader /DISABLE)
psexec -accepteula \\%MOMSSERVER% cmd /c if exist C:\MOMS\ScheduledTasks\SolarWinds_MOMSEventLoader\ (C:\Windows\system32\schtasks.exe /S %MOMSSERVER% /End /TN  SolarWinds_MOMSEventLoader)
psexec -accepteula \\%MOMSSERVER% cmd /c if exist C:\MOMS\ScheduledTasks\SolarWinds_MOMSEventLoader\ (C:\Windows\system32\schtasks.exe /S %MOMSSERVER% /Change /TN SolarWinds_MOMSEventLoader /DISABLE)
psexec -accepteula \\%MOMSSERVER% cmd /c if exist C:\MOMS\ScheduledTasks\WhatsUp_MOMSEventLoader\ (C:\Windows\system32\schtasks.exe /S %MOMSSERVER% /End /TN  WhatsUp_MOMSEventLoader)
psexec -accepteula \\%MOMSSERVER% cmd /c if exist C:\MOMS\ScheduledTasks\WhatsUp_MOMSEventLoader\ (C:\Windows\system32\schtasks.exe /S %MOMSSERVER% /Change /TN WhatsUp_MOMSEventLoader /DISABLE)

if "%ADPOLLINGTASK%" == "true" (C:\Windows\system32\schtasks.exe /S %MOMSSERVER% /End /TN  MOMSActiveDirectoryPolling)
if "%ADPOLLINGTASK%" == "true" (C:\Windows\system32\schtasks.exe /S %MOMSSERVER% /Change /TN MOMSActiveDirectoryPolling /DISABLE)
if "%ACTIVEDIRECTORY%" == "false" if "%PROJECT%" == "PRODUCT" (C:\Windows\system32\schtasks.exe /S %TRANSPORTALSERVER% /End /TN  TransPortalActiveDirectoryPollingTask)
if "%ACTIVEDIRECTORY%" == "false" if "%PROJECT%" == "PRODUCT" (C:\Windows\system32\schtasks.exe /S %TRANSPORTALSERVER% /Change /TN TransPortalActiveDirectoryPollingTask /DISABLE)

psexec -accepteula \\%MOMSSERVER% cmd /c if exist C:\MOMS\ScheduledTasks\MOMSEventLoader\ (C:\Windows\system32\schtasks.exe /S %MOMSSERVER% /End /TN  MOMSEventLoader)
psexec -accepteula \\%MOMSSERVER% cmd /c if exist C:\MOMS\ScheduledTasks\MOMSEventLoader\ (C:\Windows\system32\schtasks.exe /S %MOMSSERVER% /Change /TN MOMSEventLoader /DISABLE)
psexec -accepteula \\%MOMSSERVER% cmd /c if exist C:\MOMS\ScheduledTasks\ImageReview_MOMSEventLoader\ (C:\Windows\system32\schtasks.exe /S %MOMSSERVER% /End /TN  ImageReview_MOMSEventLoader)
psexec -accepteula \\%MOMSSERVER% cmd /c if exist C:\MOMS\ScheduledTasks\ImageReview_MOMSEventLoader\ (C:\Windows\system32\schtasks.exe /S %MOMSSERVER% /Change /TN ImageReview_MOMSEventLoader /DISABLE)
psexec -accepteula \\%MOMSSERVER% cmd /c if exist C:\MOMS\ScheduledTasks\MOMSExternalNotifier\ (C:\Windows\system32\schtasks.exe /S %MOMSSERVER% /End /TN  MOMSExternalNotification)
psexec -accepteula \\%MOMSSERVER% cmd /c if exist C:\MOMS\ScheduledTasks\MOMSExternalNotifier\ (C:\Windows\system32\schtasks.exe /S %MOMSSERVER% /Change /TN MOMSExternalNotification /DISABLE)
psexec -accepteula \\%MOMSSERVER% cmd /c if exist C:\MOMS\ScheduledTasks\MOMSTrafficDataLoader\ (C:\Windows\system32\schtasks.exe /S %MOMSSERVER% /End /TN  MOMSTrafficDataLoader)
psexec -accepteula \\%MOMSSERVER% cmd /c if exist C:\MOMS\ScheduledTasks\MOMSTrafficDataLoader\ (C:\Windows\system32\schtasks.exe /S %MOMSSERVER% /Change /TN MOMSTrafficDataLoader /DISABLE)

if "%INTEGRITY%" == "true" ( C:\Windows\system32\schtasks.exe /S %INTEGRITYSERVER% /End /TN  Integrity_MOMSEventLoader)
if "%INTEGRITY%" == "true" ( C:\Windows\system32\schtasks.exe /S %INTEGRITYSERVER% /Change /TN Integrity_MOMSEventLoader /DISABLE)

rem C:\Windows\system32\schtasks.exe /S %MOMSSERVER% /End /TN  MOMSExternalNotification
rem C:\Windows\system32\schtasks.exe /S %MOMSSERVER% /Change /TN MOMSExternalNotification /DISABLE
if %ERRORLEVEL% NEQ 0 GOTO :error
echo Step %STEP% completed...  ---------


:StopMOMSServices
SET STEP="Stop MOMS Services"
echo Step %STEP% commencing...  ----------
rem comment out mobile service for now
rem call Autobuild\do_service_stop_locrem1.cmd "MOMS Mobile WCF Internal Service Host" %MOMSSERVER%
rem call Autobuild\do_service_stop_locrem1.cmd "MOMSExternalService" %MOMSSERVER%
rem call Autobuild\do_service_stop_locrem1.cmd "MOMSGPSLocatorService" %MOMSSERVER%
rem call Autobuild\do_service_stop_locrem1.cmd "MOMSTrafficLoaderService" %MOMSSERVER%
rem  Autobuild\do_service_stop_locrem1.cmd "MOMSService" %MOMSSERVER%
rem call Autobuild\do_service_stop_locrem1.cmd "TransSuiteMOMSService" %MOMSSERVER%
rem call Autobuild\do_service_stop_locrem1.cmd "InfinityMOMSService" %MOMSSERVER%
echo Try stopping services through NANT..  ---------
NAnt.exe -buildfile:MOMS_build.build Stop-ServiceHost
if %ERRORLEVEL% NEQ 0 GOTO :error
NAnt.exe -buildfile:MOMS_build.build -D:iisserver=%MOMSSERVER%  stop-web
if %ERRORLEVEL% NEQ 0 GOTO :error
if "%INSIGHTMOBILE%"=="true" (NAnt.exe -buildfile:MOMS_build.build -D:iisserver=%INSIGHTMOBILESERVER% stop-web)
if %ERRORLEVEL% NEQ 0 GOTO :error
echo Step %STEP% completed...  ---------

GOTO END

:error
echo ------- AN ERROR OCCURED DURING THIS STEP: %STEP% ------
echo ------- AN ERROR OCCURED DURING THIS STEP: %STEP% ------

exit /b 1

:END
echo -- SLEEP of Insight is complete
echo -- SLEEP of Insight is complete

exit /b 0
