@ECHO OFF

set NOW=%date:~10,4%-%date:~4,2%-%date:~7,2%
Set CURRENTDIR=%CD%
//SET APPSERVER=%1
SET PROJECT=%2
//SET ENVTYPE=%3
//SET WEBSERVER=%4
//SET SIMULATOR=%5
//SET CLUSTERVALUE=%6
//SET ADPOLLINGTASK=%7
SET EXTSERVER=%8
//SET ZPITASK=%9
SHIFT
SHIFT
SHIFT
SHIFT
SHIFT
SHIFT
SHIFT
SHIFT
SHIFT
//SET APP03SERVER=%1
//SET DMVFeature=%2

echo %APPSERVER%


echo CPS PRe-Install sleep is starting at %NOW%
echo ----------------------------------


if "%CLUSTERVALUE%"=="02" GOTO StopIIS
:DisableCPSTasks
SET STEP="Disable CPS Tasks"
echo Step %STEP% commencing...  ---------
echo -----------------------------------
echo Disable scheduled tasks for CPS....
echo -----------------------------------
if "%ADPOLLINGTASK%" == "true"  (C:\Windows\system32\schtasks.exe /S %APPSERVER% /End /TN  ActiveDirectoryPollingTask )
if "%ADPOLLINGTASK%" == "true"  (C:\Windows\system32\schtasks.exe /S %APPSERVER% /Change /TN ActiveDirectoryPollingTask /DISABLE )
if %ERRORLEVEL% NEQ 0 GOTO :error
if "%SIMULATOR%" == "true" ( C:\Windows\system32\schtasks.exe /S %APPSERVER% /End /TN  ImageFilterSimulator)
if "%SIMULATOR%" == "true" ( C:\Windows\system32\schtasks.exe /S %APPSERVER% /Change /TN ImageFilterSimulator /DISABLE)
if %ERRORLEVEL% NEQ 0 GOTO :error

psexec -accepteula \\%APPSERVER% cmd /c if exist C:\CPS\ScheduledTasks\CPSEmailNotification\ (  C:\Windows\system32\schtasks.exe /S %APPSERVER% /End /TN  CPSEmailNotification )
psexec -accepteula \\%APPSERVER% cmd /c if exist C:\CPS\ScheduledTasks\CPSEmailNotification\ (  C:\Windows\system32\schtasks.exe /S %APPSERVER%  /Change /TN CPSEmailNotification /DISABLE )

psexec -accepteula \\%APPSERVER% cmd /c if exist C:\CPS\ScheduledTasks\AVIPromotion\ (  C:\Windows\system32\schtasks.exe /S %APPSERVER% /End /TN  AVIPromotion )
psexec -accepteula \\%APPSERVER% cmd /c if exist C:\CPS\ScheduledTasks\AVIPromotion\ (  C:\Windows\system32\schtasks.exe /S %APPSERVER%  /Change /TN AVIPromotion /DISABLE )
psexec -accepteula \\%APPSERVER% cmd /c if exist C:\CPS\ScheduledTasks\DigitalPromotion\ (  C:\Windows\system32\schtasks.exe /S %APPSERVER% /End /TN  DigitalPromotion )
psexec -accepteula \\%APPSERVER% cmd /c if exist C:\CPS\ScheduledTasks\DigitalPromotion\ (  C:\Windows\system32\schtasks.exe /S %APPSERVER%  /Change /TN DigitalPromotion /DISABLE )
psexec -accepteula \\%APPSERVER% cmd /c if exist C:\CPS\ScheduledTasks\TableauImagesPollingTask\ (  C:\Windows\system32\schtasks.exe /S %APPSERVER%  /end /TN TableauImagesPollingTask )
psexec -accepteula \\%APPSERVER% cmd /c if exist C:\CPS\ScheduledTasks\TableauImagesPollingTask\ (  C:\Windows\system32\schtasks.exe /S %APPSERVER%  /Change /TN TableauImagesPollingTask /DISABLE )
echo Step %STEP% completed...  ---------


:StopIIS
SET STEP="Stop iis"
echo Step %STEP% commencing...  ---------
rem call Autobuild\do_service_stop_locrem1.cmd "SSOService" %APPSERVER%
psexec -accepteula \\%APPSERVER% cmd  /c iisreset
psexec -accepteula \\%APPSERVER% cmd  /c (C:\Windows\System32\inetsrv\appcmd stop apppool /apppool.name:"DMAPI" )
Set OK=false
IF "%ERRORLEVEL%"=="0" set OK=true
IF "%ERRORLEVEL%"=="50" set OK=true
IF  "%ERRORLEVEL%"=="1062" set OK=true
IF "%OK%"=="false" GOTO :error
psexec -accepteula \\%APPSERVER% cmd  /c (C:\Windows\System32\inetsrv\appcmd stop apppool /apppool.name:"CFTSAPI" )
Set OK=false
IF "%ERRORLEVEL%"=="0" set OK=true
IF "%ERRORLEVEL%"=="50" set OK=true
IF  "%ERRORLEVEL%"=="1062" set OK=true
IF "%OK%"=="false" GOTO :error

psexec -accepteula \\%APPSERVER% cmd  /c (C:\Windows\System32\inetsrv\appcmd stop apppool /apppool.name:"CPSWebAPI" )
Set OK=false
IF "%ERRORLEVEL%"=="0" set OK=true
IF "%ERRORLEVEL%"=="50" set OK=true
IF  "%ERRORLEVEL%"=="1062" set OK=true
IF "%OK%"=="false" GOTO :error

psexec -accepteula \\%APPSERVER% cmd  /c (C:\Windows\System32\inetsrv\appcmd stop site /site.name:"DMAPI" )
Set OK=false
IF "%ERRORLEVEL%"=="0" set OK=true
IF "%ERRORLEVEL%"=="50" set OK=true
IF  "%ERRORLEVEL%"=="1062" set OK=true
IF "%OK%"=="false" GOTO :error
psexec -accepteula \\%APPSERVER% cmd  /c (C:\Windows\System32\inetsrv\appcmd stop site /site.name:"CFTSAPI" )
Set OK=false
IF "%ERRORLEVEL%"=="0" set OK=true
IF "%ERRORLEVEL%"=="50" set OK=true
IF  "%ERRORLEVEL%"=="1062" set OK=true
IF "%OK%"=="false" GOTO :error
psexec -accepteula \\%APPSERVER% cmd  /c (C:\Windows\System32\inetsrv\appcmd stop site /site.name:"CPSWebAPI" )
Set OK=false
IF "%ERRORLEVEL%"=="0" set OK=true
IF "%ERRORLEVEL%"=="50" set OK=true
IF  "%ERRORLEVEL%"=="1062" set OK=true
IF "%OK%"=="false" GOTO :error
rem call Autobuild\do_service_stop_locrem1.cmd "World Wide Web Publishing Service" %APPSERVER%
VERIFY > nul
rem call Autobuild\do_service_stop_locrem1.cmd "Windows Process Activation Service" %APPSERVER%
VERIFY > nul

if %ERRORLEVEL% NEQ 0 GOTO :error
rem psexec -accepteula \\%WEBSERVER% cmd  /c iisreset
call Autobuild\do_service_stop_locrem1.cmd "World Wide Web Publishing Service" %WEBSERVER%
call Autobuild\do_service_stop_locrem1.cmd "Windows Process Activation Service" %WEBSERVER%
VERIFY > nul
if %ERRORLEVEL% NEQ 0 GOTO :error

echo Step %STEP% completed...

IF "%CLUSTERVALUE%"=="02" (GOTO END)
:StopEXTservices
SET STEP="Stop ext services"
echo Step %STEP% commencing...  ---------
if "%ZPITASK%" == "true"  (C:\Windows\system32\schtasks.exe /S %EXTSERVER% /End /TN  ZPIProcessTask )
if "%ZPITASK%" == "true"  (C:\Windows\system32\schtasks.exe /S %EXTSERVER% /Change /TN ZPIProcessTask /DISABLE )
if %ERRORLEVEL% NEQ 0 GOTO :error
if "%DMVFeature%" == "true"  (C:\Windows\system32\schtasks.exe /S %EXTSERVER% /End /TN  DMVLookupMD )
if "%DMVFeature%" == "true"  (C:\Windows\system32\schtasks.exe /S %EXTSERVER% /Change /TN DMVLookupMD /DISABLE )
if %ERRORLEVEL% NEQ 0 GOTO :error
if "%DMVFeature%" == "true"  (C:\Windows\system32\schtasks.exe /S %EXTSERVER% /End /TN  DMVLookupXP )
if "%DMVFeature%" == "true"  (C:\Windows\system32\schtasks.exe /S %EXTSERVER% /Change /TN DMVLookupXP /DISABLE )
if %ERRORLEVEL% NEQ 0 GOTO :error
if "%DMVFeature%" == "true"  (C:\Windows\system32\schtasks.exe /S %EXTSERVER% /End /TN  DMVLookupNJ )
if "%DMVFeature%" == "true"  (C:\Windows\system32\schtasks.exe /S %EXTSERVER% /Change /TN DMVLookupNJ /DISABLE )
if %ERRORLEVEL% NEQ 0 GOTO :error

psexec -accepteula \\%EXTSERVER% cmd  /c (C:\Windows\System32\inetsrv\appcmd stop apppool /apppool.name:"WorkflowAPI" )
VERIFY > nul
psexec -accepteula \\%EXTSERVER% cmd  /c (C:\Windows\System32\inetsrv\appcmd stop apppool /apppool.name:"AAR" )
Set OK=false
IF "%ERRORLEVEL%"=="0" set OK=true
IF "%ERRORLEVEL%"=="50" set OK=true
IF  "%ERRORLEVEL%"=="1062" set OK=true
IF "%OK%"=="false" GOTO :error
VERIFY > nul
psexec -accepteula \\%EXTSERVER% cmd  /c (C:\Windows\System32\inetsrv\appcmd stop apppool /apppool.name:"IAS" )
Set OK=false
IF "%ERRORLEVEL%"=="0" set OK=true
IF "%ERRORLEVEL%"=="50" set OK=true
IF  "%ERRORLEVEL%"=="1062" set OK=true
IF "%OK%"=="false" GOTO :error
VERIFY > nul
psexec -accepteula \\%EXTSERVER% cmd  /c (C:\Windows\System32\inetsrv\appcmd stop apppool /apppool.name:"RAAS" )
Set OK=false
IF "%ERRORLEVEL%"=="0" set OK=true
IF "%ERRORLEVEL%"=="50" set OK=true
IF  "%ERRORLEVEL%"=="1062" set OK=true
IF "%OK%"=="false" GOTO :error
VERIFY > nul
psexec -accepteula \\%EXTSERVER% cmd  /c (C:\Windows\System32\inetsrv\appcmd stop apppool /apppool.name:"TPI" )
Set OK=false
IF "%ERRORLEVEL%"=="0" set OK=true
IF "%ERRORLEVEL%"=="50" set OK=true
IF  "%ERRORLEVEL%"=="1062" set OK=true
IF "%OK%"=="false" GOTO :error
VERIFY > nul
psexec -accepteula \\%EXTSERVER% cmd  /c (C:\Windows\System32\inetsrv\appcmd stop apppool /apppool.name:"TPI02" )
Set OK=false
VERIFY > nul

psexec -accepteula \\%EXTSERVER% cmd  /c (C:\Windows\System32\inetsrv\appcmd stop apppool /apppool.name:"DMVAPI" )
Set OK=false
IF "%ERRORLEVEL%"=="0" set OK=true
IF "%ERRORLEVEL%"=="50" set OK=true
IF  "%ERRORLEVEL%"=="1062" set OK=true
IF "%OK%"=="false" GOTO :error
VERIFY > nul

psexec -accepteula \\%EXTSERVER% cmd  /c (C:\Windows\System32\inetsrv\appcmd stop site /site.name:"WorkflowAPI" )
Set OK=false
IF "%ERRORLEVEL%"=="0" set OK=true
IF "%ERRORLEVEL%"=="50" set OK=true
IF  "%ERRORLEVEL%"=="1062" set OK=true
IF "%OK%"=="false" GOTO :error
VERIFY > nul
psexec -accepteula \\%EXTSERVER% cmd  /c (C:\Windows\System32\inetsrv\appcmd stop site /site.name:"AAR" )
Set OK=false
IF "%ERRORLEVEL%"=="0" set OK=true
IF "%ERRORLEVEL%"=="50" set OK=true
IF  "%ERRORLEVEL%"=="1062" set OK=true
IF "%OK%"=="false" GOTO :error
rem if %ERRORLEVEL% NEQ 0 GOTO :error
VERIFY > nul
psexec -accepteula \\%EXTSERVER% cmd  /c (C:\Windows\System32\inetsrv\appcmd stop site /site.name:"IAS" )
Set OK=false
IF "%ERRORLEVEL%"=="0" set OK=true
IF "%ERRORLEVEL%"=="50" set OK=true
IF  "%ERRORLEVEL%"=="1062" set OK=true
IF "%OK%"=="false" GOTO :error
rem if %ERRORLEVEL% NEQ 0 GOTO :error
VERIFY > nul
psexec -accepteula \\%EXTSERVER% cmd  /c (C:\Windows\System32\inetsrv\appcmd stop site /site.name:"RAAS" )
Set OK=false
IF "%ERRORLEVEL%"=="0" set OK=true
IF "%ERRORLEVEL%"=="50" set OK=true
IF  "%ERRORLEVEL%"=="1062" set OK=true
IF "%OK%"=="false" GOTO :error
rem if %ERRORLEVEL% NEQ 0 GOTO :error
VERIFY > nul
psexec -accepteula \\%EXTSERVER% cmd  /c (C:\Windows\System32\inetsrv\appcmd stop site /site.name:"TPI" )
Set OK=false
IF "%ERRORLEVEL%"=="0" set OK=true
IF "%ERRORLEVEL%"=="50" set OK=true
IF  "%ERRORLEVEL%"=="1062" set OK=true
IF "%OK%"=="false" GOTO :error
rem if %ERRORLEVEL% NEQ 0 GOTO :error
VERIFY > nul
psexec -accepteula \\%EXTSERVER% cmd  /c (C:\Windows\System32\inetsrv\appcmd stop site /site.name:"TPI02" )
VERIFY > nul
psexec -accepteula \\%EXTSERVER% cmd  /c (C:\Windows\System32\inetsrv\appcmd stop site /site.name:"DMVAPI" )
Set OK=false
IF "%ERRORLEVEL%"=="0" set OK=true
IF "%ERRORLEVEL%"=="50" set OK=true
IF  "%ERRORLEVEL%"=="1062" set OK=true
IF "%OK%"=="false" GOTO :error
rem if %ERRORLEVEL% NEQ 0 GOTO :error
VERIFY > nul

IF "%ENVTYPE%"=="STAGE" (GOTO SKIPPEDNYC)
echo env type is %ENVTYPE%
psexec -accepteula \\%EXTSERVER% cmd  /c ( C:\Windows\System32\inetsrv\appcmd stop apppool /apppool.name:"NYCDOT" )
Set OK=false
IF "%ERRORLEVEL%"=="0" set OK=true
IF "%ERRORLEVEL%"=="50" set OK=true
IF  "%ERRORLEVEL%"=="1062" set OK=true
IF "%OK%"=="false" GOTO :error
VERIFY > nul
psexec -accepteula \\%EXTSERVER% cmd  /c (C:\Windows\System32\inetsrv\appcmd stop site /site.name:"NYCDOT")
Set OK=false
IF "%ERRORLEVEL%"=="0" set OK=true
IF "%ERRORLEVEL%"=="50" set OK=true
IF  "%ERRORLEVEL%"=="1062" set OK=true
IF "%OK%"=="false" GOTO :error
rem if %ERRORLEVEL% NEQ 0 GOTO :error
VERIFY > nul

:SKIPPEDNYC
IF NOT "%ENVTYPE%"=="PROD" (GOTO END)
psexec -accepteula \\%APP03SERVER% cmd /c  (C:\Windows\System32\inetsrv\appcmd stop site /site.name:"DMAPI" )
Set OK=false
IF "%ERRORLEVEL%"=="0" set OK=true
IF "%ERRORLEVEL%"=="50" set OK=true
IF  "%ERRORLEVEL%"=="1062" set OK=true
IF "%OK%"=="false" GOTO :error
psexec -accepteula \\%APP03SERVER% cmd  /c (C:\Windows\System32\inetsrv\appcmd stop apppool /apppool.name:"DMAPI" )
Set OK=false
IF "%ERRORLEVEL%"=="0" set OK=true
IF "%ERRORLEVEL%"=="50" set OK=true
IF  "%ERRORLEVEL%"=="1062" set OK=true
IF "%OK%"=="false" GOTO :error

echo Step %STEP% completed...

GOTO END

:error
echo ------- AN ERROR OCCURED DURING THIS STEP: %STEP% ------

exit /b 1

:END
echo -- SLEEP of CPS is complete


exit /b 0
