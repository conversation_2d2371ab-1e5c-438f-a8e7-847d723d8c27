@echo off
rem set variables
Set CURRENTDIR=%CD%

echo ------- Your current directory is:  %CURRENTDIR% ---------

set NOW=%date:~10,4%-%date:~4,2%-%date:~7,2%
echo Deployment beginning %NOW%
echo -----------------------------------
echo Connect to Server
echo -----------------------------------
verify > nul
SET SERVER=%1
SET TYPE=%2
SET PORT=%3

ECHO %PORT%
If "%PORT%" == "none" set PORT=
echo port = %PORT%

ECHO %SERVER%
psexec -accepteula \\%SERVER% cmd  /c (C:\Windows\System32\inetsrv\appcmd stop apppool /apppool.name:"IMS%PORT%Api" )
Set OK=false
IF "%ERRORLEVEL%"=="0" set OK=true
IF  "%ERRORLEVEL%"=="50" set OK=true
IF  "%ERRORLEVEL%"=="1062" set OK=true
IF "%OK%"=="false" GOTO :error
if "%SERVER%" == "stgcpsext01.int.cbdtp.net" GOTO END
if "%SERVER%" == "prdcpsext01.int.cbdtp.net" GOTO END
psexec -accepteula \\%SERVER% cmd /c  (C:\Windows\System32\inetsrv\appcmd stop site /site.name:"IMS%PORT%Api" )
Set OK=false
IF "%ERRORLEVEL%"=="0" set OK=true
IF  "%ERRORLEVEL%"=="50" set OK=true
IF  "%ERRORLEVEL%"=="1062" set OK=true
IF "%OK%"=="false" GOTO :error

rem echo stop w3svc
rem sc \\%SERVER% stop W3SVC
rem IF "%ERRORLEVEL%"=="0" set OK=true
rem IF  "%ERRORLEVEL%"=="50" set OK=true
rem IF  "%ERRORLEVEL%"=="1062" set OK=true
rem IF "%OK%"=="false" GOTO :error
rem echo stop was
rem sc \\%SERVER% stop WAS
rem IF "%ERRORLEVEL%"=="0" set OK=true
rem IF  "%ERRORLEVEL%"=="50" set OK=true
rem IF "%OK%"=="false" GOTO :error
rem call Autobuild\do_service_stop_locrem1.cmd "World Wide Web Publishing Service" %SERVER%
rem call Autobuild\do_service_stop_locrem1.cmd "Windows Process Activation Service" %SERVER%
GOTO END

:error
echo ------- AN ERROR OCCURED DURING THIS STEP: %STEP% %SERVER% %TYPE% ------

exit /b 1

:END
echo -- Stop APIS of Nodes %SERVER% %TYPE% is complete
exit /b 0