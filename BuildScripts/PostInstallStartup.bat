@ECHO OFF

set NOW=%date:~10,4%-%date:~4,2%-%date:~7,2%
Set CURRENTDIR=%CD%
SET APPSERVER=%1
SET PROJECT=%2
SET ENVTYPE=%3
SET WEBSERVER=%4
SET SIMULATOR=%5
SET CLUSTERVALUE=%6
SET ADPOLLINGTASK=%7
SET EXTSERVER=%8
SET ZPITASK=%9
SHIFT
SHIFT
SHIFT
SHIFT
SHIFT
SHIFT
SHIFT
SHIFT
SHIFT
SET TRANSPORTALFEATURE=%1
SET TRANSPORTALSERVER=%2
SET RELEASEDATE=%3
SET RELEASEVERSION=%4
SET TXPDBLISTENER=%5
SET DBUSER=%6
SET DBPASSWORD=%7
SET APP03SERVER=%8
SET DMVFEATURE=%9

echo %APPSERVER%

Echo transportal ? %TRANSPORTALFEATURE%

echo CPS Post-Installation Startup is starting at %NOW%
echo ---------------------------------- 

SET STEP="Update Release version and date"
ECHO ----------------------------------------------------------
ECHO Update the Release version and date in the TransPortal DB
ECHO ----------------------------------------------------------
if "%TRANSPORTALFEATURE%" == "true" ( psexec -accepteula \\%TRANSPORTALSERVER% cmd /c sqlcmd -b -U %DBUSER% -P %DBPASSWORD% -Q "IF EXISTS (SELECT 1 FROM sys.columns WHERE Name=N'vcTagVer') BEGIN EXEC sp_executesql N'update stbApp set vcTagVer=''%RELEASEVERSION%'',dtReleaseDate=''%RELEASEDATE%'' where iAppID=859' END" -S %TXPDBLISTENER% -d SSO )
IF [%ERRORLEVEL%] NEQ [0] GOTO ERROR


:StartCPSService
SET STEP="Start CPS Services"
echo Step %STEP% commencing...  ---------
echo Try starting service through NANT..  ---------
if %ERRORLEVEL% NEQ 0 GOTO :error 
call Autobuild\do_service_start_locrem1.cmd "Windows Process Activation Service" %APPSERVER%
call Autobuild\do_service_start_locrem1.cmd "World Wide Web Publishing Service" %APPSERVER%
if %ERRORLEVEL% NEQ 0 GOTO :error 
call Autobuild\do_service_start_locrem1.cmd "Windows Process Activation Service" %WEBSERVER%
call Autobuild\do_service_start_locrem1.cmd "World Wide Web Publishing Service" %WEBSERVER%
if %ERRORLEVEL% NEQ 0 GOTO :error  

psexec -accepteula \\%APPSERVER% cmd  /c (C:\Windows\System32\inetsrv\appcmd start apppool /apppool.name:"DMAPI" )
Set OK=false
IF "%ERRORLEVEL%"=="0" set OK=true
IF "%ERRORLEVEL%"=="50" set OK=true
IF  "%ERRORLEVEL%"=="1062" set OK=true
IF "%OK%"=="false" GOTO :error
if %ERRORLEVEL% NEQ 0 GOTO :error 
psexec -accepteula \\%APPSERVER% cmd  /c (C:\Windows\System32\inetsrv\appcmd start apppool /apppool.name:"CFTSAPI" )
Set OK=false
IF "%ERRORLEVEL%"=="0" set OK=true
IF "%ERRORLEVEL%"=="50" set OK=true
IF  "%ERRORLEVEL%"=="1062" set OK=true
IF "%OK%"=="false" GOTO :error
if %ERRORLEVEL% NEQ 0 GOTO :error 
psexec -accepteula \\%APPSERVER% cmd  /c (C:\Windows\System32\inetsrv\appcmd start apppool /apppool.name:"CPSWebAPI" )
Set OK=false
IF "%ERRORLEVEL%"=="0" set OK=true
IF "%ERRORLEVEL%"=="50" set OK=true
IF  "%ERRORLEVEL%"=="1062" set OK=true
IF "%OK%"=="false" GOTO :error
if %ERRORLEVEL% NEQ 0 GOTO :error 
psexec -accepteula \\%APPSERVER% cmd  /c (C:\Windows\System32\inetsrv\appcmd start site /site.name:"DMAPI" )
Set OK=false
IF "%ERRORLEVEL%"=="0" set OK=true
IF "%ERRORLEVEL%"=="50" set OK=true
IF  "%ERRORLEVEL%"=="1062" set OK=true
IF "%OK%"=="false" GOTO :error
if %ERRORLEVEL% NEQ 0 GOTO :error 
psexec -accepteula \\%APPSERVER% cmd  /c (C:\Windows\System32\inetsrv\appcmd start site /site.name:"CFTSAPI" )
Set OK=false
IF "%ERRORLEVEL%"=="0" set OK=true
IF "%ERRORLEVEL%"=="50" set OK=true
IF  "%ERRORLEVEL%"=="1062" set OK=true
IF "%OK%"=="false" GOTO :error
if %ERRORLEVEL% NEQ 0 GOTO :error 
psexec -accepteula \\%APPSERVER% cmd  /c (C:\Windows\System32\inetsrv\appcmd start site /site.name:"CPSWebAPI" )
Set OK=false
IF "%ERRORLEVEL%"=="0" set OK=true
IF "%ERRORLEVEL%"=="50" set OK=true
IF  "%ERRORLEVEL%"=="1062" set OK=true
IF "%OK%"=="false" GOTO :error

IF "%CLUSTERVALUE%"=="02" (GOTO END) 
:StartEXTservices
SET STEP="Enable EXT IIS"
echo Step %STEP% commencing...  --------- 
rem call Autobuild\do_service_start_locrem1.cmd "Windows Process Activation Service" %EXTSERVER%
rem call Autobuild\do_service_start_locrem1.cmd "World Wide Web Publishing Service" %EXTSERVER%
if %ERRORLEVEL% NEQ 0 GOTO :error 
psexec -accepteula \\%EXTSERVER% cmd  /c (C:\Windows\System32\inetsrv\appcmd start apppool /apppool.name:"DMVAPI" )
Set OK=false
IF "%ERRORLEVEL%"=="0" set OK=true
IF "%ERRORLEVEL%"=="50" set OK=true
IF  "%ERRORLEVEL%"=="1062" set OK=true
IF "%OK%"=="false" GOTO :error

rem psexec -accepteula \\%EXTSERVER% cmd  /c (C:\Windows\System32\inetsrv\appcmd start apppool /apppool.name:"WorkflowAPI" )
Set OK=false
IF "%ERRORLEVEL%"=="0" set OK=true
IF "%ERRORLEVEL%"=="50" set OK=true
IF  "%ERRORLEVEL%"=="1062" set OK=true
IF "%OK%"=="false" GOTO :error
 
rem psexec -accepteula \\%EXTSERVER% cmd  /c (C:\Windows\System32\inetsrv\appcmd start apppool /apppool.name:"AAR" )
Set OK=false
IF "%ERRORLEVEL%"=="0" set OK=true
IF "%ERRORLEVEL%"=="50" set OK=true
IF  "%ERRORLEVEL%"=="1062" set OK=true
IF "%OK%"=="false" GOTO :error
 
psexec -accepteula \\%EXTSERVER% cmd  /c (C:\Windows\System32\inetsrv\appcmd start apppool /apppool.name:"IAS" )
Set OK=false
IF "%ERRORLEVEL%"=="0" set OK=true
IF "%ERRORLEVEL%"=="50" set OK=true
IF  "%ERRORLEVEL%"=="1062" set OK=true
IF "%OK%"=="false" GOTO :error

psexec -accepteula \\%EXTSERVER% cmd  /c (C:\Windows\System32\inetsrv\appcmd start apppool /apppool.name:"RAAS" )
Set OK=false
IF "%ERRORLEVEL%"=="0" set OK=true
IF "%ERRORLEVEL%"=="50" set OK=true
IF  "%ERRORLEVEL%"=="1062" set OK=true
IF "%OK%"=="false" GOTO :error

psexec -accepteula \\%EXTSERVER% cmd  /c (C:\Windows\System32\inetsrv\appcmd start apppool /apppool.name:"TPI" )
Set OK=false
IF "%ERRORLEVEL%"=="0" set OK=true
IF "%ERRORLEVEL%"=="50" set OK=true
IF  "%ERRORLEVEL%"=="1062" set OK=true
IF "%OK%"=="false" GOTO :error
rem psexec -accepteula \\%EXTSERVER% cmd  /c (C:\Windows\System32\inetsrv\appcmd start site /site.name:"WorkflowAPI" ) 
Set OK=false
IF "%ERRORLEVEL%"=="0" set OK=true
IF "%ERRORLEVEL%"=="50" set OK=true
IF  "%ERRORLEVEL%"=="1062" set OK=true
IF "%OK%"=="false" GOTO :error
VERIFY > nul
rem psexec -accepteula \\%EXTSERVER% cmd  /c (C:\Windows\System32\inetsrv\appcmd start site /site.name:"AAR" ) 
Set OK=false
IF "%ERRORLEVEL%"=="0" set OK=true
IF "%ERRORLEVEL%"=="50" set OK=true
IF  "%ERRORLEVEL%"=="1062" set OK=true
IF "%OK%"=="false" GOTO :error
rem if %ERRORLEVEL% NEQ 0 GOTO :error 
VERIFY > nul
psexec -accepteula \\%EXTSERVER% cmd  /c (C:\Windows\System32\inetsrv\appcmd start site /site.name:"IAS" )
Set OK=false
IF "%ERRORLEVEL%"=="0" set OK=true
IF "%ERRORLEVEL%"=="50" set OK=true
IF  "%ERRORLEVEL%"=="1062" set OK=true
IF "%OK%"=="false" GOTO :error
rem if %ERRORLEVEL% NEQ 0 GOTO :error 
VERIFY > nul
psexec -accepteula \\%EXTSERVER% cmd  /c (C:\Windows\System32\inetsrv\appcmd start site /site.name:"RAAS" )
Set OK=false
IF "%ERRORLEVEL%"=="0" set OK=true
IF "%ERRORLEVEL%"=="50" set OK=true
IF  "%ERRORLEVEL%"=="1062" set OK=true
IF "%OK%"=="false" GOTO :error
rem if %ERRORLEVEL% NEQ 0 GOTO :error 
VERIFY > nul
psexec -accepteula \\%EXTSERVER% cmd  /c (C:\Windows\System32\inetsrv\appcmd start site /site.name:"TPI" )
Set OK=false
IF "%ERRORLEVEL%"=="0" set OK=true
IF "%ERRORLEVEL%"=="50" set OK=true
IF  "%ERRORLEVEL%"=="1062" set OK=true
IF "%OK%"=="false" GOTO :error
rem if %ERRORLEVEL% NEQ 0 GOTO :error 
VERIFY > nul

psexec -accepteula \\%EXTSERVER% cmd  /c (C:\Windows\System32\inetsrv\appcmd start site /site.name:"DMVAPI" )
Set OK=false
IF "%ERRORLEVEL%"=="0" set OK=true
IF "%ERRORLEVEL%"=="50" set OK=true
IF  "%ERRORLEVEL%"=="1062" set OK=true
IF "%OK%"=="false" GOTO :error
rem if %ERRORLEVEL% NEQ 0 GOTO :error 
VERIFY > nul
IF "%ENVTYPE%"=="STAGE" (GOTO SKIPPEDNYC) 
echo env type is %ENVTYPE%
psexec -accepteula \\%EXTSERVER% cmd  /c (C:\Windows\System32\inetsrv\appcmd start apppool /apppool.name:"NYCDOT" )
Set OK=false
IF "%ERRORLEVEL%"=="0" set OK=true
IF "%ERRORLEVEL%"=="50" set OK=true
IF  "%ERRORLEVEL%"=="1062" set OK=true
IF "%OK%"=="false" GOTO :error
VERIFY > nul
psexec -accepteula \\%EXTSERVER% cmd  /c (C:\Windows\System32\inetsrv\appcmd start site /site.name:"NYCDOT")
Set OK=false
IF "%ERRORLEVEL%"=="0" set OK=true
IF "%ERRORLEVEL%"=="50" set OK=true
IF  "%ERRORLEVEL%"=="1062" set OK=true
IF "%OK%"=="false" GOTO :error
rem if %ERRORLEVEL% NEQ 0 GOTO :error 
VERIFY > nul

:SKIPPEDNYC
IF NOT "%ENVTYPE%"=="PROD" (GOTO EnableCPSTasks) 
psexec -accepteula \\%APP03SERVER% cmd  /c (C:\Windows\System32\inetsrv\appcmd start apppool /apppool.name:"DMAPI" )
Set OK=false
IF "%ERRORLEVEL%"=="0" set OK=true
IF "%ERRORLEVEL%"=="50" set OK=true
IF  "%ERRORLEVEL%"=="1062" set OK=true
IF "%OK%"=="false" GOTO :error
psexec -accepteula \\%APP03SERVER% cmd /c  powershell -Command "& {Start-IISSite -Name "DMAPI"}"
Set OK=false
IF "%ERRORLEVEL%"=="0" set OK=true
IF "%ERRORLEVEL%"=="50" set OK=true
IF  "%ERRORLEVEL%"=="1062" set OK=true
IF "%OK%"=="false" GOTO :error



echo Step %STEP% completed...  ---------

:EnableCPSTasks
SET STEP="Enable CPS Tasks"
echo Step %STEP% commencing...  --------- 
echo -----------------------------------
echo Enable  scheduled tasks for CPS....
echo -----------------------------------
if "%ADPOLLINGTASK%" == "true" ( C:\Windows\system32\schtasks.exe /S %APPSERVER% /Change /TN ActiveDirectoryPollingTask /ENABLE)
if %ERRORLEVEL% NEQ 0 GOTO :error 
if "%SIMULATOR%" == "true" ( C:\Windows\system32\schtasks.exe /S %APPSERVER% /Change /TN ImageFilterSimulator /ENABLE)
if %ERRORLEVEL% NEQ 0 GOTO :error 
rem if "%ZPITASK%" == "true" if not "%ENVTYPE%"=="PROD" if not "%ENVTYPE%"=="STAGE"  ( C:\Windows\system32\schtasks.exe /S %EXTSERVER% /Change /TN ZPIProcessTask /ENABLE)
if %ERRORLEVEL% NEQ 0 GOTO :error 
psexec -accepteula \\%APPSERVER% cmd /c if exist C:\CPS\ScheduledTasks\CPSEmailNotification\ ( C:\Windows\system32\schtasks.exe /S %APPSERVER% /Change /TN CPSEmailNotification /ENABLE)
psexec -accepteula \\%APPSERVER% cmd /c if exist C:\CPS\ScheduledTasks\AVIPromotion\ (  C:\Windows\system32\schtasks.exe /S %APPSERVER%  /Change /TN AVIPromotion /ENABLE )
psexec -accepteula \\%APPSERVER% cmd /c if exist C:\CPS\ScheduledTasks\DigitalPromotion\ (  C:\Windows\system32\schtasks.exe /S %APPSERVER%  /Change /TN DigitalPromotion /ENABLE )
psexec -accepteula \\%APPSERVER% cmd /c if exist C:\CPS\ScheduledTasks\TableauImagesPollingTask\ (  C:\Windows\system32\schtasks.exe /S %APPSERVER%  /Change /TN TableauImagesPollingTask /ENABLE )
echo Step %STEP% completed...  --------- 


:EnableEXTTasks
SET STEP="Enable EXT Scheduled Tasks"
echo Step %STEP% commencing...  --------- 
echo -----------------------------------
echo Enable External scheduled tasks for CPS....
echo -----------------------------------
if "%ZPITask%" == "true" if not "%ENVTYPE%"=="PROD" if not "%ENVTYPE%"=="STAGE"  ( C:\Windows\system32\schtasks.exe /S %EXTSERVER% /Change /TN ZPIProcessTask /ENABLE)
if %ERRORLEVEL% NEQ 0 GOTO :error 
if "%DMVFeature%" == "true" ( C:\Windows\system32\schtasks.exe /S %EXTSERVER% /Change /TN DMVLookupMD /ENABLE)
if %ERRORLEVEL% NEQ 0 GOTO :error 
if "%DMVFeature%" == "true" ( C:\Windows\system32\schtasks.exe /S %EXTSERVER% /Change /TN DMVLookupXP /ENABLE)
if %ERRORLEVEL% NEQ 0 GOTO :error 
if "%DMVFeature%" == "true" ( C:\Windows\system32\schtasks.exe /S %EXTSERVER% /Change /TN DMVLookupNJ /ENABLE)
if %ERRORLEVEL% NEQ 0 GOTO :error 
echo Step %STEP% completed...  --------- 



:ForceRunofADTasks
SET STEP="Forcibly run Active Directory Tasks"
echo -----------------------------------
echo %STEP%
echo ------------------------------------
if "%ADPOLLINGTASK%" == "true" (C:\Windows\system32\schtasks.exe /run /S %APPSERVER% /TN ActiveDirectoryPollingTask)
GOTO END


:error
echo ------- AN ERROR OCCURED DURING THIS STEP: %STEP% ------

exit /b 1

:END
echo -- Post-Build-Startup of CPS is complete
exit /b 0


