@ECHO OFF

set NOW=%date:~10,4%-%date:~4,2%-%date:~7,2%
Set CURRENTDIR=%CD%
SET MOMSSERVER=%1
SET PROJECT=%2
SET ENVTYPE=%3
SET INTEGRITY=%4
SET INTEGRITYSERVER=%5
SET INSIGHTMOBILESERVER=%6
SET INSIGHTMOBILE=%7
SET ADPOLLINGTASK=%8
SET TRANSPORTALFEATURE=%9
SHIFT
SHIFT
SHIFT
SHIFT
SHIFT
SHIFT
SHIFT
SHIFT
SHIFT
SET TRANSPORTALSERVER=%1
SET RELEASEDATE=%2
SET RELEASEVERSION=%3
SET DEPLOYMENTDRIVE=%4
SET WOMESSAGING=%5
SET TXPDBLISTENER=%6
SET DBSERVER=%7
SET DBUSER=%8
SET DBPASSWORD=%9

echo %MOMSSERVER%
echo %INTEGRITY%
echo %INTEGRITYSERVER%
echo ad polling %ADPOLLINGTASK%

echo MOMS/Insight Post-Installation Startup is starting at %NOW%
echo ----------------------------------

SET STEP="Insert data for Knowledge Base"
ECHO ----------------------------------------------------------
ECHO Insert data into the database tables for the Knowledge Base
ECHO ----------------------------------------------------------
psexec -accepteula \\%DBSERVER% cmd /c if exist %DEPLOYMENTDRIVE%:\Deployment\%RELEASEDATE%\Database\InsightManual\ (%DEPLOYMENTDRIVE%:\Deployment\%RELEASEDATE%\LocalDBScripts\execute_Insight_Manual_Inserts.cmd)
IF [%ERRORLEVEL%] NEQ [0] GOTO ERROR

SET STEP="Update Release version and date"
ECHO ----------------------------------------------------------
ECHO Update the Release version and date in the TransPortal DB
ECHO ----------------------------------------------------------
if "%TRANSPORTALFEATURE%" == "true" ( psexec -accepteula \\%TRANSPORTALSERVER% cmd /c sqlcmd -b -U %DBUSER% -P %DBPASSWORD% -Q "IF EXISTS (SELECT 1 FROM sys.columns WHERE Name=N'vcTagVer') BEGIN EXEC sp_executesql N'update stbApp set vcTagVer=''%RELEASEVERSION%'',dtReleaseDate=''%RELEASEDATE%'' where iAppID=852' END" -S %TXPDBLISTENER% -d SSO )
IF [%ERRORLEVEL%] NEQ [0] GOTO ERROR

:StartMOMSServices
SET STEP="Start MOMS Services"
echo Step %STEP% commencing...  ---------
rem comment out Mobile service for now
rem call Autobuild\do_service_start_locrem1.cmd "MOMS Mobile WCF Internal Service Host" %MOMSSERVER%
rem call Autobuild\do_service_start_locrem1.cmd "MOMSExternalService" %MOMSSERVER%
rem call Autobuild\do_service_start_locrem1.cmd "MOMSGPSLocatorService" %MOMSSERVER%
rem call Autobuild\do_service_start_locrem1.cmd "MOMSTrafficLoaderService" %MOMSSERVER%
rem call Autobuild\do_service_start_locrem1.cmd "MOMSService" %MOMSSERVER%
rem call Autobuild\do_service_start_locrem1.cmd "TransSuiteMOMSService" %MOMSSERVER%
rem call Autobuild\do_service_start_locrem1.cmd "InfinityMOMSService" %MOMSSERVER%
echo Try starting service through NANT..  ---------
NAnt.exe -buildfile:MOMS_build.build  Start-ServiceHost
if %ERRORLEVEL% NEQ 0 GOTO :error
echo Step %STEP% completed...  ---------

SET STEP="Start IIS"
NAnt.exe -buildfile:MOMS_build.build -D:iisserver=%MOMSSERVER% start-web
if %ERRORLEVEL% NEQ 0 GOTO :error
if "%INSIGHTMOBILE%"=="true" (NAnt.exe -buildfile:MOMS_build.build -D:iisserver=%INSIGHTMOBILESERVER%  start-web)
if %ERRORLEVEL% NEQ 0 GOTO :error

SET ISQA=%ENVTYPE:~0,2%
IF %ENVTYPE:~0,3%==DEV GOTO CHECKADPOLLING
IF %ENVTYPE:~0,2%==QA GOTO CHECKADPOLLING
:EnableMOMSTasks
SET STEP="Enable MOMS Tasks"
echo Step %STEP% commencing...  ---------
echo -----------------------------------
echo Enabling MOMS scheduled tasks...
echo -----------------------------------
rem if "%WOMESSAGING%"=="true" (C:\Windows\system32\schtasks.exe /S %MOMSSERVER% /Change /TN MOMSWorkOrderMessagingTask /ENABLE)
rem C:\Windows\system32\schtasks.exe /S %MOMSSERVER% /Change /TN MOMSPredictiveMaintenanceNotificationTask /ENABLE
rem C:\Windows\system32\schtasks.exe /S %MOMSSERVER% /Change /TN MOMSPreventiveMaintenanceTask   /ENABLE
rem C:\Windows\system32\schtasks.exe /S %MOMSSERVER% /Change /TN MOMSFailureAnalysis /ENABLE
rem C:\Windows\system32\schtasks.exe /S %MOMSSERVER% /Change /TN MOMSInventoryLevelNotification /ENABLE

if "%WOMESSAGING%"=="true" psexec -accepteula \\%MOMSSERVER% cmd /c if exist C:\MOMS\ScheduledTasks\MOMSWorkOrderMessagingTask\ (C:\Windows\system32\schtasks.exe /S %MOMSSERVER% /Change /TN MOMSWorkOrderMessagingTask /ENABLE)

psexec -accepteula \\%MOMSSERVER% cmd /c if exist C:\MOMS\ScheduledTasks\MOMSFailureAnalysisTaskConsole\ (C:\Windows\system32\schtasks.exe /S %MOMSSERVER% /Change /TN MOMSFailureAnalysis /ENABLE)

psexec -accepteula \\%MOMSSERVER% cmd /c if exist C:\MOMS\ScheduledTasks\MOMSInventoryLevelNotification\ (C:\Windows\system32\schtasks.exe /S %MOMSSERVER% /Change /TN MOMSInventoryLevelNotification /ENABLE)

psexec -accepteula \\%MOMSSERVER% cmd /c if exist C:\MOMS\ScheduledTasks\MOMSPredictiveMaintenanceNotificationTaskConsole\ (C:\Windows\system32\schtasks.exe /S %MOMSSERVER% /Change /TN MOMSPredictiveMaintenanceNotificationTask /ENABLE)

psexec -accepteula \\%MOMSSERVER% cmd /c if exist C:\MOMS\ScheduledTasks\MOMSPreventiveMaintenanceTask\ (C:\Windows\system32\schtasks.exe /S %MOMSSERVER% /Change /TN MOMSPreventiveMaintenanceTask /ENABLE)


psexec -accepteula \\%MOMSSERVER% cmd /c if exist C:\MOMS\ScheduledTasks\SolarWinds_MOMSEventLoader\ (C:\Windows\system32\schtasks.exe /S %MOMSSERVER% /Change /TN SolarWinds_MOMSEventLoader /ENABLE)


psexec -accepteula \\%MOMSSERVER% cmd /c if exist C:\MOMS\ScheduledTasks\MOMSEventLoader\ (C:\Windows\system32\schtasks.exe /S %MOMSSERVER% /Change /TN MOMSEventLoader /ENABLE)
psexec -accepteula \\%MOMSSERVER% cmd /c if exist C:\MOMS\ScheduledTasks\ImageReview_MOMSEventLoader\ (C:\Windows\system32\schtasks.exe /S %MOMSSERVER% /Change /TN ImageReview_MOMSEventLoader /ENABLE)
rem psexec -accepteula \\%MOMSSERVER% cmd /c if exist C:\MOMS\ScheduledTasks\Integrity_MOMSEventLoader\ (C:\Windows\system32\schtasks.exe /S %MOMSSERVER% /Change /TN Integrity_MOMSEventLoader /ENABLE)
psexec -accepteula \\%MOMSSERVER% cmd /c if exist C:\MOMS\ScheduledTasks\MOMSExternalNotifier\ (C:\Windows\system32\schtasks.exe /S %MOMSSERVER% /Change /TN MOMSExternalNotification /ENABLE)
psexec -accepteula \\%MOMSSERVER% cmd /c if exist C:\MOMS\ScheduledTasks\WhatsUp_MOMSEventLoader\ (C:\Windows\system32\schtasks.exe /S %MOMSSERVER% /Change /TN WhatsUp_MOMSEventLoader /ENABLE)
psexec -accepteula \\%MOMSSERVER% cmd /c if exist C:\MOMS\ScheduledTasks\MOMSTrafficDataLoader\ (C:\Windows\system32\schtasks.exe /S %MOMSSERVER% /Change /TN MOMSTrafficDataLoader /ENABLE)
psexec -accepteula \\%MOMSSERVER% cmd /c if exist C:\MOMS\ScheduledTasks\TransPortal_MOMSEventLoader\ (C:\Windows\system32\schtasks.exe /S %MOMSSERVER% /Change /TN TransPortal_MOMSEventLoader /ENABLE)
psexec -accepteula \\%MOMSSERVER% cmd /c if exist C:\MOMS\ScheduledTasks\CPS_MOMSEventLoader\ (C:\Windows\system32\schtasks.exe /S %MOMSSERVER% /Change /TN CPS_MOMSEventLoader /ENABLE)
psexec -accepteula \\%MOMSSERVER% cmd /c if exist C:\MOMS\ScheduledTasks\VOTT_MOMSEventLoader\ (C:\Windows\system32\schtasks.exe /S %MOMSSERVER% /Change /TN VOTT_MOMSEventLoader /ENABLE)

rem C:\Windows\system32\schtasks.exe /S %MOMSSERVER% /Change /TN MOMSExternalNotification /ENABLE
echo Integrity = "%INTEGRITY%
if "%INTEGRITY%"=="true" ( C:\Windows\system32\schtasks.exe /S %INTEGRITYSERVER% /Change /TN Integrity_MOMSEventLoader /ENABLE)
rem IF NOT "%PROJECT%"=="PRODUCT" ( C:\Windows\system32\schtasks.exe /S %MOMSSERVER% /Change /TN MOMSNotificationEscalation /ENABLE)

IF NOT "%PROJECT%"=="PRODUCT" psexec -accepteula \\%MOMSSERVER% cmd /c if exist C:\MOMS\ScheduledTasks\MOMSNotificationEscalationTask\ (C:\Windows\system32\schtasks.exe /S %MOMSSERVER% /Change /TN MOMSNotificationEscalation /ENABLE)


rem IF NOT "%MOMSSERVER%" == "SDV-VTADEV23.tcore.com" (C:\Windows\system32\schtasks.exe /S %MOMSSERVER% /Change /TN MOMSNotificationEscalation /ENABLE)\

:CHECKADPOLLING
if "%ADPOLLINGTASK%" == "true" (C:\Windows\system32\schtasks.exe /S %MOMSSERVER% /Change /TN MOMSActiveDirectoryPolling /ENABLE)
if %ERRORLEVEL% NEQ 0 GOTO :error
echo Step %STEP% completed...  ---------

:ForceRunofADTasks
SET STEP="Forcibly run Active Directory Task"
echo Step %STEP% commencing...  ---------
echo -----------------------------------
echo %STEP%
echo ------------------------------------
psexec -accepteula \\%MOMSSERVER% cmd /c if exist C:\MOMS\ScheduledTasks\MOMSActiveDirectoryPolling\ (C:\Windows\system32\schtasks.exe /run /S %MOMSSERVER% /TN MOMSActiveDirectoryPolling)
GOTO END


:error
echo ------- AN ERROR OCCURED DURING THIS STEP: %STEP% ------
echo ------- AN ERROR OCCURED DURING THIS STEP: %STEP% ------
rem NAnt.exe -buildfile:MOMS_build.build -logger:NAnt.Core.MailLogger -logfile:AppLogs\build.log autobuild-error
exit /b 1

:END
echo -- Post-Build-Startup of MOMS/INSIGHT is complete
echo -- Post-Build-Startup of MOMS/INSIGHT is complete
rem NAnt.exe -buildfile:MOMS_build.build -logger:NAnt.Core.MailLogger -logfile:AppLogs\build.log autobuild-complete
exit /b 0


