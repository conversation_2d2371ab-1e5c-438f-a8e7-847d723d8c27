@ECHO OFF

set NOW=%date:~10,4%-%date:~4,2%-%date:~7,2%
Set CURRENTDIR=%CD%
SET FPSERVER=%1
SET APPSERVER=%2
SET PROJECT=%3
SET ENVTYPE=%4
SET FINGERPRINTFEATURE=%5
SET HOCPFEATURE=%6
SET WEBSERVER=%7
SET CLUSTERNODE=%8
SET IFXFEATURE=%9
SHIFT
SHIFT
SHIFT
SHIFT
SHIFT
SHIFT
SHIFT
SHIFT
SHIFT
SET IFXSERVER=%1
SET ADPOLLINGTASK=%2
SET IRRFEATURE=%3
SET IMAGECLIENTFEATURE=%4
SET VOTTTASKFEATURE=%5
SET EMAILTASKFEATURE=%6
SET TRANSPORTALFEATURE=%7
SET TRANSPORTALSERVER=%8
SET RELEASEDATE=%9
SHIFT
SHIFT
SHIFT
SHIFT
SHIFT
SHIFT
SHIFT
SHIFT
SHIFT
SET RELEASEVERSION=%1
SET TRANSSERVER=%2
SET VEHICLEDETECTIONTASK=%3
SET HUMANREADSERVER=%4
SET HRFEATURE=%5
SET MANUALRESULTSFEATURE=%6
SET HOCPSERVER=%7
SET IRRSERVER=%8
SET TXPDBLISTENER=%9
SHIFT
SHIFT
SHIFT
SHIFT
SHIFT
SHIFT
SHIFT
SHIFT
SHIFT
SET ANGULAR9WEBSITEFEATURE=%1
SET DBUSER=%2
SET DBPASSWORD=%3

echo ADPOLLINGTASK is %ADPOLLINGTASK%

echo Image Review Post-Installation Startup is starting at %NOW%
echo ---------------------------------- 

if "%HRFEATURE%"=="true" GOTO HRFEATURE

SET STEP="Update Release version and date"
ECHO ----------------------------------------------------------
ECHO Update the Release version and date in the TransPortal DB
ECHO ----------------------------------------------------------
if "%TRANSPORTALFEATURE%" == "true" ( psexec -accepteula \\%TRANSPORTALSERVER% cmd /c sqlcmd -b -U %DBUSER% -P %DBPASSWORD% -Q "IF EXISTS (SELECT 1 FROM sys.columns WHERE Name=N'vcTagVer') BEGIN EXEC sp_executesql N'update stbApp set vcTagVer=''%RELEASEVERSION%'',dtReleaseDate=''%RELEASEDATE%'' where iAppID=853' END" -S %TXPDBLISTENER% -d SSO )
IF [%ERRORLEVEL%] NEQ [0] GOTO ERROR


:StartServices
SET STEP="Start IIS"
echo Step %STEP% commencing...  --------- 
if %ERRORLEVEL% NEQ 0 GOTO :error 
NAnt.exe -buildfile:ImageReview_build.build -D:iisserver=%APPSERVER% start-web
if %ERRORLEVEL% NEQ 0 GOTO :error 
if NOT "%APPSERVER%" == "%TRANSSERVER%" ( NAnt.exe -buildfile:ImageReview_build.build -D:iisserver=%TRANSSERVER% start-web)
if %ERRORLEVEL% NEQ 0 GOTO :error 
NAnt.exe -buildfile:ImageReview_build.build -D:iisserver=%WEBSERVER% start-web
if %ERRORLEVEL% NEQ 0 GOTO :error 
if "%FINGERPRINTFEATURE%" == "true" ( NAnt.exe -buildfile:ImageReview_build.build -D:iisserver=%FPSERVER%  start-web)
if %ERRORLEVEL% NEQ 0 GOTO :error 
if NOT "%APPSERVER%" == "%HOCPSERVER%" ( NAnt.exe -buildfile:ImageReview_build.build -D:iisserver=%HOCPSERVER% start-web)
if %ERRORLEVEL% NEQ 0 GOTO :error 
if NOT "%APPSERVER%" == "%IRRSERVER%" ( NAnt.exe -buildfile:ImageReview_build.build -D:iisserver=%IRRSERVER% start-web)
if %ERRORLEVEL% NEQ 0 GOTO :error 

:HRFEATURE
if "%HRFEATURE%" == "true" ( NAnt.exe -buildfile:ImageReview_build.build -D:iisserver=%HUMANREADSERVER% start-web)
if %ERRORLEVEL% NEQ 0 GOTO :error 
echo Step %STEP% completed...  --------- 




if "%CLUSTERNODE%"=="02" GOTO END
if "%IFXFEATURE%"=="false"  GOTO END
if "%ANGULAR9WEBSITEFEATURE%"=="true" GOTO END

:EnableIFXTasks
if exist \\%IFXSERVER%\C$\IFX\ScheduledTasks\IFXImageReviewSendTrans (C:\Windows\system32\schtasks.exe /S %IFXSERVER% /Change /TN IFXImageReviewSendTrans /ENABLE)
C:\Windows\system32\schtasks.exe /S %IFXSERVER% /Change /TN IFXImageReviewSendTransRetries /ENABLE
rem BAIFA has multiples of this task so lets enable them all
if exist \\%IFXSERVER%\C$\CAMS\ScheduledTasks\IFXImageReviewSendTrans  (C:\Windows\system32\schtasks.exe /S %IFXSERVER% /Change /TN IFXImageReviewSendTrans /ENABLE)
if exist \\%IFXSERVER%\C$\CAMS\ScheduledTasks\IFXImageReviewSendTrans2 (C:\Windows\system32\schtasks.exe /S %IFXSERVER% /Change /TN IFXImageReviewSendTrans2 /ENABLE)
if exist \\%IFXSERVER%\C$\CAMS\ScheduledTasks\IFXImageReviewSendTrans3 (C:\Windows\system32\schtasks.exe /S %IFXSERVER% /Change /TN IFXImageReviewSendTrans3 /ENABLE)
if exist \\%IFXSERVER%\C$\CAMS\ScheduledTasks\IFXImageReviewSendTrans4 (C:\Windows\system32\schtasks.exe /S %IFXSERVER% /Change /TN IFXImageReviewSendTrans4 /ENABLE)
rem VTA has multiples too
if exist \\%IFXSERVER%\C$\IFX\ScheduledTasks\IFXImageReviewSendTrans-1100 (C:\Windows\system32\schtasks.exe /S %IFXSERVER% /Change /TN IFXImageReviewSendTrans-1100 /ENABLE)
if exist \\%IFXSERVER%\C$\IFX\ScheduledTasks\IFXImageReviewSendTrans-1200 (C:\Windows\system32\schtasks.exe /S %IFXSERVER% /Change /TN IFXImageReviewSendTrans-1200 /ENABLE)
if exist \\%IFXSERVER%\C$\IFX\ScheduledTasks\IFXImageReviewSendTrans-1300 (C:\Windows\system32\schtasks.exe /S %IFXSERVER% /Change /TN IFXImageReviewSendTrans-1300 /ENABLE)
if exist \\%IFXSERVER%\C$\IFX\ScheduledTasks\IFXImageReviewSendTrans-1400 (C:\Windows\system32\schtasks.exe /S %IFXSERVER% /Change /TN IFXImageReviewSendTrans-1400 /ENABLE)

GOTO END


:error
echo ------- AN ERROR OCCURED DURING THIS STEP: %STEP% ------
echo ------- AN ERROR OCCURED DURING THIS STEP: %STEP% ------ 
exit /b 1

:END
echo -- Post-Build-Startup of Image Review is complete
echo -- Post-Build-Startup of Image Review is complete 
exit /b 0


