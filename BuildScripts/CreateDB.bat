@ECHO OFF

set NOW=%date:~10,4%-%date:~4,2%-%date:~7,2%
Set CURRENTDIR=%CD%
SET TRANSPORTALSERVER=%1
SET PROJECT=%2
SET ENVTYPE=%3
SET DEPLOYMENTDRIVE=%4
SET DEPLOYMENTDIR=%5

echo %MOMSSERVER%
echo %TRANSPORTALSERVER%


:CreateTransPortalSSODB
SET STEP="Create Transportal DB"
echo Step %STEP% commencing...  --------- >> AppLogs\dbcreation.log
ECHO Kill all User DB sessions..  --------- >> AppLogs\dbcreation.log
cmd /c sqlcmd -b -i Autobuild\KillSessions.sql  -v dbname = SSO -S %TRANSPORTALSERVER% -d master
if %ERRORLEVEL% NEQ 0 GOTO :error
call Autobuild\do_invoke_any_locrem1.cmd %TRANSPORTALSERVER% %DEPLOYMENTDRIVE%:\Deployment-TransPortal\%DEPLOYMENTDIR%\Database\BuildScripts "create-DB.cmd Quiet" %DEPLOYMENTDRIVE%:\Deployment-TransPortal\%DEPLOYMENTDIR%\Database\BuildScripts\create-DB.cmd unused unused unused unused
if %ERRORLEVEL% NEQ 0 GOTO :error
echo Step %STEP% completed...  --------- >> AppLogs\dbcreation.log


:AlterSSOData
SET STEP="Alter SSO Data"
rem our inernal DEV/QA servers are of project "QA" and "DEV" and they won't have any demo data so skip this step
rem if "%PROJECT%"=="QA" GOTO :END
rem if "%PROJECT%"=="DEV" GOTO :END
call Autobuild\do_invoke_any_locrem1.cmd %TRANSPORTALSERVER% %DEPLOYMENTDRIVE%:\Deployment-TransPortal\%DEPLOYMENTDIR%\Database\Alter\Projects\%PROJECT% "SSODataAutobuild.cmd %ENVTYPE% Quiet" %DEPLOYMENTDRIVE%:\Deployment-TransPortal\%DEPLOYMENTDIR%\Database\Alter\Projects\%PROJECT%\SSODataAutobuild.cmd unused unused unused unused
if %ERRORLEVEL% NEQ 0 GOTO :error
echo Step %STEP% completed...  --------- >> AppLogs\dbcreation.log
GOTO END


:error
echo ------- AN ERROR OCCURED DURING THIS STEP: %STEP% ------
echo ------- AN ERROR OCCURED DURING THIS STEP: %STEP% ------ >> AppLogs\dbcreation.log

exit /b 1

:END
echo -- Tranportal DB creation is complete
echo -- Tranportal DB creation is complete >> AppLogs\DBcreation.log

exit /b 0

