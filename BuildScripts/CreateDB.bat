@ECHO OFF

set NOW=%date:~10,4%-%date:~4,2%-%date:~7,2%
Set CURRENTDIR=%CD%
SET MOMSSERVER=%1
SET PROJECT=%2
SET ENVTYPE=%3
SET DEPLOYMENTDRIVE=%4
SET DEPLOYMENTDIR=%5
SET INFINITY=%6
SET ARCS=%7
SET MIR=%8
SET IFX=%9
SHIFT
SHIFT
SHIFT
SHIFT
SHIFT
SHIFT
SHIFT
SHIFT
SHIFT
SET TRANSSUITE=%1
SET SOLARWINDS=%2
SET INTEGRITY=%3
SET CPS=%4
SET VOTT=%5
SET MDINTEGRITY=%6
SET DBALWAYSON=%7
SET WHATSUP=%8
SET DBSERVER=%9

echo %DBSERVER%
echo %INFINITY%
echo %ARCS%
echo TransSuite %TRANSSUITE%
set TRUE=%CD%
IF "%INTEGRITY%"=="true" set TRUE=true
IF "%MIR%"=="true" set TRUE=true
echo Integrity equals %INTEGRITY%
echo MIR equals %MIR%
echo true equals %TRUE%

:CreateMOMSDB
SET STEP="Create MOMS DB"
echo Step %STEP% commencing...  --------- 
ECHO Kill all User DB sessions..  --------- 
rem cmd /c sqlcmd -b -i %DEPLOYMENTDRIVE%:\Deployment\BuildScripts\Autobuild\KillSessions.sql  -v dbname = MOMS -S %DBSERVER% -d master
if %ERRORLEVEL% NEQ 0 GOTO :error

call Autobuild\do_invoke_any_locrem1.cmd %DBSERVER% %DEPLOYMENTDRIVE%:\Deployment\%DEPLOYMENTDIR%\LocalDBScripts "create-MOMS-DB.cmd Quiet %PROJECT% %ENVTYPE%" %DEPLOYMENTDRIVE%:\Deployment\%DEPLOYMENTDIR%\LocalDBScripts\create-MOMS-DB.cmd unused unused unused unused  
if %ERRORLEVEL% NEQ 0 GOTO :error

SET STEP="Execute IFX data insert scripts = %IFX%"
ECHO Execute IFX DB Insert Scripts = %IFX%   --------- 
IF "%IFX%"=="true"  ( call Autobuild\do_invoke_any_locrem1.cmd %DBSERVER% %DEPLOYMENTDRIVE%:\Deployment\%DEPLOYMENTDIR%\LocalDBScripts "execute_IFX_Data_Inserts.cmd Quiet" %DEPLOYMENTDRIVE%:\Deployment\%DEPLOYMENTDIR%\LocalDBScripts\execute_IFX_Data_Inserts.cmd unused unused unused unused
) 
if %ERRORLEVEL% NEQ 0 GOTO :error
SET STEP="Execute Infinity DB Insert Scripts = %INFINITY%"
ECHO Execute Infinity DB Insert Scripts = %INFINITY%   --------- 
IF "%INFINITY%"=="true" IF "%CPS%"=="false" ( call Autobuild\do_invoke_any_locrem1.cmd %DBSERVER% %DEPLOYMENTDRIVE%:\Deployment\%DEPLOYMENTDIR%\LocalDBScripts "execute_Infinity_Data_Inserts.cmd Quiet" %DEPLOYMENTDRIVE%:\Deployment\%DEPLOYMENTDIR%\LocalDBScripts\execute_Infinity_Data_Inserts.cmd unused unused unused unused
) 
SET STEP="Execute ARCS DB Insert Scripts = %ARCS%"
ECHO Execute ARCS DB Insert Scripts = %ARCS%   --------- 
IF "%ARCS%"=="true" ( call Autobuild\do_invoke_any_locrem1.cmd %DBSERVER% %DEPLOYMENTDRIVE%:\Deployment\%DEPLOYMENTDIR%\LocalDBScripts "execute_ARCS_Data_Inserts.cmd Quiet" %DEPLOYMENTDRIVE%:\Deployment\%DEPLOYMENTDIR%\LocalDBScripts\execute_ARCS_Data_Inserts.cmd unused unused unused unused
) 
if %ERRORLEVEL% NEQ 0 GOTO :error
rem call Autobuild\do_invoke_any_locrem1.cmd %DBSERVER% D:\Deployment\%DEPLOYMENTDIR%\LocalDBScripts "execute_NM_Data_Inserts.cmd Quiet" D:\Deployment\%DEPLOYMENTDIR%\LocalDBScripts\execute_NM_Data_Inserts.cmd unused unused  unused unused
SET STEP="Execute Transsuite data insert scripts = %TRANSSUITE%"
ECHO Execute TRANSSUITE DB Insert Scripts = %TRANSSUITE%   --------- 
IF "%TRANSSUITE%"=="true"  ( call Autobuild\do_invoke_any_locrem1.cmd %DBSERVER% %DEPLOYMENTDRIVE%:\Deployment\%DEPLOYMENTDIR%\LocalDBScripts "execute_TransSuite_Data_Inserts.cmd Quiet" %DEPLOYMENTDRIVE%:\Deployment\%DEPLOYMENTDIR%\LocalDBScripts\execute_TransSuite_Data_Inserts.cmd unused unused unused unused
) 
if %ERRORLEVEL% NEQ 0 GOTO :error
ECHO Execute SOLARWINDS DB Insert Scripts = %SOLARWINDS%   --------- 
IF "%SOLARWINDS%"=="true"  ( call Autobuild\do_invoke_any_locrem1.cmd %DBSERVER% %DEPLOYMENTDRIVE%:\Deployment\%DEPLOYMENTDIR%\LocalDBScripts "execute_NM_Data_Inserts.cmd Quiet" %DEPLOYMENTDRIVE%:\Deployment\%DEPLOYMENTDIR%\LocalDBScripts\execute_NM_Data_Inserts.cmd unused unused unused unused
) 
if %ERRORLEVEL% NEQ 0 GOTO :error
ECHO Execute WHATSUP DB Insert Scripts = %WHATSUP%   --------- 
IF "%WHATSUP%"=="true"  ( call Autobuild\do_invoke_any_locrem1.cmd %DBSERVER% %DEPLOYMENTDRIVE%:\Deployment\%DEPLOYMENTDIR%\LocalDBScripts "execute_NM_Data_Inserts.cmd Quiet" %DEPLOYMENTDRIVE%:\Deployment\%DEPLOYMENTDIR%\LocalDBScripts\execute_NM_Data_Inserts.cmd unused unused unused unused
) 
if %ERRORLEVEL% NEQ 0 GOTO :error
ECHO Execute SOLARWINDS/INFINITY RELATED DB Insert Scripts = %SOLARWINDS%   --------- 
IF "%SOLARWINDS%"=="true" IF "%INFINITY%"=="true"  ( call Autobuild\do_invoke_any_locrem1.cmd %DBSERVER% %DEPLOYMENTDRIVE%:\Deployment\%DEPLOYMENTDIR%\LocalDBScripts "execute_Related_Infinity_SW_Data_Inserts.cmd Quiet" %DEPLOYMENTDRIVE%:\Deployment\%DEPLOYMENTDIR%\LocalDBScripts\execute_Related_Infinity_SW_Data_Inserts.cmd unused unused unused unused
) 
if %ERRORLEVEL% NEQ 0 GOTO :error
ECHO Execute WHATSUP/INFINITY RELATED DB Insert Scripts = %WHATSUP%   --------- 
IF "%WHATSUP%"=="true" IF "%INFINITY%"=="true"  ( call Autobuild\do_invoke_any_locrem1.cmd %DBSERVER% %DEPLOYMENTDRIVE%:\Deployment\%DEPLOYMENTDIR%\LocalDBScripts "execute_Related_Infinity_SW_Data_Inserts.cmd Quiet" %DEPLOYMENTDRIVE%:\Deployment\%DEPLOYMENTDIR%\LocalDBScripts\execute_Related_Infinity_SW_Data_Inserts.cmd unused unused unused unused
) 
if %ERRORLEVEL% NEQ 0 GOTO :error
ECHO Execute Integrity RELATED DB Insert Scripts = %INTEGRITY%   --------- 
IF "%INTEGRITY%"=="true" IF "%MDINTEGRITY%"=="false" ( call Autobuild\do_invoke_any_locrem1.cmd %DBSERVER% %DEPLOYMENTDRIVE%:\Deployment\%DEPLOYMENTDIR%\LocalDBScripts "execute_Integrity_Data_Inserts.cmd Quiet" %DEPLOYMENTDRIVE%:\Deployment\%DEPLOYMENTDIR%\LocalDBScripts\execute_Integrity_Data_Inserts.cmd unused unused unused unused
) 
if %ERRORLEVEL% NEQ 0 GOTO :error

ECHO Execute Integrity MDTA-flavored DB Insert Scripts = %INTEGRITY%   --------- 
IF "%MDINTEGRITY%"=="true" ( call Autobuild\do_invoke_any_locrem1.cmd %DBSERVER% %DEPLOYMENTDRIVE%:\Deployment\%DEPLOYMENTDIR%\LocalDBScripts "execute_Integrity_MD_Data_Inserts.cmd Quiet" %DEPLOYMENTDRIVE%:\Deployment\%DEPLOYMENTDIR%\LocalDBScripts\execute_Integrity_MD_Data_Inserts.cmd unused unused unused unused
) 
if %ERRORLEVEL% NEQ 0 GOTO :error

SET STEP="Execute Integrity (legacy MIR) DB Insert Scripts = %MIR%"
ECHO Execute Integrity (legacy MIR) DB Insert Scripts if valid   --------- 
IF NOT EXIST \\%DBSERVER%\%DEPLOYMENTDRIVE%$\Deployment\%DEPLOYMENTDIR%\LocalDBScripts\execute_ImageReview_Data_Inserts.cmd IF "%MIR%"=="true" IF "%INTEGRITY%"=="false" ( call Autobuild\do_invoke_any_locrem1.cmd %DBSERVER% %DEPLOYMENTDRIVE%:\Deployment\%DEPLOYMENTDIR%\LocalDBScripts "execute_Integrity_Data_Inserts.cmd Quiet" %DEPLOYMENTDRIVE%:\Deployment\%DEPLOYMENTDIR%\LocalDBScripts\execute_Integrity_Data_Inserts.cmd unused unused unused unused
) 
if %ERRORLEVEL% NEQ 0 GOTO :error

SET STEP="Execute ImageReview DB Insert Scripts = %MIR%"
ECHO Execute Image Review DB Insert Scripts = %MIR%   --------- 
IF EXIST \\%DBSERVER%\%DEPLOYMENTDRIVE%$\Deployment\%DEPLOYMENTDIR%\LocalDBScripts\execute_ImageReview_Data_Inserts.cmd  IF "%MIR%"=="true" ( call Autobuild\do_invoke_any_locrem1.cmd %DBSERVER% %DEPLOYMENTDRIVE%:\Deployment\%DEPLOYMENTDIR%\LocalDBScripts "execute_ImageReview_Data_Inserts.cmd Quiet" %DEPLOYMENTDRIVE%:\Deployment\%DEPLOYMENTDIR%\LocalDBScripts\execute_ImageReview_Data_Inserts.cmd unused unused unused unused
) 
if %ERRORLEVEL% NEQ 0 GOTO :error
ECHO Execute CPS DB Insert Scripts = %CPS%   --------- 
IF "%CPS%"=="true" ( call Autobuild\do_invoke_any_locrem1.cmd %DBSERVER% %DEPLOYMENTDRIVE%:\Deployment\%DEPLOYMENTDIR%\LocalDBScripts "execute_CPS_Data_Inserts.cmd Quiet" %DEPLOYMENTDRIVE%:\Deployment\%DEPLOYMENTDIR%\LocalDBScripts\execute_CPS_Data_Inserts.cmd unused unused unused unused
) 
if %ERRORLEVEL% NEQ 0 GOTO :error

ECHO Execute VOTT DB Insert Scripts = %VOTT%   --------- 
IF "%VOTT%"=="true" ( call Autobuild\do_invoke_any_locrem1.cmd %DBSERVER% %DEPLOYMENTDRIVE%:\Deployment\%DEPLOYMENTDIR%\LocalDBScripts "execute_VOTT_Data_Inserts.cmd Quiet" %DEPLOYMENTDRIVE%:\Deployment\%DEPLOYMENTDIR%\LocalDBScripts\execute_VOTT_Data_Inserts.cmd unused unused unused unused
) 
if %ERRORLEVEL% NEQ 0 GOTO :error



echo Step %STEP% completed...  --------- 

:CreateProject-SpecificData
SET STEP="Create Project-Specific Data"
echo Step %STEP% commencing...  --------- 
rem our MOMS QA servers are of project "QA" and they won't have any demo data so skip this step
REM if "%PROJECT%"=="PRODUCT" GOTO :END
if exist \\%DBSERVER%\%DEPLOYMENTDRIVE%$\Deployment\%DEPLOYMENTDIR%\Database\Data\INSIGHT_DATA\%PROJECT%\%ENVTYPE%_Data_Script.sql (call Autobuild\do_invoke_any_locrem1.cmd %DBSERVER% %DEPLOYMENTDRIVE%:\Deployment\%DEPLOYMENTDIR%\Database\Data\INSIGHT_DATA "DataAutobuild.cmd %PROJECT% %ENVTYPE% Quiet" %DEPLOYMENTDRIVE%:\Deployment\%DEPLOYMENTDIR%\Database\Data\INSIGHT_DATA\DataAutobuild.cmd unused unused unused unused 
) 
if %ERRORLEVEL% NEQ 0 GOTO :error

echo Step %STEP% completed...  --------- 


GOTO END


:error
echo ------- AN ERROR OCCURED DURING THIS STEP: %STEP% ------
echo ------- AN ERROR OCCURED DURING THIS STEP: %STEP% ------ 

exit /b 1

:END
echo -- DB creation of Insight is complete
echo -- DB creation of Insight is complete 

exit /b 0

