@echo off
set NOW=%date:~10,4%-%date:~4,2%-%date:~7,2%
set scripts-dir=%CD%
set /P user=[Enter SSPI account user ex. elntest\arcsyseln]:=
set "psCommand=powershell -Command "$pword = read-host 'Enter Password' -AsSecureString ; ^
    $BSTR=[System.Runtime.InteropServices.Marshal]::SecureStringToBSTR($pword); ^
        [System.Runtime.InteropServices.Marshal]::PtrToStringAuto($BSTR)""
for /f "usebackq delims=" %%p in (`%psCommand%`) do set pw=%%p
NAnt.exe -buildfile:ImageReview_build.build -logger:NAnt.Core.MailLogger -logfile:AppLogs\911-Createtasks.log create-tasks  -D:deployment.user=%user% -D:deployment.password=%pw%
pause 


