@echo off
rem set variables
Set CURRENTDIR=%CD%
Set SOURCE=%1
Set SECONDS=%2
Set APPTABLABEL=%3
Set LANDINGPAGE=%4

echo %SOURCE%
echo %SECONDS%

"%CURRENTDIR%\fnr.exe" --cl --dir "%SOURCE%" --fileMask "*.ts" --includeSubDirectories --find "sessionTimeOutInSeconds: 43200" --replace "sessionTimeOutInSeconds: %SECONDS%"

"%CURRENTDIR%\fnr.exe" --cl --dir "%SOURCE%" --fileMask "*.ts" --includeSubDirectories --find "appTabLabel: ""CPS""" --replace "appTabLabel: ""%APPTABLABEL%"""

"%CURRENTDIR%\fnr.exe" --cl --dir "%SOURCE%" --fileMask "*.ts" --includeSubDirectories --find "showLandingPage: true" --replace "showLandingPage: %LANDINGPAGE%"

"%CURRENTDIR%\fnr.exe" --cl --dir "%SOURCE%" --fileMask "*.ts" --includeSubDirectories --find "img: ""v1""" --replace "img: ""v2"""



