@ECHO OFF

set NOW=%date:~10,4%-%date:~4,2%-%date:~7,2%
Set CURRENTDIR=%CD%
SET FPSERVER=%1
SET APPSERVER=%2
SET PROJECT=%3
SET ENVTYPE=%4
SET DEPLOYMENTDRIVE=%5
SET DEPLOYMENTDIR=%6
SET FINGERPRINTFEATURE=%7
SET HOCPFEATURE=%8
SET WEBSERVER=%9
SHIFT
SHIFT
SHIFT
SHIFT
SHIFT
SHIFT
SHIFT
SHIFT
SHIFT
SET DBSERVER=%1
SET RESOURCESPATH=%2
SET CLUSTERNODE=%3
SET TRANSSERVER=%4
SET IFXSERVER=%5
SET HUMANREADSERVER=%6
SET HRFEATURE=%7
SET HOCPSERVER=%8
SET IRRSERVER=%9
SHIFT
SHIFT
SHIFT
SHIFT
SHIFT
SHIFT
SHIFT
SHIFT
SHIFT
SET PREHOCPSERVER=%1
SET QFREETASKSERVER=%2
SET QFREEFEATURE=%3

set fullstamp=%YYYY%-%MM%-%DD%_%HH%-%Min%-%Sec%


echo %FPSERVER%
echo %APPSERVER%
echo %DEPLOYMENTDRIVE%
echo %DEPLOYMENTDIR%

ECHO %DBSERVER% DBSERVER
ECHO %RESOURCESPATH% RESOURCES PATH
ECHO %FPCLIENTSERVER% FPClient 
ECHO %CLUSTERNODE% cluster

ECHO HUMANREADSERVER $HUMANREADSERVER%


echo Image Review Installation is starting at %fullstamp% 
echo ---------------------------------- 

if "%CLUSTERNODE%"=="02" GOTO :create_new_bus
:Copy_Resources
SET STEP="Copy resources"
echo Step %STEP% commencing...  --------- 
echo Step %STEP% commencing...
ECHO %DBSERVER% REPORTSERVER
ECHO %RESOURCESPATH% RESOURCES PATH
ECHO %FPCLIENTSERVER% FPClient 
ECHO %CLUSTERNODE% cluster

IF "%HRFEATURE%"=="true" GOTO HRFEATURE 

verify > nul  
echo xcopy \\%DBSERVER%\%DEPLOYMENTDRIVE%$\Deployment-ImageReview\%DEPLOYMENTDIR%\RPT01\Resources \\%DBSERVER%\""%RESOURCESPATH%"" /Y /I /F /S /R
call xcopy \\%DBSERVER%\%DEPLOYMENTDRIVE%$\Deployment-ImageReview\%DEPLOYMENTDIR%\RPT01\Resources \\%DBSERVER%\""%RESOURCESPATH%"" /Y /I /F /S /R


if %ERRORLEVEL% NEQ 0 GOTO :error
echo Step %STEP% completed...  --------- 



verify > nul

:create_new_bus
echo Step %STEP% commencing...  --------- SET STEP="Copy current FP dirs to _bu dirs"

if exist \\%FPSERVER%\C$\FPP\AppLayer (robocopy \\%FPSERVER%\C$\FPP\AppLayer \\%FPSERVER%\C$\FPP_bu\AppLayer /MIR /R:1)
if %ERRORLEVEL% GEQ 8 GOTO :error 
if exist \\%FPSERVER%\C$\FPP\CommonLayer (robocopy \\%FPSERVER%\C$\FPP\CommonLayer \\%FPSERVER%\C$\FPP_bu\CommonLayer /MIR /R:1)
if %ERRORLEVEL% GEQ 8 GOTO :error 
if exist \\%FPSERVER%\C$\FPP\ServiceHost (robocopy \\%FPSERVER%\C$\FPP\ServiceHost \\%FPSERVER%\C$\FPP_bu\ServiceHost /MIR /R:1)
if %ERRORLEVEL% GEQ 8 GOTO :error 
if exist \\%FPSERVER%\C$\FPP\ThirdParty  (robocopy \\%FPSERVER%\C$\FPP\ThirdParty \\%FPSERVER%\C$\FPP_bu\ThirdParty /MIR /R:1)
if %ERRORLEVEL% GEQ 8 GOTO :error 


if exist \\%FPSERVER%\C$\FPM\AppLayer (robocopy \\%FPSERVER%\C$\FPM\AppLayer \\%FPSERVER%\C$\FPM_bu\AppLayer /MIR /R:1)
if %ERRORLEVEL% GEQ 8 GOTO :error 
if exist \\%FPSERVER%\C$\FPM\CommonLayer (robocopy \\%FPSERVER%\C$\FPM\CommonLayer \\%FPSERVER%\C$\FPM_bu\CommonLayer /MIR /R:1)
if %ERRORLEVEL% GEQ 8 GOTO :error 
if exist \\%FPSERVER%\C$\FPM\ServiceClient (robocopy \\%FPSERVER%\C$\FPM\ServiceClient \\%FPSERVER%\C$\FPM_bu\ServiceClient /MIR /R:1)
if %ERRORLEVEL% GEQ 8 GOTO :error 
if exist \\%FPSERVER%\C$\FPM\ServiceHost (robocopy \\%FPSERVER%\C$\FPM\ServiceHost \\%FPSERVER%\C$\FPM_bu\ServiceHost /MIR /R:1)
if %ERRORLEVEL% GEQ 8 GOTO :error 
if exist \\%FPSERVER%\C$\FPM\ThirdParty (robocopy \\%FPSERVER%\C$\FPM\ThirdParty \\%FPSERVER%\C$\FPM_bu\ThirdParty /MIR /R:1)
if %ERRORLEVEL% GEQ 8 GOTO :error 
if exist \\%FPSERVER%\C$\FPM\WebApi (robocopy \\%FPSERVER%\C$\FPM\WebApi \\%FPSERVER%\C$\FPM_bu\WebApi /MIR /R:1)
if %ERRORLEVEL% GEQ 8 GOTO :error 
SET STEP="Copy current HOCP dirs to _bu dirs"
echo Copy HOCP to HOCP_bu
if exist \\%HOCPSERVER%\C$\HOCP\AppLayer (robocopy \\%HOCPSERVER%\C$\HOCP\AppLayer \\%HOCPSERVER%\C$\HOCP_bu\AppLayer /MIR /R:1)
if %ERRORLEVEL% GEQ 8 GOTO :error 
if exist \\%HOCPSERVER%\C$\HOCP\CommonLayer (robocopy \\%HOCPSERVER%\C$\HOCP\CommonLayer \\%HOCPSERVER%\C$\HOCP_bu\CommonLayer /MIR /R:1)
if %ERRORLEVEL% GEQ 8 GOTO :error 
if exist \\%HOCPSERVER%\C$\HOCP\ServiceClient (robocopy \\%HOCPSERVER%\C$\HOCP\ServiceClient \\%HOCPSERVER%\C$\HOCP_bu\ServiceClient /MIR /R:1)
if %ERRORLEVEL% GEQ 8 GOTO :error 
if exist \\%HOCPSERVER%\C$\HOCP\ServiceHost (robocopy \\%HOCPSERVER%\C$\HOCP\ServiceHost \\%HOCPSERVER%\C$\HOCP_bu\ServiceHost /MIR /R:1)
if %ERRORLEVEL% GEQ 8 GOTO :error 
if exist \\%HOCPSERVER%\C$\HOCP\ThirdParty (robocopy \\%HOCPSERVER%\C$\HOCP\ThirdParty \\%HOCPSERVER%\C$\HOCP_bu\ThirdParty /MIR /R:1)
if %ERRORLEVEL% GEQ 8 GOTO :error 
if exist \\%HOCPSERVER%\C$\HOCP\WebApi (robocopy \\%HOCPSERVER%\C$\HOCP\WebApi \\%HOCPSERVER%\C$\HOCP_bu\WebApi /MIR /R:1)
if %ERRORLEVEL% GEQ 8 GOTO :error 
echo Copy PREHOCP to PREHOCP_bu
if exist \\%PREHOCPSERVER%\C$\PREHOCP\AppLayer (robocopy \\%PREHOCPSERVER%\C$\PREHOCP\AppLayer \\%PREHOCPSERVER%\C$\PREHOCP_bu\AppLayer /MIR /R:1)
if %ERRORLEVEL% GEQ 8 GOTO :error 
if exist \\%PREHOCPSERVER%\C$\PREHOCP\CommonLayer (robocopy \\%PREHOCPSERVER%\C$\PREHOCP\CommonLayer \\%PREHOCPSERVER%\C$\PREHOCP_bu\CommonLayer /MIR /R:1)
if %ERRORLEVEL% GEQ 8 GOTO :error 
if exist \\%PREHOCPSERVER%\C$\PREHOCP\ServiceClient (robocopy \\%PREHOCPSERVER%\C$\PREHOCP\ServiceClient \\%PREHOCPSERVER%\C$\PREHOCP_bu\ServiceClient /MIR /R:1)
if %ERRORLEVEL% GEQ 8 GOTO :error 
if exist \\%PREHOCPSERVER%\C$\PREHOCP\ServiceHost (robocopy \\%PREHOCPSERVER%\C$\PREHOCP\ServiceHost \\%PREHOCPSERVER%\C$\PREHOCP_bu\ServiceHost /MIR /R:1)
if %ERRORLEVEL% GEQ 8 GOTO :error 
if exist \\%PREHOCPSERVER%\C$\PREHOCP\ThirdParty (robocopy \\%PREHOCPSERVER%\C$\PREHOCP\ThirdParty \\%PREHOCPSERVER%\C$\PREHOCP_bu\ThirdParty /MIR /R:1)
if %ERRORLEVEL% GEQ 8 GOTO :error 
if exist \\%PREHOCPSERVER%\C$\PREHOCP\WebApi (robocopy \\%PREHOCPSERVER%\C$\PREHOCP\WebApi \\%PREHOCPSERVER%\C$\PREHOCP_bu\WebApi /MIR /R:1)
if %ERRORLEVEL% GEQ 8 GOTO :error 
SET STEP="Copy current IRR dirs to _bu dirs"
echo Copy IRR to IRR_bu
if exist \\%IRRSERVER%\C$\IRR\AppLayer (robocopy \\%IRRSERVER%\C$\IRR\AppLayer \\%IRRSERVER%\C$\IRR_bu\AppLayer /MIR /R:1)
if %ERRORLEVEL% GEQ 8 GOTO :error 
if exist \\%IRRSERVER%\C$\IRR\CommonLayer (robocopy \\%IRRSERVER%\C$\IRR\CommonLayer \\%IRRSERVER%\C$\IRR_bu\CommonLayer /MIR /R:1)
if %ERRORLEVEL% GEQ 8 GOTO :error 
if exist \\%IRRSERVER%\C$\IRR\ServiceClient (robocopy \\%IRRSERVER%\C$\IRR\ServiceClient \\%IRRSERVER%\C$\IRR_bu\ServiceClient /MIR /R:1)
if %ERRORLEVEL% GEQ 8 GOTO :error 
if exist \\%IRRSERVER%\C$\IRR\ServiceHost (robocopy \\%IRRSERVER%\C$\IRR\ServiceHost \\%IRRSERVER%\C$\IRR_bu\ServiceHost /MIR /R:1)
if %ERRORLEVEL% GEQ 8 GOTO :error 
if exist \\%IRRSERVER%\C$\IRR\ThirdParty (robocopy \\%IRRSERVER%\C$\IRR\ThirdParty \\%IRRSERVER%\C$\IRR_bu\ThirdParty /MIR /R:1)
if %ERRORLEVEL% GEQ 8 GOTO :error 
if exist \\%IRRSERVER%\C$\IRR\WebApi  (robocopy \\%IRRSERVER%\C$\IRR\WebApi \\%IRRSERVER%\C$\IRR_bu\WebApi /MIR /R:1)
if %ERRORLEVEL% GEQ 8 GOTO :error 
if %ERRORLEVEL% GEQ 8 GOTO :error 
echo Copy MIR-MRT to MIR_bu MRT
rem if exist \\%APPSERVER%\C$\MIR\ManualResultsTask\ (robocopy \\%APPSERVER%\C$\MIR\ManualResultsTask\ \\%APPSERVER%\C$\MIR_bu\ManualResultsTask\ /MIR /R:1)
if %ERRORLEVEL% GEQ 8 GOTO :error 
echo Copy MIR-TRANS to MIR_bu TRANS
if exist \\%TRANSSERVER%\C$\MIR\TransactionsServer\ (robocopy \\%TRANSSERVER%\C$\MIR\TransactionsServer\ \\%TRANSSERVER%\C$\MIR_bu\TransactionsServer\ /MIR /R:1)
if %ERRORLEVEL% GEQ 8 GOTO :error 
if exist \\%APPSERVER%\C$\MIR\ImageReview\CommonLayer\ (robocopy \\%APPSERVER%\C$\MIR\ImageReview\CommonLayer\ \\%APPSERVER%\C$\MIR_bu\ImageReview\CommonLayer\ /MIR /R:1)
if %ERRORLEVEL% GEQ 8 GOTO :error 
if exist \\%APPSERVER%\C$\MIR\ImageReview\packages (robocopy \\%APPSERVER%\C$\MIR\ImageReview\packages\ \\%APPSERVER%\C$\MIR_bu\ImageReview\packages\ /MIR /R:1)
if %ERRORLEVEL% GEQ 8 GOTO :error
if exist \\%APPSERVER%\C$\MIR\ImageReview\Plugins (robocopy \\%APPSERVER%\C$\MIR\ImageReview\Plugins\ \\%APPSERVER%\C$\MIR_bu\ImageReview\Plugins\ /MIR /R:1)
if %ERRORLEVEL% GEQ 8 GOTO :error
if exist \\%APPSERVER%\C$\MIR\ImageReview\ServiceClient (robocopy \\%APPSERVER%\C$\MIR\ImageReview\ServiceClient\ \\%APPSERVER%\C$\MIR_bu\ImageReview\ServiceClient\ /MIR /R:1)
if %ERRORLEVEL% GEQ 8 GOTO :error
if exist \\%APPSERVER%\C$\MIR\ImageReview\ServiceHost\ (robocopy \\%APPSERVER%\C$\MIR\ImageReview\ServiceHost\ \\%APPSERVER%\C$\MIR_bu\ImageReview\ServiceHost\ /MIR /R:1)
if %ERRORLEVEL% GEQ 8 GOTO :error
if exist \\%APPSERVER%\C$\MIR\ImageReview\ThirdParty (robocopy \\%APPSERVER%\C$\MIR\ImageReview\ThirdParty\ \\%APPSERVER%\C$\MIR_bu\ImageReview\ThirdParty\ /MIR /R:1)
if %ERRORLEVEL% GEQ 8 GOTO :error
if exist \\%APPSERVER%\C$\MIR\ImageReview\Workflows (robocopy \\%APPSERVER%\C$\MIR\ImageReview\Workflows\ \\%APPSERVER%\C$\MIR_bu\ImageReview\Workflows\ /MIR /R:1)
if %ERRORLEVEL% GEQ 8 GOTO :error
if exist \\%APPSERVER%\C$\MIR\ImageReview\AppLayer (robocopy \\%APPSERVER%\C$\MIR\ImageReview\AppLayer\ \\%APPSERVER%\C$\MIR_bu\ImageReview\AppLayer\ /MIR /R:1)
if %ERRORLEVEL% GEQ 8 GOTO :error


if exist \\%WEBSERVER%\C$\MIR\ImageReviewWeb\ (robocopy \\%WEBSERVER%\C$\MIR\ImageReviewWeb\ \\%WEBSERVER%\C$\MIR_bu\ImageReviewWeb\ /MIR /R:1)
if %ERRORLEVEL% GEQ 8 GOTO :error 
rem if exist \\%APPSERVER%\C$\MIR\ScheduledTasks\ (robocopy \\%APPSERVER%\C$\MIR\ScheduledTasks\ \\%APPSERVER%\C$\MIR_bu\ScheduledTasks\ /MIR /R:1)
if %ERRORLEVEL% GEQ 8 GOTO :error 


echo Step %STEP% completed...  --------- 
verify > nul

:install_files
SET STEP="Image Review - Copy distribution dirs to C drives"
echo Step %STEP% commencing...  
if exist \\%FPSERVER%\%DEPLOYMENTDRIVE%$\Deployment-ImageReview\%DEPLOYMENTDIR%\FP%CLUSTERNODE%\FPP (robocopy \\%FPSERVER%\%DEPLOYMENTDRIVE%$\Deployment-ImageReview\%DEPLOYMENTDIR%\FP%CLUSTERNODE%\FPP\AppLayer \\%FPSERVER%\C$\FPP\AppLayer /MIR /R:1)
if %ERRORLEVEL% GEQ 8 GOTO :error
if exist \\%FPSERVER%\%DEPLOYMENTDRIVE%$\Deployment-ImageReview\%DEPLOYMENTDIR%\FP%CLUSTERNODE%\FPP (robocopy \\%FPSERVER%\%DEPLOYMENTDRIVE%$\Deployment-ImageReview\%DEPLOYMENTDIR%\FP%CLUSTERNODE%\FPP\CommonLayer \\%FPSERVER%\C$\FPP\CommonLayer /MIR /R:1)
if %ERRORLEVEL% GEQ 8 GOTO :error
if exist \\%FPSERVER%\%DEPLOYMENTDRIVE%$\Deployment-ImageReview\%DEPLOYMENTDIR%\FP%CLUSTERNODE%\FPP (robocopy \\%FPSERVER%\%DEPLOYMENTDRIVE%$\Deployment-ImageReview\%DEPLOYMENTDIR%\FP%CLUSTERNODE%\FPP\ServiceHost \\%FPSERVER%\C$\FPP\ServiceHost /MIR /R:1)
if %ERRORLEVEL% GEQ 8 GOTO :error
if exist \\%FPSERVER%\%DEPLOYMENTDRIVE%$\Deployment-ImageReview\%DEPLOYMENTDIR%\FP%CLUSTERNODE%\FPP (robocopy \\%FPSERVER%\%DEPLOYMENTDRIVE%$\Deployment-ImageReview\%DEPLOYMENTDIR%\FP%CLUSTERNODE%\FPP\ThirdParty \\%FPSERVER%\C$\FPP\ThirdParty /MIR /R:1)
if %ERRORLEVEL% GEQ 8 GOTO :error



if exist \\%FPSERVER%\%DEPLOYMENTDRIVE%$\Deployment-ImageReview\%DEPLOYMENTDIR%\FP%CLUSTERNODE%\FPM (robocopy \\%FPSERVER%\%DEPLOYMENTDRIVE%$\Deployment-ImageReview\%DEPLOYMENTDIR%\FP%CLUSTERNODE%\FPM\AppLayer \\%FPSERVER%\C$\FPM\AppLayer /MIR /R:1)
if %ERRORLEVEL% GEQ 8 GOTO :error
if exist \\%FPSERVER%\%DEPLOYMENTDRIVE%$\Deployment-ImageReview\%DEPLOYMENTDIR%\FP%CLUSTERNODE%\FPM (robocopy \\%FPSERVER%\%DEPLOYMENTDRIVE%$\Deployment-ImageReview\%DEPLOYMENTDIR%\FP%CLUSTERNODE%\FPM\CommonLayer \\%FPSERVER%\C$\FPM\CommonLayer /MIR /R:1)
if %ERRORLEVEL% GEQ 8 GOTO :error
if exist \\%FPSERVER%\%DEPLOYMENTDRIVE%$\Deployment-ImageReview\%DEPLOYMENTDIR%\FP%CLUSTERNODE%\FPM (robocopy \\%FPSERVER%\%DEPLOYMENTDRIVE%$\Deployment-ImageReview\%DEPLOYMENTDIR%\FP%CLUSTERNODE%\FPM\ServiceClient \\%FPSERVER%\C$\FPM\ServiceClient /MIR /R:1)
if %ERRORLEVEL% GEQ 8 GOTO :error
if exist \\%FPSERVER%\%DEPLOYMENTDRIVE%$\Deployment-ImageReview\%DEPLOYMENTDIR%\FP%CLUSTERNODE%\FPM (robocopy \\%FPSERVER%\%DEPLOYMENTDRIVE%$\Deployment-ImageReview\%DEPLOYMENTDIR%\FP%CLUSTERNODE%\FPM\ServiceHost \\%FPSERVER%\C$\FPM\ServiceHost /MIR /R:1)
if %ERRORLEVEL% GEQ 8 GOTO :error
if exist \\%FPSERVER%\%DEPLOYMENTDRIVE%$\Deployment-ImageReview\%DEPLOYMENTDIR%\FP%CLUSTERNODE%\FPM (robocopy \\%FPSERVER%\%DEPLOYMENTDRIVE%$\Deployment-ImageReview\%DEPLOYMENTDIR%\FP%CLUSTERNODE%\FPM\ThirdParty \\%FPSERVER%\C$\FPM\ThirdParty /MIR /R:1)
if %ERRORLEVEL% GEQ 8 GOTO :error
if exist \\%FPSERVER%\%DEPLOYMENTDRIVE%$\Deployment-ImageReview\%DEPLOYMENTDIR%\FP%CLUSTERNODE%\FPM (robocopy \\%FPSERVER%\%DEPLOYMENTDRIVE%$\Deployment-ImageReview\%DEPLOYMENTDIR%\FP%CLUSTERNODE%\FPM\WebApi \\%FPSERVER%\C$\FPM\WebApi /MIR /R:1)
if %ERRORLEVEL% GEQ 8 GOTO :error

if exist \\%HOCPSERVER%\%DEPLOYMENTDRIVE%$\Deployment-ImageReview\%DEPLOYMENTDIR%\HOCP%CLUSTERNODE%\HOCP (robocopy \\%HOCPSERVER%\%DEPLOYMENTDRIVE%$\Deployment-ImageReview\%DEPLOYMENTDIR%\HOCP%CLUSTERNODE%\HOCP\AppLayer \\%HOCPSERVER%\C$\HOCP\AppLayer /MIR /R:1)
if %ERRORLEVEL% GEQ 8 GOTO :error
if exist \\%HOCPSERVER%\%DEPLOYMENTDRIVE%$\Deployment-ImageReview\%DEPLOYMENTDIR%\HOCP%CLUSTERNODE%\HOCP (robocopy \\%HOCPSERVER%\%DEPLOYMENTDRIVE%$\Deployment-ImageReview\%DEPLOYMENTDIR%\HOCP%CLUSTERNODE%\HOCP\CommonLayer \\%HOCPSERVER%\C$\HOCP\CommonLayer /MIR /R:1)
if %ERRORLEVEL% GEQ 8 GOTO :error
if exist \\%HOCPSERVER%\%DEPLOYMENTDRIVE%$\Deployment-ImageReview\%DEPLOYMENTDIR%\HOCP%CLUSTERNODE%\HOCP (robocopy \\%HOCPSERVER%\%DEPLOYMENTDRIVE%$\Deployment-ImageReview\%DEPLOYMENTDIR%\HOCP%CLUSTERNODE%\HOCP\ServiceClient \\%HOCPSERVER%\C$\HOCP\ServiceClient /MIR /R:1)
if %ERRORLEVEL% GEQ 8 GOTO :error
if exist \\%HOCPSERVER%\%DEPLOYMENTDRIVE%$\Deployment-ImageReview\%DEPLOYMENTDIR%\HOCP%CLUSTERNODE%\HOCP (robocopy \\%HOCPSERVER%\%DEPLOYMENTDRIVE%$\Deployment-ImageReview\%DEPLOYMENTDIR%\HOCP%CLUSTERNODE%\HOCP\ServiceHost \\%HOCPSERVER%\C$\HOCP\ServiceHost /MIR /R:1)
if %ERRORLEVEL% GEQ 8 GOTO :error
if exist \\%HOCPSERVER%\%DEPLOYMENTDRIVE%$\Deployment-ImageReview\%DEPLOYMENTDIR%\HOCP%CLUSTERNODE%\HOCP (robocopy \\%HOCPSERVER%\%DEPLOYMENTDRIVE%$\Deployment-ImageReview\%DEPLOYMENTDIR%\HOCP%CLUSTERNODE%\HOCP\ThirdParty \\%HOCPSERVER%\C$\HOCP\ThirdParty /MIR /R:1)
if %ERRORLEVEL% GEQ 8 GOTO :error
if exist \\%HOCPSERVER%\%DEPLOYMENTDRIVE%$\Deployment-ImageReview\%DEPLOYMENTDIR%\HOCP%CLUSTERNODE%\HOCP (robocopy \\%HOCPSERVER%\%DEPLOYMENTDRIVE%$\Deployment-ImageReview\%DEPLOYMENTDIR%\HOCP%CLUSTERNODE%\HOCP\WebApi \\%HOCPSERVER%\C$\HOCP\WebApi /MIR /R:1)
if %ERRORLEVEL% GEQ 8 GOTO :error

if exist \\%PREHOCPSERVER%\%DEPLOYMENTDRIVE%$\Deployment-ImageReview\%DEPLOYMENTDIR%\PREHOCP%CLUSTERNODE%\PREHOCP (robocopy \\%PREHOCPSERVER%\%DEPLOYMENTDRIVE%$\Deployment-ImageReview\%DEPLOYMENTDIR%\PREHOCP%CLUSTERNODE%\PREHOCP\AppLayer \\%PREHOCPSERVER%\C$\PREHOCP\AppLayer /MIR /R:1)
if %ERRORLEVEL% GEQ 8 GOTO :error
if exist \\%PREHOCPSERVER%\%DEPLOYMENTDRIVE%$\Deployment-ImageReview\%DEPLOYMENTDIR%\PREHOCP%CLUSTERNODE%\PREHOCP (robocopy \\%PREHOCPSERVER%\%DEPLOYMENTDRIVE%$\Deployment-ImageReview\%DEPLOYMENTDIR%\PREHOCP%CLUSTERNODE%\PREHOCP\CommonLayer \\%PREHOCPSERVER%\C$\PREHOCP\CommonLayer /MIR /R:1)
if %ERRORLEVEL% GEQ 8 GOTO :error
if exist \\%PREHOCPSERVER%\%DEPLOYMENTDRIVE%$\Deployment-ImageReview\%DEPLOYMENTDIR%\PREHOCP%CLUSTERNODE%\PREHOCP (robocopy \\%PREHOCPSERVER%\%DEPLOYMENTDRIVE%$\Deployment-ImageReview\%DEPLOYMENTDIR%\PREHOCP%CLUSTERNODE%\PREHOCP\ServiceClient \\%PREHOCPSERVER%\C$\PREHOCP\ServiceClient /MIR /R:1)
if %ERRORLEVEL% GEQ 8 GOTO :error
if exist \\%PREHOCPSERVER%\%DEPLOYMENTDRIVE%$\Deployment-ImageReview\%DEPLOYMENTDIR%\PREHOCP%CLUSTERNODE%\PREHOCP (robocopy \\%PREHOCPSERVER%\%DEPLOYMENTDRIVE%$\Deployment-ImageReview\%DEPLOYMENTDIR%\PREHOCP%CLUSTERNODE%\PREHOCP\ServiceHost \\%PREHOCPSERVER%\C$\PREHOCP\ServiceHost /MIR /R:1)
if %ERRORLEVEL% GEQ 8 GOTO :error
if exist \\%PREHOCPSERVER%\%DEPLOYMENTDRIVE%$\Deployment-ImageReview\%DEPLOYMENTDIR%\PREHOCP%CLUSTERNODE%\PREHOCP (robocopy \\%PREHOCPSERVER%\%DEPLOYMENTDRIVE%$\Deployment-ImageReview\%DEPLOYMENTDIR%\PREHOCP%CLUSTERNODE%\PREHOCP\ThirdParty \\%PREHOCPSERVER%\C$\PREHOCP\ThirdParty /MIR /R:1)
if %ERRORLEVEL% GEQ 8 GOTO :error
if exist \\%PREHOCPSERVER%\%DEPLOYMENTDRIVE%$\Deployment-ImageReview\%DEPLOYMENTDIR%\PREHOCP%CLUSTERNODE%\PREHOCP (robocopy \\%PREHOCPSERVER%\%DEPLOYMENTDRIVE%$\Deployment-ImageReview\%DEPLOYMENTDIR%\PREHOCP%CLUSTERNODE%\PREHOCP\WebApi \\%PREHOCPSERVER%\C$\PREHOCP\WebApi /MIR /R:1)
if %ERRORLEVEL% GEQ 8 GOTO :error

if exist \\%IRRSERVER%\%DEPLOYMENTDRIVE%$\Deployment-ImageReview\%DEPLOYMENTDIR%\IRR%CLUSTERNODE%\IRR (robocopy \\%IRRSERVER%\%DEPLOYMENTDRIVE%$\Deployment-ImageReview\%DEPLOYMENTDIR%\IRR%CLUSTERNODE%\IRR\AppLayer \\%IRRSERVER%\C$\IRR\AppLayer /MIR /R:1)
if %ERRORLEVEL% GEQ 8 GOTO :error
if exist \\%IRRSERVER%\%DEPLOYMENTDRIVE%$\Deployment-ImageReview\%DEPLOYMENTDIR%\IRR%CLUSTERNODE%\IRR (robocopy \\%IRRSERVER%\%DEPLOYMENTDRIVE%$\Deployment-ImageReview\%DEPLOYMENTDIR%\IRR%CLUSTERNODE%\IRR\CommonLayer \\%IRRSERVER%\C$\IRR\CommonLayer /MIR /R:1)
if %ERRORLEVEL% GEQ 8 GOTO :error
if exist \\%IRRSERVER%\%DEPLOYMENTDRIVE%$\Deployment-ImageReview\%DEPLOYMENTDIR%\IRR%CLUSTERNODE%\IRR (robocopy \\%IRRSERVER%\%DEPLOYMENTDRIVE%$\Deployment-ImageReview\%DEPLOYMENTDIR%\IRR%CLUSTERNODE%\IRR\ServiceClient \\%IRRSERVER%\C$\IRR\ServiceClient /MIR /R:1)
if %ERRORLEVEL% GEQ 8 GOTO :error
if exist \\%IRRSERVER%\%DEPLOYMENTDRIVE%$\Deployment-ImageReview\%DEPLOYMENTDIR%\IRR%CLUSTERNODE%\IRR (robocopy \\%IRRSERVER%\%DEPLOYMENTDRIVE%$\Deployment-ImageReview\%DEPLOYMENTDIR%\IRR%CLUSTERNODE%\IRR\ServiceHost \\%IRRSERVER%\C$\IRR\ServiceHost /MIR /R:1)
if %ERRORLEVEL% GEQ 8 GOTO :error
if exist \\%IRRSERVER%\%DEPLOYMENTDRIVE%$\Deployment-ImageReview\%DEPLOYMENTDIR%\IRR%CLUSTERNODE%\IRR (robocopy \\%IRRSERVER%\%DEPLOYMENTDRIVE%$\Deployment-ImageReview\%DEPLOYMENTDIR%\IRR%CLUSTERNODE%\IRR\ThirdParty \\%IRRSERVER%\C$\IRR\ThirdParty /MIR /R:1)
if %ERRORLEVEL% GEQ 8 GOTO :error
if exist \\%IRRSERVER%\%DEPLOYMENTDRIVE%$\Deployment-ImageReview\%DEPLOYMENTDIR%\IRR%CLUSTERNODE%\IRR (robocopy \\%IRRSERVER%\%DEPLOYMENTDRIVE%$\Deployment-ImageReview\%DEPLOYMENTDIR%\IRR%CLUSTERNODE%\IRR\WebApi \\%IRRSERVER%\C$\IRR\WebApi /MIR /R:1)
if %ERRORLEVEL% GEQ 8 GOTO :error

rem if exist \\%APPSERVER%\%DEPLOYMENTDRIVE%$\Deployment-ImageReview\%DEPLOYMENTDIR%\APP%CLUSTERNODE%\MIR\ManualResultsTask (robocopy \\%APPSERVER%\%DEPLOYMENTDRIVE%$\Deployment-ImageReview\%DEPLOYMENTDIR%\APP%CLUSTERNODE%\MIR\ManualResultsTask \\%APPSERVER%\C$\MIR\ManualResultsTask /MIR /R:1)
echo MRT Task ERRORLEVEL %ERRORLEVEL%
if %ERRORLEVEL% GEQ 8 GOTO :error
if exist \\%TRANSSERVER%\%DEPLOYMENTDRIVE%$\Deployment-ImageReview\%DEPLOYMENTDIR%\TRANS%CLUSTERNODE%\MIR\TransactionsServer (robocopy \\%TRANSSERVER%\%DEPLOYMENTDRIVE%$\Deployment-ImageReview\%DEPLOYMENTDIR%\TRANS%CLUSTERNODE%\MIR\TransactionsServer \\%TRANSSERVER%\C$\MIR\TransactionsServer /MIR /R:1)
echo Tansaction server ERRORLEVEL %ERRORLEVEL%
if %ERRORLEVEL% GEQ 8 GOTO :error
if exist \\%APPSERVER%\%DEPLOYMENTDRIVE%$\Deployment-ImageReview\%DEPLOYMENTDIR%\APP%CLUSTERNODE%\MIR\ImageReview\AppLayer (robocopy \\%APPSERVER%\%DEPLOYMENTDRIVE%$\Deployment-ImageReview\%DEPLOYMENTDIR%\APP%CLUSTERNODE%\MIR\ImageReview\AppLayer \\%APPSERVER%\C$\MIR\ImageReview\AppLayer /MIR /R:1)
if %ERRORLEVEL% GEQ 8 GOTO :error

if exist \\%APPSERVER%\%DEPLOYMENTDRIVE%$\Deployment-ImageReview\%DEPLOYMENTDIR%\APP%CLUSTERNODE%\MIR\ImageReview\CommonLayer (robocopy \\%APPSERVER%\%DEPLOYMENTDRIVE%$\Deployment-ImageReview\%DEPLOYMENTDIR%\APP%CLUSTERNODE%\MIR\ImageReview\CommonLayer \\%APPSERVER%\C$\MIR\ImageReview\CommonLayer /MIR /R:1)
if %ERRORLEVEL% GEQ 8 GOTO :error

if exist \\%APPSERVER%\%DEPLOYMENTDRIVE%$\Deployment-ImageReview\%DEPLOYMENTDIR%\APP%CLUSTERNODE%\MIR\ImageReview\packages (robocopy \\%APPSERVER%\%DEPLOYMENTDRIVE%$\Deployment-ImageReview\%DEPLOYMENTDIR%\APP%CLUSTERNODE%\MIR\ImageReview\packages \\%APPSERVER%\C$\MIR\ImageReview\packages /MIR /R:1)
if %ERRORLEVEL% GEQ 8 GOTO :error

if exist \\%APPSERVER%\%DEPLOYMENTDRIVE%$\Deployment-ImageReview\%DEPLOYMENTDIR%\APP%CLUSTERNODE%\MIR\ImageReview\Plugins (robocopy \\%APPSERVER%\%DEPLOYMENTDRIVE%$\Deployment-ImageReview\%DEPLOYMENTDIR%\APP%CLUSTERNODE%\MIR\ImageReview\Plugins \\%APPSERVER%\C$\MIR\ImageReview\Plugins /MIR /R:1)
if %ERRORLEVEL% GEQ 8 GOTO :error

if exist \\%APPSERVER%\%DEPLOYMENTDRIVE%$\Deployment-ImageReview\%DEPLOYMENTDIR%\APP%CLUSTERNODE%\MIR\ImageReview\ServiceClient (robocopy \\%APPSERVER%\%DEPLOYMENTDRIVE%$\Deployment-ImageReview\%DEPLOYMENTDIR%\APP%CLUSTERNODE%\MIR\ImageReview\ServiceClient \\%APPSERVER%\C$\MIR\ImageReview\ServiceClient /MIR /R:1)
if %ERRORLEVEL% GEQ 8 GOTO :error

if exist \\%APPSERVER%\%DEPLOYMENTDRIVE%$\Deployment-ImageReview\%DEPLOYMENTDIR%\APP%CLUSTERNODE%\MIR\ImageReview\ServiceHost (robocopy \\%APPSERVER%\%DEPLOYMENTDRIVE%$\Deployment-ImageReview\%DEPLOYMENTDIR%\APP%CLUSTERNODE%\MIR\ImageReview\ServiceHost \\%APPSERVER%\C$\MIR\ImageReview\ServiceHost /MIR /R:1)
if %ERRORLEVEL% GEQ 8 GOTO :error

if exist \\%APPSERVER%\%DEPLOYMENTDRIVE%$\Deployment-ImageReview\%DEPLOYMENTDIR%\APP%CLUSTERNODE%\MIR\ImageReview\ThirdParty (robocopy \\%APPSERVER%\%DEPLOYMENTDRIVE%$\Deployment-ImageReview\%DEPLOYMENTDIR%\APP%CLUSTERNODE%\MIR\ImageReview\ThirdParty \\%APPSERVER%\C$\MIR\ImageReview\ThirdParty /MIR /R:1)
if %ERRORLEVEL% GEQ 8 GOTO :error

if exist \\%APPSERVER%\%DEPLOYMENTDRIVE%$\Deployment-ImageReview\%DEPLOYMENTDIR%\APP%CLUSTERNODE%\MIR\ImageReview\Workflows (robocopy \\%APPSERVER%\%DEPLOYMENTDRIVE%$\Deployment-ImageReview\%DEPLOYMENTDIR%\APP%CLUSTERNODE%\MIR\ImageReview\Workflows \\%APPSERVER%\C$\MIR\ImageReview\Workflows /MIR /R:1)
if %ERRORLEVEL% GEQ 8 GOTO :error



if exist \\%WEBSERVER%\%DEPLOYMENTDRIVE%$\Deployment-ImageReview\%DEPLOYMENTDIR%\WEB%CLUSTERNODE%\MIR\ImageReviewWeb (robocopy \\%WEBSERVER%\%DEPLOYMENTDRIVE%$\Deployment-ImageReview\%DEPLOYMENTDIR%\WEB%CLUSTERNODE%\MIR\ImageReviewWeb \\%WEBSERVER%\C$\MIR\ImageReviewWeb /MIR /R:1)
echo Image Rev Web ERRORLEVEL %ERRORLEVEL%
rem if exist \\%APPSERVER%\%DEPLOYMENTDRIVE%$\Deployment-ImageReview\%DEPLOYMENTDIR%\APP%CLUSTERNODE%\MIR\ScheduledTasks (robocopy \\%APPSERVER%\%DEPLOYMENTDRIVE%$\Deployment-ImageReview\%DEPLOYMENTDIR%\APP%CLUSTERNODE%\MIR\ScheduledTasks \\%APPSERVER%\C$\MIR\ScheduledTasks /MIR /R:1)
echo MRT Task ERRORLEVEL %ERRORLEVEL%


if "%QFREEFEATURE%" == "false" GOTO END
if exist \\%QFREETASKSERVER%\%DEPLOYMENTDRIVE%$\Deployment-ImageReview\%DEPLOYMENTDIR%\Plugins (robocopy \\%QFREETASKSERVER%\%DEPLOYMENTDRIVE%$\Deployment-ImageReview\%DEPLOYMENTDIR%\Plugins \\%QFREETASKSERVER%\C$\MIR\ImageReview\Plugins /MIR /R:1)
if %ERRORLEVEL% GEQ 8 GOTO :error 
echo Step %STEP% completed...  --------- 

GOTO END


:HRFEATURE
if "%CLUSTERNODE%"=="02" ECHO CLUSTER is 2 go to end
if "%CLUSTERNODE%"=="02"  GOTO END
SET STEP="HR FEATURE copy resources"
echo copy resources
call xcopy \\%HUMANREADSERVER%\%DEPLOYMENTDRIVE%$\Deployment-ImageReview\%DEPLOYMENTDIR%\HR01\RPT01\Resources \\%HUMANREADSERVER%\""%RESOURCESPATH%"" /Y /I /F /S /R
if %ERRORLEVEL% NEQ 0 GOTO :error
echo copy RPT01 out of HR01
call xcopy \\%HUMANREADSERVER%\%DEPLOYMENTDRIVE%$\Deployment-ImageReview\%DEPLOYMENTDIR%\HR01\RPT01 \\%HUMANREADSERVER%\%DEPLOYMENTDRIVE%$\Deployment-ImageReview\%DEPLOYMENTDIR%\RPT01 /Y /I /F /S /R
if %ERRORLEVEL% NEQ 0 GOTO :error
SET STEP="HR FEATURE copy BUs"
rem if exist \\%HUMANREADSERVER%\C$\MIR\ (robocopy \\%HUMANREADSERVER%\C$\MIR\ \\%HUMANREADSERVER%\C$\MIR_bu\ScheduledTasks\ /MIR /R:1)
if %ERRORLEVEL% GEQ 8 GOTO :error 
SET STEP="HR FEATURE install to C drive"
if exist \\%HUMANREADSERVER%\%DEPLOYMENTDRIVE%$\Deployment-ImageReview\%DEPLOYMENTDIR%\HR%CLUSTERNODE%\MIR (robocopy \\%HUMANREADSERVER%\%DEPLOYMENTDRIVE%$\Deployment-ImageReview\%DEPLOYMENTDIR%\HR%CLUSTERNODE%\MIR \\%HUMANREADSERVER%\C$\MIR /MIR /R:1)
if %ERRORLEVEL% GEQ 8 GOTO :error
GOTO END 

:error
echo ------- AN ERROR OCCURED DURING THIS STEP: %STEP% ------
echo ------- AN ERROR OCCURED DURING THIS STEP: %STEP% ------ rem NAnt.exe -buildfile:ImageReview_build.build -logger:NAnt.Core.MailLogger -logfile:AppLogs\build.log autobuild-error
exit /b 1

:END
echo -- Install of ImageReview is complete, wait for DBA to run last step
echo -- Install of ImageReview is complete rem NAnt.exe -buildfile:ImageReview_build.build -logger:NAnt.Core.MailLogger -logfile:AppLogs\build.log autobuild-complete
exit /b 0


