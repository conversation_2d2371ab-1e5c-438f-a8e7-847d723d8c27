@ECHO OFF

set NOW=%date:~10,4%-%date:~4,2%-%date:~7,2%
Set CURRENTDIR=%CD%
SET APPSERVER=%1
SET PROJECT=%2
SET ENVTYPE=%3
SET DEPLOYMENTDRIVE=%4
SET DEPLOYMENTDIR=%5
SET CLUSTERVALUE=%6
SET WEBSERVER=%7
SET EXTSERVER=%8
SET APP03SERVER=%9
set fullstamp=%YYYY%-%MM%-%DD%_%HH%-%Min%-%Sec%


echo %APPSERVER%
echo ext server is %EXTSERVER%


echo CPS Installation is starting at %fullstamp%
echo ----------------------------------

:CPS_Delete_BU_DIRS
SET STEP="Remove all _bu directories from CPS server"
echo Step %STEP% commencing...  ---------
psexec -accepteula \\%APPSERVER% cmd /c if exist c:\CPS_bu\ (rmdir /s /q c:\CPS_bu)
if %ERRORLEVEL% NEQ 0 GOTO :error 
psexec -accepteula \\%APPSERVER% cmd /c if exist c:\DM_bu\ (rmdir /s /q c:\DM_bu)
if %ERRORLEVEL% NEQ 0 GOTO :error 
psexec -accepteula \\%APPSERVER% cmd /c if exist c:\VRG_bu\ (rmdir /s /q c:\VRG_bu)
if %ERRORLEVEL% NEQ 0 GOTO :error 
psexec -accepteula \\%APPSERVER% cmd /c if exist c:\DNAMatch_bu\ (rmdir /s /q c:\DNAMatch_bu)
if %ERRORLEVEL% NEQ 0 GOTO :error 
psexec -accepteula \\%APPSERVER% cmd /c if exist c:\CFTS_bu\ (rmdir /s /q c:\CFTS_bu)
if %ERRORLEVEL% NEQ 0 GOTO :error 
psexec -accepteula \\%WEBSERVER% cmd /c if exist c:\CPS_bu\ (rmdir /s /q c:\CPS_bu)
if %ERRORLEVEL% NEQ 0 GOTO :error 
psexec -accepteula \\%APPSERVER% cmd /c if exist c:\CPS_Simulators_bu\ (rmdir /s /q c:\CPS_Simulators_bu)
if %ERRORLEVEL% NEQ 0 GOTO :error 
psexec -accepteula \\%APPSERVER% cmd /c if exist c:\TRANSCOM_bu\ (rmdir /s /q c:\TRANSCOM_bu)
if %ERRORLEVEL% NEQ 0 GOTO :error 


echo Step %STEP% completed..  ---------

verify > nul
:CPS_Copy_Current_DIRS_TO_BU_DIRS
echo Step %STEP% commencing...  ---------
SET STEP="Copy current CPS dirs to _bu dirs"
psexec -accepteula \\%APPSERVER% cmd /c if exist C:\CPS\ScheduledTasks (robocopy C:\CPS\ScheduledTasks C:\CPS_bu\ScheduledTasks /MIR /R:1)
if %ERRORLEVEL% GEQ 8 GOTO :error
psexec -accepteula \\%APPSERVER% cmd /c if exist C:\CPS\Services (robocopy C:\CPS\Services C:\CPS_bu\Services /MIR /R:1)
if %ERRORLEVEL% GEQ 8 GOTO :error
psexec -accepteula \\%APPSERVER% cmd /c if exist C:\CPS\WebAPI (robocopy C:\CPS\WebAPI C:\CPS_bu\WebAPI /MIR /R:1)
if %ERRORLEVEL% GEQ 8 GOTO :error
psexec -accepteula \\%WEBSERVER% cmd /c if exist C:\CPS\WebUI (robocopy C:\CPS\WebUI C:\CPS_bu\WebUI /MIR /R:1)
if %ERRORLEVEL% GEQ 8 GOTO :error
psexec -accepteula \\%WEBSERVER% cmd /c if exist C:\CPS\ExtWebUI (robocopy C:\CPS\WebUI C:\CPS_bu\ExtWebUI /MIR /R:1)
if %ERRORLEVEL% GEQ 8 GOTO :error
psexec -accepteula \\%WEBSERVER% cmd /c if exist C:\CPS\WebReportViewer (robocopy C:\CPS\WebReportViewer C:\CPS_bu\WebReportViewer /MIR /R:1)
if %ERRORLEVEL% GEQ 8 GOTO :error
psexec -accepteula \\%APPSERVER% cmd /c if exist C:\CPS_Simulators (robocopy C:\CPS_Simulators C:\CPS_Simulators /MIR /R:1)
if %ERRORLEVEL% GEQ 8 GOTO :error
psexec -accepteula \\%APPSERVER% cmd /c if exist C:\DM (robocopy C:\DM C:\DM_bu /MIR /R:1)
if %ERRORLEVEL% GEQ 8 GOTO :error
psexec -accepteula \\%APPSERVER% cmd /c if exist C:\VRG (robocopy C:\VRG C:\VRG_bu /MIR /R:1)
if %ERRORLEVEL% GEQ 8 GOTO :error
psexec -accepteula \\%APPSERVER% cmd /c if exist C:\DNAMatch (robocopy C:\DNAMatch C:\DNAMatch_bu /MIR /R:1)
if %ERRORLEVEL% GEQ 8 GOTO :error
psexec -accepteula \\%APPSERVER% cmd /c if exist C:\CFTS (robocopy C:\CFTS C:\CFTS_bu /MIR /R:1)
if %ERRORLEVEL% GEQ 8 GOTO :error
psexec -accepteula \\%APPSERVER% cmd /c if exist C:\TRANSCOM (robocopy C:\TRANSCOM C:\TRANSCOM_bu /MIR /R:1)
if %ERRORLEVEL% GEQ 8 GOTO :error
psexec -accepteula \\%APPSERVER% cmd /c if exist C:\AARProcessingService (robocopy C:\AARProcessingService C:\AARProcessingService_bu /MIR /R:1)
if %ERRORLEVEL% GEQ 8 GOTO :error


echo Step %STEP% completed...  ---------
VERIFY > nul

:CPS_Copy_Distribution_DIRS
SET STEP="CPS - Copy distribution dirs to C drives"
echo Step %STEP% commencing...  ---------
psexec -accepteula \\%APPSERVER% cmd /c if exist %DEPLOYMENTDRIVE%:\Deployment-CPS\%DEPLOYMENTDIR%\APP%CLUSTERVALUE%\CPS\ScheduledTasks (robocopy %DEPLOYMENTDRIVE%:\Deployment-CPS\%DEPLOYMENTDIR%\APP%CLUSTERVALUE%\CPS\ScheduledTasks C:\CPS\ScheduledTasks /MIR /R:1)
if %ERRORLEVEL% GEQ 8 GOTO :error
psexec -accepteula \\%APPSERVER% cmd /c if exist %DEPLOYMENTDRIVE%:\Deployment-CPS\%DEPLOYMENTDIR%\APP%CLUSTERVALUE%\CPS\Services (robocopy %DEPLOYMENTDRIVE%:\Deployment-CPS\%DEPLOYMENTDIR%\APP%CLUSTERVALUE%\CPS\Services C:\CPS\Services /MIR /R:1)
if %ERRORLEVEL% GEQ 8 GOTO :error
psexec -accepteula \\%APPSERVER% cmd /c if exist %DEPLOYMENTDRIVE%:\Deployment-CPS\%DEPLOYMENTDIR%\APP%CLUSTERVALUE%\CPS\WebAPI (robocopy %DEPLOYMENTDRIVE%:\Deployment-CPS\%DEPLOYMENTDIR%\APP%CLUSTERVALUE%\CPS\WebAPI C:\CPS\WebAPI /MIR /R:1)
if %ERRORLEVEL% GEQ 8 GOTO :error
psexec -accepteula \\%WEBSERVER% cmd /c if exist %DEPLOYMENTDRIVE%:\Deployment-CPS\%DEPLOYMENTDIR%\WEB%CLUSTERVALUE%\CPS\WebUI (robocopy %DEPLOYMENTDRIVE%:\Deployment-CPS\%DEPLOYMENTDIR%\WEB%CLUSTERVALUE%\CPS\WebUI C:\CPS\WebUI /MIR /R:1)
if %ERRORLEVEL% GEQ 8 GOTO :error
psexec -accepteula \\%WEBSERVER% cmd /c if exist %DEPLOYMENTDRIVE%:\Deployment-CPS\%DEPLOYMENTDIR%\WEB%CLUSTERVALUE%\CPS\ExtWebUI (robocopy %DEPLOYMENTDRIVE%:\Deployment-CPS\%DEPLOYMENTDIR%\WEB%CLUSTERVALUE%\CPS\ExtWebUI C:\CPS\ExtWebUI /MIR /R:1)
if %ERRORLEVEL% GEQ 8 GOTO :error
psexec -accepteula \\%WEBSERVER% cmd /c if exist %DEPLOYMENTDRIVE%:\Deployment-CPS\%DEPLOYMENTDIR%\WEB%CLUSTERVALUE%\CPS\WebReportViewer (robocopy %DEPLOYMENTDRIVE%:\Deployment-CPS\%DEPLOYMENTDIR%\WEB%CLUSTERVALUE%\CPS\WebReportViewer C:\CPS\WebReportViewer /MIR /R:1)
if %ERRORLEVEL% GEQ 8 GOTO :error
psexec -accepteula \\%APPSERVER% cmd /c if exist %DEPLOYMENTDRIVE%:\Deployment-CPS\%DEPLOYMENTDIR%\APP%CLUSTERVALUE%\CPS_Simulators (robocopy %DEPLOYMENTDRIVE%:\Deployment-CPS\%DEPLOYMENTDIR%\APP%CLUSTERVALUE%\CPS_Simulators C:\CPS_Simulators /MIR /R:1)
if %ERRORLEVEL% GEQ 8 GOTO :error
psexec -accepteula \\%APPSERVER% cmd /c if exist %DEPLOYMENTDRIVE%:\Deployment-CPS\%DEPLOYMENTDIR%\APP%CLUSTERVALUE%\DM (robocopy %DEPLOYMENTDRIVE%:\Deployment-CPS\%DEPLOYMENTDIR%\APP%CLUSTERVALUE%\DM C:\DM /MIR /R:1)
if %ERRORLEVEL% GEQ 8 GOTO :error
psexec -accepteula \\%APPSERVER% cmd /c if exist %DEPLOYMENTDRIVE%:\Deployment-CPS\%DEPLOYMENTDIR%\APP%CLUSTERVALUE%\VRG (robocopy %DEPLOYMENTDRIVE%:\Deployment-CPS\%DEPLOYMENTDIR%\APP%CLUSTERVALUE%\VRG C:\VRG /MIR /R:1)
if %ERRORLEVEL% GEQ 8 GOTO :error
psexec -accepteula \\%APPSERVER% cmd /c if exist %DEPLOYMENTDRIVE%:\Deployment-CPS\%DEPLOYMENTDIR%\APP%CLUSTERVALUE%\DNAMatch (robocopy %DEPLOYMENTDRIVE%:\Deployment-CPS\%DEPLOYMENTDIR%\APP%CLUSTERVALUE%\DNAMatch C:\DNAMatch /MIR /R:1)
if %ERRORLEVEL% GEQ 8 GOTO :error
psexec -accepteula \\%APPSERVER% cmd /c if exist %DEPLOYMENTDRIVE%:\Deployment-CPS\%DEPLOYMENTDIR%\APP%CLUSTERVALUE%\CFTS (robocopy %DEPLOYMENTDRIVE%:\Deployment-CPS\%DEPLOYMENTDIR%\APP%CLUSTERVALUE%\CFTS C:\CFTS /MIR /R:1)
if %ERRORLEVEL% GEQ 8 GOTO :error
psexec -accepteula \\%APPSERVER% cmd /c if exist %DEPLOYMENTDRIVE%:\Deployment-CPS\%DEPLOYMENTDIR%\APP%CLUSTERVALUE%\AARProcessingService (robocopy %DEPLOYMENTDRIVE%:\Deployment-CPS\%DEPLOYMENTDIR%\APP%CLUSTERVALUE%\AARProcessingService C:\AARProcessingService /MIR /R:1)
if %ERRORLEVEL% GEQ 8 GOTO :error
psexec -accepteula \\%APPSERVER% cmd /c if exist %DEPLOYMENTDRIVE%:\Deployment-CPS\%DEPLOYMENTDIR%\APP%CLUSTERVALUE%\TransComService (robocopy %DEPLOYMENTDRIVE%:\Deployment-CPS\%DEPLOYMENTDIR%\APP%CLUSTERVALUE%\TransComService C:\TransComService /MIR /R:1)
if %ERRORLEVEL% GEQ 8 GOTO :error
echo copy CPS 3rdparty to C drive

VERIFY > nul

IF "%CLUSTERVALUE%"=="02" (GOTO END)
echo ext server is %EXTSERVER%
echo check if app and ext are the same
if not %APPSERVER%==%EXTSERVER% (psexec -accepteula \\%EXTSERVER% cmd /c if exist c:\CPS_bu\ rmdir /s /q c:\CPS_bu
) 
echo done checking 
if %ERRORLEVEL% NEQ 0 GOTO :error 
echo copy CPS 3rdparty to BU
psexec -accepteula \\%EXTSERVER% cmd /c if exist C:\CPS\ThirdPartyScheduledTasks (robocopy C:\CPS\ThirdPartyScheduledTasks C:\CPS_bu\ThirdPartyScheduledTasks /MIR /R:1)
if %ERRORLEVEL% GEQ 8 GOTO :error
psexec -accepteula \\%EXTSERVER% cmd /c if exist C:\CPS\AAR (robocopy C:\CPS\AAR C:\CPS_bu\AAR /MIR /R:1)
if %ERRORLEVEL% GEQ 8 GOTO :error
psexec -accepteula \\%EXTSERVER% cmd /c if exist C:\CPS\TPI (robocopy C:\CPS\TPI C:\CPS_bu\TPI /MIR /R:1)
if %ERRORLEVEL% GEQ 8 GOTO :error
psexec -accepteula \\%EXTSERVER% cmd /c if exist C:\CPS\TPI02 (robocopy C:\CPS\TPI02 C:\CPS_bu\TPI02 /MIR /R:1)
if %ERRORLEVEL% GEQ 8 GOTO :error
psexec -accepteula \\%EXTSERVER% cmd /c if exist C:\CPS\TPIWebhookService (robocopy C:\CPS\TPIWebhookService C:\CPS_bu\TPIWebhookService /MIR /R:1)
if %ERRORLEVEL% GEQ 8 GOTO :error
psexec -accepteula \\%EXTSERVER% cmd /c if exist C:\CPS\IAS (robocopy C:\CPS\IAS C:\CPS_bu\IAS /MIR /R:1)
if %ERRORLEVEL% GEQ 8 GOTO :error
psexec -accepteula \\%EXTSERVER% cmd /c if exist C:\CPS\DMV (robocopy C:\CPS\DMV C:\CPS_bu\DMV /MIR /R:1)
if %ERRORLEVEL% GEQ 8 GOTO :error
psexec -accepteula \\%EXTSERVER% cmd /c if exist C:\CPS\RaasAcknowledge (robocopy C:\CPS\RaasAcknowledge C:\CPS_bu\RaasAcknowledge /MIR /R:1)
if %ERRORLEVEL% GEQ 8 GOTO :error
psexec -accepteula \\%EXTSERVER% cmd /c if exist C:\CPS\NYCDOT (robocopy C:\CPS\NYCDOT C:\CPS_bu\NYCDOT /MIR /R:1)
if %ERRORLEVEL% GEQ 8 GOTO :error
psexec -accepteula \\%EXTSERVER% cmd /c if exist %DEPLOYMENTDRIVE%:\Deployment-CPS\%DEPLOYMENTDIR%\EXT%CLUSTERVALUE%\CPS\ThirdPartyScheduledTasks (robocopy %DEPLOYMENTDRIVE%:\Deployment-CPS\%DEPLOYMENTDIR%\EXT%CLUSTERVALUE%\CPS\ThirdPartyScheduledTasks C:\CPS\ThirdPartyScheduledTasks /MIR /R:1)
if %ERRORLEVEL% GEQ 8 GOTO :error
psexec -accepteula \\%EXTSERVER% cmd /c if exist %DEPLOYMENTDRIVE%:\Deployment-CPS\%DEPLOYMENTDIR%\EXT%CLUSTERVALUE%\CPS\AAR (robocopy %DEPLOYMENTDRIVE%:\Deployment-CPS\%DEPLOYMENTDIR%\EXT%CLUSTERVALUE%\CPS\AAR C:\CPS\AAR /MIR /R:1)
if %ERRORLEVEL% GEQ 8 GOTO :error
psexec -accepteula \\%EXTSERVER% cmd /c if exist %DEPLOYMENTDRIVE%:\Deployment-CPS\%DEPLOYMENTDIR%\EXT%CLUSTERVALUE%\CPS\TPI (robocopy %DEPLOYMENTDRIVE%:\Deployment-CPS\%DEPLOYMENTDIR%\EXT%CLUSTERVALUE%\CPS\TPI C:\CPS\TPI /MIR /R:1)
if %ERRORLEVEL% GEQ 8 GOTO :error
psexec -accepteula \\%EXTSERVER% cmd /c if exist %DEPLOYMENTDRIVE%:\Deployment-CPS\%DEPLOYMENTDIR%\EXT%CLUSTERVALUE%\CPS\TPI02 (robocopy %DEPLOYMENTDRIVE%:\Deployment-CPS\%DEPLOYMENTDIR%\EXT%CLUSTERVALUE%\CPS\TPI02 C:\CPS\TPI02 /MIR /R:1)
if %ERRORLEVEL% GEQ 8 GOTO :error
psexec -accepteula \\%EXTSERVER% cmd /c if exist %DEPLOYMENTDRIVE%:\Deployment-CPS\%DEPLOYMENTDIR%\EXT%CLUSTERVALUE%\CPS\DMV (robocopy %DEPLOYMENTDRIVE%:\Deployment-CPS\%DEPLOYMENTDIR%\EXT%CLUSTERVALUE%\CPS\DMV C:\CPS\DMV /MIR /R:1)
if %ERRORLEVEL% GEQ 8 GOTO :error
psexec -accepteula \\%EXTSERVER% cmd /c if exist %DEPLOYMENTDRIVE%:\Deployment-CPS\%DEPLOYMENTDIR%\EXT%CLUSTERVALUE%\CPS\IAS (robocopy %DEPLOYMENTDRIVE%:\Deployment-CPS\%DEPLOYMENTDIR%\EXT%CLUSTERVALUE%\CPS\IAS C:\CPS\IAS /MIR /R:1)
if %ERRORLEVEL% GEQ 8 GOTO :error
psexec -accepteula \\%EXTSERVER% cmd /c if exist %DEPLOYMENTDRIVE%:\Deployment-CPS\%DEPLOYMENTDIR%\EXT%CLUSTERVALUE%\CPS\RaasAcknowledge (robocopy %DEPLOYMENTDRIVE%:\Deployment-CPS\%DEPLOYMENTDIR%\EXT%CLUSTERVALUE%\CPS\RaasAcknowledge C:\CPS\RaasAcknowledge /MIR /R:1)
if %ERRORLEVEL% GEQ 8 GOTO :error
psexec -accepteula \\%EXTSERVER% cmd /c if exist %DEPLOYMENTDRIVE%:\Deployment-CPS\%DEPLOYMENTDIR%\EXT%CLUSTERVALUE%\CPS\NYCDOT (robocopy %DEPLOYMENTDRIVE%:\Deployment-CPS\%DEPLOYMENTDIR%\EXT%CLUSTERVALUE%\CPS\NYCDOT C:\CPS\NYCDOT /MIR /R:1)
if %ERRORLEVEL% GEQ 8 GOTO :error
:APP03SERVER
IF NOT "%ENVTYPE%"=="PROD" (GOTO END)
psexec -accepteula \\%APP03SERVER% cmd /c if exist c:\DM_bu\ (rmdir /s /q c:\DM_bu)
if %ERRORLEVEL% NEQ 0 GOTO :error 
psexec -accepteula \\%APP03SERVER% cmd /c if exist c:\VRG_bu\ (rmdir /s /q c:\VRG_bu)
if %ERRORLEVEL% NEQ 0 GOTO :error 
psexec -accepteula \\%APP03SERVER% cmd /c if exist C:\DM (robocopy C:\DM C:\DM_bu /MIR /R:1)
if %ERRORLEVEL% GEQ 8 GOTO :error
psexec -accepteula \\%APP03SERVER% cmd /c if exist C:\VRG (robocopy C:\VRG C:\VRG_bu /MIR /R:1)
if %ERRORLEVEL% GEQ 8 GOTO :error
psexec -accepteula \\%APP03SERVER% cmd /c if exist %DEPLOYMENTDRIVE%:\Deployment-CPS\%DEPLOYMENTDIR%\APP03\DM (robocopy %DEPLOYMENTDRIVE%:\Deployment-CPS\%DEPLOYMENTDIR%\APP03\DM C:\DM /MIR /R:1)
if %ERRORLEVEL% GEQ 8 GOTO :error
psexec -accepteula \\%APP03SERVER% cmd /c if exist %DEPLOYMENTDRIVE%:\Deployment-CPS\%DEPLOYMENTDIR%\APP03\VRG (robocopy %DEPLOYMENTDRIVE%:\Deployment-CPS\%DEPLOYMENTDIR%\APP03\VRG C:\VRG /MIR /R:1)
if %ERRORLEVEL% GEQ 8 GOTO :error
echo Step %STEP% completed...  ---------



GOTO END

:error
echo ------- AN ERROR OCCURED DURING THIS STEP: %STEP% ------
exit /b 1

:END
echo -- Install of CPS is complete

exit /b 0


