@ECHO OFF

set NOW=%date:~10,4%-%date:~4,2%-%date:~7,2%
Set CURRENTDIR=%CD%
SET MOMSSERVER=%1
SET PROJECT=%2
SET ENVTYPE=%3
SET DEPLOYMENTDRIVE=%4
SET DEPLOYMENTDIR=%5
SET SQLDRIVE=%6
SET SQLDIR=%7
SET INTEGRITY=%8
SET INTEGRITYSERVER=%9
SHIFT
SHIFT
SHIFT
SHIFT
SHIFT
SHIFT
SHIFT
SHIFT
SHIFT
SET INSIGHTMOBILESERVER=%1
SET INSIGHTMOBILE=%2
SET DBSERVER=%3
SET REPORTSERVER=%4


echo %MOMSSERVER%

echo MOMS/Insight Installation is starting at %DEPLOYMENTDIR%
echo ---------------------------------- 


verify > nul



verify > nul

:MOMS_Copy_Current_DIRS_TO_BU_DIRS
echo Step %STEP% commencing...  --------- 
SET STEP="Copy current MOMS dirs to _bu dirs"
rem call Autobuild\do_copy_force_r_locrem1.cmd  MOMS C:\MOMS C:\MOMS_bu %MOMSSERVER%\
if exist \\%MOMSSERVER%\C$\MOMS (robocopy \\%MOMSSERVER%\C$\MOMS \\%MOMSSERVER%\C$\MOMS_bu /MIR /R:1)
if %ERRORLEVEL% GEQ 8 GOTO :error 
rem call Autobuild\do_copy_force_r_locrem1.cmd  MOMSInterfaces C:\MOMSInterfaces C:\MOMSInterfaces_bu %MOMSSERVER%
if exist \\%MOMSSERVER%\C$\MOMSInterfaces\ (robocopy \\%MOMSSERVER%\C$\MOMSInterfaces \\%MOMSSERVER%\C$\MOMSInterfaces_bu /MIR /R:1)
if %ERRORLEVEL% GEQ 8 GOTO :error 
rem call Autobuild\do_copy_force_r_locrem1.cmd  MOMSMobile C:\MOMSMobile C:\MOMSMobile_bu %MOMSSERVER%
if exist \\%MOMSSERVER%\C$\MOMSMobile\ (robocopy \\%MOMSSERVER%\C$\MOMSMobile \\%MOMSSERVER%\C$\MOMSMobile_bu  /MIR /R:1)
if %ERRORLEVEL% GEQ 8 GOTO :error 
rem call Autobuild\do_copy_force_r_locrem1.cmd  Insight C:\Insight C:\Insight_bu %MOMSSERVER%
if exist \\%MOMSSERVER%\C$\Insight (robocopy \\%MOMSSERVER%\C$\Insight \\%MOMSSERVER%\C$\Insight_bu  /MIR /R:1)
if %ERRORLEVEL% GEQ 8 GOTO :error 
if "%INTEGRITY%"=="true" if exist \\%INTEGRITYSERVER%\C$\MOMS (robocopy \\%INTEGRITYSERVER%\C$\MOMS \\%INTEGRITYSERVER%\C$\MOMS_BU /MIR /R:1)
if %ERRORLEVEL% GEQ 8 GOTO :error
psexec -accepteula \\%INSIGHTMOBILESERVER% cmd /c if exist c:\InsightMobile (robocopy C:\InsightMobile C:\InsightMobile_bu /MIR /R:1)
if %ERRORLEVEL% GEQ 8 GOTO :error
VERIFY > nul
echo Step %STEP% completed...  --------- 

verify > nul


:MOMS_Copy_Distribution_DIRS
SET STEP="MOMS/Insight - Copy distribution dirs to C drives"
echo Step %STEP% commencing...  --------- 
rem call Autobuild\do_copy_force_r_locrem1.cmd  MOMS %DEPLOYMENTDRIVE%:\Deployment\%DEPLOYMENTDIR%\Distribution\MOMS C:\MOMS %MOMSSERVER%
if exist \\%MOMSSERVER%\%DEPLOYMENTDRIVE%$\Deployment\%DEPLOYMENTDIR%\Distribution\MOMS (robocopy \\%MOMSSERVER%\%DEPLOYMENTDRIVE%$\Deployment\%DEPLOYMENTDIR%\Distribution\MOMS \\%MOMSSERVER%\C$\MOMS /MIR /R:1)
if %ERRORLEVEL% GEQ 8 GOTO :error
rem call Autobuild\do_copy_force_r_locrem1.cmd  MOMSInterfaces %DEPLOYMENTDRIVE%:\Deployment\%DEPLOYMENTDIR%\Distribution\MOMSInterfaces C:\MOMSInterfaces %MOMSSERVER%
if exist \\%MOMSSERVER%\%DEPLOYMENTDRIVE%$\Deployment\%DEPLOYMENTDIR%\Distribution\MOMSInterfaces\ (robocopy \\%MOMSSERVER%\%DEPLOYMENTDRIVE%$\Deployment\%DEPLOYMENTDIR%\Distribution\MOMSInterfaces \\%MOMSSERVER%\C$\MOMSInterfaces /MIR /R:1)
if %ERRORLEVEL% GEQ 8 GOTO :error
rem call Autobuild\do_copy_force_r_locrem1.cmd  MOMSMobile %DEPLOYMENTDRIVE%:\Deployment\%DEPLOYMENTDIR%\Distribution\MOMSMobile C:\MOMSMobile %MOMSSERVER%
if exist \\%MOMSSERVER%\%DEPLOYMENTDRIVE%$\Deployment\%DEPLOYMENTDIR%\Distribution\MOMSMobile\ (robocopy \\%MOMSSERVER%\%DEPLOYMENTDRIVE%$\Deployment\%DEPLOYMENTDIR%\Distribution\MOMSMobile \\%MOMSSERVER%\C$\MOMSMobile /MIR /R:1)
if %ERRORLEVEL% GEQ 8 GOTO :error
rem call Autobuild\do_copy_force_r_locrem1.cmd  Insight %DEPLOYMENTDRIVE%:\Deployment\%DEPLOYMENTDIR%\Distribution\Insight C:\Insight %MOMSSERVER%
if exist \\%MOMSSERVER%\%DEPLOYMENTDRIVE%$\Deployment\%DEPLOYMENTDIR%\Distribution\Insight (cmd /c robocopy \\%MOMSSERVER%\%DEPLOYMENTDRIVE%$\Deployment\%DEPLOYMENTDIR%\Distribution\Insight \\%MOMSSERVER%\C$\Insight /MIR /R:1)
if %ERRORLEVEL% GEQ 8 GOTO :error
if "%INTEGRITY%"=="true" if exist \\%MOMSSERVER%\%DEPLOYMENTDRIVE%$\Deployment\%DEPLOYMENTDIR%\Distribution\MOMS\ScheduledTasks\Integrity_MOMSEventLoader (robocopy \\%MOMSSERVER%\%DEPLOYMENTDRIVE%$\Deployment\%DEPLOYMENTDIR%\Distribution\MOMS\ScheduledTasks\Integrity_MOMSEventLoader \\%INTEGRITYSERVER%\C$\MOMS\ScheduledTasks\Integrity_MOMSEventLoader /MIR /R:1)
if %ERRORLEVEL% GEQ 8 GOTO :error
if "%INTEGRITY%"=="true" if exist \\%MOMSSERVER%\%DEPLOYMENTDRIVE%$\Deployment\%DEPLOYMENTDIR%\Distribution\MOMS\ThirdParty (robocopy \\%MOMSSERVER%\%DEPLOYMENTDRIVE%$\Deployment\%DEPLOYMENTDIR%\Distribution\MOMS\ThirdParty \\%INTEGRITYSERVER%\C$\MOMS\ThirdParty /MIR /R:1)
if %ERRORLEVEL% GEQ 8 GOTO :error
if "%INTEGRITY%"=="true" if exist \\%MOMSSERVER%\%DEPLOYMENTDRIVE%$\Deployment\%DEPLOYMENTDIR%\Distribution\MOMS\AppLayer (robocopy \\%MOMSSERVER%\%DEPLOYMENTDRIVE%$\Deployment\%DEPLOYMENTDIR%\Distribution\MOMS\AppLayer \\%INTEGRITYSERVER%\C$\MOMS\AppLayer /MIR /R:1)
if %ERRORLEVEL% GEQ 8 GOTO :error
if "%INTEGRITY%"=="true" if exist \\%MOMSSERVER%\%DEPLOYMENTDRIVE%$\Deployment\%DEPLOYMENTDIR%\Distribution\MOMS\CommonLayer (robocopy \\%MOMSSERVER%\%DEPLOYMENTDRIVE%$\Deployment\%DEPLOYMENTDIR%\Distribution\MOMS\CommonLayer \\%INTEGRITYSERVER%\C$\MOMS\CommonLayer /MIR /R:1)
if %ERRORLEVEL% GEQ 8 GOTO :error
if "%INTEGRITY%"=="true" if exist \\%MOMSSERVER%\%DEPLOYMENTDRIVE%$\Deployment\%DEPLOYMENTDIR%\Distribution\MOMS\ServiceClient (robocopy \\%MOMSSERVER%\%DEPLOYMENTDRIVE%$\Deployment\%DEPLOYMENTDIR%\Distribution\MOMS\ServiceClient \\%INTEGRITYSERVER%\C$\MOMS\ServiceClient /MIR /R:1)
if %ERRORLEVEL% GEQ 8 GOTO :error
if "%INSIGHTMOBILE%"=="true" if exist \\%INSIGHTMOBILESERVER%\%DEPLOYMENTDRIVE%$\Deployment-InsightMobile\%DEPLOYMENTDIR%\InsightMobile (robocopy \\%INSIGHTMOBILESERVER%\%DEPLOYMENTDRIVE%$\Deployment-InsightMobile\%DEPLOYMENTDIR%\InsightMobile \\%INSIGHTMOBILESERVER%\C$\InsightMobile /MIR /R:1)
if %ERRORLEVEL% GEQ 8 GOTO :error
VERIFY > nul
:SPECIAL INTEGRITY EVENT LOADER ENCRYPTION
echo %INTEGRITY% is integrity
if "%INTEGRITY%"=="true" if exist \\%MOMSSERVER%\%DEPLOYMENTDRIVE%$\Deployment\BuildScripts\AES ( robocopy \\%MOMSSERVER%\%DEPLOYMENTDRIVE%$\Deployment\BuildScripts\AES \\%INTEGRITYSERVER%\C$\AES /MIR /R:1)
if "%INTEGRITY%"=="true" if exist \\%INTEGRITYSERVER%\C$\AES\EncryptIntegrity.cmd ( call Autobuild\do_invoke_any_locrem1.cmd %INTEGRITYSERVER% C:\AES "EncryptIntegrity.cmd" C:\AES\EncryptIntegrity.cmd unused unused unused unused)
if "%INTEGRITY%"=="true" if exist \\%INTEGRITYSERVER%\C$\AES ( psexec -accepteula \\%INTEGRITYSERVER% cmd /c del /s /q C:\AES\*.*)
if "%INTEGRITY%"=="true" if exist \\%MOMSSERVER%\C$\MOMS\ScheduledTasks\Integrity_MOMSEventLoader ( psexec -accepteula \\%MOMSSERVER% cmd /c del /s /q C:\MOMS\ScheduledTasks\Integrity_MOMSEventLoader\*.*)
if "%INTEGRITY%"=="true" if exist \\%MOMSSERVER%\C$\MOMS\ScheduledTasks\Integrity_MOMSEventLoader ( psexec -accepteula \\%MOMSSERVER% cmd /c rmdir /s /q C:\MOMS\ScheduledTasks\Integrity_MOMSEventLoader)
if %ERRORLEVEL% NEQ 0 GOTO :error


echo Step %STEP% completed...  --------- 
echo SQL dir is %SQLDIR% and REPORT server is %REPORTSERVER%
:Copy_Resources
SET STEP="Copy resources"
echo Step %STEP% commencing...  --------- 
if "%SQLDIR%"=="MSRS13" (echo 2016)
if "%SQLDIR%"=="MSRS13" if exist \\%REPORTSERVER%\%DEPLOYMENTDRIVE%$\Deployment\%DEPLOYMENTDIR%\Resources (call Autobuild\do_copy_force_r_locrem1.cmd  MOMS %DEPLOYMENTDRIVE%:\Deployment\%DEPLOYMENTDIR%\Resources "%SQLDRIVE%:\Program Files\Microsoft SQL Server\%SQLDIR%.MSSQLSERVER\Reporting Services\ReportServer\bin" %REPORTSERVER%)
if %ERRORLEVEL% NEQ 0 GOTO :error
if "%SQLDIR%"=="MSRS14" (echo IS 2017)
if "%SQLDIR%"=="MSRS14" if exist \\%REPORTSERVER%\%DEPLOYMENTDRIVE%$\Deployment\%DEPLOYMENTDIR%\Resources (call Autobuild\do_copy_force_r_locrem1.cmd  MOMS %DEPLOYMENTDRIVE%:\Deployment\%DEPLOYMENTDIR%\Resources "%SQLDRIVE%:\Program Files\Microsoft SQL Server Reporting Services\SSRS\ReportServer\bin" %REPORTSERVER%)
if %ERRORLEVEL% NEQ 0 GOTO :error
if "%SQLDIR%"=="MSRS15" (echo IS 2019)
if "%SQLDIR%"=="MSRS15" if exist \\%REPORTSERVER%\%DEPLOYMENTDRIVE%$\Deployment\%DEPLOYMENTDIR%\Resources (call Autobuild\do_copy_force_r_locrem1.cmd  MOMS %DEPLOYMENTDRIVE%:\Deployment\%DEPLOYMENTDIR%\Resources "%SQLDRIVE%:\Program Files\Microsoft SQL Server Reporting Services\SSRS\ReportServer\bin" %REPORTSERVER%)
if %ERRORLEVEL% NEQ 0 GOTO :error
if "%SQLDIR%"=="MSRS16" if exist \\%REPORTSERVER%\%DEPLOYMENTDRIVE%$\Deployment\%DEPLOYMENTDIR%\Resources (call Autobuild\do_copy_force_r_locrem1.cmd  MOMS %DEPLOYMENTDRIVE%:\Deployment\%DEPLOYMENTDIR%\Resources "%SQLDRIVE%:\Program Files\Microsoft SQL Server Reporting Services\SSRS\ReportServer\bin" %REPORTSERVER%)
if %ERRORLEVEL% NEQ 0 GOTO :error
echo Step %STEP% completed...  --------- 
VERIFY > nul

GOTO END


:error
echo ------- AN ERROR OCCURED DURING THIS STEP: %STEP% ------
echo ------- AN ERROR OCCURED DURING THIS STEP: %STEP% ------ 
rem NAnt.exe -buildfile:MOMS_build.build -logger:NAnt.Core.MailLogger -logfile:AppLogs\build.log autobuild-error
exit /b 1

:END
echo -- Install of MOMS/INSIGHT is complete, wait for DBA to run last step
echo -- Install of MOMS/INSIGHT is complete 
rem NAnt.exe -buildfile:MOMS_build.build -logger:NAnt.Core.MailLogger -logfile:AppLogs\build.log autobuild-complete
exit /b 0


