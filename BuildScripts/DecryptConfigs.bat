@echo off
rem set variables
Set CURRENTDIR=%CD%

echo ------- Your current directory is:  %CURRENTDIR% ---------
SET FPSERVER=%1
SET APPSERVER=%2
SET WEBSERVER=%3
SET DEPLOYMENTDRIVE=%4
SET DEPLOYMENTDIR=%5
SET FINGERPRINTFEATURE=%6
SET IFXSERVER=%7
SET HRSERVER=%8
SET HUMANREADFEATURE=%9
SHIFT
SHIFT
SHIFT
SHIFT
SHIFT
SHIFT
SHIFT
SHIFT
SHIFT
SET HOCPFEATURE=%1
SET IRRFEATURE=%2
SET TRANSAPIFEATURE=%3
SET PREHOCPFEATURE=%4
SET HOCPSERVER=%5
SET IRRSERVER=%6
SET TRANSAPISERVER=%7
SET PREHOCPSERVER=%8

echo HR features is %HUMANREADFEATURE%

SET STEP="Decrypt APP Config Files"
if "%HUMANREADFEATURE%"=="false" (call Autobuild\do_invoke_any_locrem1.cmd %APPSERVER% %DEPLOYMENTDRIVE%:\Deployment-ImageReview\%DEPLOYMENTDIR%\AES "DecryptAppConfigFile.cmd" %DEPLOYMENTDRIVE%:\Deployment-ImageReview\%DEPLOYMENTDIR%\AES\DecryptAppConfigFile.cmd unused unused unused unused)
if %ERRORLEVEL% NEQ 0 GOTO :error

SET STEP="Decrypt WEB Config Files"
if "%HUMANREADFEATURE%"=="false" (call Autobuild\do_invoke_any_locrem1.cmd %WEBSERVER% %DEPLOYMENTDRIVE%:\Deployment-ImageReview\%DEPLOYMENTDIR%\AES "DecryptWebConfigFile.cmd" %DEPLOYMENTDRIVE%:\Deployment-ImageReview\%DEPLOYMENTDIR%\AES\DecryptWebConfigFile.cmd unused unused unused unused)
if %ERRORLEVEL% NEQ 0 GOTO :error

SET STEP="Decrypt Fingerprint Config Files"
if "%FINGERPRINTFEATURE%"=="true" if "%HUMANREADFEATURE%"=="false" (call Autobuild\do_invoke_any_locrem1.cmd %FPSERVER% %DEPLOYMENTDRIVE%:\Deployment-ImageReview\%DEPLOYMENTDIR%\AES "DecryptFingerprintConfigFile.cmd" %DEPLOYMENTDRIVE%:\Deployment-ImageReview\%DEPLOYMENTDIR%\AES\DecryptFingerprintConfigFile.cmd unused unused unused unused)
if %ERRORLEVEL% NEQ 0 GOTO :error

SET STEP="Decrypt HOCP Config Files"
if "%HOCPFEATURE%"=="true" if "%HUMANREADFEATURE%"=="false" (call Autobuild\do_invoke_any_locrem1.cmd %HOCPSERVER% %DEPLOYMENTDRIVE%:\Deployment-ImageReview\%DEPLOYMENTDIR%\AES "DecryptHOCPConfigFile.cmd" %DEPLOYMENTDRIVE%:\Deployment-ImageReview\%DEPLOYMENTDIR%\AES\DecryptHOCPConfigFile.cmd unused unused unused unused)
if %ERRORLEVEL% NEQ 0 GOTO :error

SET STEP="Decrypt PREHOCP Config Files"
if "%PREHOCPFEATURE%"=="true" if "%HUMANREADFEATURE%"=="false" (call Autobuild\do_invoke_any_locrem1.cmd %PREHOCPSERVER% %DEPLOYMENTDRIVE%:\Deployment-ImageReview\%DEPLOYMENTDIR%\AES "DecryptPREHOCPConfigFile.cmd" %DEPLOYMENTDRIVE%:\Deployment-ImageReview\%DEPLOYMENTDIR%\AES\DecryptPREHOCPConfigFile.cmd unused unused unused unused)
if %ERRORLEVEL% NEQ 0 GOTO :error

SET STEP="Decrypt IRR Config Files"
if "%IRRFEATURE%"=="true" if "%HUMANREADFEATURE%"=="false" (call Autobuild\do_invoke_any_locrem1.cmd %IRRSERVER% %DEPLOYMENTDRIVE%:\Deployment-ImageReview\%DEPLOYMENTDIR%\AES "DecryptIRRConfigFile.cmd" %DEPLOYMENTDRIVE%:\Deployment-ImageReview\%DEPLOYMENTDIR%\AES\DecryptIRRConfigFile.cmd unused unused unused unused)
if %ERRORLEVEL% NEQ 0 GOTO :error

SET STEP="Decrypt TRANSApi Config Files"
if "%TRANSAPIFEATURE%"=="true" if "%HUMANREADFEATURE%"=="false" (call Autobuild\do_invoke_any_locrem1.cmd %TRANSAPISERVER% %DEPLOYMENTDRIVE%:\Deployment-ImageReview\%DEPLOYMENTDIR%\AES "DecryptTransAPIConfigFile.cmd" %DEPLOYMENTDRIVE%:\Deployment-ImageReview\%DEPLOYMENTDIR%\AES\DecryptTransAPIConfigFile.cmd unused unused unused unused)
if %ERRORLEVEL% NEQ 0 GOTO :error

SET STEP="Decrypt HR Config Files"
if "%HUMANREADFEATURE%"=="true" (call Autobuild\do_invoke_any_locrem1.cmd %HRSERVER% %DEPLOYMENTDRIVE%:\Deployment-ImageReview\%DEPLOYMENTDIR%\AES "DecryptHumanReadabilityConfigFile.cmd" %DEPLOYMENTDRIVE%:\Deployment-ImageReview\%DEPLOYMENTDIR%\AES\DecryptHumanReadabilityConfigFile.cmd unused unused unused unused)
if %ERRORLEVEL% NEQ 0 GOTO :error


GOTO :END

:error
echo ------- AN ERROR OCCURED DURING THIS STEP: %STEP% ------
exit /b 1

:END
