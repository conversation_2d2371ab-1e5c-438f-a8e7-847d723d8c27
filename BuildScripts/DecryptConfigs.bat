@echo off
rem set variables
Set CURRENTDIR=%CD%

echo ------- Your current directory is:  %CURRENTDIR% ---------
SET MOMSSERVER=%1
SET DEPLOYMENTDRIVE=%2

SET STEP="Decrypt Config Files"
call Autobuild\do_invoke_any_locrem1.cmd %MOMSSERVER% %DEPLOYMENTDRIVE%:\Deployment\BuildScripts\AES "DecryptAppWebConfigFile.cmd" %DEPLOYMENTDRIVE%:\Deployment\BuildScripts\AES\DecryptAppWebConfigFile.cmd unused unused unused unused
if %ERRORLEVEL% NEQ 0 GOTO :error
GOTO :END

:error
echo ------- AN ERROR OCCURED DURING THIS STEP: %STEP% ------
exit /b 1

:END
