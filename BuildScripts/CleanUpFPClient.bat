@echo off
rem set variables
Set CURRENTDIR=%CD%

echo ------- Your current directory is:  %CURRENTDIR% ---------

set NOW=%date:~10,4%-%date:~4,2%-%date:~7,2%
echo Deployment Cleanup beginning for FPCLient servers
echo -----------------------------------


SET SERVER=%1
SET DEPLOYMENTDRIVE=%4
SET USER=%5
SET PASSWORD=%6


if %ERRORLEVEL% NEQ 0 GOTO :error 
if exist \\%SERVER%\%DEPLOYMENTDRIVE%$\Deployment-ImageReview (del /s /q \\%SERVER%\%DEPLOYMENTDRIVE%$\Deployment-ImageReview\*.config)
if exist \\%SERVER%\%DEPLOYMENTDRIVE%$\Deployment-ImageReview (del /s /q \\%SERVER%\%DEPLOYMENTDRIVE%$ Deployment-ImageReview\*.zip)
if %ERRORLEVEL% NEQ 0 GOTO :error 


echo %STEP% Deployment complete
GOTO END


:error
echo ------- AN ERROR OCCURED DURING THIS STEP: %STEP% ------
exit /b 1

:END