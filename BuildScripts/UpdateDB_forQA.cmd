@ECHO OFF
ECHO Updating Database for QA use...
cmd /c sqlcmd -b -Q "update tbuserpwd set vcUserPWD = 'NgH8er/hMaSDjyjNJ/r5Cw=='" -S localhost -d MOMS 
IF [%ERRORLEVEL%] NEQ [0] GOTO ERROR


cmd /c sqlcmd -b -Q "update tbEscalationUser set vcAddress = '<EMAIL>'" -S localhost -d MOMS
IF [%ERRORLEVEL%] NEQ [0] GOTO ERROR

cmd /c sqlcmd -b -Q "update tbUser set vcEmailAddress = '<EMAIL>'" -S localhost -d MOMS
IF [%ERRORLEVEL%] NEQ [0] GOTO ERROR

cmd /c sqlcmd -b -Q  "update stbrole set vcRoleSecurityGroup = 'SD-IFXDev' where siRoleID = 1" -S localhost -d MOMS
IF [%ERRORLEVEL%] NEQ [0] GOTO ERROR

cmd /c sqlcmd -b -Q "update tbPhone set vcPhoneNumber = 1234567890" -S localhost -d MOMS
IF [%ERRORLEVEL%] NEQ [0] GOTO ERROR

cmd /c sqlcmd -b -Q "update tbUser set vcPhoneNumber = 1234567890" -S localhost -d MOMS
IF [%ERRORLEVEL%] NEQ [0] GOTO ERROR

cmd /c sqlcmd -b -Q "update tbApplParam set vcParamValue =  '<EMAIL>' where vcParamName = 'NOTIFICATIONTOADDRESS'" -S localhost -d MOMS
IF [%ERRORLEVEL%] NEQ [0] GOTO ERROR

cmd /c sqlcmd -b -Q "update tbApplParam set vcParamValue =  '<EMAIL>' where vcParamName = 'NOTIFICATIONFROMADDRESS'" -S localhost -d MOMS
IF [%ERRORLEVEL%] NEQ [0] GOTO ERROR

cmd /c sqlcmd -b -Q "update tbApplParam set vcParamValue =  '<EMAIL>' where vcParamName = 'EMAILUSERNAME'" -S localhost -d MOMS
IF [%ERRORLEVEL%] NEQ [0] GOTO ERROR

cmd /c sqlcmd -b -Q "update tbApplParam set vcParamValue =  'S@ndieg0' where vcParamName = 'EMAILPASSWORD'" -S localhost -d MOMS
IF [%ERRORLEVEL%] NEQ [0] GOTO ERROR

cmd /c sqlcmd -b -Q "update tbApplParam set vcParamValue =  'tcsdhosting.com' where vcParamName = 'EMAILDOMAINNAME'" -S localhost -d MOMS
IF [%ERRORLEVEL%] NEQ [0] GOTO ERROR

cmd /c sqlcmd -b -Q "update tbApplParam set vcParamValue =  'mail.tcsdhosting.com' where vcParamName = 'SMTPSERVER'" -S localhost -d MOMS
IF [%ERRORLEVEL%] NEQ [0] GOTO ERROR

cmd /c sqlcmd -b -Q "update tbApplParam set vcParamValue =  'mail.tcsdhosting.com' where vcParamName = 'IMAPMAILSERVER'" -S localhost -d MOMS
IF [%ERRORLEVEL%] NEQ [0] GOTO ERROR

cmd /c sqlcmd -b -Q "update tbApplParam set vcParamValue =  '<EMAIL>' where vcParamName = 'WORKORDERSECONDARYADDRESS'" -S localhost -d MOMS
IF [%ERRORLEVEL%] NEQ [0] GOTO ERROR

cmd /c sqlcmd -b -Q "update tbNotificationRecipient set vcToAddress = '<EMAIL>'" -S localhost -d MOMS
IF [%ERRORLEVEL%] NEQ [0] GOTO ERROR

cmd /c sqlcmd -b -Q "update tbNotificationRecipient set bIsSent = 1" -S localhost -d MOMS
IF [%ERRORLEVEL%] NEQ [0] GOTO ERROR

cmd /c sqlcmd -b -Q "update tbApplParam set vcParamValue = 'tcore.com' where vcParamName = 'AD_DEFAULTDOMAIN'" -S localhost -d MOMS
IF [%ERRORLEVEL%] NEQ [0] GOTO ERROR

cmd /c sqlcmd -b -Q "update tbApplParam set vcParamValue = 'OU=Security Groups,OU=San Diego 335,OU=San Diego,DC=tcore,DC=com' where vcParamName = 'AD_GROUPDEFAULTOU'" -S localhost -d MOMS
IF [%ERRORLEVEL%] NEQ [0] GOTO ERROR

cmd /c sqlcmd -b -Q "update tbApplParam set vcParamValue = 'DC=tcore,DC=com' where vcParamName = 'AD_DEFAULTROOTOU'" -S localhost -d MOMS
IF [%ERRORLEVEL%] NEQ [0] GOTO ERROR

cmd /c sqlcmd -b -Q "update tbQNotificationRequest set iQID=2001  where iQID in (2000,2002,2003)" -S localhost -d MOMS
IF [%ERRORLEVEL%] NEQ [0] GOTO ERROR

cmd /c sqlcmd -b -Q "update tbPreventiveMaintenanceSchedule set bActive=0" -S localhost -d MOMS
IF [%ERRORLEVEL%] NEQ [0] GOTO ERROR

cmd /c sqlcmd -b -Q "update tbApplParam set vcParamValue = '25' where vcParamName = 'SMTPPORT'" -S localhost -d MOMS
IF [%ERRORLEVEL%] NEQ [0] GOTO ERROR



GOTO SUCCESS

:ERROR
ECHO -------------------------------------------------------------------------------
ECHO !!!!!                 ERROR OCCURRED WHILE UPDATING DB FOR QA USE             !!!!!
ECHO -------------------------------------------------------------------------------
PAUSE
GOTO END

:SUCCESS
ECHO.
ECHO Database built successfully 

GOTO END

:END
ECHO on %date% %time%

pause



