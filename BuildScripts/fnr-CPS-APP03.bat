@echo off
rem set variables
Set CURRENTDIR=%CD%
Set DEPLOYDIR=%1
"%CURRENTDIR%\fnr.exe" --cl --dir "%DEPLOYDIR%\APP03\DM" --fileMask "appsettings.json" --includeSubDirectories --find """CSVFileNameSuffix"": ""01""" --replace """CSVFileNameSuffix"": ""03"""


"%CURRENTDIR%\fnr.exe" --cl --dir "%DEPLOYDIR%\APP03\VRG" --fileMask "*.json" --excludeFileMask "*.dll, *.exe" --includeSubDirectories --find """ProcessMIRPendingImageStatus"": true" --replace """ProcessMIRPendingImageStatus"": false"
"%CURRENTDIR%\fnr.exe" --cl --dir "%DEPLOYDIR%\APP03\VRG" --fileMask "*.json" --excludeFileMask "*.dll, *.exe" --includeSubDirectories --find """ProcessTransactionReviewsStatus"": true" --replace """ProcessTransactionReviewsStatus"": false"
"%CURRENTDIR%\fnr.exe" --cl --dir "%DEPLOYDIR%\APP03\VRG" --fileMask "*.json" --excludeFileMask "*.dll, *.exe" --includeSubDirectories --find """ProcessUVIDClassificationReviewsStatus"": true" --replace """ProcessUVIDClassificationReviewsStatus"": false"
"%CURRENTDIR%\fnr.exe" --cl --dir "%DEPLOYDIR%\APP03\VRG" --fileMask "*.json" --excludeFileMask "*.dll, *.exe" --includeSubDirectories --find """ProcessUVIDLicensePlateReviewsStatus"": true" --replace """ProcessUVIDLicensePlateReviewsStatus"": false"
"%CURRENTDIR%\fnr.exe" --cl --dir "%DEPLOYDIR%\APP03\VRG" --fileMask "*.json" --excludeFileMask "*.dll, *.exe" --includeSubDirectories --find """ProcessUVIDNewClassificationReviewsStatus"": true" --replace """ProcessUVIDNewClassificationReviewsStatus"": false"
"%CURRENTDIR%\fnr.exe" --cl --dir "%DEPLOYDIR%\APP03\VRG" --fileMask "*.json" --excludeFileMask "*.dll, *.exe" --includeSubDirectories --find """ProcessUVIDNewLicensePlateReviewsStatus"": true" --replace """ProcessUVIDNewLicensePlateReviewsStatus"": false"
"%CURRENTDIR%\fnr.exe" --cl --dir "%DEPLOYDIR%\APP03\VRG" --fileMask "*.json" --excludeFileMask "*.dll, *.exe" --includeSubDirectories --find """ProcessUVIDNewVehicleTypeReviewsStatus"": true" --replace """ProcessUVIDNewVehicleTypeReviewsStatus"": false"
"%CURRENTDIR%\fnr.exe" --cl --dir "%DEPLOYDIR%\APP03\VRG" --fileMask "*.json" --excludeFileMask "*.dll, *.exe" --includeSubDirectories --find """ProcessUVIDVehicleTypeReviewsStatus"": true" --replace """ProcessUVIDVehicleTypeReviewsStatus"": false"
"%CURRENTDIR%\fnr.exe" --cl --dir "%DEPLOYDIR%\APP03\VRG" --fileMask "*.json" --excludeFileMask "*.dll, *.exe" --includeSubDirectories --find """ProcessDMVRequests"": true" --replace """ProcessDMVRequests"": false"
"%CURRENTDIR%\fnr.exe" --cl --dir "%DEPLOYDIR%\APP03\VRG" --fileMask "*.json" --excludeFileMask "*.dll, *.exe" --includeSubDirectories --find """ProcessDMVResponses"": true" --replace """ProcessDMVResponses"": false"
"%CURRENTDIR%\fnr.exe" --cl --dir "%DEPLOYDIR%\APP03\VRG" --fileMask "*.json" --excludeFileMask "*.dll, *.exe" --includeSubDirectories --find """ProcessVehicleCorrectionReviewResults"": true" --replace """ProcessVehicleCorrectionReviewResults"": false"
"%CURRENTDIR%\fnr.exe" --cl --dir "%DEPLOYDIR%\APP03\VRG" --fileMask "*.json" --excludeFileMask "*.dll, *.exe" --includeSubDirectories --find """ProcessUVIDNewVDMReadiness"": false" --replace """ProcessUVIDNewVDMReadiness"": true"

rem "%CURRENTDIR%\fnr.exe" --cl --dir "%DEPLOYDIR%\APP03\VRG" --fileMask "*.json" --excludeFileMask "*.dll, *.exe" --includeSubDirectories --find """ProcessContraFlowQueue"": true" --replace """ProcessContraFlowQueue"": false"

"%CURRENTDIR%\fnr.exe" --cl --dir "%DEPLOYDIR%\APP03\VRG" --fileMask "*.json" --excludeFileMask "*.dll, *.exe" --includeSubDirectories --find """ProcessVehicleCorrectionRequests"": true" --replace """ProcessVehicleCorrectionRequests"": false"

"%CURRENTDIR%\fnr.exe" --cl --dir "%DEPLOYDIR%\APP03\VRG" --fileMask "*.json" --excludeFileMask "*.dll, *.exe" --includeSubDirectories --find """ProcessVehicleCorrectionReviewResults"": true" --replace """ProcessVehicleCorrectionReviewResults"": false"

"%CURRENTDIR%\fnr.exe" --cl --dir "%DEPLOYDIR%\APP03\VRG" --fileMask "*.json" --excludeFileMask "*.dll, *.exe" --includeSubDirectories --find """ProcessVehicleCorrectionReadyForAnalysis"": true" --replace """ProcessVehicleCorrectionReadyForAnalysis"": false"

"%CURRENTDIR%\fnr.exe" --cl --dir "%DEPLOYDIR%\APP03\VRG" --fileMask "appsettings.json" --includeSubDirectories --find """VDMTablePendingPollWaitDuration"": 2000" --replace """VDMTablePendingPollWaitDuration"": 1000"

"%CURRENTDIR%\fnr.exe" --cl --dir "%DEPLOYDIR%\APP03\VRG" --fileMask "appsettings.json" --includeSubDirectories --find """VDMTablePollBatchSize"": 50" --replace """VDMTablePollBatchSize"": 100"

"%CURRENTDIR%\fnr.exe" --cl --dir "%DEPLOYDIR%\APP03\VRG" --fileMask "*.json" --excludeFileMask "*.dll, *.exe" --includeSubDirectories --find """VRGGroupID"": 1" --replace """VRGGroupID"": 3"

"%CURRENTDIR%\fnr.exe" --cl --dir "%DEPLOYDIR%\APP03\VRG" --fileMask "*.json" --excludeFileMask "*.dll, *.exe" --includeSubDirectories --find """ProcessTransactionClassificationReviews"": true" --replace """ProcessTransactionClassificationReviews"": false"

"%CURRENTDIR%\fnr.exe" --cl --dir "%DEPLOYDIR%\APP03\VRG" --fileMask "*.json" --excludeFileMask "*.dll, *.exe" --includeSubDirectories --find """ProcessUVIDClassificationReviews"": true" --replace """ProcessUVIDClassificationReviews"": false"


"%CURRENTDIR%\fnr.exe" --cl --dir "%DEPLOYDIR%\APP03\VRG" --fileMask "*.json" --excludeFileMask "*.dll, *.exe" --includeSubDirectories --find """ProcessTransactionLicensePlateReviews"": true" --replace """ProcessTransactionLicensePlateReviews"": false"

"%CURRENTDIR%\fnr.exe" --cl --dir "%DEPLOYDIR%\APP03\VRG" --fileMask "*.json" --excludeFileMask "*.dll, *.exe" --includeSubDirectories --find """ProcessUVIDLicensePlateReviews"": true" --replace """ProcessUVIDLicensePlateReviews"": false"


"%CURRENTDIR%\fnr.exe" --cl --dir "%DEPLOYDIR%\APP03\VRG" --fileMask "*.json" --excludeFileMask "*.dll, *.exe" --includeSubDirectories --find """ProcessTransactionVehicleTypeReviews"": true" --replace """ProcessTransactionVehicleTypeReviews"": false"

"%CURRENTDIR%\fnr.exe" --cl --dir "%DEPLOYDIR%\APP03\VRG" --fileMask "*.json" --excludeFileMask "*.dll, *.exe" --includeSubDirectories --find """ProcessUVIDVehicleTypeReviews"": true" --replace """ProcessUVIDVehicleTypeReviews"": false"

"%CURRENTDIR%\fnr.exe" --cl --dir "%DEPLOYDIR%\APP03\VRG" --fileMask "*.json" --excludeFileMask "*.dll, *.exe" --includeSubDirectories --find """MIRGroupID"": ""VRG1""" --replace """MIRGroupID"": ""VRG3"""
