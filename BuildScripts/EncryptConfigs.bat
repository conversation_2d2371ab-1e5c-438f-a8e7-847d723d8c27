@echo off
rem set variables
Set CURRENTDIR=%CD%

echo ------- Your current directory is:  %CURRENTDIR% ---------
SET FPSERVER=%1
SET APPSERVER=%2
SET WEBSERVER=%3
SET DEPLOYMENTDRIVE=%4
SET DEPLOYMENTDIR=%5
SET FINGERPRINTFEATURE=%6
SET IFXSERVER=%7
SET HRSERVER=%8
SET HUMANREADFEATURE=%9
SHIFT
SHIFT
SHIFT
SHIFT
SHIFT
SHIFT
SHIFT
SHIFT
SHIFT
SET HOCPFEATURE=%1
SET IRRFEATURE=%2
SET TRANSAPIFEATURE=%3
SET PREHOCPFEATURE=%4
SET HOCPSERVER=%5
SET IRRSERVER=%6
SET TRANSAPISERVER=%7
SET PREHOCPSERVER=%8

echo HR features is %HUMANREADFEATURE%

SET STEP="Encrypt APP Config Files"
if "%HUMANREADFEATURE%"=="false" (call Autobuild\do_invoke_any_locrem1.cmd %APPSERVER% %DEPLOYMENTDRIVE%:\Deployment-ImageReview\%DEPLOYMENTDIR%\AES "EncryptAppConfigFile.cmd" %DEPLOYMENTDRIVE%:\Deployment-ImageReview\%DEPLOYMENTDIR%\AES\EncryptAppConfigFile.cmd unused unused unused unused)
if %ERRORLEVEL% NEQ 0 GOTO :error

SET STEP="Encrypt WEB Config Files"
if "%HUMANREADFEATURE%"=="false" (call Autobuild\do_invoke_any_locrem1.cmd %WEBSERVER% %DEPLOYMENTDRIVE%:\Deployment-ImageReview\%DEPLOYMENTDIR%\AES "EncryptWebConfigFile.cmd" %DEPLOYMENTDRIVE%:\Deployment-ImageReview\%DEPLOYMENTDIR%\AES\EncryptWebConfigFile.cmd unused unused unused unused)
if %ERRORLEVEL% NEQ 0 GOTO :error

SET STEP="Encrypt Fingerprint Config Files"
if "%FINGERPRINTFEATURE%"=="true" if "%HUMANREADFEATURE%"=="false" (call Autobuild\do_invoke_any_locrem1.cmd %FPSERVER% %DEPLOYMENTDRIVE%:\Deployment-ImageReview\%DEPLOYMENTDIR%\AES "EncryptFingerprintConfigFile.cmd" %DEPLOYMENTDRIVE%:\Deployment-ImageReview\%DEPLOYMENTDIR%\AES\EncryptFingerprintConfigFile.cmd unused unused unused unused)
if %ERRORLEVEL% NEQ 0 GOTO :error

SET STEP="Encrypt HOCP Config Files"
if "%HOCPFEATURE%"=="true" if "%HUMANREADFEATURE%"=="false" (call Autobuild\do_invoke_any_locrem1.cmd %HOCPSERVER% %DEPLOYMENTDRIVE%:\Deployment-ImageReview\%DEPLOYMENTDIR%\AES "EncryptHOCPConfigFile.cmd" %DEPLOYMENTDRIVE%:\Deployment-ImageReview\%DEPLOYMENTDIR%\AES\EncryptHOCPConfigFile.cmd unused unused unused unused)
if %ERRORLEVEL% NEQ 0 GOTO :error

SET STEP="Encrypt PREHOCP Config Files"
if "%PREHOCPFEATURE%"=="true" if "%HUMANREADFEATURE%"=="false" (call Autobuild\do_invoke_any_locrem1.cmd %PREHOCPSERVER% %DEPLOYMENTDRIVE%:\Deployment-ImageReview\%DEPLOYMENTDIR%\AES "EncryptPREHOCPConfigFile.cmd" %DEPLOYMENTDRIVE%:\Deployment-ImageReview\%DEPLOYMENTDIR%\AES\EncryptPREHOCPConfigFile.cmd unused unused unused unused)
if %ERRORLEVEL% NEQ 0 GOTO :error

SET STEP="Encrypt IRR Config Files"
if "%IRRFEATURE%"=="true" if "%HUMANREADFEATURE%"=="false" (call Autobuild\do_invoke_any_locrem1.cmd %IRRSERVER% %DEPLOYMENTDRIVE%:\Deployment-ImageReview\%DEPLOYMENTDIR%\AES "EncryptIRRConfigFile.cmd" %DEPLOYMENTDRIVE%:\Deployment-ImageReview\%DEPLOYMENTDIR%\AES\EncryptIRRConfigFile.cmd unused unused unused unused)
if %ERRORLEVEL% NEQ 0 GOTO :error

SET STEP="Encrypt TRANSApi Config Files"
if "%TRANSAPIFEATURE%"=="true" if "%HUMANREADFEATURE%"=="false" (call Autobuild\do_invoke_any_locrem1.cmd %TRANSAPISERVER% %DEPLOYMENTDRIVE%:\Deployment-ImageReview\%DEPLOYMENTDIR%\AES "EncryptTransAPIConfigFile.cmd" %DEPLOYMENTDRIVE%:\Deployment-ImageReview\%DEPLOYMENTDIR%\AES\EncryptTransAPIConfigFile.cmd unused unused unused unused)
if %ERRORLEVEL% NEQ 0 GOTO :error

SET STEP="Encrypt HR Config Files"
if "%HUMANREADFEATURE%"=="true" (call Autobuild\do_invoke_any_locrem1.cmd %HRSERVER% %DEPLOYMENTDRIVE%:\Deployment-ImageReview\%DEPLOYMENTDIR%\AES "EncryptHumanReadabilityConfigFile.cmd" %DEPLOYMENTDRIVE%:\Deployment-ImageReview\%DEPLOYMENTDIR%\AES\EncryptHumanReadabilityConfigFile.cmd unused unused unused unused)
if %ERRORLEVEL% NEQ 0 GOTO :error


GOTO :END

:error
echo ------- AN ERROR OCCURED DURING THIS STEP: %STEP% ------
exit /b 1

:END
