@echo off
rem set variables
Set CURRENTDIR=%CD%

echo ------- Your current directory is:  %CURRENTDIR% ---------

set NOW=%date:~10,4%-%date:~4,2%-%date:~7,2%
echo Deployment beginning %NOW%
echo -----------------------------------
echo Connect to Server
echo -----------------------------------
verify > nul
SET SERVER=%2
SET USER=%3
SET PASSWORD=%4
SET DEPLOYMENTDRIVE=%5
SET DEPLOYMENTDIR=%6
SET JUMPDRIVE=%7
SET GENTASK=%8
SET road=%9
SHIFT
SHIFT
SHIFT
SHIFT
SHIFT
SHIFT
SHIFT
SHIFT
SHIFT
SET CORRIDORS=%1
SET PROCESSTYPE=%2


ECHO %road%
If NOT "%road%" == "none" SET road=-%road%
If "%road%" == "none" SET road=
echo ROAD = %road%

ECHO %CORRIDORS%
If "%CORRIDORS%" == "none" set CORRIDORS=
echo CORRIDORS = %CORRIDORS%

ECHO %CORRIDORS%
If "%PROCESSTYPE%" == "none" set PROCESSTYPE=
echo PROCESSTYPE = %PROCESSTYPE%

SET PLAZAIDS=

ECHO %SERVER%
SET BASEDIR=%JUMPDRIVE%:\Staging-ImageReview\%DEPLOYMENTDIR%
net use p: /delete

echo ------- map drive ----------
net use p: \\%SERVER%\%DEPLOYMENTDRIVE%$ %PASSWORD% /user:%USER%

echo Server: %SERVER% GENTASK: %GENTASK% ROAD: %road%

if %ERRORLEVEL% NEQ 0 GOTO :error 
echo ------- destination cleanup -----------
if exist \\%SERVER%\%DEPLOYMENTDRIVE%$\Deployment-ImageReview\%DEPLOYMENTDIR%\%GENTASK%.zip (del /s /q \\%SERVER%\%DEPLOYMENTDRIVE%$\Deployment-ImageReview\%DEPLOYMENTDIR%\%GENTASK%.zip)
if %ERRORLEVEL% NEQ 0 GOTO :error 
if exist \\%SERVER%\%DEPLOYMENTDRIVE%$\Deployment-ImageReview\%DEPLOYMENTDIR%\%GENTASK% (rmdir /s /q \\%SERVER%\%DEPLOYMENTDRIVE%$\Deployment-ImageReview\%DEPLOYMENTDIR%\%GENTASK%)
if %ERRORLEVEL% NEQ 0 GOTO :error 
if exist \\%SERVER%\%DEPLOYMENTDRIVE%$\Deployment-ImageReview\%DEPLOYMENTDIR%\%GENTASK%%road% (rmdir /s /q \\%SERVER%\%DEPLOYMENTDRIVE%$\Deployment-ImageReview\%DEPLOYMENTDIR%\%GENTASK%%road%)
if %ERRORLEVEL% NEQ 0 GOTO :error 
if exist \\%SERVER%\%DEPLOYMENTDRIVE%$\Deployment-ImageReview\%DEPLOYMENTDIR%\AES (rmdir /s /q \\%SERVER%\%DEPLOYMENTDRIVE%$\Deployment-ImageReview\%DEPLOYMENTDIR%\AES)
if %ERRORLEVEL% NEQ 0 GOTO :error 
if NOT exist \\%SERVER%\%DEPLOYMENTDRIVE%$\Deployment-ImageReview\%DEPLOYMENTDIR% (mkdir \\%SERVER%\%DEPLOYMENTDRIVE%$\Deployment-ImageReview\%DEPLOYMENTDIR%)
if %ERRORLEVEL% NEQ 0 GOTO :error 


echo Error Level is %ERRORLEVEL%
:deploy
echo ------------------------------------
echo Copy NODES....to %SERVER% %GENTASK%
echo ------------------------------------
set STEP="Deploy Zips to %SERVER% %GENTASK% %road%"
for /F %%i in ('dir /b "%BASEDIR%\%GENTASK%.zip"') do (
robocopy /R:3 %BASEDIR% \\%SERVER%\%DEPLOYMENTDRIVE%$\Deployment-ImageReview\%DEPLOYMENTDIR% %GENTASK%.zip
)
echo error level %ERRORLEVEL% 
if %ERRORLEVEL% GEQ 8 GOTO :error 
verify > nul
if not exist \\%SERVER%\%DEPLOYMENTDRIVE%$\Deployment-ImageReview\%DEPLOYMENTDIR%\AES.zip (xcopy /R /Y %BASEDIR%\AES.zip \\%SERVER%\%DEPLOYMENTDRIVE%$\Deployment-ImageReview\%DEPLOYMENTDIR%
)
if %ERRORLEVEL% NEQ 0 GOTO :error 


echo Nodes Unzip is starting at %fullstamp% 
echo ----------------------------------

:Node_Delete_Current_unzips
SET STEP="Remove unzipped files from %SERVER% %GENTASK%"
echo Step %STEP% commencing...  --------- 
ECHO "if exist remove directory for %GENTASK%"
rem psexec -accepteula \\%SERVER% cmd /c if exist %DEPLOYMENTDRIVE%:\Deployment-ImageReview\%DEPLOYMENTDIR%\%GENTASK% (rmdir /s /q %DEPLOYMENTDRIVE%:\Deployment-ImageReview\%DEPLOYMENTDIR%\%GENTASK%)
if %ERRORLEVEL% NEQ 0 GOTO :error
ECHO "if exist remove directory for %GENTASK% AES"
rem psexec -accepteula \\%SERVER% cmd /c if exist %DEPLOYMENTDRIVE%:\Deployment-ImageReview\%DEPLOYMENTDIR%\AES (rmdir /s /q %DEPLOYMENTDRIVE%:\Deployment-ImageReview\%DEPLOYMENTDIR%\AES)
if %ERRORLEVEL% NEQ 0 GOTO :error
echo Step %STEP% completed...  ---------


:UnzipNode
SET STEP="Unzip Nodes %SERVER% %GENTASK%"
echo Step %STEP% commencing...  --------- 
rem call Autobuild\do_unzip_locrem1.cmd %GENTASK%.zip %DEPLOYMENTDRIVE%:\Deployment-ImageReview\%DEPLOYMENTDIR% -qo %SERVER%
unzip -ou \\%SERVER%\%DEPLOYMENTDRIVE%$\Deployment-ImageReview\%DEPLOYMENTDIR%\%GENTASK%.zip -d  \\%SERVER%\%DEPLOYMENTDRIVE%$\Deployment-ImageReview\%DEPLOYMENTDIR%\
if %ERRORLEVEL% NEQ 0 GOTO :error
unzip -ou \\%SERVER%\%DEPLOYMENTDRIVE%$\Deployment-ImageReview\%DEPLOYMENTDIR%\AES.zip -d  \\%SERVER%\%DEPLOYMENTDRIVE%$\Deployment-ImageReview\%DEPLOYMENTDIR%\
rem call Autobuild\do_unzip_locrem1.cmd AES.zip %DEPLOYMENTDRIVE%:\Deployment-ImageReview\%DEPLOYMENTDIR%\ -qo %SERVER%
if %ERRORLEVEL% NEQ 0 GOTO :error
rem if "%road%" == "none" GOTO POKEGENERALONLY
rem rename \\%SERVER%\%DEPLOYMENTDRIVE%$\Deployment-ImageReview\%DEPLOYMENTDIR%\%GENTASK% %GENTASK%%road%
if "%road%"=="" GOTO :Poke
echo rename %GENTASK% directory to %GENTASK%%road%
xcopy "\\%SERVER%\%DEPLOYMENTDRIVE%$\Deployment-ImageReview\%DEPLOYMENTDIR%\%GENTASK%" "\\%SERVER%\%DEPLOYMENTDRIVE%$\Deployment-ImageReview\%DEPLOYMENTDIR%\%GENTASK%%road%" /E /C /I
del /s /q "\\%SERVER%\%DEPLOYMENTDRIVE%$\Deployment-ImageReview\%DEPLOYMENTDIR%\%GENTASK%\*"
rmdir /s /q "\\%SERVER%\%DEPLOYMENTDRIVE%$\Deployment-ImageReview\%DEPLOYMENTDIR%\%GENTASK%"
if %ERRORLEVEL% NEQ 0 GOTO :error

echo Step %STEP% completed...  ---------

if %ERRORLEVEL% NEQ 0 GOTO :error

:Poke
ReplaceUtil.exe \\%SERVER%\%DEPLOYMENTDRIVE%$\Deployment-ImageReview\%DEPLOYMENTDIR%\%GENTASK%%road% json "CorridorIDValues" "%CORRIDORS%"
if %ERRORLEVEL% NEQ 0 GOTO :error
ReplaceUtil.exe \\%SERVER%\%DEPLOYMENTDRIVE%$\Deployment-ImageReview\%DEPLOYMENTDIR%\%GENTASK%%road% json "d:\\IFXLogs\\" "d:\\ScheduledTaskLogs\\%GENTASK%%road%\\"
if %ERRORLEVEL% NEQ 0 GOTO :error
ReplaceUtil.exe \\%SERVER%\%DEPLOYMENTDRIVE%$\Deployment-ImageReview\%DEPLOYMENTDIR%\%GENTASK%%road% json "d:\\MIRLogs\\" "d:\\ScheduledTaskLogs\\%GENTASK%%road%\\"
if %ERRORLEVEL% NEQ 0 GOTO :error
ReplaceUtil.exe \\%SERVER%\%DEPLOYMENTDRIVE%$\Deployment-ImageReview\%DEPLOYMENTDIR%\%GENTASK%%road% json "D:\\IFXLogs\\" "D:\\ScheduledTaskLogs\\%GENTASK%%road%\\"
if %ERRORLEVEL% NEQ 0 GOTO :error
ReplaceUtil.exe \\%SERVER%\%DEPLOYMENTDRIVE%$\Deployment-ImageReview\%DEPLOYMENTDIR%\%GENTASK%%road% json "D:\\MIRLogs\\" "D:\\ScheduledTaskLogs\\%GENTASK%%road%\\"
if %ERRORLEVEL% NEQ 0 GOTO :error

ReplaceUtil.exe \\%SERVER%\%DEPLOYMENTDRIVE%$\Deployment-ImageReview\%DEPLOYMENTDIR%\%GENTASK%%road% config "SENDRESULTS_PROCESSTYPE" "%PROCESSTYPE%"
if %ERRORLEVEL% NEQ 0 GOTO :error
ReplaceUtil.exe \\%SERVER%\%DEPLOYMENTDRIVE%$\Deployment-ImageReview\%DEPLOYMENTDIR%\%GENTASK%%road% config "ProcessedResultValue" "%PROCESSTYPE%"
if %ERRORLEVEL% NEQ 0 GOTO :error
ReplaceUtil.exe \\%SERVER%\%DEPLOYMENTDRIVE%$\Deployment-ImageReview\%DEPLOYMENTDIR%\%GENTASK%%road% config "PlazaIDValues" "%PLAZAIDS%"
rem call fnr.exe --cl --dir \\%SERVER%\%DEPLOYMENTDRIVE%$\Deployment-ImageReview\%DEPLOYMENTDIR%\%GENTASK%%road% --fileMask "*.config" --includeSubDirectories --find "PlazaIDValues" --replace ""
if %ERRORLEVEL% NEQ 0 GOTO :error
ReplaceUtil.exe \\%SERVER%\%DEPLOYMENTDRIVE%$\Deployment-ImageReview\%DEPLOYMENTDIR%\%GENTASK%%road% config D:\MIRLogs\ D:\ScheduledTaskLogs\%GENTASK%%road%\ ALL
if %ERRORLEVEL% NEQ 0 GOTO :error
ReplaceUtil.exe \\%SERVER%\%DEPLOYMENTDRIVE%$\Deployment-ImageReview\%DEPLOYMENTDIR%\%GENTASK%%road% config D:\HOCPLogs\ D:\ScheduledTaskLogs\%GENTASK%%road%\ ALL
if %ERRORLEVEL% NEQ 0 GOTO :error
ReplaceUtil.exe \\%SERVER%\%DEPLOYMENTDRIVE%$\Deployment-ImageReview\%DEPLOYMENTDIR%\%GENTASK%%road% config D:\PreHOCPLogs\ D:\ScheduledTaskLogs\%GENTASK%%road%\ ALL
if %ERRORLEVEL% NEQ 0 GOTO :error
ReplaceUtil.exe \\%SERVER%\%DEPLOYMENTDRIVE%$\Deployment-ImageReview\%DEPLOYMENTDIR%\%GENTASK%%road% config D:\FPMLogs\ D:\ScheduledTaskLogs\%GENTASK%%road%\ ALL
if %ERRORLEVEL% NEQ 0 GOTO :error
ReplaceUtil.exe \\%SERVER%\%DEPLOYMENTDRIVE%$\Deployment-ImageReview\%DEPLOYMENTDIR%\%GENTASK%%road% config D:\FPPLogs\ D:\ScheduledTaskLogs\%GENTASK%%road%\ ALL
if %ERRORLEVEL% NEQ 0 GOTO :error
ReplaceUtil.exe \\%SERVER%\%DEPLOYMENTDRIVE%$\Deployment-ImageReview\%DEPLOYMENTDIR%\%GENTASK%%road% config D:\IRRLogs\ D:\ScheduledTaskLogs\%GENTASK%%road%\ ALL
if %ERRORLEVEL% NEQ 0 GOTO :error

ReplaceUtil.exe \\%SERVER%\%DEPLOYMENTDRIVE%$\Deployment-ImageReview\%DEPLOYMENTDIR%\%GENTASK%%road% config D:/MIRLogs/ D:\ScheduledTaskLogs\%GENTASK%%road%\ ALL
if %ERRORLEVEL% NEQ 0 GOTO :error
ReplaceUtil.exe \\%SERVER%\%DEPLOYMENTDRIVE%$\Deployment-ImageReview\%DEPLOYMENTDIR%\%GENTASK%%road% config D:/HOCPLogs/ D:\ScheduledTaskLogs\%GENTASK%%road%\ ALL
if %ERRORLEVEL% NEQ 0 GOTO :error
ReplaceUtil.exe \\%SERVER%\%DEPLOYMENTDRIVE%$\Deployment-ImageReview\%DEPLOYMENTDIR%\%GENTASK%%road% config D:/PreHOCPLogs/ D:\ScheduledTaskLogs\%GENTASK%%road%\ ALL
if %ERRORLEVEL% NEQ 0 GOTO :error
ReplaceUtil.exe \\%SERVER%\%DEPLOYMENTDRIVE%$\Deployment-ImageReview\%DEPLOYMENTDIR%\%GENTASK%%road% config D:/FPMLogs/ D:\ScheduledTaskLogs\%GENTASK%%road%\ ALL
if %ERRORLEVEL% NEQ 0 GOTO :error
ReplaceUtil.exe \\%SERVER%\%DEPLOYMENTDRIVE%$\Deployment-ImageReview\%DEPLOYMENTDIR%\%GENTASK%%road% config D:/FPPLogs/ D:\ScheduledTaskLogs\%GENTASK%%road%\ ALL
if %ERRORLEVEL% NEQ 0 GOTO :error
ReplaceUtil.exe \\%SERVER%\%DEPLOYMENTDRIVE%$\Deployment-ImageReview\%DEPLOYMENTDIR%\%GENTASK%%road% config D:/IRRLogs/ D:\ScheduledTaskLogs\%GENTASK%%road%\ ALL
if %ERRORLEVEL% NEQ 0 GOTO :error

ReplaceUtil.exe \\%SERVER%\%DEPLOYMENTDRIVE%$\Deployment-ImageReview\%DEPLOYMENTDIR%\%GENTASK%%road% config "CorridorIDValues" "%CORRIDORS%" ALL
if %ERRORLEVEL% NEQ 0 GOTO :error
GOTO :UNMAP

:POKEGENERALONLY 
rem ReplaceUtil.exe \\%SERVER%\%DEPLOYMENTDRIVE%$\Deployment-ImageReview\%DEPLOYMENTDIR%\%GENTASK% config D:\MIRLogs\ D:\ScheduledTaskLogs\%GENTASK%\
rem ReplaceUtil.exe \\%SERVER%\%DEPLOYMENTDRIVE%$\Deployment-ImageReview\%DEPLOYMENTDIR%\%GENTASK% config D:\HOCPLogs\ D:\ScheduledTaskLogs\%GENTASK%\
rem ReplaceUtil.exe \\%SERVER%\%DEPLOYMENTDRIVE%$\Deployment-ImageReview\%DEPLOYMENTDIR%\%GENTASK% config D:\FPMLogs\ D:\ScheduledTaskLogs\%GENTASK%\
rem ReplaceUtil.exe \\%SERVER%\%DEPLOYMENTDRIVE%$\Deployment-ImageReview\%DEPLOYMENTDIR%\%GENTASK% config D:\FPPLogs\ D:\ScheduledTaskLogs\%GENTASK%\
rem ReplaceUtil.exe \\%SERVER%\%DEPLOYMENTDRIVE%$\Deployment-ImageReview\%DEPLOYMENTDIR%\%GENTASK% config D:\IRRLogs\ D:\ScheduledTaskLogs\%GENTASK%\

:UNMAP
net use p: /delete
GOTO END


:error
echo ------- AN ERROR OCCURED DURING THIS STEP: %STEP% %SERVER% %GENTASK% %road%------
echo ------- AN ERROR OCCURED DURING THIS STEP: %STEP% %SERVER% %GENTASK% %road%------ 
rem NAnt.exe -buildfile:FPClient_build.build -logger:NAnt.Core.MailLogger -logfile:AppLogs\build.log autobuild-error
exit /b 1

:END
echo -- Unzip Packages of Nodes %SERVER% %GENTASK% %road% is complete
echo -- Unzip Packages of Nodes %SERVER% %GENTASK% %road% is complete 
rem NAnt.exe -buildfile:FPClient_build.build -logger:NAnt.Core.MailLogger -logfile:AppLogs\build.log autobuild-complete
exit /b 0




:error
echo ------- AN ERROR OCCURED DURING THIS STEP: %STEP% ------
exit /b 1

:END