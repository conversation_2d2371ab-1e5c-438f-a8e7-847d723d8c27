@echo off
rem set variables
Set CURRENTDIR=%CD%

echo ------- Your current directory is:  %CURRENTDIR% ---------

set NOW=%date:~10,4%-%date:~4,2%-%date:~7,2%
echo Deployment beginning %NOW%
echo -----------------------------------
echo Connect to Server
echo -----------------------------------
verify > nul
SET SERVER=%2
SET USER=%3
SET PASSWORD=%4
SET DEPLOYMENTDRIVE=%5
SET DEPLOYMENTDIR=%6
SET JUMPDRIVE=%7
SET TYPE=%8
SET URLPORT=%9
SHIFT
SHIFT
SHIFT
SHIFT
SHIFT
SHIFT
SHIFT
SHIFT
SHIFT
SET LOGDIR=%1
SET IMSBASEURL=%2
SET PORT=-%URLPORT%

echo type is %TYPE%
ECHO %PORT%
ECHO %URLPORT%

SET LOGGINGPORT=%URLPORT%
IF "%LOGGINGPORT%" == "none" set LOGGINGPORT=4500
If "%PORT%" == "-none" set PORT=
If "%URLPORT%" == "none" set URLPORT=

echo port = %PORT%

ECHO %SERVER%
SET BASEDIR=%JUMPDRIVE%:\Staging-CPS\%DEPLOYMENTDIR%
net use m: /delete

echo ------- map drive ----------
net use m: \\%SERVER%\%DEPLOYMENTDRIVE%$ %PASSWORD% /user:%USER%

echo Server: %SERVER% Type: %TYPE%

if %ERRORLEVEL% NEQ 0 GOTO :error 
echo ------- destination cleanup -----------
if exist m:\Deployment-CPS\%DEPLOYMENTDIR%\%TYPE%01.zip (del /s /q m:\Deployment-CPS\%DEPLOYMENTDIR%\%TYPE%01.zip)
if %ERRORLEVEL% NEQ 0 GOTO :error 
if exist m:\Deployment-CPS\%DEPLOYMENTDIR%\%TYPE%01 (rmdir /s /q m:\Deployment-CPS\%DEPLOYMENTDIR%\%TYPE%01)
if %ERRORLEVEL% NEQ 0 GOTO :error 
if exist m:\Deployment-CPS\%DEPLOYMENTDIR%\%TYPE%%PORT% (rmdir /s /q m:\Deployment-CPS\%DEPLOYMENTDIR%\%TYPE%%PORT%)
if %ERRORLEVEL% NEQ 0 GOTO :error 
if not exist m:\Deployment-CPS\%DEPLOYMENTDIR% (mkdir m:\Deployment-CPS\%DEPLOYMENTDIR%)

echo Error Level is %ERRORLEVEL%
:deploy
echo ------------------------------------
echo Copy NODES....to %SERVER% %TYPE%
echo ------------------------------------
set STEP="Deploy Zips to %SERVER% %TYPE%%PORT%"
for /F %%i in ('dir /b "%BASEDIR%\%TYPE%01.zip"') do (
robocopy /R:3 %BASEDIR% m:\Deployment-CPS\%DEPLOYMENTDIR% %TYPE%01.zip
)
echo error level %ERRORLEVEL% 
if %ERRORLEVEL% GEQ 8 GOTO :error 

echo DEPLOYMENT COMPLETE
net use m: /delete


:UnzipNode
SET STEP="Unzip Nodes %SERVER% %TYPE%%PORT%"
echo Step %STEP% commencing...  --------- 
rem call Autobuild\do_unzip_locrem1.cmd %TYPE%.zip %DEPLOYMENTDRIVE%:\Deployment-CPS\%DEPLOYMENTDIR%\ -qo %SERVER%
call powershell.exe Invoke-Command -ComputerName  %SERVER% -ScriptBlock {Expand-Archive %DEPLOYMENTDRIVE%:\Deployment-CPS\%DEPLOYMENTDIR%\%TYPE%01.zip -DestinationPath %DEPLOYMENTDRIVE%:\Deployment-CPS\%DEPLOYMENTDIR%\%TYPE%%PORT%}
if %ERRORLEVEL% NEQ 0 GOTO :error
call powershell.exe Invoke-Command -ComputerName  %SERVER% -ScriptBlock {Move-Item %DEPLOYMENTDRIVE%:\Deployment-CPS\%DEPLOYMENTDIR%\%TYPE%%PORT%\IMS\API -Destination %DEPLOYMENTDRIVE%:\Deployment-CPS\%DEPLOYMENTDIR%\%TYPE%%PORT%\API}
if %ERRORLEVEL% NEQ 0 GOTO :error
call powershell.exe Invoke-Command -ComputerName  %SERVER% -ScriptBlock {Remove-Item -Path %DEPLOYMENTDRIVE%:\Deployment-CPS\%DEPLOYMENTDIR%\%TYPE%%PORT%\IMS}
if %ERRORLEVEL% NEQ 0 GOTO :error


ReplaceUtil.exe \\%SERVER%\%DEPLOYMENTDRIVE%$\Deployment-CPS\%DEPLOYMENTDIR%\%TYPE%%PORT%\API json "D:\\Logs\\Buffer\\IMS-Buffer-" "%LOGDIR%\\Buffer\\IMS%port%-Buffer-"
if %ERRORLEVEL% NEQ 0 GOTO :error
ReplaceUtil.exe \\%SERVER%\%DEPLOYMENTDRIVE%$\Deployment-CPS\%DEPLOYMENTDIR%\%TYPE%%PORT%\API json "%LOGDIR%\\IMS-logs-.log" "%LOGDIR%\\IMS%port%\\IMS%port%-logs-.log"
if %ERRORLEVEL% NEQ 0 GOTO :error
echo replace dir
psexec -accepteula \\%SERVER% cmd /c (fnr.exe --cl --dir "%DEPLOYMENTDRIVE%:\Deployment-CPS\%DEPLOYMENTDIR%\%TYPE%%PORT%\API" --fileMask "*.json" --excludeFileMask "*.dll, *.exe" --includeSubDirectories --find """LogDirectory"": ""%LOGDIR%""" --replace """LogDirectory"": ""%LOGDIR%\\IMS%port%""")
echo replace log
psexec -accepteula \\%SERVER% cmd /c (fnr.exe --cl --dir "%DEPLOYMENTDRIVE%:\Deployment-CPS\%DEPLOYMENTDIR%\%TYPE%%PORT%\API" --fileMask "*.json" --excludeFileMask "*.dll, *.exe" --includeSubDirectories --find """LogFileName"": ""IMS-logs-.log""" --replace " ""LogFileName"": ""IMS%port%-logs-.log""")
echo error level is %ERRORLEVEL%
echo replace placeholder in the logging path
psexec -accepteula \\%SERVER% cmd /c (fnr.exe --cl --dir "%DEPLOYMENTDRIVE%:\Deployment-CPS\%DEPLOYMENTDIR%\%TYPE%%PORT%\API" --fileMask "appsettings.json" --excludeFileMask "*.dll, *.exe" --includeSubDirectories --find "placeholder" --replace "%SERVER%-%LOGGINGPORT%")

psexec -accepteula \\%SERVER% cmd /c (fnr.exe --cl --dir "%DEPLOYMENTDRIVE%:\Deployment-CPS\%DEPLOYMENTDIR%\%TYPE%%PORT%\API" --fileMask "*.json" --excludeFileMask "*.dll, *.exe" --includeSubDirectories --find """ServiceBasePath"": ""cps-hostname""" --replace """ServiceBasePath"": ""%IMSBASEURL%""")




if "%SERVER%" == "stgcpsext01.int.cbdtp.net" GOTO DOSTAGE
if "%SERVER%" == "prdcpsext01.int.cbdtp.net" GOTO DOPROD

:NOTEXTINT
psexec -accepteula \\%SERVER% cmd /c (fnr.exe --cl --dir "%DEPLOYMENTDRIVE%:\Deployment-CPS\%DEPLOYMENTDIR%\%TYPE%%PORT%\API" --fileMask "*.json" --excludeFileMask "*.dll, *.exe" --includeSubDirectories --find """ImsBaseUrl"": ""cps-hostname""" --replace """ImsBaseUrl"": ""%IMSBASEURL%""")
echo error level is %ERRORLEVEL%
if %ERRORLEVEL% NEQ 0 GOTO :error
GOTO END

:DOSTAGE
psexec -accepteula \\%SERVER% cmd /c (fnr.exe --cl --dir "%DEPLOYMENTDRIVE%:\Deployment-CPS\%DEPLOYMENTDIR%\%TYPE%%PORT%\API" --fileMask "*.json" --excludeFileMask "*.dll, *.exe" --includeSubDirectories --find """ImsBaseUrl"": ""cps-hostname""" --replace """ImsBaseUrl"": ""https://stgextint.cbdtp.net:9003""")
echo error level is %ERRORLEVEL%
if %ERRORLEVEL% NEQ 0 GOTO :error
GOTO END

:DOPROD
psexec -accepteula \\%SERVER% cmd /c (fnr.exe --cl --dir "%DEPLOYMENTDRIVE%:\Deployment-CPS\%DEPLOYMENTDIR%\%TYPE%%PORT%\API" --fileMask "*.json" --excludeFileMask "*.dll, *.exe" --includeSubDirectories --find """ImsBaseUrl"": ""cps-hostname""" --replace """ImsBaseUrl"": ""https://prdextint.cbdtp.net:9003""")
echo error level is %ERRORLEVEL%
if %ERRORLEVEL% NEQ 0 GOTO :error
GOTO END


:error
echo ------- AN ERROR OCCURED DURING THIS STEP: %STEP% %SERVER% %TYPE% ------

exit /b 1

:END
echo Step %STEP% completed...  --------- 
echo -- Unzip Packages of Nodes %SERVER% %TYPE% is complete
exit /b 0




:error
echo ------- AN ERROR OCCURED DURING THIS STEP: %STEP% ------
exit /b 1

:END