<?xml version="1.0" encoding="utf-8" ?>

<project name="MOMS" >
  
    	<script language="C#" prefix="userInput" >
	  <code>  
		<![CDATA[ 
		[Function("confirmTargetEnvironment")]               
		public static string ConfirmTargetEnvironment(string deployEnvironment) 
		{
			string isEnvironment = string.Empty;
	
			while(String.IsNullOrEmpty(isEnvironment))
			{
				Console.WriteLine("Environment set to build for {0}.  Enter Y if this is correct. Else enter N", deployEnvironment);
				//Reading keyword entry
				isEnvironment = Console.ReadLine();  
				
				if(isEnvironment.ToUpper() != "Y" && isEnvironment.ToUpper() != "N")
				{
					Console.WriteLine("Valid Entries are: Y or N");
					isEnvironment = String.Empty;
				}
				
				if(isEnvironment.ToUpper() == "Y")
				{
				 return "true" ;
				}
				else
				{
				 return "false";
				}

				}
					
			 
			 return isEnvironment;
		} 
		
		[Function("getDBpassword")]               
		public static string GetPassword() 
		{
			Console.WriteLine("Please enter the DB password: ");
			
			//This is to hide the password being typed. Changing foreground color to background color
			//ConsoleColor oldForegroundColor = Console.ForegroundColor;                         
			//Console.ForegroundColor = Console.BackgroundColor;                   
			
			//Reading keyword entry
			string password = Console.ReadLine();              
			
			//Changing back foreground color to normal.
			//Console.ForegroundColor = oldForegroundColor;         
			
			//return password
			return password;               
		} 
 
        [Function("checkIfItIsInfinity")]               
		public static string IsInfinity() 
		{
			string isInfinity = string.Empty;
			
			while(String.IsNullOrEmpty(isInfinity))
			{
				Console.WriteLine("Enter Y if this is an Infinity Deployment. Else enter N");
				//Reading keyword entry
				isInfinity = Console.ReadLine();  

				if(isInfinity.ToUpper() != "Y" && isInfinity.ToUpper() != "N")
				{
					Console.WriteLine("Valid Entries are: Y or N");
					isInfinity = String.Empty;
				}
			}
			if(isInfinity.ToUpper() == "Y")
			{
			 return "true" ;
			}
			else
			{
			 return "false";
			}
			
		} 
		
		[Function("checkIfItIsMobile")]               
		public static string IsMobile() 
		{
			string isMobile = string.Empty;
			
			while(String.IsNullOrEmpty(isMobile))
			{
				Console.WriteLine("Enter Y if this includes MOMS Mobile Deployment. Else enter N");
				//Reading keyword entry
				isMobile = Console.ReadLine();  

				if(isMobile.ToUpper() != "Y" && isMobile.ToUpper() != "N")
				{
					Console.WriteLine("Valid Entries are: Y or N");
					isMobile = String.Empty;
				}
			}
			if(isMobile.ToUpper() == "Y")
			{
			 return "true" ;
			}
			else
			{
			 return "false";
			}	
		}
		
		[Function("checkIfItIsTcoreSSO")]               
		public static string IsTcoreSSO() 
		{
			string isTcoreSSO = string.Empty;
			
			while(String.IsNullOrEmpty(isTcoreSSO))
			{
				Console.WriteLine("Enter Y if this includes MOMS TcoreSSO Deployment. Else enter N");
				//Reading keyword entry
				isTcoreSSO = Console.ReadLine();  

				if(isTcoreSSO.ToUpper() != "Y" && isTcoreSSO.ToUpper() != "N")
				{
					Console.WriteLine("Valid Entries are: Y or N");
					isTcoreSSO = String.Empty;
				}
			}
			if(isTcoreSSO.ToUpper() == "Y")
			{
			 return "true" ;
			}
			else
			{
			 return "false";
			}	
		} 
		
		]]>           
	  </code>
	</script>
	  
</project>