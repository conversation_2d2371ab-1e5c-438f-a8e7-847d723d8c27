@ECHO OFF
rem update build scripts from GIT
set /P pull= Do you want to pull the latest from Git (Y/N)=
for /f "usebackq delims=" %%I in (`powershell "\"%pull%\".toUpper()"`) do set "pull=%%~I"
IF "%pull%"=="N" (GOTO :SKIPPULL) 
git.exe pull --progress -v --no-rebase "origin"
if %ERRORLEVEL% NEQ 0 GOTO :error 
:SKIPPULL
Set CURRENTDIR=%CD%
set NOW=%date:~10,4%-%date:~4,2%-%date:~7,2%
set dateddir=%date:~10,4%-%date:~4,2%-%date:~7,2%
set /P project=[Enter Project (PRODUCT/CBDCP)]:=
set /P env=[Enter Environment (DEVSD/DEV/QA/PROD)]:=
set /P build_id=[Enter ex. Tags/Product/DEV/ID;Tags/Project/DEVSD/ID]:=
set scripts-dir=%CD%



@ECHO OFF
rem update build scripts from SVN
rem svn cleanup
rem svn up .
if not exist %CURRENTDIR%\AppLogs (mkdir %CURRENTDIR%\AppLogs)
del /s /q AppLogs\*.*
nuget locals all -clear

echo clean up powershell
call PowerShell -Command "& {Powershell\stop_psexec.ps1}"
echo set node
rem call nvm use 12.16.1
rem call nvm use 14.16.0
call nvm use 18.19.1

IF "%project%"=="" (GOTO :error)
IF "%env%"=="" (GOTO :error)
IF "%build_id%"=="" (GOTO :error)

:COPYENVIRONMENTINCLUDE
if exist ENVIRONMENT.INCLUDE del ENVIRONMENT.INCLUDE
@echo Start  %CURRENTDIR%\Projects\%project%\ENVIRONMENT_%env%.INCLUDE 
copy  %CURRENTDIR%\Projects\%project%\ENVIRONMENT_%env%.INCLUDE  %CURRENTDIR%\ENVIRONMENT.INCLUDE  
if %ERRORLEVEL% NEQ 0 GOTO :error 
if exist MultiServerList_*.txt del MultiServerList_*.txt
if exist RabbitNodes_*.txt del RabbitNodes_*.txt
if exist %CURRENTDIR%\Projects\%project%\MultiServerList_%env%.txt copy  %CURRENTDIR%\Projects\%project%\MultiServerList_%env%.txt %CURRENTDIR%\MultiServerList_%env%.txt
if %ERRORLEVEL% NEQ 0 GOTO :error 
if exist %CURRENTDIR%\Projects\%project%\RabbitNodes_%env%.txt copy  %CURRENTDIR%\Projects\%project%\RabbitNodes_%env%.txt %CURRENTDIR%\RabbitNodes_%env%.txt
if %ERRORLEVEL% NEQ 0 GOTO :error 
@echo Copy environment include completed... 


:setdateddirectory
rem let's set a dated directory variable so that we can build and deliver the day before, but then deploy day after.
call fnr.exe --cl --dir "%CURRENTDIR%" --fileMask "ENVIRONMENT.include" --find "DATEDDIR" --replace "%dateddir%"
@echo Set dated directory to %dateddir% complete.

:setreleaseversion
rem let's set the relese version.
set "param=%build_id%"
for %%a in (%param:/= %) do set releaseVer=%%a


call fnr.exe --cl --dir "%CURRENTDIR%" --fileMask "ENVIRONMENT.include" --find "RELEASEVERSION" --replace "%releaseVer%"
@echo Set  release version to %releaseVer% complete.

:set assembly version
set "assemblyVer=%releaseVer%"
set "_result=%assemblyVer:~0,3%"
if "%_result%"=="202" set "assemblyVer=1.0.0-%releaseVer%"
if "%_result%"=="mai" set "assemblyVer=1.0.0-%releaseVer%"
echo %assemblyVer%


call fnr.exe --cl --dir "%CURRENTDIR%" --fileMask "ENVIRONMENT.include" --find "ASSEMBLYVERSION" --replace "%assemblyVer%"
@echo Set assembly version to %assemblyVer% complete.

:set-build-id
rem let's set the build id so that we can see its output during the build
call fnr.exe --cl --dir "%CURRENTDIR%" --fileMask "ENVIRONMENT.include" --find "BUILDID" --replace "%build_id%"
@echo Set Build ID to %build_id% complete.

:CLEAN
echo change attribs
if not exist \AppBuild-CPS (mkdir \AppBuild-CPS)
cd \AppBuild-CPS\
call attrib -r /s *
echo delete files
del /s /q *
rmdir /s /q Source
rmdir /s /q DIST
if %ERRORLEVEL% NEQ 0 GOTO :errordelete
cd %CURRENTDIR%

:FETCHANDBUILD

NAnt.exe -buildfile:Product_build.build -logger:NAnt.Core.MailLogger -logfile:AppLogs\1-Compile.log init checkout build  
rem NAnt.exe -buildfile:Product_build.build -logger:NAnt.Core.MailLogger -logfile:AppLogs\step1.log clean checkout-src build checkout-src-devops

GOTO END


:error
@echo -----------------------------------------------------------
@echo -----------------------------------------------------------
@echo -e-:%0:**** You Failed to Provide the Proper Parameters!!! **** 	
@echo -----------------------------------------------------------
@echo  MAY THE FORCE BE WITH YOU.								
GOTO END

:errordelete
@echo -----------------------------------------------------------
@echo -----------------------------------------------------------
@echo -e-:%0:**** File deletion FAILED **** 	
@echo -----------------------------------------------------------
@echo  Check to make sure you do not have any files or directories open from D:\AppBuild-CPS directory								
GOTO END

:END

pause 


