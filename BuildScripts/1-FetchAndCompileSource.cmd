@ECHO OFF
rem update build scripts from GIT
set /P pull= Do you want to pull the latest from Git (Y/N)=
for /f "usebackq delims=" %%I in (`powershell "\"%pull%\".toUpper()"`) do set "pull=%%~I"
IF "%pull%"=="N" (GOTO :SKIPPULL) 
git.exe pull --progress -v --no-rebase "origin"
if %ERRORLEVEL% NEQ 0 GOTO :error 
:SKIPPULL
Set CURRENTDIR=%CD%
set NOW=%date:~10,4%-%date:~4,2%-%date:~7,2%
set dateddir=%date:~10,4%-%date:~4,2%-%date:~7,2%
set year=%date:~10,4%
set /P project=[Enter Project (PRODUCT/NCTA/VTA2)]:=
set /P env=[Enter Environment (DEV/QA/PROD, for Product: DEV01, DEV02, QA01 etc.)]:=
set /P build_id=[Enter Branches/DEV;Tags/Product/ID;Tags/Project/ID]:=
rem set /P release=[Enter the release version i.e. TXP-REL0001, or for VTA2 ex. BP0001]:=
set scripts-dir=%CD%

@ECHO OFF

rem svn cleanup
rem svn up .
if not exist %CURRENTDIR%\AppLogs (mkdir %CURRENTDIR%\AppLogs)
del /s /q AppLogs\*.*


echo clean up powershell
call PowerShell -Command "& {Powershell\stop_psexec.ps1}"
del /s /q AppLogs\*.*

IF "%project%"=="" (GOTO :error)
IF "%env%"=="" (GOTO :error)
IF "%build_id%"=="" (GOTO :error)

:COPYENVIRONMENTINCLUDE
if exist ENVIRONMENT.INCLUDE del ENVIRONMENT.INCLUDE
@echo Start  %CURRENTDIR%\Projects\%project%\ENVIRONMENT_%env%.INCLUDE 
copy  %CURRENTDIR%\Projects\%project%\ENVIRONMENT_%env%.INCLUDE  %CURRENTDIR%\ENVIRONMENT.INCLUDE  
if %ERRORLEVEL% NEQ 0 GOTO :error 
@echo copy in server list for deployment
if exist fppclientlist_*.txt del fppclientlist_*.txt 
if exist MultiServerList_*.txt del MultiServerList_*.txt
if exist %CURRENTDIR%\Projects\%project%\fppclientlist_%env%.txt copy  %CURRENTDIR%\Projects\%project%\fppclientlist_%env%.txt %CURRENTDIR%\fppclientlist_%env%.txt
if exist %CURRENTDIR%\Projects\%project%\MultiServerList_%env%.txt copy  %CURRENTDIR%\Projects\%project%\MultiServerList_%env%.txt %CURRENTDIR%\MultiServerList_%env%.txt
@echo Copy environment files completed... 

@echo try to set node via find
set angular=false
echo anguler equals %angular%
rem call find /C "Angular9WebSiteFeature"" value=""true" ENVIRONMENT.include > output.txt
call findstr /C:"Angular9WebSiteFeature\" value=\"true" ENVIRONMENT.include > output.txt
FOR /F %%G IN (output.txt) DO set "angular=true"
echo  anguler equals %angular%

rem set Release Version, check version for tag to see if we need to use newest Angular
echo set node
set "param=%build_id%"
for %%a in (%param:/= %) do set releaseVer=%%a
echo release ver = %releaseVer%
for /f "tokens=1 delims=." %%a in ("%releaseVer%") do (
  set checkversion=%%a
  )
echo checkversion = %checkversion%
if "%angular%"=="false" ( nvm use 10.19.0 )
if "%angular%"=="true" if %checkversion% gtr 22 ( nvm use 16.12.0 )
if "%angular%"=="true" if not %checkversion% gtr 22 ( nvm use 12.16.1 )
echo node has been set
nvm list

:setdateddirectory
rem let's set a dated directory variable so that we can build and deliver the day before, but then deploy day after.
call fnr.exe --cl --dir "%CURRENTDIR%" --fileMask "ENVIRONMENT.include" --find "DATEDDIR" --replace "%dateddir%"
@echo Set dated directory to %dateddir% complete.


:setreleaseversion
rem let's set the relese version.
call fnr.exe --cl --dir "%CURRENTDIR%" --fileMask "ENVIRONMENT.include" --find "RELEASEVERSION" --replace "%releaseVer%"
@echo Set  release version to %releaseVer% complete.

:set-build-id
rem let's set the build id so that we can see its output during the build
call fnr.exe --cl --dir "%CURRENTDIR%" --fileMask "ENVIRONMENT.include" --find "BUILDID" --replace "%build_id%"
@echo Set Build ID to %build_id% complete.


:CLEAN
echo change attribs
if not exist \AppBuild-MIR (mkdir \AppBuild-MIR)
rem if not exist \dummy (mkdir \dummy)
rem robocopy \dummy \AppBuild-MIR /MIR
cd \AppBuild-MIR\
call attrib -r /s *
echo delete files
del /s /q *
cd %CURRENTDIR%

:FETCHANDBUILD


rem NAnt.exe -buildfile:ImageReview_build.build -logger:NAnt.Core.MailLogger -logfile:AppLogs\build.log clean
NAnt.exe -buildfile:ImageReview_build.build -logger:NAnt.Core.MailLogger -logfile:AppLogs\1-Compile.log -D:copyright.year=%year% init checkout build-ImageReview 
GOTO END


:error
@echo -----------------------------------------------------------
@echo -----------------------------------------------------------
@echo -e-:%0:**** You Failed to Provide the Proper Parameters!!! **** 	
@echo -----------------------------------------------------------
@echo  MAY THE FORCE BE WITH YOU.								
GOTO END

:END

pause 


