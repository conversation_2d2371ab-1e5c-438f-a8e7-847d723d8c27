@ECHO OFF
rem update build scripts from GIT
set /P pull= Do you want to pull the latest from Git (Y/N)=
for /f "usebackq delims=" %%I in (`powershell "\"%pull%\".toUpper()"`) do set "pull=%%~I"
IF "%pull%"=="N" (GOTO :SKIPPULL) 
git.exe pull --progress -v --no-rebase "origin"
if %ERRORLEVEL% NEQ 0 GOTO :error 
:SKIPPULL
set NOW=%date:~10,4%-%date:~4,2%-%date:~7,2%
Set CURRENTDIR=%CD%
set dateddir=%date:~10,4%-%date:~4,2%-%date:~7,2%
set /P project=[Enter Project (PRODUCT/NCTA/VTA2)]:=
set /P env=[Enter Environment (DEV/QA/PROD, for Product: DEV01, DEV02, QA01 etc.)]:=
set /P build_id=[Enter Branches/DEV;Tags/Product/ID;Tags/Project/ID]:=


@ECHO OFF

rem svn cleanup
rem svn up .
if not exist %CURRENTDIR%\AppLogs (mkdir %CURRENTDIR%\AppLogs)
del /s /q AppLogs\*.*
echo set node
call nvm use 10.19.0

echo clean up powershell
call PowerShell -Command "& {Powershell\stop_psexec.ps1}"
del /s /q AppLogs\*.*

IF "%project%"=="" (GOTO :error)
IF "%env%"=="" (GOTO :error)
IF "%dateddir%"=="" (GOTO :error)

echo Building %build_id% for %project% %env%
echo Building %build_id% for %project% %env% 

:COPYENVIRONMENTINCLUDE
if exist ENVIRONMENT.INCLUDE del ENVIRONMENT.INCLUDE
@echo Start  %CURRENTDIR%\Projects\%project%\ENVIRONMENT_%env%.INCLUDE 
copy  %CURRENTDIR%\Projects\%project%\ENVIRONMENT_%env%.INCLUDE  %CURRENTDIR%\ENVIRONMENT.INCLUDE  
if %ERRORLEVEL% NEQ 0 GOTO :error 
@echo Copy environment include completed... 

:setdateddirectory
rem let's set a dated directory variable so that we can build and deliver the day before, but then deploy day after.
call fnr.exe --cl --dir "%CURRENTDIR%" --fileMask "ENVIRONMENT.include" --find "DATEDDIR" --replace "%dateddir%"
@echo Set dated directory to %dateddir% complete.


:setreleaseversion
rem let's set the relese version.
set "param=%build_id%"
for %%a in (%param:/= %) do set releaseVer=%%a
call fnr.exe --cl --dir "%CURRENTDIR%" --fileMask "ENVIRONMENT.include" --find "RELEASEVERSION" --replace "%releaseVer%"
@echo Set  release version to %releaseVer% complete.

:set-build-id
rem let's set the build id so that we can see its output during the build
call fnr.exe --cl --dir "%CURRENTDIR%" --fileMask "ENVIRONMENT.include" --find "Branches/DEV" --replace "%build_id%"
@echo Set Build ID to %build_id% complete.


:CLEAN
echo change attribs
if not exist \AppBuild-MOMS (mkdir \AppBuild-MOMS)
cd \AppBuild-MOMS\
call attrib -r /s *
echo delete files
rem del /s /q *
cd ..
rmdir /Q /S AppBuild-MOMS
if not exist \AppBuild-MOMS (mkdir \AppBuild-MOMS)
cacls \AppBuild-MOMS /t /e /g Users:f
cd %CURRENTDIR%


:DEPLOY 
NAnt.exe -buildfile:MOMS_build.build -logger:NAnt.Core.MailLogger -logfile:AppLogs\1-Compile.log init checkout build-MOMS
rem NAnt.exe -buildfile:MOMS_build.build -logger:NAnt.Core.MailLogger -logfile:AppLogs\build.log clean
GOTO END



:error
@echo -----------------------------------------------------------
@echo -----------------------------------------------------------
@echo -e-:%0:**** You Failed to Provide the Proper Parameters!!! **** 	
@echo -----------------------------------------------------------
@echo  MAY THE FORCE BE WITH YOU.								
GOTO END

:END
echo Build of %build_id% for %project% %env% step 1 done
pause 


