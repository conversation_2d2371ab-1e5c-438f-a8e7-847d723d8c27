@echo off
set NOW=%date:~10,4%-%date:~4,2%-%date:~7,2%
set scripts-dir=%CD%

rem set /P clusterenv=Enter 1 for Primary (APP01) or 2 for secondary (APP02) deployment:=
rem IF "%clusterenv%"=="" (GOTO :error)
rem IF not %clusterenv%==1 (GOTO :checkfor2)

rem :letsgo
rem if %clusterenv%==1 ( NAnt.exe -buildfile:TransPortal_build.build -logger:NAnt.Core.MailLogger -logfile:AppLogs\install.log pre-install-sleep 
rem )
rem if %clusterenv%==2 ( NAnt.exe -buildfile:TransPortal_build.build -logger:NAnt.Core.MailLogger -logfile:AppLogs\install.log pre-install-sleep-02
rem )
NAnt.exe -buildfile:Product_build.build -logger:NAnt.Core.MailLogger -logfile:AppLogs\6-sleep.log sleep-cluster


GOTO END
:checkfor2
IF not %clusterenv%==2 (GOTO :error)
GOTO :letsgo

:error
@echo -----------------------------------------------------------
@echo -----------------------------------------------------------
@echo -e-:%0:**** You Failed to Provide the Proper Parameters!!! **** 	
@echo -----------------------------------------------------------
@echo  Enter 1 for Primary (APP01) or 2 for secondary (APP02)							
GOTO END

:END

pause 


