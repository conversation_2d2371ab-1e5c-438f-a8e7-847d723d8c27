﻿sleep-cluster<?xml version="1.0" encoding="UTF-8"?>
<project name="ImageReview" default="build" basedir=".">

  <include buildfile="UserInput.include"/>
  <include buildfile="BUILD.include"/>
  <include buildfile="ENVIRONMENT.include"/>
  <property name="env" value="${deployment.environment}"/>

	<!-- Request User to Confirm Environment as defined in ENVIRONMENT.include" -->

	<!-- Echo Environment Target -->
	<echo message="****** Deployment Target is ${env} *******" />

		<!-- Create all default directories -->
	<target name="init" description="create build folders">
	<!-- remove old folders -->
	<call target="clean" />
	<mkdir dir="${imagereview.build.dir}" />
	</target>

	<!-- Remove all debris from any prior builds -->
	<target name="clean" description="Remove temporary folders">
		<property name="MailLogger.failure.subject" value="ImageReview ${deployment.environment} ${deployment.type} - SVN cleanup step failed." />

		<delete dir="${base.dir}/BuildScripts" failonerror="false" />
		<delete dir="${imagereview.mir.release.directory}" failonerror="false" />
		<delete dir="${imagereview.mir.mrt.release.directory}" failonerror="false" />
		<delete dir="${imagereview.trans.release.directory}" failonerror="false" />
		<delete dir="${imagereview.hocp.release.directory}" failonerror="false" />
		<delete dir="${imagereview.irr.release.directory}" failonerror="false" />
		<delete dir="${imagereview.mir.web.release.directory}" failonerror="false" />
		<delete dir="${base.dir}\WEB01" failonerror="false" />
		<delete dir="${base.dir}\WEB02" failonerror="false" />
		<delete dir="${base.dir}\APP01" failonerror="false" />
		<delete dir="${base.dir}\APP02" failonerror="false" />
		<delete dir="${base.dir}\RPT01" failonerror="false" />
		<delete dir="${base.dist.dir}\WEB01" failonerror="false" />
		<delete dir="${base.dist.dir}\WEB02" failonerror="false" />
		<delete dir="${base.dist.dir}\APP01" failonerror="false" />
		<delete dir="${base.dist.dir}\APP02" failonerror="false" />
		<delete dir="${base.dist.dir}\RPT01" failonerror="false" />
		<delete dir="${imagereview.build.dir}" failonerror="false" />
		<delete dir="${base.dist.dir}\ImgReviewSendTransTask" failonerror="false" />
		<delete dir="${base.dist.dir}\ImgReviewSendTransTaskRetries" failonerror="false" />
		<delete dir="${base.dist.dir}\IRRSendResultsTaskRetries" failonerror="false" />
		<delete dir="${base.dist.dir}\GenerateROITask" failonerror="false" />
		<delete dir="${base.dist.dir}\GenerateROITaskRetries" failonerror="false" />
		<delete dir="${base.dist.dir}\AuditReviewResultsTask" failonerror="false" />
		<delete dir="${base.dist.dir}\ManualResultsTask" failonerror="false" />
		<delete dir="${base.dist.dir}\HighOcrConfProcessor" failonerror="false" />
		<delete dir="${base.dist.dir}\HOCSendResultsProcessor" failonerror="false" />
		<delete dir="${base.dist.dir}\PreHighOcrConfProcessor" failonerror="false" />
		<delete dir="${base.dist.dir}\PreHOCSendResultsProcessor" failonerror="false" />
		<delete dir="${base.dist.dir}\FPMSendResultsTask" failonerror="false" />
		<delete dir="${base.dist.dir}\FPPMatchPoolTask" failonerror="false" />
		<delete dir="${base.dist.dir}\ActiveDirectoryPollingTask" failonerror="false" />
		<delete dir="${base.dist.dir}\IRRSendResultsTask" failonerror="false" />
		<delete dir="${base.dist.dir}\VehicleDetectionTask" failonerror="false" />
		<delete dir="${base.dist.dir}\ImageClientServiceTask_RetrieveTransactions" failonerror="false" />
		<delete dir="${base.dist.dir}\ImageClientServiceTask_SendResponse" failonerror="false" />
		<delete dir="${base.dist.dir}\MIREmailNotificationTask" failonerror="false" />
		<delete dir="${base.dist.dir}\VOTTQueueTask" failonerror="false" />
		<delete dir="${base.dist.dir}\HumanReadabilitySelectionTask" failonerror="false" />
		<delete dir="${base.dist.dir}\HOCP01" failonerror="false" />
		<delete dir="${base.dist.dir}\IRR01" failonerror="false" />
		<delete dir="${base.dist.dir}\TRANS01" failonerror="false" />
		<delete dir="${base.dist.dir}\HOCP02" failonerror="false" />
		<delete dir="${base.dist.dir}\IRR02" failonerror="false" />
		<delete dir="${base.dist.dir}\TRANS02" failonerror="false" />
		<delete dir="${base.dist.dir}\PREHOCP01" failonerror="false" />
		<delete dir="${base.dist.dir}\PREHOCP02" failonerror="false" />
		<delete dir="${base.dir}\FP01" failonerror="false" />
		<delete dir="${base.dir}\FP02" failonerror="false" />
		<delete dir="${base.dir}\TRANS01" failonerror="false" />
		<delete dir="${base.dir}\IRR01" failonerror="false" />
		<delete dir="${base.dir}\HOCP01" failonerror="false" />
		<delete dir="${base.dir}\PREHOCP01" failonerror="false" />
		<delete dir="${base.dir}\PREHOCP02" failonerror="false" />
		<delete dir="${base.dir}\TRANS02" failonerror="false" />
		<delete dir="${base.dir}\IRR02" failonerror="false" />
		<delete dir="${base.dir}\HOCP02" failonerror="false" />
		<delete dir="${base.dir}\ImgReviewSendTransTask" failonerror="false" />
		<delete dir="${base.dir}\IFXTxnMultiServer" failonerror="false" />
		<delete dir="${base.dir}\FPClient" failonerror="false" />
		<delete dir="${base.dir}\HR01" failonerror="false" />
		<delete dir="${base.dist.dir}\FP01" failonerror="false" />
		<delete dir="${base.dist.dir}\FP02" failonerror="false" />
		<delete dir="${base.dist.dir}\TRANS01" failonerror="false" />
		<delete dir="${base.dist.dir}\TRANS02" failonerror="false" />
		<delete dir="${base.dist.dir}\ImgReviewSendTransTask" failonerror="false" />
		<delete dir="${base.dist.dir}\IFXTxnMultiServer" failonerror="false" />
		<delete dir="${base.dist.dir}\FPClient" failonerror="false" />
		<delete dir="${base.dist.dir}\HR01" failonerror="false" />
		<delete dir="${imagereview.fpc.dist.dir}" failonerror="false" />
		<delete>
			<fileset>
			<include name="${base.dist.dir}/*.*"/>
			<include name="${base.dir}/*.*"/>
			</fileset>
		</delete>
		<delete dir="${base.dir}/Source" failonerror="false" />
	</target>


	<!-- Checkout source from correct repo -->
	<target name="checkout" description="Get current version of source">
	<if test="${devops=='true'}">
		<call target="checkout-src-devops" failonerror="true"/>
	</if>
	<if test="${devops=='false'}">
		<call target="checkout-src" failonerror="true"/>
	</if>
	</target>
	<!-- Get the source files from DevOps -->
	<target name="checkout-src-devops" description="Get current version of source">

		<exec program="devopsfetch.bat" failonerror="true">
			<arg value="${devops.dir}" />
			<arg value="${base.dir}" />
			<arg value="${build_id}" />
			<arg value="${devops.repo}" />
			<arg value="${devops.common.repo}" />
		</exec>
			<if test="${FingerprintFeature=='true'}">

				<copy todir="${imagereview.build.dir}\FingerPrintProcessor\ThirdParty" failonerror="true" overwrite="true">
					<fileset basedir="${imagereview.build.dir}\FingerPrintProcessor\ThirdParty\IntradaLicenses\${env}"/>
				</copy>
			</if>

	</target>

	<!-- Get the source files from SVN -->
	<target name="checkout-src" description="Get current version of source">
		<property name="MailLogger.failure.subject" value="ImageReview  ${deployment.environment} ${deployment.type} - SVN checkout step failed." />

			<exec program="svn" failonerror="true">
			<arg value="export" />
			<arg value="--username" />
			<arg value="${svn.user}" />
			<arg value="--password" />
			<arg value="${svn.password}" />
			<arg value="-r" />
			<arg value="HEAD" />
			<arg value="${imagereview.svn.url-dev-branch}" />
			<arg value="${imagereview.build.dir}" />
			<arg value="--non-interactive" />
			<arg value="--force" />
			<arg value="--quiet" />
			</exec>

		<if test="${ADPollingTask=='true'}">
		<exec program="svn" failonerror="true">
			<arg value="export" />
			<arg value="--username" />
			<arg value="${svn.user}" />
			<arg value="--password" />
			<arg value="${svn.password}" />
			<arg value="-r" />
			<arg value="HEAD" />
			<arg value="${activedirectory.polling.svn}" />
			<arg value="${activedirectory.build.dir}" />
			<arg value="--non-interactive" />
			<arg value="--force" />
			<arg value="--quiet" />
			</exec>
		</if>
		<if test="${FingerprintFeature=='true'}">

				<copy todir="${imagereview.build.dir}\FingerPrintProcessor\ThirdParty" failonerror="true" overwrite="true">
					<fileset basedir="${imagereview.build.dir}\FingerPrintProcessor\ThirdParty\IntradaLicenses\${env}"/>
				</copy>
			</if>

	</target>

	<!-- Build the solutions -->
	<target name="build-ImageReview" description="Build solution">
		<property name="MailLogger.success.subject" value="ImageReview ${deployment.environment} ${deployment.type} - Step 1 Build Successfully Completed." />
		<property name="MailLogger.failure.subject" value="ImageReview ${deployment.environment} ${deployment.type} - Build step failed" />
			<copy todir="${imagereview.build.dir}\IntegrityImageReview\Reports\ImageReviewReports" overwrite="true" failonerror="true">
					<fileset basedir="${imagereview.build.dir}\IntegrityImageReview\Reports\ImageReviewReports\${env}" />
			</copy>
			<copy todir="${imagereview.build.dir}\IntegrityImageReview\WebLayer\WebSite\ClientApp\src\assets\en_US\help" overwrite="true" failonerror="false">
					<fileset basedir="${imagereview.build.dir}\IntegrityImageReview\WebLayer\WebSite\ClientApp\src\assets\en_US\help\${deployment.environment}" />
			</copy>
			<if test="${HumanReadabilityFeature=='true'}">
			<copy todir="${imagereview.build.dir}\IntegrityImageReview\Reports\ImageReviewReports" overwrite="true" failonerror="true">
					<fileset basedir="${imagereview.build.dir}\IntegrityImageReview\Reports\ImageReviewReports\HR" />
			</copy>
			</if>
			<if test="${Angular9WebSiteFeature=='true'}">

			<exec program="fnr-ts.bat" failonerror="true" >
					<arg value="${imagereview.build.dir}\IntegrityImageReview\WebLayer\WebSite\ClientApp\src\environments" />
					<arg value="${locals.project}" />
					<arg value="${useDvasTransParamNameValue}" />
					<arg value="${deployment.environment}" />
			</exec>
			<exec program="ReplaceUtil.exe" failonerror="true">
				<arg value="${imagereview.build.dir}\IntegrityImageReview\WebLayer\WebSite\ClientApp\src\app\shared\constant" />
				<arg value="ts"/>
				<arg value="11/08/2019" />
				<arg value="${dated.deployment.dir}" />
				<arg value="ALL"/>
			</exec>
			<exec program="ReplaceUtil.exe" failonerror="true">
				<arg value="${imagereview.build.dir}\IntegrityImageReview\WebLayer\WebSite\ClientApp\src\app\shared\constant" />
				<arg value="ts"/>
				<arg value="1.0.0" />
				<arg value="${release.version}" />
				<arg value="common.constant.ts"/>
				<arg value="ALL"/>
			</exec>
			<exec program="ReplaceUtil.exe" failonerror="true">
				<arg value="${imagereview.build.dir}\IntegrityImageReview\WebLayer\WebSite\ClientApp\src\app\shared\constant" />
				<arg value="ts"/>
				<arg value="2019 TransCore LP All rights Reserved" />
				<arg value="${copyright.year} TransCore LP All rights Reserved" />
				<arg value="common.constant.ts"/>
				<arg value="ALL"/>
			</exec>
				<if test="${SSLFeature=='true'}">
				<exec program="ReplaceUtil.exe" failonerror="true">
				<arg value="${imagereview.build.dir}\IntegrityImageReview\WebLayer\WebSite\ClientApp\src\environments" />
				<arg value="ts"/>
				<arg value="http://localhost:6555" />
				<arg value="https://${imagereview.web.alias}:6443" />
				<arg value="ALL"/>
				</exec>
				<exec program="ReplaceUtil.exe" failonerror="true">
				<arg value="${imagereview.build.dir}\IntegrityImageReview\WebLayer\WebSite\ClientApp\src\environments" />
				<arg value="ts"/>
				<arg value="http://txp-api:7006/api" />
				<arg value="https://${transportal.web.alias}:4436/api" />
				<arg value="ALL"/>
				</exec>
				<exec program="ReplaceUtil.exe" failonerror="true">
				<arg value="${imagereview.build.dir}\IntegrityImageReview\WebLayer\WebSite\ClientApp\src\environments" />
				<arg value="ts"/>
				<arg value="http://txp-servername" />
				<arg value="${web.Transportal.website}" />
				<arg value="ALL"/>
				</exec>
				<exec program="ReplaceUtil.exe" failonerror="true">
				<arg value="${imagereview.build.dir}\IntegrityImageReview\WebLayer\WebSite\ClientApp\src\environments" />
				<arg value="ts"/>
				<arg value="http://dvas-servername" />
				<arg value="https://${dvas.web.alias}" />
				<arg value="ALL"/>
				</exec>
				<exec program="ReplaceUtil.exe" failonerror="true">
				<arg value="${imagereview.build.dir}\IntegrityImageReview\WebLayer\WebSite\ClientApp\src\environments" />
				<arg value="ts"/>
				<arg value="http://MIR-servername" />
				<arg value="https://${imagereview.web.alias}" />
				<arg value="ALL"/>
				</exec>

				</if>
				<if test="${SSLFeature=='false'}">
				<exec program="ReplaceUtil.exe" failonerror="true">
				<arg value="${imagereview.build.dir}\IntegrityImageReview\WebLayer\WebSite\ClientApp\src\environments" />
				<arg value="ts"/>
				<arg value="http://localhost:6555" />
				<arg value="http://${imagereview.web.alias}:6555" />
				<arg value="ALL"/>
				</exec>
				<exec program="ReplaceUtil.exe" failonerror="true">
				<arg value="${imagereview.build.dir}\IntegrityImageReview\WebLayer\WebSite\ClientApp\src\environments" />
				<arg value="ts"/>
				<arg value="http://txp-api:7006/api" />
				<arg value="http://${transportal.web.alias}:7006/api" />
				<arg value="ALL"/>
				</exec>
				<exec program="ReplaceUtil.exe" failonerror="true">
				<arg value="${imagereview.build.dir}\IntegrityImageReview\WebLayer\WebSite\ClientApp\src\environments" />
				<arg value="ts"/>
				<arg value="http://txp-servername" />
				<arg value="http://${transportal.web.alias}" />
				<arg value="ALL"/>
				</exec>
				<exec program="ReplaceUtil.exe" failonerror="true">
				<arg value="${imagereview.build.dir}\IntegrityImageReview\WebLayer\WebSite\ClientApp\src\environments" />
				<arg value="ts"/>
				<arg value="http://dvas-servername" />
				<arg value="http://${dvas.web.alias}" />
				<arg value="ALL"/>
				</exec>
				<exec program="ReplaceUtil.exe" failonerror="true">
				<arg value="${imagereview.build.dir}\IntegrityImageReview\WebLayer\WebSite\ClientApp\src\environments" />
				<arg value="ts"/>
				<arg value="http://MIR-servername" />
				<arg value="http://${imagereview.web.alias}" />
				<arg value="ALL"/>
				</exec>

				</if>
			</if>

		<if test="${QFreePluginFeature=='true'}">
		<exec program="ReplaceUtil.exe" failonerror="true">
				<arg value="${imagereview.build.dir}/IntegrityImageReview/Plugins/Qfree/Imageclient/Connected Services/ManualReviewInterface" />
				<arg value="wsdl"/>
				<arg value="http://localhost/ManualReview.asmx" />
				<arg value="${imageclientservice.qfree.endpoint}" />
				<arg value="ALL"/>
		</exec>
		<exec program="ReplaceUtil.exe" failonerror="true">
				<arg value="${imagereview.build.dir}/IntegrityImageReview/Plugins/Qfree/Imageclient/Connected Services/ManualReviewInterface" />
				<arg value="svcmap"/>
				<arg value="http://localhost/ManualReview.asmx" />
				<arg value="${imageclientservice.qfree.endpoint}" />
				<arg value="ALL"/>
		</exec>
		<exec program="ReplaceUtil.exe" failonerror="true">
				<arg value="${imagereview.build.dir}/IntegrityImageReview/Plugins/Qfree/Imageclient/Connected Services/ManualReviewInterface" />
				<arg value="svcinfo"/>
				<arg value="http://localhost/ManualReview.asmx" />
				<arg value="${imageclientservice.qfree.endpoint}" />
				<arg value="ALL"/>
		</exec>
		<delete>
			<fileset>
			<include name="${imagereview.build.dir}/IntegrityImageReview/Plugins/Qfree/Imageclient/Connected Services/ManualReviewInterface/Reference.cs"/>
			</fileset>
		</delete>
		<if test="${deployment.type=='PROD'}">
		<copy todir="${imagereview.build.dir}/IntegrityImageReview/Plugins/Qfree/Imageclient/Connected Services/ManualReviewInterface" overwrite="true">
			<fileset>
				<include name="${imagereview.build.dir}/IntegrityImageReview/Plugins/Qfree/Imageclient/Connected Services/ManualReviewInterface/Prod/Reference.cs" />
			</fileset>
		</copy>
		</if >
		<ifnot test="${deployment.type=='PROD'}">
		<copy todir="${imagereview.build.dir}/IntegrityImageReview/Plugins/Qfree/Imageclient/Connected Services/ManualReviewInterface" overwrite="true">
			<fileset>
				<include name="${imagereview.build.dir}/IntegrityImageReview/Plugins/Qfree/Imageclient/Connected Services/ManualReviewInterface/QADev/Reference.cs" />
			</fileset>
		</copy>
		</ifnot>
		</if>

		<call target="build-dotnet-core" failonerror="true" />

		<if test="${FingerprintFeature=='true'}">
			<call target="build-FPM-solution" failonerror="true" />
			<call target="build-FPP-solution" failonerror="true" />
		</if>
		<if test="${HOCPFeature=='true' or PreHOCPFeature=='true'}">
			<call target="build-HOCP-solution" failonerror="true" />
		</if>
		<if test="${IRRFeature=='true'}">
		<call target="build-IRR-solution" failonerror="true" />
		</if>
		<call target="build-MIR-MRT-solution" failonerror="true" />
		<if test="${TransAPIandMIRIntServiceFeature=='true'}">
			<call target="build-TRANS-solution" failonerror="true" />
		</if>
		<call target="build-MIR-solution" failonerror="true" />
		<!--<if test="${QFreePluginFeature=='true'}">
			<call target="build-QFreePlugin-solution" failonerror="true" />
		</if>-->
	</target>

	<target name="build-dotnet-core" description="Build dotenet Core apps">
		<property name="MailLogger.failure.subject" value="ImageReview ${deployment.environment} ${deployment.type}  - dotnet core build failed" />
		<exec program="compiledotnet.bat" failonerror="true">
		<arg value="${imagereview.build.dir}" />
		<arg value="${base.dist.dir}" />
		<arg value="${VehicleDetectionTaskFeature}" />
		<arg value="${ImgReviewSendTransTaskFeature}" />
		<arg value="${HumanReadabilityFeature}" />
		<arg value="${GenerateROITaskFeature}" />
		</exec>
	</target>

	<!-- Build the ImageReview Solution -->
	<target name="build-FPM-solution" description="Build ImageReview Service solution">
		<property name="MailLogger.failure.subject" value="ImageReview FPM - Solution build step failed" />
		<exec program="nuget" failonerror="true">
			<arg value="restore" />
			<arg value="${imagereview.fpm.solution.file}" />
		</exec>
		<exec program="C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\MSBuild\Current\Bin\msbuild.exe" failonerror="true">
			<arg value="${imagereview.fpm.solution.file}" />
			<arg value="/p:Configuration=Release" />
		</exec>
	</target>

		<!-- Build the ImageReview Solution -->
	<target name="build-FPP-solution" description="Build ImageReview Service solution">
		<property name="MailLogger.failure.subject" value="ImageReview FPP - Solution build step failed" />
		<exec program="nuget" failonerror="true">
			<arg value="restore" />
			<arg value="${imagereview.fpp.solution.file}" />
		</exec>
		<exec program="C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\MSBuild\Current\Bin\msbuild.exe" failonerror="true">
			<arg value="${imagereview.fpp.solution.file}" />
			<arg value="/p:Configuration=Release" />
		</exec>
	</target>

	<target name="build-HOCP-solution" description="Build ImageReview HOCP  solution">
		<property name="MailLogger.failure.subject" value="ImageReview HOCP - Solution build step failed" />
		<exec program="nuget" failonerror="true">
			<arg value="restore" />
			<arg value="${imagereview.hocp.solution.file}" />
		</exec>
		<exec program="C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\MSBuild\Current\Bin\msbuild.exe" failonerror="true">
			<arg value="${imagereview.hocp.solution.file}" />
			<arg value="/p:Configuration=Release" />
		</exec>
	</target>

	<target name="build-IRR-solution" description="Build ImageReview IRR solution">
		<property name="MailLogger.failure.subject" value="ImageReview IRR - Solution build step failed" />
		<exec program="nuget" failonerror="true">
			<arg value="restore" />
			<arg value="${imagereview.irr.solution.file}" />
		</exec>
		<exec program="C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\MSBuild\Current\Bin\msbuild.exe" failonerror="true">
			<arg value="${imagereview.irr.solution.file}" />
			<arg value="/p:Configuration=Release" />
		</exec>
	</target>

	<target name="build-MIR-MRT-solution" description="Build ImageReview MIR solution">
		<property name="MailLogger.failure.subject" value="ImageReview MIR - Solution build step failed" />
		<exec program="nuget" failonerror="true">
			<arg value="restore" />
			<arg value="${imagereview.mir.mrt.solution.file}" />
		</exec>
		<exec program="C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\MSBuild\Current\Bin\msbuild.exe" failonerror="true">
			<arg value="${imagereview.mir.mrt.solution.file}" />
			<arg value="/p:Configuration=Release" />
		</exec>
	</target>

	<target name="build-MIR-solution" description="Build ImageReview MIR solution">
		<property name="MailLogger.failure.subject" value="ImageReview MIR - Solution build step failed" />
		<delete>
			<fileset>
			<include name="${imagereview.build.dir}\IntegrityImageReview\Nuget.config"/>
			</fileset>
		</delete>
		<exec program="nuget" failonerror="true">
			<arg value="restore" />
			<arg value="${imagereview.mir.solution.file}" />
		</exec>

		<if test="${Angular9WebSiteFeature=='false'}">
		<delete>
			<fileset>
			<include name="${imagereview.build.dir}\IntegrityImageReview\WebLayer\AngularWebUI\dist\css\app.css"/>
			</fileset>
		</delete>
		<exec program="IRGulp.bat" failonerror="true">
			<arg value="${imagereview.build.dir}\IntegrityImageReview\WebLayer\AngularWebUI" />
			<arg value="${source_drive}" />
		</exec>
		</if>

		<exec program="C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\MSBuild\Current\Bin\msbuild.exe" failonerror="true">
			<arg value="${imagereview.mir.solution.file}" />
			<arg value="/p:Configuration=Release" />
		</exec>
			<if test="${ADPollingTask=='true'}">
		<exec program="C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\MSBuild\Current\Bin\msbuild.exe" failonerror="true">
			<arg value="${activedirectory.solution.file}" />
			<arg value="/p:Configuration=Release" />
		</exec>
		</if>
		<if test="${Angular9WebSiteFeature=='true'}">
			<exec program="compilewebui.bat" failonerror="true">
			<arg value="${imagereview.build.dir}" />
			<arg value="${base.dist.dir}" />
		</exec>
		</if>
	</target>

	<target name="build-TRANS-solution" description="Build ImageReview TRANS solution">
		<property name="MailLogger.failure.subject" value="ImageReview TRANS - Solution build step failed" />
		<exec program="nuget" failonerror="true">
			<arg value="restore" />
			<arg value="${imagereview.trans.solution.file}" />
		</exec>
		<exec program="C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\MSBuild\Current\Bin\msbuild.exe" failonerror="true">
			<arg value="${imagereview.trans.solution.file}" />
			<arg value="/p:Configuration=Release" />
		</exec>
	</target>

	<!-- Build the QFree  Solution -->
	<target name="build-QFreePlugin-solution" description="Build ImageReview QFreePlugin solution">
		<property name="MailLogger.failure.subject" value="ImageReview QFreePlugin - Solution build step failed" />
		<exec program="nuget" failonerror="true">
			<arg value="restore" />
			<arg value="${imagereview.plugin.solution.file}" />
		</exec>
		<exec program="C:\Program Files (x86)\Microsoft Visual Studio\2019\BuildTools\MSBuild\Current\Bin\msbuild.exe" failonerror="true">
			<arg value="${imagereview.plugin.solution.file}" />
			<arg value="/p:Configuration=Release" />
		</exec>
	</target>

	<!-- Ready the solution for deployment -->
	<target name="distribute" description="Ready the solution for deployment">
		<property name="MailLogger.failure.subject" value="ImageReview ${env} ${deployment.type} - Distribution step failed" />
		<property name="MailLogger.success.subject" value="ImageReview ${env} ${deployment.type} - Step 2 Package Successfully Completed." />

		<!-- Copy  files from build directory to distribution directory on local machine -->
		<if test="${FingerprintFeature=='true'}">
		<call target="distribute-ImageReview-fpm-service" failonerror="true" />
		<call target="distribute-ImageReview-fpp-service" failonerror="true" />
		<call target="distribute-ImageReview-fpm-webapi" failonerror="true"/>
		</if>
		<if test="${HOCPFeature=='true'}">
		<call target="distribute-ImageReview-hocp-service" failonerror="true" />
		<call target="distribute-ImageReview-hocp-webapi" failonerror="true"/>
		</if>
		<if test="${PreHOCPFeature=='true'}">
		<call target="distribute-ImageReview-prehocp-service" failonerror="true" />
		<call target="distribute-ImageReview-prehocp-webapi" failonerror="true"/>
		</if>

		<if test="${IRRFeature=='true'}">
			<call target="distribute-ImageReview-irr-service" failonerror="true" />
			<call target="distribute-ImageReview-irr-webapi" failonerror="true"/>
		</if>
		<call target="distribute-ImageReview-mir-mrt-service" failonerror="true"/>
		<if test="${TransAPIandMIRIntServiceFeature=='true'}">
			<call target="distribute-ImageReview-trans-service" failonerror="true"/>
			<call target="distribute-ImageReview-trans-webapi" failonerror="true"/>
		</if>
		<call target="distribute-ImageReview-mir-service" failonerror="true"/>
		<call target="distribute-ImageReview-mir-web" failonerror="true"/>
		<if test="${ADPollingTask=='true'}">
			<call target="distribute-activedirectorypolling" failonerror="true"/>
		</if>
		<if test="${QFreePluginFeature=='true'}">
			<call target="distribute-ImageReview-qfree-plugin" failonerror="true" />
		</if>

		<!-- Copy Reports directories to distribution directory on local machine -->
		<call target="distribute-Reports" />

		<if test="${TableauFeature=='true'}">
			<call target="distribute-tableau" failonerror="true"/>
		</if>

	</target>

	<!-- Distribute the tableau -->
	<target name="distribute-tableau" description="Ready the solution for deployment of MIR Tableau">
		<property name="MailLogger.failure.subject" value="MIR ${deployment.environment} ${deployment.type} - Distribution of Tableau step failed" />
		<copy todir="${tableau.dist.dir}\Tableau">
				<fileset basedir="${imagereview.build.dir}\Tableau">
					<include name="**/*" />
				</fileset>

		</copy>
	</target>

	<target name="prepare-buildscripts" description="Prepare build scripts for zip and deploy">
		<property name="MailLogger.failure.subject" value="MIR ${deployment.environment} ${deployment.type} - Prepare build scripts for deployment ${deployment.environment} ${deployment.type} step failed" />
		<exec program="PrepareBuildScripts.bat" failonerror="true" >
			<arg value="${base.dir}" />
		</exec>
		<exec program="fnr-TableauTask-json.bat" failonerror="true">
			<arg value="${base.dir}\TableauDeployTool" />
			<arg value="${tableau.tool.parentfolder}" />
			<arg value="${imagereview.tableau.content.url}" />
		</exec>
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${base.dir}\TableauDeployTool" />
			<arg value="json"/>
			<arg value="http://tableau-tool-server:8000/api/3.8/" />
			<arg value="${tableau.tool.apiUrl}" />
			<arg value="ALL"/>
		</exec>
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${base.dir}\TableauDeployTool" />
			<arg value="json"/>
			<arg value="http://tableau-tool-server:8000/" />
			<arg value="${imagereview.tableau.server.url}" />
			<arg value="ALL"/>
		</exec>
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${base.dir}\TableauDeployTool" />
			<arg value="json"/>
			<arg value="SD-IFXTestUserValue" />
			<arg value="${tableau.tool.userName}" />
			<arg value="ALL"/>
		</exec>
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${base.dir}\TableauDeployTool" />
			<arg value="json"/>
			<arg value="secretpassword" />
			<arg value="${tableau.tool.secret}" />
			<arg value="ALL"/>
		</exec>
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${base.dir}\TableauDeployTool" />
			<arg value="json"/>
			<arg value="gc-cpsdevrpt01" />
			<arg value="${tableau.tool.connectionServer}" />
			<arg value="ALL"/>
		</exec>
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${base.dir}\TableauDeployTool" />
			<arg value="json"/>
			<arg value="TableauAppUserValue" />
			<arg value="${tableau.tool.connectionUser}" />
			<arg value="ALL"/>
		</exec>
		<exec program="ReplaceUtil.exe" failonerror="false">
			<arg value="${base.dir}\TableauDeployTool" />
			<arg value="json"/>
			<arg value="connectionpw" />
			<arg value="${tableau.tool.connectionPassword}" />
			<arg value="ALL"/>
		</exec>
		<exec program="ReplaceUtil.exe" failonerror="true">
			<arg value="${base.dir}\TableauDeployTool" />
			<arg value="json"/>
			<arg value="ProjectNameValue" />
			<arg value="${imagereview.tableau.project.name}" />
			<arg value="ALL"/>
		</exec>
		<exec program="fnr-TableauTask-json.bat" failonerror="true">
			<arg value="${base.dir}\TableauDeployTool" />
			<arg value="${imagereview.tableau.content.url}" />
		</exec>
	</target>

	<!-- Distribute Reports -->
	<target name="distribute-Reports" description="Ready the solution for deployment of reports">
		<property name="MailLogger.failure.subject" value="Image Review - ${env} ${deployment.type} Distribution of reports dir step failed" />
		<copy todir="${imagereview.rpt.reports.dist.dir}">
			<fileset basedir="${imagereview.build.dir}\IntegrityImageReview\Reports\ImageReviewReports">
				<include name="**/*" />
				<exclude name="**/*.cs" />
				<exclude name="**/bin/*.cs" />
				<exclude name="**/.svn/*.*" />
				<exclude name="**/**/.svn/*.*" />
				<exclude name="**/**/**/.svn/*.*" />
			</fileset>
		</copy>
		<copy todir="${imagereview.rpt.resources.dist.dir}">
			<fileset basedir="${imagereview.mir.dist.CommonLayer.dir}\Resources">
				<include name="**/*" />
			</fileset>
		</copy>
		<copy todir="${imagereview.rpt.dist.dir}">
			<fileset>
				<include name="ReportsDeployer.exe" />
				<include name="ReportsDeployer.exe.config" />
			</fileset>
		</copy>
	</target>


	<!-- Distribute the ImageReview FPM Service folder -->
	<target name="distribute-ImageReview-fpm-service" description="Ready the solution for deployment of ImageReview Service">
		<property name="MailLogger.failure.subject" value="ImageReview ${deployment.environment} ${deployment.type} FPM - Distribution of service step failed" />
		<copy todir="${imagereview.fpm.dist.AppLayer.dir}">
				<fileset basedir="${imagereview.fpm.release.directory}\AppLayer">
					<include name="**/*" />
					<exclude name="**/*.pdb" />
				</fileset>
			</copy>
			<copy todir="${imagereview.fpm.dist.CommonLayer.dir}">
				<fileset basedir="${imagereview.fpm.release.directory}\CommonLayer">
					<include name="**/*" />
					<exclude name="**/*.pdb" />
				</fileset>
			</copy>
			<copy todir="${imagereview.fpm.dist.ServiceClient.dir}">
				<fileset basedir="${imagereview.fpm.release.directory}\ServiceClient">
					<include name="**/*" />
					<exclude name="**/*.pdb" />
				</fileset>
			</copy>
			<copy todir="${imagereview.fpm.dist.ServiceHost.dir}">
				<fileset basedir="${imagereview.fpm.release.directory}\ServiceHost">
					<include name="**/*" />
					<exclude name="**/*.pdb" />
				</fileset>
			</copy>
			<copy todir="${imagereview.fpm.dist.ThirdParty.dir}">
				<fileset basedir="${imagereview.fpm.release.directory}\ThirdParty">
					<include name="**/*" />
					<exclude name="**/*.pdb" />
				</fileset>
			</copy>
			<copy todir="${base.dist.dir}\FPMSendResultsTask">
				<fileset basedir="${imagereview.fpm.release.directory}\ScheduledTasks\FPMSendResultsTask">
					<include name="**/*" />
					<exclude name="**/*.pdb" />
				</fileset>
			</copy>
			<copy todir="${base.dist.dir}\AuditReviewResultsTask">
				<fileset basedir="${imagereview.mir.mrt.release.directory}\ScheduledTasks\AuditReviewResultsTask">
					<include name="**/*" />
					<exclude name="**/*.pdb" />
				</fileset>
			</copy>
	</target>

		<!-- Distribute the ImageReview FPM Service folder -->
	<target name="distribute-ImageReview-fpp-service" description="Ready the solution for deployment of ImageReview Service">
		<property name="MailLogger.failure.subject" value="ImageReview${deployment.environment} ${deployment.type} FPP Service - Distribution of service step failed" />
		<copy todir="${imagereview.fpp.dist.AppLayer.dir}">
				<fileset basedir="${imagereview.fpp.release.directory}\AppLayer">
					<include name="**/*" />
					<exclude name="**/*.pdb" />
				</fileset>
			</copy>
			<copy todir="${imagereview.fpp.dist.CommonLayer.dir}">
				<fileset basedir="${imagereview.fpp.release.directory}\CommonLayer">
					<include name="**/*" />
					<exclude name="**/*.pdb" />
				</fileset>
			</copy>
			<copy todir="${imagereview.fpp.dist.ThirdParty.dir}">
				<fileset basedir="${imagereview.fpp.release.directory}\ThirdParty">
					<include name="**/*" />
					<exclude name="**/*.pdb" />
				</fileset>
			</copy>
			<!-- Copy to FPClient dir for multi-server distribution -->
				<copy todir="${imagereview.fpp.dist.ServiceClient.dir}">
				<fileset basedir="${imagereview.fpp.release.directory}\ServiceClient">
					<include name="**/*" />
					<exclude name="**/*.pdb" />
				</fileset>
			</copy>
			<copy todir="${imagereview.fpp.dist.ServiceHost.dir}">
				<fileset basedir="${imagereview.fpp.release.directory}\ServiceHost">
					<include name="**/*" />
					<exclude name="**/*.pdb" />
				</fileset>
			</copy>
			<copy todir="${base.dist.dir}\FPPMatchPoolTask">
				<fileset basedir="${imagereview.fpp.release.directory}\ScheduledTasks\FPPMatchPoolTask">
					<include name="**/*" />
					<exclude name="**/*.pdb" />
				</fileset>
			</copy>
	</target>

		<!-- Ready the solution for deployment of ImageReview FPM webApi -->
	<target name="distribute-ImageReview-fpm-webapi" description="Ready the solution for deployment of  webApi files">
		<property name="MailLogger.failure.subject" value="ImageReview FPM - Distribution of webAPI step failed" />

		<copy todir="${imagereview.fpm.dist.WebAPI.dir}">
			<fileset basedir="${imagereview.fpm.release.directory}\WebAPI">
				<include name="**/*" />
				<exclude name="bin\Tcore.FPM.WebAPI.dll.config" />
				<exclude name="bin\*.xml" />
			</fileset>
		</copy>
		<copy todir="${imagereview.fpm.dist.WebAPI.dir}">
			<fileset basedir="${imagereview.build.dir}\FingerPrintMatching\WebLayer\WebAPI">
				<include name="Web.config" />
				<include name="packages.config" />
				<include name="ApplicationInsights.config" />
				<include name="TransCore.snk" />
			</fileset>
		</copy>

		<!-- Cleanup the directory of not needed files -->
		<delete dir="${imagereview.fpm.dist.WebAPI.dir}\obj" />
		<delete dir="${imagereview.fpm.dist.WebAPI.dir}\Properties" />
	</target>

	<!-- Distribute the ImageReview hocp Service folder -->
	<target name="distribute-ImageReview-hocp-service" description="Ready the solution for deployment of ImageReview Service">
		<property name="MailLogger.failure.subject" value="ImageReview ${deployment.environment} ${deployment.type} hocp - Distribution of service step failed" />
		<copy todir="${imagereview.hocp.dist.AppLayer.dir}">
				<fileset basedir="${imagereview.hocp.release.directory}\AppLayer">
					<include name="**/*" />
					<exclude name="**/*.pdb" />
				</fileset>
			</copy>
			<copy todir="${imagereview.hocp.dist.CommonLayer.dir}">
				<fileset basedir="${imagereview.hocp.release.directory}\CommonLayer">
					<include name="**/*" />
					<exclude name="**/*.pdb" />
				</fileset>
			</copy>
			<copy todir="${imagereview.hocp.dist.ServiceClient.dir}">
				<fileset basedir="${imagereview.hocp.release.directory}\ServiceClient">
					<include name="**/*" />
					<exclude name="**/*.pdb" />
				</fileset>
			</copy>
			<copy todir="${imagereview.hocp.dist.ServiceHost.dir}">
				<fileset basedir="${imagereview.hocp.release.directory}\ServiceHost">
					<include name="**/*" />
					<exclude name="**/*.pdb" />
				</fileset>
			</copy>
			<copy todir="${imagereview.hocp.dist.ThirdParty.dir}">
				<fileset basedir="${imagereview.hocp.release.directory}\ThirdParty">
					<include name="**/*" />
					<exclude name="**/*.pdb" />
				</fileset>
			</copy>
			<copy todir="${base.dist.dir}\HighOcrConfProcessor">
				<fileset basedir="${imagereview.hocp.release.directory}\ScheduledTasks\HighOcrConfProcessor">
					<include name="**/*" />
					<exclude name="**/*.pdb" />
				</fileset>
			</copy>
			<copy todir="${base.dist.dir}\HOCSendResultsProcessor">
				<fileset basedir="${imagereview.hocp.release.directory}\ScheduledTasks\HOCSendResultsProcessor">
					<include name="**/*" />
					<exclude name="**/*.pdb" />
				</fileset>
			</copy>
	</target>

	<!-- Distribute the ImageReview hocp Service folder -->
	<target name="distribute-ImageReview-prehocp-service" description="Ready the solution for deployment of PreHOCP ImageReview Service">
		<property name="MailLogger.failure.subject" value="ImageReview ${deployment.environment} ${deployment.type} prehocp - Distribution of service step failed" />
		<copy todir="${imagereview.prehocp.dist.AppLayer.dir}">
				<fileset basedir="${imagereview.hocp.release.directory}\AppLayer">
					<include name="**/*" />
					<exclude name="**/*.pdb" />
				</fileset>
			</copy>
			<copy todir="${imagereview.prehocp.dist.CommonLayer.dir}">
				<fileset basedir="${imagereview.hocp.release.directory}\CommonLayer">
					<include name="**/*" />
					<exclude name="**/*.pdb" />
				</fileset>
			</copy>
			<copy todir="${imagereview.prehocp.dist.ServiceClient.dir}">
				<fileset basedir="${imagereview.hocp.release.directory}\ServiceClient">
					<include name="**/*" />
					<exclude name="**/*.pdb" />
				</fileset>
			</copy>
			<copy todir="${imagereview.prehocp.dist.ServiceHost.dir}">
				<fileset basedir="${imagereview.hocp.release.directory}\ServiceHost">
					<include name="**/*" />
					<exclude name="**/*.pdb" />
				</fileset>
			</copy>
			<copy todir="${imagereview.prehocp.dist.ThirdParty.dir}">
				<fileset basedir="${imagereview.hocp.release.directory}\ThirdParty">
					<include name="**/*" />
					<exclude name="**/*.pdb" />
				</fileset>
			</copy>
			<copy todir="${base.dist.dir}\PreHighOcrConfProcessor">
				<fileset basedir="${imagereview.hocp.release.directory}\ScheduledTasks\HighOcrConfProcessor">
					<include name="**/*" />
					<exclude name="**/*.pdb" />
				</fileset>
			</copy>
			<copy todir="${base.dist.dir}\PreHOCSendResultsProcessor">
				<fileset basedir="${imagereview.hocp.release.directory}\ScheduledTasks\HOCSendResultsProcessor">
					<include name="**/*" />
					<exclude name="**/*.pdb" />
				</fileset>
			</copy>
	</target>

	<!-- Distribute the ImageReview irr Service folder -->
	<target name="distribute-ImageReview-irr-service" description="Ready the solution for deployment of ImageReview Service">
		<property name="MailLogger.failure.subject" value="ImageReview ${deployment.environment} ${deployment.type} irr - Distribution of service step failed" />
		<copy todir="${imagereview.irr.dist.AppLayer.dir}">
				<fileset basedir="${imagereview.irr.release.directory}\AppLayer">
					<include name="**/*" />
					<exclude name="**/*.pdb" />
				</fileset>
			</copy>
			<copy todir="${imagereview.irr.dist.CommonLayer.dir}">
				<fileset basedir="${imagereview.irr.release.directory}\CommonLayer">
					<include name="**/*" />
					<exclude name="**/*.pdb" />
				</fileset>
			</copy>
			<copy todir="${imagereview.irr.dist.ServiceClient.dir}">
				<fileset basedir="${imagereview.irr.release.directory}\ServiceClient">
					<include name="**/*" />
					<exclude name="**/*.pdb" />
				</fileset>
			</copy>
			<copy todir="${imagereview.irr.dist.ServiceHost.dir}">
				<fileset basedir="${imagereview.irr.release.directory}\ServiceHost">
					<include name="**/*" />
					<exclude name="**/*.pdb" />
				</fileset>
			</copy>
			<copy todir="${imagereview.irr.dist.ThirdParty.dir}">
				<fileset basedir="${imagereview.irr.release.directory}\ThirdParty">
					<include name="**/*" />
					<exclude name="**/*.pdb" />
				</fileset>
			</copy>
			<copy todir="${base.dist.dir}\IRRSendResultsTask">
				<fileset basedir="${imagereview.irr.release.directory}\ScheduledTasks\IRRSendResultsTask">
					<include name="**/*" />
					<exclude name="**/*.pdb" />
				</fileset>
			</copy>
			<copy todir="${base.dist.dir}\IRRSendResultsTaskRetries">
				<fileset basedir="${imagereview.irr.release.directory}\ScheduledTasks\IRRSendResultsTask">
					<include name="**/*" />
					<exclude name="**/*.pdb" />
				</fileset>
			</copy>
	</target>

	<!-- Distribute the activedirectorypolling folder -->
	<target name="distribute-activedirectorypolling" description="Ready the solution for deployment of activedirectorypolling Service">
		<property name="MailLogger.failure.subject" value="ImageReview ${deployment.environment} ${deployment.type} activedirectorypolling - Distribution of service step failed" />

			<copy todir="${base.dist.dir}\ActiveDirectoryPollingTask">
				<fileset basedir="${activedirectory.release.directory}\ActiveDirectoryPollingTask">
					<include name="**/*" />
					<exclude name="**/*.pdb" />
				</fileset>
			</copy>
	</target>

	<!-- Distribute the ImageReview mir mrt Service folder -->
	<target name="distribute-ImageReview-mir-mrt-service" description="Ready the solution for deployment of ImageReview Service">
		<property name="MailLogger.failure.subject" value="ImageReview ${deployment.environment} ${deployment.type} mir mrt - Distribution of service step failed" />
			<if test="${ManualResultsFeature=='true'}">
				<copy todir="${base.dist.dir}\ManualResultsTask">
					<fileset basedir="${imagereview.mir.mrt.release.directory}\ScheduledTasks\ManualResultsTask">
						<include name="**/*" />
						<exclude name="**/*.pdb" />
					</fileset>
				</copy>
			</if>

	</target>

		<!-- Distribute the ImageReview trans Service folder -->
	<target name="distribute-ImageReview-trans-service" description="Ready the solution for deployment of ImageReview Service">
		<property name="MailLogger.failure.subject" value="ImageReview ${deployment.environment} ${deployment.type} trans- Distribution of service step failed" />
		<copy todir="${imagereview.trans.dist.AppLayer.dir}">
				<fileset basedir="${imagereview.trans.release.directory}\AppLayer">
					<include name="**/*" />
					<exclude name="**/*.pdb" />
				</fileset>
			</copy>
			<copy todir="${imagereview.trans.dist.CommonLayer.dir}">
				<fileset basedir="${imagereview.trans.release.directory}\CommonLayer">
					<include name="**/*" />
					<exclude name="**/*.pdb" />
				</fileset>
			</copy>
			<copy todir="${imagereview.trans.dist.ServiceClient.dir}">
				<fileset basedir="${imagereview.trans.release.directory}\ServiceClient">
					<include name="**/*" />
					<exclude name="**/*.pdb" />
				</fileset>
			</copy>
			<copy todir="${imagereview.trans.dist.ServiceHost.dir}">
				<fileset basedir="${imagereview.trans.release.directory}\ServiceHost">
					<include name="**/*" />
					<exclude name="**/*.pdb" />
				</fileset>
			</copy>
			<copy todir="${imagereview.trans.dist.ThirdParty.dir}">
				<fileset basedir="${imagereview.trans.release.directory}\ThirdParty">
					<include name="**/*" />
					<exclude name="**/*.pdb" />
				</fileset>
			</copy>
			<copy todir="${imagereview.trans.dist.WebLayer.dir}">
				<fileset basedir="${imagereview.trans.release.directory}\WebLayer">
					<include name="**/*" />
					<exclude name="**/*.pdb" />
				</fileset>
			</copy>
	</target>

	<!-- Distribute the ImageReview irr Service folder -->
	<target name="distribute-ImageReview-mir-service" description="Ready the solution for deployment of ImageReview Application server">
		<property name="MailLogger.failure.subject" value="ImageReview ${deployment.environment} ${deployment.type} MIR - Distribution of service step failed" />
		<copy todir="${imagereview.mir.dist.AppLayer.dir}">
				<fileset basedir="${imagereview.mir.release.directory}\AppLayer">
					<include name="**/*" />
					<exclude name="**/*.pdb" />
				</fileset>
			</copy>
			<copy todir="${imagereview.mir.dist.CommonLayer.dir}">
				<fileset basedir="${imagereview.mir.release.directory}\CommonLayer">
					<include name="**/*" />
					<exclude name="**/*.pdb" />
				</fileset>
			</copy>
			<copy todir="${imagereview.mir.dist.ServiceClient.dir}">
				<fileset basedir="${imagereview.mir.release.directory}\ServiceClient">
					<include name="**/*" />
					<exclude name="**/*.pdb" />
				</fileset>
			</copy>
			<copy todir="${imagereview.mir.dist.ServiceHost.dir}">
				<fileset basedir="${imagereview.mir.release.directory}\ServiceHost">
					<include name="**/*" />
					<exclude name="**/*.pdb" />
				</fileset>
			</copy>
			<copy todir="${imagereview.mir.main.dist.ThirdParty.dir}">
				<fileset basedir="${imagereview.mir.release.directory}\ThirdParty">
					<include name="**/*" />
					<exclude name="**/*.pdb" />
				</fileset>
			</copy>
			<copy todir="${imagereview.mir.dist.packages.dir}">
				<fileset basedir="${imagereview.mir.release.directory}\packages">
				</fileset>
			</copy>
			<if test="${ImageClientServiceFeature=='true'}">
			<copy todir="${base.dist.dir}\ImageClientServiceTask_RetrieveTransactions">
				<fileset basedir="${imagereview.mir.release.directory}\ScheduledTasks\ImageClientServiceTask">
					<include name="**/*" />
					<exclude name="**/*.pdb" />
				</fileset>
			</copy>
			<copy todir="${base.dist.dir}\ImageClientServiceTask_SendResponse">
				<fileset basedir="${imagereview.mir.release.directory}\ScheduledTasks\ImageClientServiceTask">
					<include name="**/*" />
					<exclude name="**/*.pdb" />
				</fileset>
			</copy>
			</if>
			<if test="${VOTTQueueTaskFeature=='true'}">
			<copy todir="${base.dist.dir}\VOTTQueueTask">
				<fileset basedir="${imagereview.mir.release.directory}\ScheduledTasks\VOTTQueueTask">
					<include name="**/*" />
					<exclude name="**/*.pdb" />
				</fileset>
			</copy>
			</if>
			<if test="${MIREmailNotificationTaskFeature=='true'}">
			<copy todir="${base.dist.dir}\MIREmailNotificationTask">
				<fileset basedir="${imagereview.mir.release.directory}\ScheduledTasks\MIREmailNotificationTask">
					<include name="**/*" />
					<exclude name="**/*.pdb" />
				</fileset>
			</copy>
			</if>
	</target>

	<!-- Distribute the ImageReview qfree plugin Service folder -->
	<target name="distribute-ImageReview-qfree-plugin" description="Ready the solution for deployment of ImageReview Application server">
		<property name="MailLogger.failure.subject" value="ImageReview ${deployment.environment} ${deployment.type} MIR - Distribution of plugin step failed" />
		<copy todir="${imagereview.mir.main.dist.Plugin.dir}">
				<fileset basedir="${imagereview.mir.release.directory}\Plugins">
					<include name="**/*" />
					<exclude name="**/*.pdb" />
				</fileset>
			</copy>
	</target>

		<!-- Distribute the ImageReview irr Service folder -->
	<target name="distribute-ImageReview-mir-web" description="Ready the solution for deployment of ImageReview Web server">
		<property name="MailLogger.failure.subject" value="ImageReview ${deployment.environment} ${deployment.type} MIR WEB - Distribution of service step failed" />
			<copy todir="${imagereview.mir.web.dist.Workflows.dir}">
				<fileset basedir="${imagereview.mir.web.release.directory}\Workflows">
					<include name="**/*" />
					<exclude name="**/*.pdb" />
				</fileset>
			</copy>
			<copy todir="${imagereview.mir.web.dist.WebLayer.dir}">
				<fileset basedir="${imagereview.mir.release.directory}\WebLayer">
					<!--<include name="**/*" />-->
					<exclude name="**/*.pdb" />
					<exclude name="/WebAPI/**/*" />
					<exclude name="/WebAPI/**" />
					<exclude name="/WebAPI/bin" />
					<exclude name="/WebAPI/" />
				</fileset>
			</copy>
			<if test="${Angular9WebSiteFeature=='true'}">
			<copy todir="${imagereview.mir.web.dist.WebLayer.dir}\WebSite">
					<fileset basedir="${imagereview.build.dir}\IntegrityImageReview\WebLayer\WebSite\ClientApp\dist">
						<include name="**/*" />
					</fileset>
			</copy>
			<!-- clean out webui -->
			<delete dir="${imagereview.mir.web.dist.WebLayer.dir}\WebUI" />
			</if>
			<copy todir="${imagereview.mir.web.dist.WebAPI.dir}">
			<fileset basedir="${imagereview.build.dir}\IntegrityImageReview\WebLayer\WebAPI">
				<include name="Global.*" />
				<include name="**/bin/*.*" />
				<include name="**/bin/roslyn/*.*" />
			    <include name="Web.config*" />
				<include name="keys.config" />
				<include name="packages.config" />
				<include name="ApplicationInsights.config" />
				<include name="TransCore.snk" />
				<include name="**/thirdparty/**/*.*" />
				<include name="**/thirdparty/*.*" />
			</fileset>
			</copy>
			<copy todir="${imagereview.mir.web.dist.WebAPI.dir}">
			<fileset basedir="${imagereview.mir.web.release.directory}\WebLayer\WebAPI">
				<include name="Global.*" />
				<include name="**/bin/*.*" />
				<include name="**/bin/roslyn/*.*" />
			    <include name="Web.config*" />
				<include name="keys.config" />
				<include name="packages.config" />
				<include name="ApplicationInsights.config" />
				<include name="TransCore.snk" />
				<include name="**/thirdparty/**/*.*" />
				<include name="**/thirdparty/*.*" />
			</fileset>
			</copy>
			<if test="${Angular9WebSiteFeature=='false'}">
			<copy todir="${imagereview.mir.web.dist.WebLayer.dir}\WebUI">
			<fileset basedir="${imagereview.build.dir}\IntegrityImageReview\WebLayer\AngularWebUI">
				<include name="**/*" />
				<exclude name="aspnet_client" />
				<exclude name="**/bin/*.cs" />
				<exclude name="**/.svn/*.*" />
				<exclude name="**/**/.svn/*.*" />
				<exclude name="**/**/**/.svn/*.*" />
				<exclude name="**/*.cs" />
			</fileset>
			</copy>
			</if>

			<if test="${Angular9WebSiteFeature=='true'}">
			<copy todir="${imagereview.mir.web.dist.WebLayer.dir}\ImageReviewReports">
			<fileset basedir="${imagereview.build.dir}\IntegrityImageReview\tcore.ImageReview.Reports">
				<include name="**/*" />
				<exclude name="aspnet_client" />
				<exclude name="**/bin/*.cs" />
				<exclude name="**/bin/*.pdb" />
				<exclude name="**/.svn/*.*" />
				<exclude name="**/**/.svn/*.*" />
				<exclude name="**/**/**/.svn/*.*" />
			</fileset>
			</copy>
			</if>


			<if test="${QFreePluginFeature=='true'}">
			<copy todir="${imagereview.mir.web.dist.WebLayer.dir}\ExternalTransactions">
			<fileset basedir="${imagereview.build.dir}\IntegrityImageReview\WebLayer\ExternalTransactions">
				<include name="Global.*" />
				<include name="**/bin/*.*" />
				<include name="**/bin/roslyn/*.*" />
			    <include name="Web.config*" />
				<include name="keys.config" />
				<include name="packages.config" />
				<include name="ApplicationInsights.config" />
				<include name="TransCore.snk" />
			</fileset>
			</copy>
			</if>

			<copy todir="${imagereview.mir.web.dist.ThirdParty.dir}">
				<fileset basedir="${imagereview.mir.web.release.directory}\ThirdParty">
					<include name="**/*" />
					<exclude name="**/*.pdb" />
				</fileset>
			</copy>
			<copy todir="${imagereview.mir.web.dist.packages.dir}">
				<fileset basedir="${imagereview.mir.web.release.directory}\packages">
				</fileset>
			</copy>
			<copy todir="${imagereview.mir.dist.ServiceHost.dir}">
				<fileset basedir="${imagereview.mir.release.directory}\ServiceHost">
					<include name="**/*" />
					<exclude name="**/*.pdb" />
				</fileset>
			</copy>
			<copy todir="${imagereview.mir.dist.Workflows.dir}">
				<fileset basedir="${imagereview.mir.web.release.directory}\Workflows">
					<include name="**/*" />
					<exclude name="**/*.pdb" />
				</fileset>
			</copy>
	</target>

	<!-- Ready the solution for deployment of ImageReview hocp webApi -->
	<target name="distribute-ImageReview-hocp-webapi" description="Ready the solution for deployment of  webApi files">
		<property name="MailLogger.failure.subject" value="ImageReview hocp - Distribution of webAPI step failed" />

		<copy todir="${imagereview.hocp.dist.WebAPI.dir}">
			<fileset basedir="${imagereview.build.dir}\HighOcrConfProcessing\WebLayer\WebAPI">
				<include name="Global.asax" />
				<include name="**/bin/*.*" />
				<include name="**/bin/roslyn/*.*" />
			    <include name="Web.config*" />
				<include name="packages.config" />
				<include name="TransCore.snk" />
				<include name="ApplicationInsights.config" />
				<include name="TransCore.snk" />
			</fileset>
		</copy>
	</target>

	<!-- Ready the solution for deployment of ImageReview hocp webApi -->
	<target name="distribute-ImageReview-prehocp-webapi" description="Ready the solution for deployment of prehocp  webApi files">
		<property name="MailLogger.failure.subject" value="ImageReview prehocp - Distribution of prehocp webAPI step failed" />

		<copy todir="${imagereview.prehocp.dist.WebAPI.dir}">
			<fileset basedir="${imagereview.build.dir}\HighOcrConfProcessing\WebLayer\WebAPI">
				<include name="Global.asax" />
				<include name="**/bin/*.*" />
				<include name="**/bin/roslyn/*.*" />
			    <include name="Web.config*" />
				<include name="packages.config" />
				<include name="TransCore.snk" />
				<include name="ApplicationInsights.config" />
				<include name="TransCore.snk" />
			</fileset>
		</copy>
	</target>


	<!-- Ready the solution for deployment of ImageReview irr webApi -->
	<target name="distribute-ImageReview-irr-webapi" description="Ready the solution for deployment of  webApi files">
		<property name="MailLogger.failure.subject" value="ImageReview irr - Distribution of webAPI step failed" />

		<copy todir="${imagereview.irr.dist.WebAPI.dir}">
			<fileset basedir="${imagereview.build.dir}\ImageReviewResult\WebLayer\IRRWebApi">
				<include name="Global.asax" />
				<include name="**/bin/*.*" />
				<include name="**/bin/roslyn/*.*" />
			    <include name="Web.config*" />
				<include name="packages.config" />
				<include name="TransCore.snk" />
			</fileset>
		</copy>
	</target>


	<!-- Ready the solution for deployment of ImageReview trans webApi -->
	<target name="distribute-ImageReview-trans-webapi" description="Ready the solution for deployment of  webApi files">
		<property name="MailLogger.failure.subject" value="ImageReview trans - Distribution of webAPI step failed" />

		<copy todir="${imagereview.trans.dist.WebAPI.dir}">
			<fileset basedir="${imagereview.build.dir}\ImageReviewTransactionsServer\WebLayer\MIRWebApi">
				<include name="Global.asax" />
				<include name="**/bin/*.*" />
				<include name="**/bin/roslyn/*.*" />
				<include name="Web.config" />
				<include name="packages.config" />
				<include name="ApplicationInsights.config" />
				<include name="TransCore.snk" />
			</fileset>
		</copy>
	</target>


	<!-- Deploy/Copy files and directories to the destination machines -->
	<target name="deploy-ImageReview" description="">
		<property name="MailLogger.failure.subject" value="ImageReview ${deployment.environment} ${deployment.type} - Step 4 Deploy files to the ${env} servers step failed" />
		<property name="MailLogger.success.subject" value="ImageReview ${deployment.environment} ${deployment.type} - Step 4 Deploy Successfully Completed." />

		<call target="prepare-buildscripts" failonerror="true" />

		<exec program="Deploy.bat" failonerror="true" >
		<arg value="${base.dist.dir}" />
		<arg value="${deployment.user}" />
		<arg value="${deployment.password}" />
		<arg value="${jump.drive}" />
		<arg value="${dated.deployment.dir}" />
		<arg value="${deployment.server}" />
		<arg value="${ClusterFeature}" />
		<arg value="${HumanReadabilityFeature}" />
		<arg value="${base.dir}" />
		<arg value="${TableauFeature}" />
		</exec>

	</target>

	<!-- Deploy/Copy files and directories to the destination machines -->
	<target name="deploy-Jump" description="">
		<property name="MailLogger.failure.subject" value="ImageReview ${deployment.environment} ${deployment.type} - Deploy JUMP files to the ${env} servers step failed" />
		<property name="MailLogger.success.subject" value="ImageReview ${deployment.environment} ${deployment.type} - Step 4  JUMP Deploy Successfully Completed." />

		<exec program="DeployJump.bat" failonerror="true" >
		<arg value="${base.dist.dir}" />
		<arg value="${install.imagereview.fingerprint.server}" />
		<arg value="${install.imagereview.app.server}" />
		<arg value="${deployment.user}" />
		<arg value="${deployment.password}" />
		<arg value="${deployment.drive}" />
		<arg value="${dated.deployment.dir}" />
		<arg value="${install.imagereview.web.server}" />
		<arg value="${install.imagereview.report.server}" />
		<arg value="${deployment.server}" />
		<arg value="${install.imagereview.fingerprint02.server}" />
		<arg value="${install.imagereview.app02.server}" />
		<arg value="${install.imagereview.web02.server}" />
		<arg value="${ClusterFeature}" />
		<arg value="${jump.drive}" />
		<arg value="${install.imagereview.trans.server}" />
		<arg value="${install.imagereview.trans02.server}" />
		<arg value="${install.ifx.app01.server}" />
		<arg value="${install.ifx.app02.server}" />
		<arg value="${install.imagereview.humanread.server}" />
		<!--<arg value="${MultiServerFeature}" />-->
		<arg value="${HumanReadabilityFeature}" />
		<arg value="${install.imagereview.hocp01.server}" />
		<arg value="${install.imagereview.hocp02.server}" />
		<arg value="${install.imagereview.irr01.server}" />
		<arg value="${install.imagereview.irr02.server}" />
		<arg value="${HOCPFeature}" />
		<arg value="${IRRFeature}" />
		<arg value="${PreHOCPFeature}" />
		<arg value="${install.imagereview.hocp01.server}" />
		<arg value="${install.imagereview.hocp02.server}" />
		</exec>
		<if test="${FingerprintFeature=='true'}">
		<foreach item="Line" in="fppclientlist_${deployment.type}.txt" delim=";" property="_server,_type,_maindir,_subdir">
			<exec program="DisperseFPClient.bat" failonerror="true" >
			<arg value="${base.dist.dir}" />
			<arg value="${_server}" />
			<arg value="${deployment.user}" />
			<arg value="${deployment.password}" />
			<arg value="${deployment.drive}" />
			<arg value="${dated.deployment.dir}" />
			<arg value="${jump.drive}" />
			<arg value="${_type}" />
			</exec>
		</foreach>
		</if>
		<!--<if test="${MultiServerFeature=='true'}">-->
		<foreach item="Line" in="MultiServerList_${deployment.type}.txt" delim=";" property="_server,_gentask,_road,_maindir,_subdir,_corridor,_processtype,_taskstatus">
			<exec program="DisperseNodes.bat" failonerror="true" >
			<arg value="${base.dist.dir}" />
			<arg value="${_server}" />
			<arg value="${deployment.user}" />
			<arg value="${deployment.password}" />
			<arg value="${deployment.drive}" />
			<arg value="${dated.deployment.dir}" />
			<arg value="${jump.drive}" />
			<arg value="${_gentask}" />
			<arg value="${_road}" />
			<arg value="${_corridor}" />
			<arg value="${_processtype}" />
			</exec>
		</foreach>
		<!--</if>-->

	</target>


	<target name="unzip-packages" description="Unzip packages for official Insight builds">
	<property name="MailLogger.failure.subject" value="ERROR: ${env} ${deployment.type} ImageReview - Unzip failed" />
	<property name="MailLogger.success.subject" value="SUCCESS: ${env} ${deployment.type} ImageReview - Unzip Succeeded" />
	<exec program="UnzipPackages.bat" failonerror="true">
		<arg value="${install.imagereview.fingerprint.server}" />
		<arg value="${install.imagereview.app.server}" />
		<arg value="${env}" />
		<arg value="${deployment.type}" />
		<arg value="${deployment.drive}" />
		<arg value="${dated.deployment.dir}" />
		<arg value="${FingerprintFeature}" />
		<arg value="${HOCPFeature}" />
		<arg value="${install.imagereview.web.server}" />
		<arg value="${install.imagereview.report.server}" />
		<arg value="${ClusterFeature}" />
		<arg value="${install.imagereview.app02.server}" />
		<arg value="${install.imagereview.web02.server}" />
		<arg value="${install.imagereview.fingerprint02.server}" />
		<arg value="${install.imagereview.trans.server}" />
		<arg value="${install.imagereview.trans02.server}" />
		<arg value="${install.ifx.app01.server}" />
		<arg value="${install.ifx.app02.server}" />
		<arg value="${install.imagereview.humanread.server}" />
		<!--<arg value="${MultiServerFeature}" />-->
		<arg value="${HumanReadabilityFeature}" />
		<arg value="${deployment.user}" />
		<arg value="${deployment.password}" />
		<arg value="${install.imagereview.hocp01.server}" />
		<arg value="${install.imagereview.hocp02.server}" />
		<arg value="${install.imagereview.irr01.server}" />
		<arg value="${install.imagereview.irr02.server}" />
		<arg value="${HOCPFeature}" />
		<arg value="${IRRFeature}" />
		<arg value="${TransAPIandMIRIntServiceFeature}" />
		<arg value="${PreHOCPFeature}" />
		<arg value="${install.imagereview.hocp01.server}" />
		<arg value="${install.imagereview.hocp02.server}" />
		<arg value="${install.qfree.task.server}" />
		<arg value="${deployment.server}" />
	</exec>
	</target>

	<target name="sleep-cluster" description="Sleep Cluster Test onwards">
	<if test="${ClusterFeature=='false'}">
	<echo message="I Am Not Clustered" />
	<call target="pre-install-sleep" failonerror="true" />
	</if>
	<if test="${ClusterFeature=='true'}">
		<call target="pre-install-sleep" failonerror="true" />
		<call target="pre-install-sleep-02" failonerror="true" />
	</if>
	<if test="${FingerprintFeature=='true'}">
	<call target="pre-install-sleep-clientservices" failonerror="true" />
	</if>

	<!--<if test="${MultiServerFeature=='true'}">-->
	<call target="pre-install-sleep-MultiServer" failonerror="true" />
	<!--</if>-->
	</target>

	<target name="pre-install-sleep" description="Put the application to sleep (stop tasks/services)">
	<property name="MailLogger.failure.subject" value="ERROR: ${env} ${deployment.type}  ImageReview  - Pre-Install Sleep failed" />
	<property name="MailLogger.success.subject" value="SUCCESS: ${env} ${deployment.type}  ImageReview - Pre-Install Sleep Succeeded" />
	<exec program="PreInstallSleep.bat" failonerror="true">
	<arg value="${install.imagereview.fingerprint.server}" />
	<arg value="${install.imagereview.app.server}" />
	<arg value="${env}" />
	<arg value="${deployment.type}" />
	<arg value="${FingerprintFeature}" />
	<arg value="${HOCPFeature}" />
	<arg value="${install.imagereview.web.server}" />
	<arg value="01" />
	<arg value="${IFXFeature}" />
	<arg value="${ifx.app01.server}" />
	<arg value="${ADPollingTask}" />
	<arg value="${IRRFeature}" />
	<arg value="${ImageClientServiceFeature}" />
	<arg value="${VOTTQueueTaskFeature}" />
	<arg value="${MIREmailNotificationTaskFeature}" />
	<arg value="${install.imagereview.trans.server}" />
	<arg value="${VehicleDetectionTaskFeature}" />
	<arg value="${install.imagereview.humanread.server}" />
	<!--<arg value="${MultiServerFeature}" />-->
	<arg value="${HumanReadabilityFeature}" />
	<arg value="${ManualResultsFeature}" />
	<arg value="${install.imagereview.hocp01.server}" />
	<arg value="${install.imagereview.irr01.server}" />
	</exec>
	<call target="stop-services01" failonerror="true" />
	</target>

	<target name="pre-install-sleep-02" description="Put the application to sleep (stop tasks/services)">
	<property name="MailLogger.failure.subject" value="ERROR: ${env} ${deployment.type}  ImageReview  - Pre-Install Sleep failed" />
	<property name="MailLogger.success.subject" value="SUCCESS: ${env} ${deployment.type}  ImageReview - Pre-Install Sleep Succeeded" />
	<exec program="PreInstallSleep.bat" failonerror="true">
	<arg value="${install.imagereview.fingerprint02.server}" />
	<arg value="${install.imagereview.app02.server}" />
	<arg value="${env}" />
	<arg value="${deployment.type}" />
	<arg value="${FingerprintFeature}" />
	<arg value="${HOCPFeature}" />
	<arg value="${install.imagereview.web02.server}" />
	<arg value="02" />
	<arg value="${IFXFeature}" />
	<arg value="${ifx.app02.server}" />
	<arg value="${ADPollingTask}" />
	<arg value="${IRRFeature}" />
	<arg value="${ImageClientServiceFeature}" />
	<arg value="${VOTTQueueTaskFeature}" />
	<arg value="${MIREmailNotificationTaskFeature}" />
	<arg value="${install.imagereview.trans02.server}" />
	<arg value="${VehicleDetectionTaskFeature}" />
	<arg value="${install.imagereview.humanread.server}" />
	<!--<arg value="${MultiServerFeature}" />-->
	<arg value="${HumanReadabilityFeature}" />
	<arg value="${ManualResultsFeature}" />
	<arg value="${install.imagereview.hocp02.server}" />
	<arg value="${install.imagereview.irr02.server}" />
	</exec>
	<call target="stop-services02" failonerror="true" />
	</target>
	<target name="pre-install-sleep-clientservices" description="Put the application to sleep (stop tasks/services)">
	<property name="MailLogger.failure.subject" value="ERROR: ${env} ${deployment.type}  ImageReview  - Pre-Install FPClient Sleep failed" />
		<foreach item="Line" in="fppclientlist_${deployment.type}.txt" delim=";" property="_server,_type,_maindir,_subdir">
		<echo message="Stop FPPInternalServiceClient to ${_server} type ${_type}..."/>
		<servicecontroller action="Stop" machine="${_server}" service="FPPInternalServiceClient" failonerror="${ratout.service.errors}"  timeout="200000"/>
	</foreach>
	</target>

	<target name="pre-install-sleep-MultiServer" description="Put the IFXTxnMultiServer tasks to sleep (stop tasks/services)">
	<property name="MailLogger.failure.subject" value="ERROR: ${env} ${deployment.type}  ImageReview  - Pre-Install MultiServer Sleep failed" />
		<foreach item="Line" in="MultiServerList_${deployment.type}.txt" delim=";" property="_server,_gentask,_road,_maindir,_subdir,_corridor,_processtype,_taskstatus">

		<if test="${_road!='none'}">
		<echo message="Stop IFXTxnMultiServer ${_server} type ${_gentask}-${_road}..."/>
		<exec program="C:\Windows\system32\schtasks.exe" failonerror="false">
		<arg line="/S ${_server} /End /TN  ${_gentask}-${_road}" />
		</exec>
		<exec program="C:\Windows\system32\schtasks.exe" failonerror="false">
		<arg line="/S ${_server} /Change /TN ${_gentask}-${_road} /DISABLE" />
		</exec>
		</if>
		<if test="${_road=='none'}">
		<echo message="Stop IFXTxnMultiServer ${_server} type ${_gentask}..."/>
		<exec program="C:\Windows\system32\schtasks.exe" failonerror="false">
		<arg line="/S ${_server} /End /TN  ${_gentask}" />
		</exec>
		<exec program="C:\Windows\system32\schtasks.exe" failonerror="false">
		<arg line="/S ${_server} /Change /TN ${_gentask} /DISABLE" />
		</exec>
		</if>

	</foreach>
	</target>

	<!-- Stop Services -->
	<target name="stop-services01" description="Stop Services 01 for Image Review">
		<property name="MailLogger.failure.subject" value="ImageReview ${deployment.environment} ${deployment.type} - Stop services failed" />
		<if test="${FingerprintFeature=='true'}">
			<call target="Stop-Fingerprint-ServiceHost" failonerror="true" />
		</if>
		<if test="${HOCPFeature=='true'}">
			<call target="Stop-HOCP-ServiceHost" failonerror="true" />
		</if>
		<if test="${PreHOCPFeature=='true'}">
			<call target="Stop-PREHOCP-ServiceHost" failonerror="true" />
		</if>
		<if test="${HumanReadabilityFeature=='false'}">
		<call target="Stop-Required-ServiceHost" failonerror="true" />
		</if>
		<if test="${HumanReadabilityFeature=='true'}">
			<call target="Stop-HR-ServiceHost" failonerror="true" />
		</if>
	</target>

	<target name="stop-services02" description="Stop Services 02 for Image Review">
		<property name="MailLogger.failure.subject" value="ImageReview ${deployment.environment} ${deployment.type} - Stop services failed" />
		<if test="${FingerprintFeature=='true'}">
			<call target="Stop-Fingerprint-ServiceHost02" failonerror="true" />
		</if>
		<if test="${HOCPFeature=='true'}">
			<call target="Stop-HOCP-ServiceHost02" failonerror="true" />
		</if>
		<if test="${PreHOCPFeature=='true'}">
			<call target="Stop-PREHOCP-ServiceHost02" failonerror="true" />
		</if>
		<if test="${HumanReadabilityFeature=='false'}">
		<call target="Stop-Required-ServiceHost02" failonerror="true" />
		</if>
	</target>

	<!-- Start Services -->
	<target name="start-services" description="Start Services for Image Review">
		<property name="MailLogger.failure.subject" value="ImageReview ${deployment.environment} ${deployment.type} - Start services failed" />
		<if test="${FingerprintFeature=='true'}">
			<call target="Start-Fingerprint-ServiceHost" failonerror="true" />
		</if>
		<if test="${HOCPFeature=='true'}">
			<call target="Start-HOCP-ServiceHost" failonerror="true" />
		</if>
		<if test="${PreHOCPFeature=='true'}">
			<call target="Start-PREHOCP-ServiceHost" failonerror="true" />
		</if>
	<if test="${HumanReadabilityFeature=='false'}">
		<call target="Start-Required-ServiceHost" failonerror="true" />
	</if>
		<if test="${HumanReadabilityFeature=='true'}">
			<call target="Start-HR-ServiceHost" failonerror="true" />
		</if>
	</target>

	<target name="start-services02" description="Start Services 02 for Image Review">
		<property name="MailLogger.failure.subject" value="ImageReview ${deployment.environment} ${deployment.type} - Start services failed" />
		<if test="${FingerprintFeature=='true'}">
			<call target="Start-Fingerprint-ServiceHost02" failonerror="true" />
		</if>
		<if test="${HOCPFeature=='true'}">
			<call target="Start-HOCP-ServiceHost02" failonerror="true" />
		</if>
		<if test="${PreHOCPFeature=='true'}">
			<call target="Start-PREHOCP-ServiceHost02" failonerror="true" />
		</if>
		<if test="${HumanReadabilityFeature=='false'}">
			<call target="Start-Required-ServiceHost02" failonerror="true" />
		</if>
	</target>

	 <!-- Starts the WCF service host on the primary and secondary app servers -->

	<target name="Start-Fingerprint-ServiceHost" description="Starts 02 Fingerprint Services" >
		<property name="MailLogger.failure.subject" value="Image Review ${env} ${deployment.type} - Fingerprint Services Failed to Start" />
		<servicecontroller action="Start" machine="${imagereview.fingerprint.server}" service="FPMInternalServiceHost" failonerror="${ratout.service.errors}"  timeout="200000"/>
		<servicecontroller action="Start" machine="${imagereview.fingerprint.server}" service="FPPInternalServiceHost" failonerror="${ratout.service.errors}"  timeout="200000"/>
		<servicecontroller action="Start" machine="${imagereview.fingerprint.server}" service="FPPInternalServiceClient" failonerror="${ratout.service.errors}"  timeout="200000"/>
	</target>
	<target name="Start-Fingerprint-ServiceHost02" description="Starts Fingerprint Services" >
		<property name="MailLogger.failure.subject" value="Image Review ${env} ${deployment.type} 02 - Fingerprint Services Failed to Start" />
		<servicecontroller action="Start" machine="${imagereview.fingerprint02.server}" service="FPMInternalServiceHost" failonerror="${ratout.service.errors}"  timeout="200000"/>
		<!--<servicecontroller action="Start" machine="${imagereview.fingerprint02.server}" service="FPPInternalServiceHost" failonerror="${ratout.service.errors}"  timeout="200000"/>-->
		<servicecontroller action="Start" machine="${imagereview.fingerprint02.server}" service="FPPInternalServiceClient" failonerror="${ratout.service.errors}"  timeout="200000"/>
	</target>

	 <!-- Stops the WCF service host on the primary and secondary app servers -->
	<target name="Stop-Fingerprint-ServiceHost" description="Stops Fingerprint Services" >
		<property name="MailLogger.failure.subject" value="Image Review ${env} ${deployment.type} - FingerPrint Services Failed to Stop" />
		<servicecontroller action="Stop" machine="${imagereview.fingerprint.server}" service="FPMInternalServiceHost" failonerror="${ratout.service.errors}"  timeout="200000"/>
		<servicecontroller action="Stop" machine="${imagereview.fingerprint.server}" service="FPPInternalServiceHost" failonerror="${ratout.service.errors}"  timeout="200000"/>
		<servicecontroller action="Stop" machine="${imagereview.fingerprint.server}" service="FPPInternalServiceClient" failonerror="${ratout.service.errors}"  timeout="200000"/>
	</target>
	<target name="Stop-Fingerprint-ServiceHost02" description="Stops Fingerprint Services" >
		<property name="MailLogger.failure.subject" value="Image Review ${env} ${deployment.type} 02 - FingerPrint Services Failed to Stop" />
		<servicecontroller action="Stop" machine="${imagereview.fingerprint02.server}" service="FPMInternalServiceHost" failonerror="${ratout.service.errors}"  timeout="200000"/>
		<!--<servicecontroller action="Stop" machine="${imagereview.fingerprint02.server}" service="FPPInternalServiceHost" failonerror="${ratout.service.errors}"  timeout="200000"/>-->
	</target>

	<!-- Starts the HOCP Service -->
	<target name="Start-HOCP-ServiceHost" description="Starts HOCP Services" >
		<property name="MailLogger.failure.subject" value="Image Review ${env} ${deployment.type} - HOCP Services Failed to Start" />
		<servicecontroller action="Start" machine="${imagereview.hocp01.server}" service="HOCPInternalServiceHost" failonerror="${ratout.service.errors}"  timeout="200000"/>
	</target>
	<target name="Start-HOCP-ServiceHost02" description="Starts HOCP 02 Services" >
		<property name="MailLogger.failure.subject" value="Image Review ${env} ${deployment.type} 02 - HOCP Services Failed to Start" />
		<servicecontroller action="Start" machine="${imagereview.hocp02.server}" service="HOCPInternalServiceHost" failonerror="${ratout.service.errors}"  timeout="200000"/>
	</target>

	 <!-- Stops the HOCP Service-->
	<target name="Stop-HOCP-ServiceHost" description="Stops HOCP Services" >
		<property name="MailLogger.failure.subject" value="Image Review ${env} ${deployment.type} - HOCP Services Failed to Stop" />
		<servicecontroller action="Stop" machine="${imagereview.hocp01.server}" service="HOCPInternalServiceHost" failonerror="${ratout.service.errors}"  timeout="200000"/>
	</target>
	<target name="Stop-HOCP-ServiceHost02" description="Stops HOCP 02 Services" >
		<property name="MailLogger.failure.subject" value="Image Review ${env} ${deployment.type} 02 - HOCP Services Failed to Stop" />
		<servicecontroller action="Stop" machine="${imagereview.hocp02.server}" service="HOCPInternalServiceHost" failonerror="${ratout.service.errors}"  timeout="200000"/>
	</target>

		<!-- Starts the PREHOCP Service -->
	<target name="Start-PREHOCP-ServiceHost" description="Starts PREHOCP Services" >
		<property name="MailLogger.failure.subject" value="Image Review ${env} ${deployment.type} - PREHOCP Services Failed to Start" />
		<servicecontroller action="Start" machine="${imagereview.prehocp01.server}" service="PREHOCPInternalServiceHost" failonerror="${ratout.service.errors}"  timeout="200000"/>
	</target>
	<target name="Start-PREHOCP-ServiceHost02" description="Starts PREHOCP 02 Services" >
		<property name="MailLogger.failure.subject" value="Image Review ${env} ${deployment.type} 02 - PREHOCP Services Failed to Start" />
		<servicecontroller action="Start" machine="${imagereview.prehocp02.server}" service="PREHOCPInternalServiceHost" failonerror="${ratout.service.errors}"  timeout="200000"/>
	</target>

	 <!-- Stops the PREHOCP Service-->
	<target name="Stop-PREHOCP-ServiceHost" description="Stops PREHOCP Services" >
		<property name="MailLogger.failure.subject" value="Image Review ${env} ${deployment.type} - PREHOCP Services Failed to Stop" />
		<servicecontroller action="Stop" machine="${imagereview.prehocp01.server}" service="PREHOCPInternalServiceHost" failonerror="${ratout.service.errors}"  timeout="200000"/>
	</target>
	<target name="Stop-PREHOCP-ServiceHost02" description="Stops PREHOCP 02 Services" >
		<property name="MailLogger.failure.subject" value="Image Review ${env} ${deployment.type} 02 - PREHOCP Services Failed to Stop" />
		<servicecontroller action="Stop" machine="${imagereview.prehocp02.server}" service="PREHOCPInternalServiceHost" failonerror="${ratout.service.errors}"  timeout="200000"/>
	</target>

	 <!-- Starts the WCF service host on the primary and secondary app servers -->
	<target name="Start-Required-ServiceHost" description="Starts Required Services" >
		<property name="MailLogger.failure.subject" value="Image Review ${env} ${deployment.type} - Required Services Failed to Start" />
		<if test="${TransAPIandMIRIntServiceFeature=='true'}">
			<servicecontroller action="Start" machine="${imagereview.trans.server}" service="MIRInternalServiceHost" failonerror="${ratout.service.errors}"  timeout="200000"/>
		</if>
		<if test="${IRRFeature=='true'}">
			<servicecontroller action="Start" machine="${imagereview.irr01.server}" service="IRRInternalServiceHost" failonerror="${ratout.service.errors}"  timeout="200000"/>
		</if>
		<servicecontroller action="Start" machine="${imagereview.app.server}" service="MIRImageReviewServiceHost" failonerror="${ratout.service.errors}"  timeout="200000"/>
		<servicecontroller action="Start" machine="${imagereview.app.server}" service="tcore.ImageReview.ImageTransactionQueueingService" failonerror="false"  timeout="200000"/>
		<servicecontroller action="Start" machine="${imagereview.app.server}" service="MIR Transaction Queue Service" failonerror="false"  timeout="200000"/>

	</target>
	<target name="Start-Required-ServiceHost02" description="Starts Required 02 Services" >
		<property name="MailLogger.failure.subject" value="Image Review ${env} ${deployment.type} 02 - Required Services Failed to Start" />
		<if test="${TransAPIandMIRIntServiceFeature=='true'}">
			<servicecontroller action="Start" machine="${imagereview.trans02.server}" service="MIRInternalServiceHost" failonerror="${ratout.service.errors}"  timeout="200000"/>
		</if>
		<if test="${IRRFeature=='true'}">
			<servicecontroller action="Start" machine="${imagereview.irr02.server}" service="IRRInternalServiceHost" failonerror="${ratout.service.errors}"  timeout="200000"/>
		</if>
		<servicecontroller action="Start" machine="${imagereview.app02.server}" service="MIRImageReviewServiceHost" failonerror="${ratout.service.errors}"  timeout="200000"/>
		<servicecontroller action="Start" machine="${imagereview.app02.server}" service="tcore.ImageReview.ImageTransactionQueueingService" failonerror="false"  timeout="200000"/>
		<servicecontroller action="Start" machine="${imagereview.app02.server}" service="MIR Transaction Queue Service" failonerror="false"  timeout="200000"/>
	</target>

	 <!-- Stops the WCF service host on the primary and secondary app servers -->
	<target name="Stop-Required-ServiceHost" description="Stops Required Services" >
		<property name="MailLogger.failure.subject" value="Image Review ${env} ${deployment.type} - Required Services Failed to Stop" />
		<if test="${TransAPIandMIRIntServiceFeature=='true'}">
			<servicecontroller action="Stop" machine="${imagereview.trans.server}" service="MIRInternalServiceHost" failonerror="${ratout.service.errors}"  timeout="200000"/>
		</if>
		<if test="${IRRFeature=='true'}">
			<servicecontroller action="Stop" machine="${imagereview.irr01.server}" service="IRRInternalServiceHost" failonerror="${ratout.service.errors}"  timeout="200000"/>
		</if>
		<servicecontroller action="Stop" machine="${imagereview.app.server}" service="MIRImageReviewServiceHost" failonerror="${ratout.service.errors}"  timeout="200000"/>
		<servicecontroller action="Stop" machine="${imagereview.app.server}" service="tcore.ImageReview.ImageTransactionQueueingService" failonerror="false"  timeout="200000"/>
		<servicecontroller action="Stop" machine="${imagereview.app.server}" service="MIR Transaction Queue Service" failonerror="false"  timeout="200000"/>
	</target>
	<target name="Stop-Required-ServiceHost02" description="Stops Required Services" >
		<property name="MailLogger.failure.subject" value="Image Review ${env} ${deployment.type} 02 - Required Services Failed to Stop" />
		<if test="${TransAPIandMIRIntServiceFeature=='true'}">
			<servicecontroller action="Stop" machine="${imagereview.trans02.server}" service="MIRInternalServiceHost" failonerror="${ratout.service.errors}"  timeout="200000"/>
		</if>
		<if test="${IRRFeature=='true'}">
			<servicecontroller action="Stop" machine="${imagereview.irr02.server}" service="IRRInternalServiceHost" failonerror="${ratout.service.errors}"  timeout="200000"/>
		</if>
		<servicecontroller action="Stop" machine="${imagereview.app02.server}" service="MIRImageReviewServiceHost" failonerror="${ratout.service.errors}"  timeout="200000"/>
		<servicecontroller action="Stop" machine="${imagereview.app02.server}" service="tcore.ImageReview.ImageTransactionQueueingService" failonerror="false"  timeout="200000"/>
		<servicecontroller action="Start" machine="${imagereview.app02.server}" service="MIR Transaction Queue Service" failonerror="false"  timeout="200000"/>
	</target>

	<target name="Stop-HR-ServiceHost" description="Stops HR Services" >
		<property name="MailLogger.failure.subject" value="Image Review ${env} ${deployment.type} - Required HR Services Failed to Stop" />
		<servicecontroller action="Stop" machine="${imagereview.humanread.server}" service="MIRInternalServiceHost" failonerror="${ratout.service.errors}"  timeout="200000"/>
		<servicecontroller action="Stop" machine="${imagereview.humanread.server}" service="MIRImageReviewServiceHost" failonerror="${ratout.service.errors}"  timeout="200000"/>
	</target>

	<target name="Start-HR-ServiceHost" description="Start HR Services" >
		<property name="MailLogger.failure.subject" value="Image Review ${env} ${deployment.type} - Required HR Services Failed to Start" />
		<servicecontroller action="Start" machine="${imagereview.humanread.server}" service="MIRInternalServiceHost" failonerror="${ratout.service.errors}"  timeout="200000"/>
		<servicecontroller action="Start" machine="${imagereview.humanread.server}" service="MIRImageReviewServiceHost" failonerror="${ratout.service.errors}"  timeout="200000"/>
	</target>

	<target name="install-cluster" description="install Cluster Test onwards">
	<if test="${ClusterFeature=='false'}">
	<echo message="I Am Not Clustered" />
	<call target="install-build" failonerror="true" />
	</if>
	<if test="${ClusterFeature=='true'}">
		<call target="install-build" failonerror="true" />
		<call target="install-build-02" failonerror="true" />
	</if>
	<if test="${FingerprintFeature=='true'}">
	<call target="install-clientservices" failonerror="true" />
	</if>
	<!--<if test="${MultiServerFeature=='true'}">-->
	<call target="install-MultiServer" failonerror="true" />
	<!--</if>-->
	</target>

	<target name="install-clientservices" description="Install for official IMage Review Client Services">
	<property name="MailLogger.failure.subject" value="ERROR: ${env} ${deployment.type} IMage Review Client Services - Install failed" />
	<foreach item="Line" in="fppclientlist_${deployment.type}.txt" delim=";" property="_server,_type,_maindir,_subdir">
		<exec program="InstallFPClient.bat" failonerror="true">
			<arg value="${_server}" />
			<arg value="${env}" />
			<arg value="${deployment.type}" />
			<arg value="${deployment.drive}" />
			<arg value="${dated.deployment.dir}" />
			<arg value="${_type}" />
			<arg value="${_maindir}" />
			<arg value="${_subdir}" />
		</exec>
	</foreach>
	</target>

	<target name="install-MultiServer" description="Install for official IMage Review Client Services">
	<property name="MailLogger.failure.subject" value="ERROR: ${env} ${deployment.type} Image Review IFXTxnMultiServer- Install failed" />
	<foreach item="Line" in="MultiServerList_${deployment.type}.txt" delim=";" property="_server,_gentask,_road,_maindir,_subdir,_corridor,_processtype,_taskstatus">
		<exec program="InstallNodes.bat" failonerror="true">
			<arg value="${_server}" />
			<arg value="${env}" />
			<arg value="${deployment.type}" />
			<arg value="${deployment.drive}" />
			<arg value="${dated.deployment.dir}" />
			<arg value="${_gentask}" />
			<arg value="${_maindir}" />
			<arg value="${_subdir}" />
			<arg value="${_road}" />
		</exec>
	</foreach>
	</target>


	<target name="install-build" description="Install for official Insight builds">
	<property name="MailLogger.failure.subject" value="ERROR: ${env} ${deployment.type} ImageReview - Install failed" />
	<property name="MailLogger.success.subject" value="SUCCESS: ${env} ${deployment.type} ImageReview - Install Succeeded" />
	<if test="${HumanReadabilityFeature=='false'}">
		<servicecontroller action="Stop" machine="${imagereview.report.server}" service="ReportServer" failonerror="false"  timeout="200000"/>
		<servicecontroller action="Stop" machine="${imagereview.report.server}" service="SQLServerReportingServices" failonerror="false"  timeout="200000"/>
	</if>
	<exec program="InstallBuild.bat" failonerror="true">
	<arg value="${install.imagereview.fingerprint.server}" />
	<arg value="${install.imagereview.app.server}" />
	<arg value="${env}" />
	<arg value="${deployment.type}" />
	<arg value="${deployment.drive}" />
	<arg value="${dated.deployment.dir}" />
	<arg value="${FingerprintFeature}" />
	<arg value="${HOCPFeature}" />
	<arg value="${install.imagereview.web.server}" />
	<arg value="${install.imagereview.report.server}" />
	<arg value="${report.resources.path}" />
	<arg value="01" />
	<arg value="${install.imagereview.trans.server}" />
	<arg value="${install.ifx.app01.server}" />
	<arg value="${install.imagereview.humanread.server}" />
	<!--<arg value="${MultiServerFeature}" />-->
	<arg value="${HumanReadabilityFeature}" />
	<arg value="${install.imagereview.hocp01.server}" />
	<arg value="${install.imagereview.irr01.server}" />
	<arg value="${install.imagereview.prehocp01.server}" />
	<arg value="${install.qfree.task.server}" />
	<arg value="${QFreePluginFeature}" />
	</exec>
	<if test="${HumanReadabilityFeature=='false'}">
		<servicecontroller action="Start" machine="${imagereview.report.server}" service="ReportServer" failonerror="false"  timeout="200000"/>
		<servicecontroller action="Start" machine="${imagereview.report.server}" service="SQLServerReportingServices" failonerror="false"  timeout="200000"/>
	</if>
	</target>

	<target name="install-build-02" description="Install for official Insight builds">
	<property name="MailLogger.failure.subject" value="ERROR: ${env} ${deployment.type} ImageReview - Install failed" />
	<property name="MailLogger.success.subject" value="SUCCESS: ${env} ${deployment.type} ImageReview - Install Succeeded" />
	<exec program="InstallBuild.bat" failonerror="true">
	<arg value="${install.imagereview.fingerprint02.server}" />
	<arg value="${install.imagereview.app02.server}" />
	<arg value="${env}" />
	<arg value="${deployment.type}" />
	<arg value="${deployment.drive}" />
	<arg value="${dated.deployment.dir}" />
	<arg value="${FingerprintFeature}" />
	<arg value="${HOCPFeature}" />
	<arg value="${install.imagereview.web02.server}" />
	<arg value="${install.imagereview.report.server}" />
	<arg value="${report.resources.path}" />
	<arg value="02" />
	<arg value="${install.imagereview.trans02.server}" />
	<arg value="${install.ifx.app02.server}" />
	<arg value="${install.imagereview.humanread.server}" />
	<!--<arg value="${MultiServerFeature}" />-->
	<arg value="${HumanReadabilityFeature}" />
	<arg value="${install.imagereview.hocp02.server}" />
	<arg value="${install.imagereview.irr02.server}" />
	<arg value="${install.imagereview.prehocp02.server}" />
	<arg value="${install.qfree.task.server}" />
	<arg value="${QFreePluginFeature}" />
	</exec>
	</target>

	<target name="deploy-reports" description="Deploy Reports for official Insight builds">
		<property name="MailLogger.failure.subject" value="ERROR: ${env} ${deployment.type} ImageReview - Deploy Reports failed" />
		<property name="MailLogger.success.subject" value="SUCCESS: ${env} ${deployment.type} ImageReview - Deploy Reports Succeeded" />
		<if test="${HumanReadabilityFeature=='false'}">
			<exec program="DeployReports.bat" failonerror="true">
			<arg value="${install.imagereview.report.server}" />
			<arg value="${env}" />
			<arg value="${deployment.type}" />
			<arg value="${deployment.drive}" />
			<arg value="${dated.deployment.dir}" />
			<arg value="${sqlserver.installation.drive}" />
			<arg value="${sqlserver.rpt.version}" />
			<arg value="${report.resources.path}" />
			</exec>
		</if>
		<if test="${HumanReadabilityFeature=='true'}">
			<exec program="DeployReports.bat" failonerror="true">
			<arg value="${install.imagereview.humanread.server}" />
			<arg value="${env}" />
			<arg value="${deployment.type}" />
			<arg value="${deployment.drive}" />
			<arg value="${dated.deployment.dir}" />
			<arg value="${sqlserver.installation.drive}" />
			<arg value="${sqlserver.rpt.version}" />
			<arg value="${report.resources.path}" />
			</exec>
		</if>

		<if test="${TableauFeature=='true'}">
				<call target="deploy-tableau" failonerror="true" />
			</if>
	</target>

	<target name="deploy-tableau" description="Deploy Tableau">
		<property name="MailLogger.failure.subject" value="ERROR: MIR ${deployment.environment} ${deployment.type} - Tableau Deployment failed" />
		<exec program="DeployTableau.bat" failonerror="true">
			<arg value="${deployment.server}" />
			<arg value="${deployment.type}" />
			<arg value="${deployment.drive}" />
			<arg value="${dated.deployment.dir}" />
			<arg value="${deployment.environment}" />
		</exec>
	</target>

	<target name="wake-cluster" description="wake Cluster Test onwards">
	<if test="${ClusterFeature=='false'}">
	<echo message="I Am Not Clustered" />
	<call target="post-install-startup" failonerror="true" />
	</if>
	<if test="${ClusterFeature=='true'}">
		<call target="post-install-startup" failonerror="true" />
		<call target="post-install-startup-02" failonerror="true" />
	</if>
	<if test="${FingerprintFeature=='true'}">
	<call target="post-install-startup-clientservices" failonerror="true" />
	</if>

	<!--<if test="${MultiServerFeature=='true'}">-->
	<call target="post-install-startup-MultiServer" failonerror="true" />
	<!--</if>-->
	</target>

	<target name="post-install-startup" description="Run after installation and after DBA finished DB deployment">
	<property name="MailLogger.failure.subject" value="ERROR: ${env} ${deployment.type} ImageReview - Post-Install Startup failed" />
	<property name="MailLogger.success.subject" value="SUCCESS: ${env} ${deployment.type} ImageReview - Post-Install Startup Succeeded" />
	<call target="start-services" failonerror="true" />
	<exec program="PostInstallStartup.bat" failonerror="true">
	<arg value="${install.imagereview.fingerprint.server}" />
	<arg value="${install.imagereview.app.server}" />
	<arg value="${env}" />
	<arg value="${deployment.type}" />
	<arg value="${FingerprintFeature}" />
	<arg value="${HOCPFeature}" />
	<arg value="${install.imagereview.web.server}" />
	<arg value="01" />
	<arg value="${IFXFeature}" />
	<arg value="${ifx.app01.server}" />
	<arg value="${ADPollingTask}" />
	<arg value="${IRRFeature}" />
	<arg value="${ImageClientServiceFeature}" />
	<arg value="${VOTTQueueTaskFeature}" />
	<arg value="${MIREmailNotificationTaskFeature}" />
	<arg value="${TransPortalFeature}" />
	<arg value="${transportal.db.server}" />
	<arg value="${dated.deployment.dir}" />
	<arg value="${release.version}" />
	<arg value="${install.imagereview.trans.server}" />
	<arg value="${VehicleDetectionTaskFeature}" />
	<arg value="${install.imagereview.humanread.server}" />
	<!--<arg value="${MultiServerFeature}" />-->
	<arg value="${HumanReadabilityFeature}" />
	<arg value="${ManualResultsFeature}" />
	<arg value="${install.imagereview.hocp01.server}" />
	<arg value="${install.imagereview.irr01.server}" />
	<arg value="${transportal.db.listener}" />
	<arg value="${Angular9WebSiteFeature}" />
	<arg value="${db.user}" />
	<arg value="${db.password}" />
	</exec>
	</target>

	<target name="post-install-startup-02" description="Run after installation and after DBA finished DB deployment">
	<property name="MailLogger.failure.subject" value="ERROR: ${env} ${deployment.type} ImageReview - Post-Install Startup failed" />
	<property name="MailLogger.success.subject" value="SUCCESS: ${env} ${deployment.type} ImageReview - Post-Install Startup Succeeded" />
	<call target="start-services02" failonerror="true" />
	<exec program="PostInstallStartup.bat" failonerror="true">
	<arg value="${install.imagereview.fingerprint02.server}" />
	<arg value="${install.imagereview.app02.server}" />
	<arg value="${env}" />
	<arg value="${deployment.type}" />
	<arg value="${FingerprintFeature}" />
	<arg value="${HOCPFeature}" />
	<arg value="${install.imagereview.web02.server}" />
	<arg value="02" />
	<arg value="${IFXFeature}" />
	<arg value="${ifx.app02.server}" />
	<arg value="${ADPollingTask}" />
	<arg value="${IRRFeature}" />
	<arg value="${ImageClientServiceFeature}" />
	<arg value="${VOTTQueueTaskFeature}" />
	<arg value="${MIREmailNotificationTaskFeature}" />
	<arg value="${TransPortalFeature}" />
	<arg value="${transportal.db.server}" />
	<arg value="${dated.deployment.dir}" />
	<arg value="${release.version}" />
	<arg value="${install.imagereview.trans02.server}" />
	<arg value="${VehicleDetectionTaskFeature}" />
	<arg value="${install.imagereview.humanread.server}" />
	<!--<arg value="${MultiServerFeature}" />-->
	<arg value="${HumanReadabilityFeature}" />
	<arg value="${ManualResultsFeature}" />
	<arg value="${install.imagereview.hocp02.server}" />
	<arg value="${install.imagereview.irr02.server}" />
	<arg value="${transportal.db.listener}" />
	<arg value="${Angular9WebSiteFeature}" />
	<arg value="${db.user}" />
	<arg value="${db.password}" />
	</exec>
	</target>

	<target name="post-install-startup-clientservices" description="Put the application to wake tasks/services)">
	<property name="MailLogger.failure.subject" value="ERROR: ${env} ${deployment.type}  ImageReview  - Post-Install FPClient Wake failed" />
	<foreach item="Line" in="fppclientlist_${deployment.type}.txt" delim=";" property="_server,_type,_maindir,_subdir">
		<echo message="Start FPPInternalServiceClient to ${_server} type ${_type}..."/>
		<servicecontroller action="Start" machine="${_server}" service="FPPInternalServiceClient" failonerror="${ratout.service.errors}"  timeout="200000"/>
	</foreach>
	</target>

	<target name="post-install-startup-MultiServer" description="Set the IFXTxnMultiServer tasks to startup ">
	<property name="MailLogger.failure.subject" value="ERROR: ${env} ${deployment.type}  ImageReview  - Post-Install FXTxnMultiServer WAKE failed" />

		<foreach item="Line" in="MultiServerList_${deployment.type}.txt" delim=";" property="_server,_gentask,_road,_maindir,_subdir,_corridor,_processtype,_taskstatus">
		<if test="${_taskstatus=='enabled'}">
			<echo message="Start IFXTxnMultiServer ${_server} type ${_gentask}-${_road}..."/>
			<if test="${_road!='none'}">
			<exec program="C:\Windows\system32\schtasks.exe" failonerror="true">
			<arg line="/S ${_server} /Change /TN ${_gentask}-${_road} /ENABLE" />
			</exec>
			</if>
			<if test="${_road=='none'}">
			<exec program="C:\Windows\system32\schtasks.exe" failonerror="true">
			<arg line="/S ${_server} /Change /TN ${_gentask} /ENABLE" />
			</exec>
			</if>
		</if>
	</foreach>
	</target>

	<target name="encrypt-configs-cluster" description="Encrypt-configs Cluster Test onwards">
	<if test="${ClusterFeature=='false'}">
		<echo message="I Am Not Clustered" />
		<call target="encrypt-configs" failonerror="true" />
	</if>
	<if test="${ClusterFeature=='true'}">
		<call target="encrypt-configs" failonerror="true" />
		<call target="encrypt-configs-02" failonerror="true" />
	</if>
	<if test="${FingerprintFeature=='true'}">
		<call target="encrypt-client-configs" failonerror="true" />
	</if>
	<!--<if test="${MultiServerFeature=='true'}">-->
	<call target="encrypt-task-configs" failonerror="true" />
	<!--</if>-->
	</target>


	<target name="encrypt-configs" description="Encrypt Config files (for PROD)">
		<property name="MailLogger.failure.subject" value="ERROR: ${env} ${deployment.type} ImageReview - Encrypt failed" />
		<property name="MailLogger.success.subject" value="SUCCESS: ${env} ${deployment.type} ImageReview -  Encrypt Succeeded" />
		<exec program="EncryptConfigs.bat" failonerror="true">
			<arg value="${install.imagereview.fingerprint.server}" />
			<arg value="${install.imagereview.app.server}" />
			<arg value="${install.imagereview.web.server}" />
			<arg value="${deployment.drive}" />
			<arg value="${dated.deployment.dir}" />
			<arg value="${FingerprintFeature}" />
			<arg value="${install.ifx.app01.server}" />
			<arg value="${install.imagereview.humanread.server}" />
			<arg value="${HumanReadabilityFeature}" />
			<arg value="${HOCPFeature}" />
			<arg value="${IRRFeature}" />
			<arg value="${TransAPIandMIRIntServiceFeature}" />
			<arg value="${PreHOCPFeature}" />
			<arg value="${install.imagereview.hocp01.server}" />
			<arg value="${install.imagereview.irr01.server}" />
			<arg value="${install.imagereview.trans.server}" />
			<arg value="${install.imagereview.prehocp01.server}" />
		</exec>
	</target>

	<target name="encrypt-client-configs" description="Encrypt Config files (for PROD)">
		<property name="MailLogger.failure.subject" value="ERROR: ${env} ${deployment.type} ImageReview Client config - Encrypt failed" />
		<foreach item="Line" in="fppclientlist_${deployment.type}.txt" delim=";" property="_server,_type,_maindir,_subdir" >
		<exec program="EncryptFPClient.bat" failonerror="true">
			<arg value="${_server}" />
			<arg value="${deployment.drive}" />
			<arg value="${dated.deployment.dir}" />
		</exec>
		</foreach>
	</target>

	<target name="encrypt-task-configs" description="Encrypt tasks Config files">
		<property name="MailLogger.failure.subject" value="ERROR: ${env} ${deployment.type} tasks - Encrypt failed" />
		<foreach item="Line" in="MultiServerList_${deployment.type}.txt" delim=";" property="_server,_gentask,_road,_maindir,_subdir,_corridor,_processtype,_taskstatus">
		<exec program="EncryptNodes.bat" failonerror="true">
			<arg value="${_server}" />
			<arg value="${deployment.drive}" />
			<arg value="${dated.deployment.dir}" />
			<arg value="${_gentask}" />
			<arg value="${_road}" />
		</exec>
		</foreach>
	</target>

	<target name="encrypt-configs-02" description="Encrypt Config files (for PROD)">
		<property name="MailLogger.failure.subject" value="ERROR: ${env} ${deployment.type} ImageReview - Encrypt failed" />
		<property name="MailLogger.success.subject" value="SUCCESS: ${env} ${deployment.type} ImageReview -  Encrypt Succeeded" />
		<exec program="EncryptConfigs.bat" failonerror="true">
			<arg value="${install.imagereview.fingerprint02.server}" />
			<arg value="${install.imagereview.app02.server}" />
			<arg value="${install.imagereview.web02.server}" />
			<arg value="${deployment.drive}" />
			<arg value="${dated.deployment.dir}" />
			<arg value="${FingerprintFeature}" />
			<arg value="${install.ifx.app02.server}" />
			<arg value="${install.imagereview.humanread.server}" />
			<arg value="${HumanReadabilityFeature}" />
			<arg value="${HOCPFeature}" />
			<arg value="${IRRFeature}" />
			<arg value="${TransAPIandMIRIntServiceFeature}" />
			<arg value="${PreHOCPFeature}" />
			<arg value="${install.imagereview.hocp02.server}" />
			<arg value="${install.imagereview.irr02.server}" />
			<arg value="${install.imagereview.trans02.server}" />
			<arg value="${install.imagereview.prehocp02.server}" />
		</exec>
	</target>


	<target name="decrypt-configs-cluster" description="Decrypt-configs Cluster Test onwards">
	<if test="${ClusterFeature=='false'}">
		<echo message="I Am Not Clustered" />
		<call target="decrypt-configs" failonerror="true" />
	</if>
	<if test="${ClusterFeature=='true'}">
		<call target="decrypt-configs" failonerror="true" />
		<call target="decrypt-configs-02" failonerror="true" />
	</if>
	<if test="${FingerprintFeature=='true'}">
		<call target="decrypt-client-configs" failonerror="true" />
	</if>
	<!--<if test="${MultiServerFeature=='true'}">-->
	<call target="decrypt-task-configs" failonerror="true" />
	<!--</if>-->
	</target>


	<target name="decrypt-configs" description="Decrypt Config files (for PROD)">
		<property name="MailLogger.failure.subject" value="ERROR: ${env} ${deployment.type} ImageReview - Decrypt failed" />
		<property name="MailLogger.success.subject" value="SUCCESS: ${env} ${deployment.type} ImageReview -  Decrypt Succeeded" />
		<exec program="DecryptConfigs.bat" failonerror="true">
			<arg value="${install.imagereview.fingerprint.server}" />
			<arg value="${install.imagereview.app.server}" />
			<arg value="${install.imagereview.web.server}" />
			<arg value="${deployment.drive}" />
			<arg value="${dated.deployment.dir}" />
			<arg value="${FingerprintFeature}" />
			<arg value="${install.ifx.app01.server}" />
			<arg value="${install.imagereview.humanread.server}" />
			<arg value="${HumanReadabilityFeature}" />
			<arg value="${HOCPFeature}" />
			<arg value="${IRRFeature}" />
			<arg value="${TransAPIandMIRIntServiceFeature}" />
			<arg value="${PreHOCPFeature}" />
			<arg value="${install.imagereview.hocp01.server}" />
			<arg value="${install.imagereview.irr01.server}" />
			<arg value="${install.imagereview.trans.server}" />
			<arg value="${install.imagereview.prehocp01.server}" />
		</exec>
	</target>

	<target name="decrypt-client-configs" description="Decrypt Config files (for PROD)">
		<property name="MailLogger.failure.subject" value="ERROR: ${env} ${deployment.type} ImageReview Client config - Decrypt failed" />
		<foreach item="Line" in="fppclientlist_${deployment.type}.txt" delim=";" property="_server,_type,_maindir,_subdir" >
		<exec program="DecryptFPClient.bat" failonerror="true">
			<arg value="${_server}" />
			<arg value="${deployment.drive}" />
			<arg value="${dated.deployment.dir}" />
		</exec>
		</foreach>
	</target>

	<target name="decrypt-task-configs" description="Decrypt tasks Config files">
		<property name="MailLogger.failure.subject" value="ERROR: ${env} ${deployment.type} tasks - Decrypt failed" />
		<foreach item="Line" in="MultiServerList_${deployment.type}.txt" delim=";" property="_server,_gentask,_road,_maindir,_subdir,_corridor,_processtype,_taskstatus">
		<exec program="DecryptNodes.bat" failonerror="true">
			<arg value="${_server}" />
			<arg value="${deployment.drive}" />
			<arg value="${dated.deployment.dir}" />
			<arg value="${_gentask}" />
			<arg value="${_road}" />
		</exec>
		</foreach>
	</target>

	<target name="decrypt-configs-02" description="Decrypt Config files (for PROD)">
		<property name="MailLogger.failure.subject" value="ERROR: ${env} ${deployment.type} ImageReview - Decrypt failed" />
		<property name="MailLogger.success.subject" value="SUCCESS: ${env} ${deployment.type} ImageReview -  Decrypt Succeeded" />
		<exec program="DecryptConfigs.bat" failonerror="true">
			<arg value="${install.imagereview.fingerprint02.server}" />
			<arg value="${install.imagereview.app02.server}" />
			<arg value="${install.imagereview.web02.server}" />
			<arg value="${deployment.drive}" />
			<arg value="${dated.deployment.dir}" />
			<arg value="${FingerprintFeature}" />
			<arg value="${install.ifx.app02.server}" />
			<arg value="${install.imagereview.humanread.server}" />
			<arg value="${HumanReadabilityFeature}" />
			<arg value="${HOCPFeature}" />
			<arg value="${IRRFeature}" />
			<arg value="${TransAPIandMIRIntServiceFeature}" />
			<arg value="${PreHOCPFeature}" />
			<arg value="${install.imagereview.hocp02.server}" />
			<arg value="${install.imagereview.irr02.server}" />
			<arg value="${install.imagereview.trans02.server}" />
			<arg value="${install.imagereview.prehocp02.server}" />
		</exec>
	</target>



	<target name="stop-web" description="Put the application to sleep (stop tasks/services)">
	<property name="MailLogger.failure.subject" value="ERROR: ${env} ${deployment.type}  ImageReview  - Stop IIS Web failed" />
	<servicecontroller action="Stop" machine="${iisserver}" service="World Wide Web Publishing Service" failonerror="${ratout.service.errors}"  timeout="200000"/>
	<servicecontroller action="Stop" machine="${iisserver}" service="Net.Tcp Listener Adapter" failonerror="${ratout.service.errors}"  timeout="200000"/>
	<servicecontroller action="Stop" machine="${iisserver}" service="Windows Process Activation Service" failonerror="${ratout.service.errors}"  timeout="200000"/>
	</target>



	<target name="start-web" description="Put the application to sleep (stop tasks/services)">
	<property name="MailLogger.failure.subject" value="ERROR: ${env} ${deployment.type}  ImageReview  - Start IIS WEB failed" />
	<servicecontroller action="Start" machine="${iisserver}" service="Windows Process Activation Service" failonerror="${ratout.service.errors}"  timeout="200000"/>
	<servicecontroller action="Start" machine="${iisserver}" service="Net.Tcp Listener Adapter" failonerror="${ratout.service.errors}"  timeout="200000"/>
	<servicecontroller action="Start" machine="${iisserver}" service="World Wide Web Publishing Service" failonerror="${ratout.service.errors}"  timeout="200000"/>

	</target>

	<target name="cleanup-deployment" description="Cleanup Deployment (for PROD)">
		<property name="MailLogger.failure.subject" value="ERROR: ${env} ${deployment.type} ImageReview- Deployment Cleanup Failure" />
		<property name="MailLogger.success.subject" value="SUCCESS: ${env} ${deployment.type} ImageReview-  Deployment Cleanup Succeeded" />
		<if test="${FingerprintFeature=='true'}">
		<foreach item="Line" in="fppclientlist_${deployment.type}.txt" delim=";" property="_server,_type,_maindir,_subdir">
		<exec program="CleanUpFPClient.bat" failonerror="true">
			<arg value="${_server}" />
			<arg value="${deployment.drive}" />
			<arg value="${deployment.user}" />
			<arg value="${deployment.password}" />
		</exec>
		</foreach>
		</if>
		<exec program="CleanUpDeployment.bat" failonerror="true">
		<arg value="${install.imagereview.app.server}" />
		<arg value="${install.imagereview.web.server}" />
		<arg value="${install.imagereview.fingerprint.server}" />
		<arg value="${deployment.drive}" />
		<arg value="${deployment.user}" />
		<arg value="${deployment.password}" />
		<arg value="${install.imagereview.app02.server}" />
		<arg value="${install.imagereview.web02.server}" />
		<arg value="${install.imagereview.fingerprint02.server}" />
		<arg value="${ClusterFeature}" />
		<arg value="${FingerprintFeature}" />
		<arg value="${IRRFeature}" />
		</exec>
	</target>


	<target name="create-tasks" description="Create scheduled tasks">
	<property name="MailLogger.failure.subject" value="ERROR: ${env} ${deployment.type} ImageReview-Create tasks Failure" />
	<property name="MailLogger.success.subject" value="SUCCESS: ${env} ${deployment.type} ImageReview- Create tasks Succeeded" />
		<!--<if test="${MultiServerFeature=='true'}">-->
		<foreach item="Line" in="MultiServerList_${deployment.type}.txt" delim=";" property="_server,_gentask,_road,_maindir,_subdir,_corridor,_processtype,_taskstatus">
			<exec program="CreateScheduledTasks.bat" failonerror="true" >
			<arg value="${base.dist.dir}" />
			<arg value="${_server}" />
			<arg value="${deployment.user}" />
			<arg value="${deployment.password}" />
			<arg value="${deployment.drive}" />
			<arg value="${dated.deployment.dir}" />
			<arg value="${jump.drive}" />
			<arg value="${_gentask}" />
			<arg value="${_road}" />
			<arg value="${_maindir}" />
			<arg value="${_subdir}" />
			</exec>
		</foreach>
		<!--</if>-->
	</target>
</project>
