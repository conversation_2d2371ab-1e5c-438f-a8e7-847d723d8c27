@echo off
rem set variables
Set CURRENTDIR=%CD%
Set SOURCE=%1
Set LOCALSPROJECT=%2
Set useDvasTransParamNameValue=%3
Set PROJECT=%4


echo %SOURCE%

if NOT "%PROJECT%"=="SING" GOTO CONTINUE
"%CURRENTDIR%\fnr.exe" --cl --dir "%SOURCE%" --fileMask "*.ts" --includeSubDirectories --find "enableChecksumForSG: false" --replace "enableChecksumForSG: true"


:CONTINUE
"%CURRENTDIR%\fnr.exe" --cl --dir "%SOURCE%" --fileMask "*.ts" --includeSubDirectories --find "localsProject: 'DEFAULT'" --replace "localsProject: '%LOCALSPROJECT%'"

"%CURRENTDIR%\fnr.exe" --cl --dir "%SOURCE%" --fileMask "*.ts" --includeSubDirectories --find "useDvasTransactionParamName: 'useDvasTransactionParamNameValue'" --replace "useDvasTransactionParamName: %useDvasTransParamNameValue%"

