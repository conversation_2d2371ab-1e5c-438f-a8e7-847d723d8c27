@ECHO OFF
rem SET /P userInput=This action will deploy (copy) MOMS to the servers, do you wish to continue? (Y/N): 
set NOW=%date:~10,4%-%date:~4,2%-%date:~7,2%
rem IF "%userInput%"=="Y" GOTO DEPLOY
rem IF "%userInput%"=="y" GOTO DEPLOY
set /P user=[Enter Project Deployment User i.e. ncta\kelleym]:=
set "psCommand=powershell -Command "$pword = read-host 'Enter Password' -AsSecureString ; ^
    $BSTR=[System.Runtime.InteropServices.Marshal]::SecureStringToBSTR($pword); ^
        [System.Runtime.InteropServices.Marshal]::PtrToStringAuto($BSTR)""
for /f "usebackq delims=" %%p in (`%psCommand%`) do set pw=%%p
set scripts-dir=%CD%


:DEPLOY
NAnt.exe -buildfile:Product_build.build -logger:NAnt.Core.MailLogger -logfile:AppLogs\10-cleanup.log cleanup-deployment -D:deployment.user=%user% -D:deployment.password=%pw%

GOTO END

:END

pause 