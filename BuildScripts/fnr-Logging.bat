@echo off
rem set variables
Set CURRENTDIR=%CD%
Set DEPLOYDIR=%1
Set AAR=%2


"%CURRENTDIR%\fnr.exe" --cl --dir "%DEPLOYDIR%\APP01\DM\API" --fileMask "appsettings.json" --includeSubDirectories  --find """fileSizeLimitBytes"": 10485760" --replace """fileSizeLimitBytes"": 25000000"

"%CURRENTDIR%\fnr.exe" --cl --dir "%DEPLOYDIR%\APP01\DM\API" --fileMask "appsettings.json" --includeSubDirectories  --find """retainedFileCountLimit"": 7" --replace """retainedFileCountLimit"": 500"

"%CURRENTDIR%\fnr.exe" --cl --dir "%DEPLOYDIR%\IMS\API" --fileMask "appsettings.json" --includeSubDirectories  --find """fileSizeLimitBytes"": 10485760" --replace """fileSizeLimitBytes"": 25000000"

"%CURRENTDIR%\fnr.exe" --cl --dir "%DEPLOYDIR%\IMS\API" --fileMask "appsettings.json" --includeSubDirectories  --find """retainedFileCountLimit"": 100" --replace """retainedFileCountLimit"": 500"

"%CURRENTDIR%\fnr.exe" --cl --dir "%DEPLOYDIR%\IMS\API" --fileMask "appsettings.json" --includeSubDirectories  --find """rollingInterval"": ""Hour""" --replace """rollingInterval"": ""Day"""


if "AAR"=="true" ("%CURRENTDIR%\fnr.exe" --cl --dir "%DEPLOYDIR%\APP01\AAR\AARProcessingService" --fileMask "appsettings.json" --includeSubDirectories  --find """retainedFileCountLimit"": 60" --replace """retainedFileCountLimit"": 100")

if "AAR"=="true" ("%CURRENTDIR%\fnr.exe" --cl --dir "%DEPLOYDIR%\APP01\AAR\AARProcessingService" --fileMask "appsettings.json" --includeSubDirectories  --find """rollingInterval"": ""Hour""" --replace """rollingInterval"": ""Day""")