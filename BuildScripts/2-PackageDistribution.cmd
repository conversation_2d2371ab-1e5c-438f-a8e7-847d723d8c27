@ECHO OFF
rem SET /P userInput=This action will Package MOMS for Distribution, do you wish to continue? (Y/N): 
set NOW=%date:~10,4%-%date:~4,2%-%date:~7,2%
set scripts-dir=%CD%
rem IF "%userInput%"=="Y" GOTO DEPLOY
rem IF "%userInput%"=="y" GOTO DEPLOY

rem GOTO END

:DEPLOY
NAnt.exe -buildfile:ImageReview_build.build -logger:NAnt.Core.MailLogger -logfile:AppLogs\2-Distribute.log distribute

GOTO END

:END

pause 


