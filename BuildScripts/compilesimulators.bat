@ECHO OFF
ver > nul
Set CURRENTDIR=%CD%
Set BUILDDIR=%1%
Set BASEDIR=%2%
SET STEP=text
echo error level is %ERRORLEVEL%
if %ERRORLEVEL% NEQ 0 GOTO :error 
echo compile simulators
cd %BUILDDIR%\ImageManagementService
set STEP=ImageManagementServiceSimulator
dotnet publish -c Release --output "%BASEDIR%\DIST\APP01\CPS_Simulators\ImageManagementService" --self-contained --runtime win-x64
if %ERRORLEVEL% NEQ 0 GOTO :error 
cd %BUILDDIR%\ImageFilter
set STEP=polling
call dotnet publish -c Release --output %BASEDIR%\DIST\APP01\CPS_Simulators\ImageFilter --self-contained --runtime win-x64
if %ERRORLEVEL% NEQ 0 GOTO :error 
cd %CURRENTDIR%
goto :END

:error
echo ------- ERRORLEVEL simulators returned ------
echo %ERRORLEVEL% is the errorlevel and failure is on %STEP%
cd %CURRENTDIR%
exit /b 1

:END
echo -- simulators success


exit /b 0