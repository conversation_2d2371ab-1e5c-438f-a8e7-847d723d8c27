@ECHO OFF
set /P drive=Enter the desired Drive (ex. D or F):=
%drive%:
echo create deployment dir
mkdir %drive%:\Deployment-ImageReview
cacls %drive%:\Deployment-ImageReview /t /e /g Users:f
echo create deployment dir
mkdir  %drive%:\MIRLogs 
cacls %drive%:\MIRLogs /t /e /g Users:f
echo create MIR dir
mkdir C:\MIR
cacls C:\MIR /t /e /g Users:f
echo create MIR_bu dir
mkdir C:\MIR_bu
cacls C:\MIR_bu /t /e /g Users:f
echo done
goto :EOF