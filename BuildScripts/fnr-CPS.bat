@echo off
rem set variables
Set CURRENTDIR=%CD%
Set DEPLOYDIR=%1
Set DOMAIN=%2
Set ContraFlowMaxCount=%3
Set TXPValidatorEnabled=%4
Set STORAGEBASEPATH=%5
Set DMSERILOGLEVEL=%6
Set FILECOUNTLIMIT=%7
Set INCLUDEEVENTS=%8
Set PlatesToVRG=%9
SHIFT
SHIFT
SHIFT
SHIFT
SHIFT
SHIFT
SHIFT
SHIFT
SHIFT		
Set REQUESTURI=%1
Set RESTRICTEDTOMINIMUMLEVEL=%2
Set DMBUFFEREDPATHFORMAT=%3
Set IMSBUFFEREDPATHFORMAT=%4
Set CFTSBUFFEREDPATHFORMAT=%5
Set KEYEXPIRATION=%6
Set IMSDIR=%7
Set DETECTIONMSGTIMETOLIVE=%8
Set DNAMATCHINGMSGTIMETOLIVE=%9
SHIFT
SHIFT
SHIFT
SHIFT
SHIFT
SHIFT
SHIFT
SHIFT
SHIFT
Set VRGMESSAGETIMETOLIVE=%1
Set	DMLogDirectory=%2
Set	DMNFSStorageBasePath=%3
Set	DMDetectionMessagesLogPath=%4
Set	DMInvalidDetectionMsgLogPath=%5
Set	DMInvalidDetectionCSVLogPath=%6
Set	DMDetectionFilesMaximumSize=%7
Set	DMDetectionCSVFilesLocation=%8
Set	DMCSVFileNameSuffix=%9
SHIFT
SHIFT
SHIFT
SHIFT
SHIFT
SHIFT
SHIFT
SHIFT
SHIFT
Set	DMFailedCsvStorageDirectory=%1
Set	IMSDetectionMessagesLogPath=%2
Set deploymenttype=%3
Set cftsLog=%4
SET DMVWORKER=%5
SET MIRTBLPENDINGPOLL=%6
SET MIRTBLPENDINGNODATA=%7


if "%deploymenttype%"=="DEV" GOTO SKIPPEDKEY
"%CURRENTDIR%\fnr.exe" --cl --dir "%DEPLOYDIR%\APP01\DNAMatch" --fileMask "appsettings.json" --includeSubDirectories --find "TTFzdFNlY3VyZVBsYWNlU1MxQXV0aFNlcnZpY2VAIVRvZmZlZTJTd2VldDJFbmhhbmNlZA==" --replace "TTBzdFNlY3VyZVBsYWNlU1MwQXV0aFNlcnZpY2U="


:SKIPPEDKEY

if NOT "%deploymenttype%"=="PROD" GOTO SKIPPROD
"%CURRENTDIR%\fnr.exe" --cl --dir "%DEPLOYDIR%\APP01\DNAMatch\DNAMatchService" --fileMask "appsettings.json" --includeSubDirectories  --find """retainedFileCountLimit"": 100" --replace """retainedFileCountLimit"": 3000"
"%CURRENTDIR%\fnr.exe" --cl --dir "%DEPLOYDIR%\APP01\VRG\VRGService" --fileMask "appsettings.json" --includeSubDirectories  --find """retainedFileCountLimit"": 500" --replace """retainedFileCountLimit"": 3000"

:SKIPPROD
if "%DMVWORKER%"=="true" ("%CURRENTDIR%\fnr.exe" --cl --dir "%DEPLOYDIR%\APP01\VRG" --fileMask "*.json" --excludeFileMask "*.dll, *.exe" --includeSubDirectories --find """ProcessDMVRequests"": true" --replace """ProcessDMVRequests"": false"
)
if "%DMVWORKER%"=="true" ("%CURRENTDIR%\fnr.exe" --cl --dir "%DEPLOYDIR%\APP01\VRG" --fileMask "*.json" --excludeFileMask "*.dll, *.exe" --includeSubDirectories --find """ProcessDMVResponses"": true" --replace """ProcessDMVResponses"": false"
)
"%CURRENTDIR%\fnr.exe" --cl --dir "%DEPLOYDIR%\APP01\CFTS\ConfigTrackingService" --fileMask "appsettings.json" --includeSubDirectories --find "D:\\Logs\\CFTS-logs-.log" --replace "%cftslog%\\CFTS-logs-.log"

"%CURRENTDIR%\fnr.exe" --cl --dir "%DEPLOYDIR%" --fileMask "appsettings.json" --includeSubDirectories --find """_CLUSTERING"": """"" --replace """_CLUSTERING"": ""SQL"""

"%CURRENTDIR%\fnr.exe" --cl --dir "%DEPLOYDIR%" --fileMask "appsettings.json" --includeSubDirectories --find """Domain"": ""tcore""" --replace """Domain"": ""%DOMAIN%"""

"%CURRENTDIR%\fnr.exe" --cl --dir "%DEPLOYDIR%\APP01\DM" --fileMask "appsettings.json" --includeSubDirectories --find """ContraFlowMaxCount"": 3" --replace """ContraFlowMaxCount"": %ContraFlowMaxCount%"

"%CURRENTDIR%\fnr.exe" --cl --dir "%DEPLOYDIR%" --fileMask "appsettings.json" --includeSubDirectories --find """TXPValidatorEnabled"": true" --replace """TXPValidatorEnabled"": %TXPValidatorEnabled%"

"%CURRENTDIR%\fnr.exe" --cl --dir "%DEPLOYDIR%\IMS" --fileMask "appsettings.json" --includeSubDirectories --find """StorageBasePath"": ""c:\\test""" --replace """StorageBasePath"": ""%STORAGEBASEPATH%"""

"%CURRENTDIR%\fnr.exe" --cl --dir "%DEPLOYDIR%\APP01\DM" --fileMask "appsettings.json" --includeSubDirectories --find """Default"": ""Information""" --replace """Default"": ""%DMSERILOGLEVEL%"""

"%CURRENTDIR%\fnr.exe" --cl --dir "%DEPLOYDIR%\APP01\DM" --fileMask "appsettings.json" --includeSubDirectories --find """DetectionMessageTimeToLive"": ""259200000""" --replace """DetectionMessageTimeToLive"": ""%DETECTIONMSGTIMETOLIVE%"""

"%CURRENTDIR%\fnr.exe" --cl --dir "%DEPLOYDIR%\APP01\DM" --fileMask "appsettings.json" --includeSubDirectories --find """LogDirectory"": ""C:\\Logs""" --replace """LogDirectory"": ""%DMLogDirectory%"""

"%CURRENTDIR%\fnr.exe" --cl --dir "%DEPLOYDIR%\APP01\DM" --fileMask "appsettings.json" --includeSubDirectories --find """StorageBasePath"": ""cps-detectioncsv-archive""" --replace """StorageBasePath"": ""%DMNFSStorageBasePath%"""

"%CURRENTDIR%\fnr.exe" --cl --dir "%DEPLOYDIR%\APP01\DM" --fileMask "appsettings.json" --includeSubDirectories --find """DetectionMessagesLogPath"": ""C:\\Logs\\DM\\""" --replace """DetectionMessagesLogPath"": ""%DMDetectionMessagesLogPath%"""

"%CURRENTDIR%\fnr.exe" --cl --dir "%DEPLOYDIR%\APP01\DM" --fileMask "appsettings.json" --includeSubDirectories --find """DetectionMessagesLogPath"": ""D:\\CPSLogs\\DM\\""" --replace """DetectionMessagesLogPath"": ""%DMDetectionMessagesLogPath%"""


"%CURRENTDIR%\fnr.exe" --cl --dir "%DEPLOYDIR%\IMS\API"  --fileMask "appsettings.json" --includeSubDirectories --find """LogPath"": ""C:\\Logs\\CPS-IMS\\Detections""" --replace """LogPath"": ""%IMSDetectionMessagesLogPath%"""
"%CURRENTDIR%\fnr.exe" --cl --dir "%DEPLOYDIR%\IMS\API"  --fileMask "appsettings.json" --includeSubDirectories --find """LogPath"": ""D:\\CPSLogs\\CPS-IMS\\Detections""" --replace """LogPath"": ""%IMSDetectionMessagesLogPath%"""

"%CURRENTDIR%\fnr.exe" --cl --dir "%DEPLOYDIR%\APP01\DM" --fileMask "appsettings.json" --includeSubDirectories --find """InvalidDetectionMessagesLogPath"": ""C:\\Logs\\DM\\Invalid""" --replace """InvalidDetectionMessagesLogPath"": ""%DMInvalidDetectionMsgLogPath%"""

"%CURRENTDIR%\fnr.exe" --cl --dir "%DEPLOYDIR%\APP01\DM" --fileMask "appsettings.json" --includeSubDirectories --find """InvalidDetectionCSVLogPath"": ""C:\\Logs\\DM\\Invalid""" --replace """InvalidDetectionCSVLogPath"": ""%DMInvalidDetectionCSVLogPath%"""

"%CURRENTDIR%\fnr.exe" --cl --dir "%DEPLOYDIR%\APP01\DM" --fileMask "appsettings.json" --includeSubDirectories --find """DetectionFilesMaximumSize"": 3072" --replace """DetectionFilesMaximumSize"": %DMDetectionFilesMaximumSize%"

"%CURRENTDIR%\fnr.exe" --cl --dir "%DEPLOYDIR%\APP01\DM" --fileMask "appsettings.json" --includeSubDirectories --find """DetectionFilesMaximumSize"": 51200" --replace """DetectionFilesMaximumSize"": %DMDetectionFilesMaximumSize%"


"%CURRENTDIR%\fnr.exe" --cl --dir "%DEPLOYDIR%\APP01\DM" --fileMask "appsettings.json" --includeSubDirectories --find """DetectionCSVFilesLocation"": ""cps-dmdb-sharedfolder""" --replace """DetectionCSVFilesLocation"": ""%DMDetectionCSVFilesLocation%"""

"%CURRENTDIR%\fnr.exe" --cl --dir "%DEPLOYDIR%\APP01\DM" --fileMask "appsettings.json" --includeSubDirectories --find """CSVFileNameSuffix"": """"" --replace """CSVFileNameSuffix"": ""%DMCSVFileNameSuffix%"""

"%CURRENTDIR%\fnr.exe" --cl --dir "%DEPLOYDIR%\APP01\DM" --fileMask "appsettings.json" --includeSubDirectories --find """FailedCsvStorageDirectory"": ""cps-dmdb-sharedfolder\\unprocessed""" --replace """FailedCsvStorageDirectory"": ""%DMFailedCsvStorageDirectory%"""

"%CURRENTDIR%\fnr.exe" --cl --dir "%DEPLOYDIR%" --fileMask "appsettings.json" --includeSubDirectories --find """requestUri"": ""http://gc-cpsdevsim01:5000""" --replace """requestUri"": ""%REQUESTURI%"""

"%CURRENTDIR%\fnr.exe" --cl --dir "%DEPLOYDIR%" --fileMask "appsettings.json" --includeSubDirectories --find """requestUri"": ""elk-base-uri""" --replace """requestUri"": ""%REQUESTURI%"""

"%CURRENTDIR%\fnr.exe" --cl --dir "%DEPLOYDIR%" --fileMask "appsettings.json" --includeSubDirectories --find """restrictedToMinimumLevel"": ""Information""" --replace """restrictedToMinimumLevel"": ""%RESTRICTEDTOMINIMUMLEVEL%"""

"%CURRENTDIR%\fnr.exe" --cl --dir "%DEPLOYDIR%" --fileMask "appsettings.json" --includeSubDirectories --find """restrictedToMinimumLevel"": ""10""" --replace """restrictedToMinimumLevel"": ""%RESTRICTEDTOMINIMUMLEVEL%"""

"%CURRENTDIR%\fnr.exe" --cl --dir "%DEPLOYDIR%\APP01\DM" --fileMask "appsettings.json" --includeSubDirectories --find """bufferPathFormat"": ""D:\\Logs\\Buffer\\DM-Buffer-{HalfHour}.json""" --replace """bufferPathFormat"": ""%DMBUFFEREDPATHFORMAT%"""

"%CURRENTDIR%\fnr.exe" --cl --dir "%DEPLOYDIR%\IMS" --fileMask "appsettings.json" --includeSubDirectories --find """bufferPathFormat"": ""D:\\Logs\\Buffer\\IMS-Buffer-{HalfHour}.json""" --replace """bufferPathFormat"": ""%IMSBUFFEREDPATHFORMAT%"""

"%CURRENTDIR%\fnr.exe" --cl --dir "%DEPLOYDIR%\IMS" --fileMask "appsettings.json" --includeSubDirectories --find """DNAMatchingMessageTimeToLive"": ""259200000""" --replace """DNAMatchingMessageTimeToLive"": ""%DNAMATCHINGMSGTIMETOLIVE%"""

"%CURRENTDIR%\fnr.exe" --cl --dir "%DEPLOYDIR%\IMS" --fileMask "appsettings.json" --includeSubDirectories --find """VRGMessageTimeToLive"": ""259200000""" --replace """VRGMessageTimeToLive"": ""%VRGMESSAGETIMETOLIVE%"""

"%CURRENTDIR%\fnr.exe" --cl --dir "%DEPLOYDIR%\APP01\CFTS" --fileMask "appsettings.json" --includeSubDirectories --find """bufferPathFormat"": ""D:\\Logs\\Buffer\\CFTS-Buffer-{HalfHour}.json""" --replace """bufferPathFormat"": ""%CFTSBUFFEREDPATHFORMAT%"""

"%CURRENTDIR%\fnr.exe" --cl --dir "%DEPLOYDIR%" --fileMask "appsettings.json" --includeSubDirectories --find """retainedFileCountLimit"": 500" --replace """retainedFileCountLimit"": %FILECOUNTLIMIT%"

"%CURRENTDIR%\fnr.exe" --cl --dir "%DEPLOYDIR%" --fileMask "appsettings.json" --includeSubDirectories --find """IncludeDeviceDataEventsInCsv"": true" --replace """IncludeDeviceDataEventsInCsv"": %INCLUDEEVENTS%"

"%CURRENTDIR%\fnr.exe" --cl --dir "%DEPLOYDIR%" --fileMask "appsettings.json" --includeSubDirectories --find """SendPlatesToVRG"": ""false""" --replace """SendPlatesToVRG"": ""%PlatesToVRG%"""

"%CURRENTDIR%\fnr.exe" --cl --dir "%IMSDIR%" --fileMask "appsettings.json" --includeSubDirectories --find """KeyExpiration"": 30" --replace """KeyExpiration"": %KEYEXPIRATION%"

"%CURRENTDIR%\fnr.exe" --cl --dir "%DEPLOYDIR%\APP01\DNAMatch" --fileMask "appsettings.json" --includeSubDirectories --find """ProcessDNAPlateUpdate"": false" --replace """ProcessDNAPlateUpdate"": true"

rem "%CURRENTDIR%\fnr.exe" --cl --dir "%DEPLOYDIR%\APP01\VRG\VRGService" --fileMask "appsettings.json" --includeSubDirectories  --find """ContraFlowBatchSize"": 100" --replace """ContraFlowBatchSize"": 100"

"%CURRENTDIR%\fnr.exe" --cl --dir "%DEPLOYDIR%\APP01\VRG" --fileMask "appsettings.json" --includeSubDirectories --find """VDMTablePollBatchSize"": 50" --replace """VDMTablePollBatchSize"": 100"

"%CURRENTDIR%\fnr.exe" --cl --dir "%DEPLOYDIR%\EXT01\CPS\ThirdPartyScheduledTasks\ZPIProcessTask" --fileMask "appsettings.json" --includeSubDirectories --find """IntervalInMinutes"": 1" --replace """IntervalInMinutes"": 15"

"%CURRENTDIR%\fnr.exe" --cl --dir "%DEPLOYDIR%\APP01\VRG" --fileMask "appsettings.json" --includeSubDirectories --find """MirTablePendingPollWaitDuration"": 2000" --replace """MirTablePendingPollWaitDuration"": %MIRTBLPENDINGPOLL%"

"%CURRENTDIR%\fnr.exe" --cl --dir "%DEPLOYDIR%\APP01\VRG" --fileMask "appsettings.json" --includeSubDirectories --find """MirTablePendingNoDataPollWaitDuration"": 10000" --replace """MirTablePendingNoDataPollWaitDuration"": %MIRTBLPENDINGNODATA%"
