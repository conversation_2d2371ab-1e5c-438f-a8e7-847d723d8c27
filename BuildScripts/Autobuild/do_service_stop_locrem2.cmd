@echo off
rem ===========================================================
rem do_service_stop_locrem1.cmd
rem
rem Description:
rem     Stop a service on a remote machine.
rem
rem Args:
rem arg1 = %1 (service name in quotes)
rem arg2 = %2 (remote machine)
rem
rem hist:
rem     20071116 hawkinsj Create
rem     20080902 hawkinsj Clone for error handling
rem     20101229 hawkinsj Clone 
rem     20110715 hawkinsj Clone for service stopping
rem     20110719 hawkinsj Clone for remote service stopping
rem     20111228 hawkinsj Clone for cm_central
rem ============================================================
if /I "%2%" EQU "LOCALHOST" goto do_local
rem
@echo -i-:do_cm_0004-i-:Remote service to stop on %2 is %1
psexec \\%2 -c -f Autobuild\do_service_stop_locrem1.cmd %1 LOCALHOST
if ERRORLEVEL 1 goto error_out
@echo -i-:do_cm_0004:Remote service stopped success on %2 for service %1
exit /b 0
:error_out
@echo -e-:%0:  "Error back from remote net stop on %2 for service %1."
exit /B 1

:do_local
@echo -i-:do_cm_0004:Service to stop is %1
rem --TEST for sevice running --
net start | find %1 > look_0004.txt
@echo if %%%^~^z^1 equ 0 exit/b 1 > temp0004.bat
call temp0004.bat look_0004.txt
if ERRORLEVEL 1 goto not_running
rem -- Service is running. Stop it. --
net stop %1
if ERRORLEVEL 1 goto error_out
@echo -i-:do_cm_0004:Service stopped success for %1
goto :EOF
:error_out
@echo -e-:%0:  "Error back from net stop for service %1."
exit /B 1
:not_running
@echo -i-:do_cm_0004:Service %1 already not running.
exit /B 0
goto :EOF


