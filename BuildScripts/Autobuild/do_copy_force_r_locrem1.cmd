@echo off
rem ===========================================================
rem do_copy_force_r_locrem1.cmd
rem
rem Description:
rem     Forcefully copy source to destination
rem     Recurse into subdirectories.
rem
rem Args:
rem arg1 = %1 (ID of copy)
rem arg2 = "%~2" (Source)
rem arg3 = "%~3" (destination)
rem arg4 = %4 (remote machine) (LOCALHOST or server_name)
rem
rem hist:
rem     20120112 hawkinsj Create
rem ============================================================
if /I "%4%" EQU "LOCALHOST" goto do_local
rem --
@echo -i-:do_cm_0012:Remote force copy on "%~2" for %1
psexec \\%4 -c -f Autobuild\do_copy_force_r_locrem1.cmd %1 "%~2" "%~3" LOCALHOST
if ERRORLEVEL 1 goto error_out
@echo -i-:do_cm_0012:Remote force copy success on %4 for zip %1
exit /b 0
:error_out
@echo -e-:%0:  "Error back from remote force copy on %4 for zip %1."
exit /B 1
rem =============================================================
:do_local
@echo -i-:do_cm_0012: Force copy ID is %1
rem ============================================================
if NOT exist "%~2" goto s_not_there
@echo "=========LOOK OS VERSION===================="
ver > .\cm0012_out.txt
find /I "2000" .\cm0012_out.txt
if errorlevel 1 goto normal
@echo --i-:do_cm_0012:Found OS 2000. Leave off /B
xcopy "%~2" "%~3" /E /V /F /R /Y /I 
if ERRORLEVEL 1 goto error_copy
goto resume
:normal
@echo "=======start copy ID=%1======================"
@echo -i-NOT found OS 2000. Keep /B
xcopy "%~2" "%~3" /E /B /V /F /R /Y /I 
if ERRORLEVEL 1 goto error_copy
@echo "=======end copy %1======================"
:resume
if NOT exist "%~3" goto d_not_there
@echo "============================="
exit /B 0

rem --
:error_copy
@echo --i-:do_cm_0012: "Error during forced copy of %~2 to %~3."
exit /B 1
rem --
:s_not_there
@echo --i-:do_cm_0012: "The source directory is not there (%~2)."
exit /B 1
rem --
:d_not_there
@echo --i-:do_cm_0012: "The destination directory is not there after copy (%~3)."
exit /B 1
rem --


