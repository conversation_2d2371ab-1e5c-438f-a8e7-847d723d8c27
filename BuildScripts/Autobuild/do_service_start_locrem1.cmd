@echo off
rem ===========================================================
rem do_service_start_locrem1.cmd
rem
rem Description:
rem     Start a service on a remote or local machine.
rem
rem Args:
rem arg1 = %1 (service name in quotes)
rem arg2 = %2 (remote machine)
rem
rem hist:
rem     20071116 hawkinsj Create
rem     20080902 hawkinsj Clone for error handling
rem     20101229 hawkinsj Clone 
rem     20110715 hawkinsj Clone for service stoping
rem     20110719 hawkinsj Clone for remote service starting
rem     20111228 hawkinsj Clone for cm_central
rem ============================================================
if /I "%2%" EQU "LOCALHOST" goto do_local
rem --
@echo -i-:do_cm_0005:Remote service to start on %2 is %1
psexec \\%2 -c -f Autobuild\do_service_start_locrem1.cmd %1 LOCALHOST
if ERRORLEVEL 1 goto error_out
@echo -i-:do_cm_0005:Remote service start success on %2 for service %1
exit /b 0
:error_out
@echo -e-:%0:  "Error back from remote net start on %2 for service %1."
exit /B 1
rem =============================================================
:do_local
@echo -i-:do_cm_0005:Service to start is %1
rem --TEST for sevice running --
net start | find %1 > look_0005.txt
@echo if %%%^~^z^1 equ 0 exit/b 1 > temp0005.bat
call temp0005.bat look_0005.txt
if ERRORLEVEL 1 goto try_start
goto already_running
rem --
:try_start
rem -- Service is not running. Start it. --
net start %1
if ERRORLEVEL 1 goto error_out
@echo -i-:do_cm_0005:Service start success for %1
goto :EOF
rem --
:error_out
@echo -e-:%0:  "Error back from net start for service %1."
exit /B 1
rem --
:already_running
@echo -i-:do_cm_0005:Service %1 already running.
exit /B 0
goto :EOF


