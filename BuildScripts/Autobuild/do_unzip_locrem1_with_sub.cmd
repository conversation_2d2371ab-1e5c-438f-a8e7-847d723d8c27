@echo off
rem ===========================================================
rem do_unzip_locrem1_with_sub.cmd
rem
rem Description:
rem     Unzip remotely or locally.
rem
rem Args:
rem arg1 = %1 (zip file name)
rem arg2 = "%~2" (unzip root dir)
rem arg3 = %3 (unzip switches)
rem arg4 = %4 (remote machine) (LOCALHOST or server_name)
rem arg5 = %5 (unzip subdirectory)
rem
rem hist:
rem     20120614 hawkinsj Clone
rem ============================================================
if /I "%4%" EQU "LOCALHOST" goto do_local
rem --
@echo -i-:do_cm_0023:Remote unzip of %1 on %4 in dir %2
psexec -accepteula \\%4 -c -f Autobuild\do_unzip_locrem1_with_sub.cmd %1 "%~2" %3 LOCALHOST %5
if ERRORLEVEL 1 goto error_out
@echo -i-:do_cm_0023:Remote unzip success on %4 for unzip %1
exit /b 0
:error_out
@echo -e-:%0:  "Error back from remote unzip on %4 for unzip %1."
exit /B 1
rem =============================================================
:do_local
cd /D "%~2"
if ERRORLEVEL 1 goto bad_cd
@echo -i-:do_cm_0023: Target to unzip is %1 in "%~2"
rem --Test for target existing --
if NOT exist .\%1 goto not_there
rem -- Target does exist, make it.
@echo -i-:do_unzip_locrem1_with_sub.cmd:unzip cmd is unzip %~3 %1
unzip %~3 %1 -d %5
if ERRORLEVEL 1 goto error_occured
goto its_there
rem --
:bad_cd
@echo -e-:%0:  "CD to %~2 fails."
exit /B 1
rem --
:not_there
@echo -e-:%0:  "Target not there to unzip is %1."
dir
exit /B 1
rem --
:error_occured
@echo -e-:%0:  "Error back from unzip on %1."
exit /B 1
rem --
:its_there
@echo -i-:do_cm_0023:unzip  success of %1.
exit /B 0
goto :EOF

