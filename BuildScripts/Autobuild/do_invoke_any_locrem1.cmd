@echo off
rem===========================================================
rem do_invoke_any_locrem1.cmd
rem
rem Description:
rem     Invoke any; remotely or locally.
rem
rem Args:
rem arg1 = %1 (The target server)(LOCALHOST or server_name)
rem arg2 = "%~2" (invoke_in_dir)
rem arg3 = %3 (invoke_method)
rem arg4 = "%~4" (full_path_invoke_target)
rem arg5 = %5 (invoke_arg_1) 
rem arg6 = %6 (invoke_arg_2) 
rem arg7 = %7 (invoke_arg_3) 
rem arg8 = %8 (invoke_arg_4) 
rem
rem hist:
rem     20121204 hawkinsj Clone
rem============================================================
if /I "%1%" EQU "LOCALHOST" goto do_local
rem --
@echo -i-:do_cm_0031:Remote invoke_any on "%~2" is %1
psexec -accepteula \\%1 -c -f Autobuild\do_invoke_any_locrem1.cmd LOCALHOST "%~2" %3 "%~4" %5 %6 %7 %8
if ERRORLEVEL 1 goto error_out
@echo -i-:do_cm_0031:Remote invoke_any success on %1 for invoke_any "%~4"
exit /b 0
:error_out
@echo -e-:%0:  "Error back from remote invoke_any on %1 for invoke_any %~4."
exit /B 1
rem =============================================================
:do_local
cd /D "%~2"
if errorlevel 1 goto errcd
@echo -i-:do_cm_0031: Target to invoke_any("%~4") is in "%~2"
rem --Test for target existing --
if NOT exist "%~4" goto not_there
rem -- Target does exist, make it.
@echo -i-:do_invoke_any_locrem1.cmd:invoke_any cmd is %~3  "%~4" %5 %6 %7 %8
set l_arg1=%5
if ERRORLEVEL 1 goto error_occured
set l_arg2=%6
if ERRORLEVEL 1 goto error_occured
set l_arg3=%7
if ERRORLEVEL 1 goto error_occured
set l_arg4=%8
if ERRORLEVEL 1 goto error_occured
if %5=="unused" set l_arg1=
if ERRORLEVEL 1 goto error_occured
if %6=="unused" set l_arg2=
if ERRORLEVEL 1 goto error_occured
if %7=="unused" set l_arg3=
if ERRORLEVEL 1 goto error_occured
if %8=="unused" set l_arg4=
@echo -i-:do_invoke_any_locrem1.cmd:invoke_any final cmd is %~3 "%~4" %l_arg1% %l_arg2% %l_arg3% %l_arg4% 
@echo -i-:Current Directory (cd output) follows this line.
cd
%~3 "%~4" %l_arg1% %l_arg2% %l_arg3% %l_arg4% 
if ERRORLEVEL 1 goto error_occured
goto its_there
rem --
:errcd
@echo -e-:%0:  "Error cd to %~2."
exit /B 1
rem --
:not_there
@echo -e-:%0:  "Target not there to invoke_any is %~4."
exit /B 1
rem --
:error_occured
@echo -e-:%0:  "Error back from invoke_any of (%~4) on %1."
exit /B 1
rem --
:its_there
@echo -i-:do_cm_0031:invoke_any  success on %1.
exit /B 0
goto :EOF


