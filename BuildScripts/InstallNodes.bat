@ECHO OFF

set NOW=%date:~10,4%-%date:~4,2%-%date:~7,2%
Set CURRENTDIR=%CD%
SET SERVER=%1
SET PROJECT=%2
SET ENVTYPE=%3
SET DEPLOYMENTDRIVE=%4
SET DEPLOYMENTDIR=%5
SET type=%6
SET port=-%7
set fullstamp=%YYYY%-%MM%-%DD%_%HH%-%Min%-%Sec%


echo %SERVER%
echo type is %type%
ECHO %port%
If "%port%" == "-none" set port=
echo port = %port%
echo Node Installation is starting at %fullstamp%
echo ---------------------------------- 

if not "%type%"=="IMS" GOTO error


verify > nul
:Copy_Current_DIRS_TO_BU_DIRS
echo Step %STEP% commencing...  --------- 
SET STEP="Copy current Nodes dirs to _bu dirs"
rem psexec -accepteula \\%SERVER% cmd /c if exist C:\%maindir%\%subdir% (robocopy C:\%maindir%\%subdir% C:\%maindir%_bu\%subdir% /MIR /R:1)
if exist \\%SERVER%\C$\%type%%port% (robocopy \\%SERVER%\C$\%type%%port% \\%SERVER%\C$\%type%%port%_bu /MIR /R:1
)
if %ERRORLEVEL% GEQ 8 GOTO :error
echo Step %STEP% completed...  --------- 
VERIFY > nul


:Copy_Distribution_DIRS
SET STEP="NODES - Copy distribution dirs to C drives"
echo Step %STEP% commencing...  --------- 

robocopy \\%SERVER%\%DEPLOYMENTDRIVE%$\Deployment-CPS\%DEPLOYMENTDIR%\%type%%port% \\%SERVER%\C$\%type%%port% /MIR /R:1
if %ERRORLEVEL% GEQ 8 GOTO :error
echo Step %STEP% completed...  --------- 
VERIFY > nul
GOTO END


:error
echo ------- AN ERROR OCCURED DURING THIS STEP: %STEP% ------
exit /b 1

:END
echo -- Install of CPS nodes is complete

exit /b 0


