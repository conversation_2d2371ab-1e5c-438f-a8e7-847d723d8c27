@ECHO OFF

set NOW=%date:~10,4%-%date:~4,2%-%date:~7,2%
Set CURRENTDIR=%CD%
SET SERVER=%1
SET PROJECT=%2
SET ENVGENTASK=%3
SET DEPLOYMENTDRIVE=%4
SET DEPLOYMENTDIR=%5
SET GENTASK=%6
SET maindir=%7
SET subdir=%8
SET road=%9
SHIFT
SHIFT
SHIFT
SHIFT
SHIFT
SHIFT
SHIFT
SHIFT
SHIFT
SET QFREETASKSERVER=%1

set fullstamp=%YYYY%-%MM%-%DD%_%HH%-%Min%-%Sec%


echo %SERVER%
echo ROAD = %road%
If NOT "%road%" == "none" SET road=-%road%
If "%road%" == "none" SET road=
echo ROAD = %road%

ECHO %CORRIDORS%
If "%CORRIDORS%" == "none" set CORRIDORS=
echo CORRIDORS = %CORRIDORS%

echo Image Review Installation is starting at %fullstamp%
echo ---------------------------------- 


verify > nul
:Copy_Current_DIRS_TO_BU_DIRS
echo Step %STEP% commencing...  --------- 
SET STEP="Copy current Image Review dirs to _bu dirs"
rem psexec -accepteula \\%SERVER% cmd /c if exist C:\%maindir%\%subdir% (robocopy C:\%maindir%\%subdir% C:\%maindir%_bu\%subdir% /MIR /R:1)
if exist \\%SERVER%\C$\%maindir%\%subdir%\%GENTASK%%road%  (robocopy \\%SERVER%\C$\%maindir%\%subdir%\%GENTASK%%road% \\%SERVER%\C$\%maindir%_bu\%subdir%\%GENTASK%%road% /MIR /R:1)
if %ERRORLEVEL% GEQ 8 GOTO :error
echo Step %STEP% completed...  --------- 
VERIFY > nul


:Copy_Distribution_DIRS
SET STEP="Image Review - Copy distribution dirs to C drives"
echo Step %STEP% commencing...  --------- 

robocopy \\%SERVER%\%DEPLOYMENTDRIVE%$\Deployment-ImageReview\%DEPLOYMENTDIR%\%GENTASK%%road% \\%SERVER%\C$\%maindir%\%subdir%\%GENTASK%%road% /MIR /R:1
if %ERRORLEVEL% GEQ 8 GOTO :error


echo Step %STEP% completed...  --------- 
VERIFY > nul
GOTO END


:error
echo ------- AN ERROR OCCURED DURING THIS STEP: %STEP% ------
echo ------- AN ERROR OCCURED DURING THIS STEP: %STEP% ------ 
rem NAnt.exe -buildfile:DVAS_build.build -logger:NAnt.Core.MailLogger -logfile:AppLogs\build.log autobuild-error
exit /b 1

:END
echo -- Install of Image Review is complete, wait for DBA to run last step
echo -- Install of Image Review is complete 
rem NAnt.exe -buildfile:DVAS_build.build -logger:NAnt.Core.MailLogger -logfile:AppLogs\build.log autobuild-complete
exit /b 0


