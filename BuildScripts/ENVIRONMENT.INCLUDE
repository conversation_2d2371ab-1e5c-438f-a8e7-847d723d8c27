<?xml version="1.0" encoding="UTF-8"?>
<project name="ImageReview" default="build" basedir=".">
	<property name="nant.settings.currentframework" value="net-4.0" />

	<!-- Define the Deployment Project Environment-->
	<!-- Project Acronym ex. VTA2/NCTA/ etc. - matches acronym in SVN and in Build Scripts \ Projects \ directory -->
	<property name="deployment.environment" value="CBDTP" />
	<!-- Build Type:  DEV, QA, PROD... should match the name of this file ENVIRONMENT_DEV.include or ENVIRONMENT_QA.include etc -->
	<property name="deployment.type" value="STAGE" />
	<!-- used by the build, do not modify the below lines -->
	<property name="dated.deployment.dir" value="2024-12-05" />
	<property name="release.version" value="23.2.0-rc1" />
    <property name="new.symmetric.key" value="true" />
	<property name="build_id" value="CBDTP/STAGE/23.2.0-rc1" />
	<property name="imagereview.svn.url-dev-branch" value="http://sd-vcmsvn03.tcore.com:8080/svn/ifx/Product/ImageReview/${build_id}/" />
	<property name="activedirectory.polling.svn" value="https://sd-vcmsvn02.tcore.com/svn/MOMS/Products/ActiveDirectoryTask/Source/Trunk" />

	<!--  Server definitions, use Fully Qualified Domain Name, except Deployment Server is always the IP For Image Review the deployment server should match the IRR server -->
	<property name="deployment.server" value="prdjump.int.cbdtp.net" />

	<!-- These will rarely change - unless you had to install a different version of SQL or have an E drive instead of a D drive SQL Server 2016 values are below -->
	<property name="sqlserver.installation.drive" value="D" />
	<property name="sqlserver.rpt.version" value="MSRS15" />
		<!-- Report Resources Destination Path -->
	<property name="report.resources.path" value="${sqlserver.installation.drive}$\Program Files\Microsoft SQL Server Reporting Services\SSRS\ReportServer\bin" />

	<!--Image Review servers -->
	<property name="imagereview.app.server" value="stgmirapp01.int.cbdtp.net" />
	<property name="imagereview.trans.server" value="stgmirapp01.int.cbdtp.net" />
	<property name="imagereview.web.server" value="stgmirweb01.int.cbdtp.net" />
	<property name="imagereview.fingerprint.server" value="stgmirapp01.int.cbdtp.net" />
	<property name="imagereview.db.server" value="stgmirdb01.int.cbdtp.net" />
	<property name="imagereview.report.server" value="stgmirdb03.int.cbdtp.net" />
	x
	<property name="imagereview.app.cluster" value="stgmirapp.cbdtp.net" />
	<property name="imagereview.trans.cluster" value="stgmirapp.cbdtp.net" />
	<property name="imagereview.fingerprint.cluster" value="stgmirapp.cbdtp.net" />
	<!-- Image review web url alias - if no alias than this should be the web servername -->
	<property name="imagereview.web.alias" value="stgmir.cbdtp.net"/>
	<property name="imagereview.app.alias" value="stgmirapp.cbdtp.net"/>
	<property name="imagereview.trans.alias" value="stgmirapp.cbdtp.net"/>
	<property name="imagereview.humanread.alias" value="?????"/>
	<property name="imagereview.fpm.alias" value="stgmirapp.cbdtp.net" />
	<property name="imagereview.app02.server" value="stgmirapp02.int.cbdtp.net" />
	<property name="imagereview.trans02.server" value="stgmirapp02.int.cbdtp.net" />
	<property name="imagereview.web02.server" value="stgmirweb02.int.cbdtp.net" />
	<property name="imagereview.fingerprint02.server" value="stgmirapp02.int.cbdtp.net" />

	<property name="imagereview.rpt.db.listener" value="stgmirdb03.int.cbdtp.net" />
	<property name="imagereview.ips.db.listener" value="stgmirdb01.int.cbdtp.net" />
	<property name="imagereview.trans.db.listener" value="stgmirdb01.int.cbdtp.net" />
	<property name="imagereview.hocp.db.listener" value="stgmirdb01.int.cbdtp.net" />
	<property name="imagereview.irr.db.listener" value="stgmirdb01.int.cbdtp.net" />
	<property name="imagereview.fingerprint.db.listener" value="stgmirdb01.int.cbdtp.net" />
	<property name="imagereview.fpm.db.listener" value="stgmirdb01.int.cbdtp.net" />
	<property name="imagereview.prehocp.db.listener" value="stgmirdb01.int.cbdtp.net" />

	<!--Image server "deploy" server because BAIFA (and maybe others can't use the FQDN-->
	<property name="install.imagereview.app.server" value="stgmirapp01.int.cbdtp.net" />
	<property name="install.imagereview.trans.server" value="stgmirapp01.int.cbdtp.net" />
	<property name="install.imagereview.web.server" value="stgmirweb01.int.cbdtp.net" />
	<property name="install.imagereview.fingerprint.server" value="stgmirapp01.int.cbdtp.net" />
	<property name="install.imagereview.db.server" value="stgmirdb01.int.cbdtp.net" />
	<property name="install.imagereview.report.server" value="stgmirdb03.int.cbdtp.net" />
	<property name="install.imagereview.app02.server" value="stgmirapp02.int.cbdtp.net" />
	<property name="install.imagereview.trans02.server" value="stgmirapp02.int.cbdtp.net" />
	<property name="install.imagereview.web02.server" value="stgmirweb02.int.cbdtp.net" />
	<property name="install.imagereview.fingerprint02.server" value="stgmirapp02.int.cbdtp.net" />
	<property name="install.ifx.app01.server" value="n.a" />
	<property name="install.ifx.app02.server" value="n.a" />
	<property name="install.imagereview.humanread.server" value="n.a" />
	<property name="install.qfree.task.server" value="n.a" />

	<!-- new IRR and HOCP servers -->
	<property name="imagereview.hocp01.server" value="stgmirapp01.int.cbdtp.net" />
	<property name="imagereview.hocp02.server" value="stgmirapp02.int.cbdtp.net" />
	<property name="imagereview.irr01.server" value="stgmirapp01.int.cbdtp.net" />
	<property name="imagereview.irr02.server" value="stgmirapp02.int.cbdtp.net" />
	<property name="install.imagereview.hocp01.server" value="stgmirapp01.int.cbdtp.net" />
	<property name="install.imagereview.hocp02.server" value="stgmirapp02.int.cbdtp.net" />
	<property name="install.imagereview.irr01.server" value="stgmirapp01.int.cbdtp.net" />
	<property name="install.imagereview.irr02.server" value="stgmirapp02.int.cbdtp.net" />
	<property name="imagereview.irr.alias" value="stgmirapp.cbdtp.net"/>
	<property name="imagereview.hocp.alias" value="stgmirapp.cbdtp.net"/>
	<property name="imagereview.irr.cluster" value="stgmirapp.cbdtp.net"/>
	<property name="imagereview.hocp.cluster" value="stgmirapp.cbdtp.net"/>
	<!-- PreHOCP Servers and aliases -->
	<property name="imagereview.prehocp01.server" value="n.a" />
	<property name="imagereview.prehocp02.server" value="n.a" />
	<property name="install.imagereview.prehocp01.server" value="n.a" />
	<property name="install.imagereview.prehocp02.server" value="n.a" />
	<property name="imagereview.prehocp.alias" value="n.a"/>
	<property name="imagereview.prehocp.cluster" value="n.a"/>

	<!-- Tableau Server Definitions-->
	<property name="imagereview.tableau.server" value="stg-tableau.cbdtp.net" />
	<property name="imagereview.tableau.server.url" value="https://${imagereview.tableau.server}/" />
	<property name="imagereview.tableau.content.url" value="" />
	<property name="imagereview.tableau.project.name" value="MIR" />

	<!-- IFX App servers for disabling/enabling ImagePack task-->
	<property name="ifx.app01.server" value="n.a" />
	<property name="ifx.app02.server" value="n.a" />


	<!-- Used in the MIR WebAPI to point to the IFX / Host Internal Service endpoint -->
	<property name="ifx.app.alias" value="n.a" />
	<property name="ifx.service.endpoint.port.name" value=":7200/IFXService" />
	<property name="GoToAppServerDirectly" value="false" />
	<!-- Only really used for old AMS/IFX - vta3/baifa - 2 = IFX and 1 = AMS -->
	<property name="InvokeWcfForNasValue" value="2" />

	<!-- used for image review send trans task. this server could be any of three different ones. Check wtih Gaurav and Sakshi.  -->
	<property name="mir.txn.alias" value="http://????:5001" />
	<property name="txn.db.listener" value="????" />

	<!-- IFX Web Server -->
	<property name="ifx.web.alias" value="" />
	<property name="transportal.web.alias" value="stgtxp.cbdtp.net" />
	<property name="transportal.db.server" value="stgtxp01.int.cbdtp.net" />
	<property name="transportal.db.listener" value="stgtxp01.int.cbdtp.net" />
	<!-- used for the vehicle detection task, the db is 3rd party customer server -->
	<property name="thirdparty.detection.db.server" value="????" />

	<!-- The CPS-DVAS Web Alias -->
	<property name="dvas.web.alias" value="stgcps.cbdtp.net" />
	<property name="useDvasTransParamNameValue" value="false"/>

	<!-- The VOTT Web Alias -->
	<property name="vott.app.alias" value="STGVOTTAPP.cbdtp.net" />

	<!--<property name="imagereview.report.server" value="SDV-TxPDEV01.tcore.com" /> -->

	<!-- Only drive can change depending on the server, default is always D drive -->
	<property name="imagereview.log.dir" value="D:" />

	<!-- Logging settings -->
	<property name="log.error.level" value="Debug"/>

	<!-- FEATURES -->
	<property name="FingerprintFeature" value="false"/>
	<property name="HOCPFeature" value="false"/>
	<property name="ImageClientServiceFeature" value="false"/>
	<property name="ManualResultsFeature" value="false"/>
	<property name="ClusterFeature" value="true"/>
	<property name="IFXFeature" value="false" />
	<property name="QFreePluginFeature" value="false" />
	<property name="IRRFeature" value="false" />
	<property name="Angular9WebSiteFeature" value="true" />
	<property name="VOTTQueueTaskFeature" value="true" />
	<property name="MIREmailNotificationTaskFeature" value="true" />
	<property name="TransPortalFeature"  value="true"/>
	<property name="VehicleDetectionTaskFeature"  value="false"/>
	<!-- Is Project using ADPollingTask -->
	<property name="ADPollingTask"  value="false"/>
	<property name="HumanReadabilityFeature"  value="false"/>
	<property name="TransAPIandMIRIntServiceFeature"  value="true"/>
	<property name="ImgReviewSendTransTaskFeature"  value="false"/>
	<property name="GenerateROITaskFeature"  value="false"/>
	<property name="PreHOCPFeature"  value="false"/>
	<property name="TransactionQueueServiceFeature"  value="false"/>

	<property name="TableauFeature" value="false" />
	<!-- Tableau Deployment Tool Values -->
		<!--parentfolder:  if none, leave as "none" for the property value, otherwise put in the parent folder value - this is extremely rare-->
		<property name="tableau.tool.parentfolder" value="none"/>
		<property name="tableau.tool.apiUrl" value="https://stg-tableau.cbdtp.net/api/3.8/" />
		<property name="tableau.tool.userName" value="SRV_TableauAppUser" />
		<property name="tableau.tool.secret" value="hALL$jULY$hOURS$fINGERS$cOUNTRY$28$" />
		<property name="tableau.tool.connectionServer" value="stgdb01.int.cbdtp.net" />
		<property name="tableau.tool.connectionUser" value="TableauAppUser" />
		<property name="tableau.tool.connectionPassword" value="S@ndieg0" />

	<!-- If TransPortal Feature = true, need the following DB information to update TransPortal DB -->
	<property name="db.user"  value="SSOAppUser"/>
	<property name="db.password"  value="B3st@pp"/>

	<!-- GenerateROI Feature -->
		<property name="split.string" value="-" />

	<!-- QFree Endpoint that exists in the Image Review Client Service -->
		<property name="imageclientservice.qfree.endpoint" value="http://localhost:55056/ManualReview.asmx" />

		<!-- MIR Service Host -->
		<!-- California = 6, New York = 37, North Carolina = 38, Virginia = 55 -->
		<property name="DefaultStateId" value="37" />

		<!-- Transaction Server Service Host-->
		<property name="trans.nettcp.base.address" value="net.tcp://${imagereview.trans.server}:8200" />
		<property name="trans.http.base.address" value="http://${imagereview.trans.server}:8201" />
		<property name="trans.DefaultDBTimeout" value="120" />
		<property name="TransactionTimeZone" value="US/Eastern" />

		<!-- Trans WebAPI Config -->
		<property name="ImageReview.trans.endpoint" value="net.tcp://${imagereview.trans.cluster}:8200/MIRService" />


		<!-- IRR Service Host-->
		<property name="irr.nettcp.base.address" value="net.tcp://${imagereview.irr01.server}:8970" />
		<property name="irr.http.base.address" value="http://${imagereview.irr01.server}:8971" />
		<property name="irr.DefaultDBTimeout" value="120" />
		<!-- IRR Scheduled Task -->
		<property name="irr.task.GenerateToken" value="true" />
		<property name="irr.task.JWTClientId" value="dGNvcmVTU09BdXRoU2VydmVy" />
		<property name="irr.task.UserName" value="TransPortalBackend" />
		<property name="irr.task.PassWord" value="$1t3!L1neS" />

		<!--IFX-related values HTTP/HTTPS check -->
		<!-- IRR Scheduled Task -->
			<!-- Send Image URL and Token URL may change to https and port 9443 when using SSL for IFX-->
			<property name="irr.task.SendImageResultUrl" value="https://${ifx.web.alias}:9443/api/ManualImageReview/SendTransaction/" />
			<property name="irr.task.SendImageResultTokenUrl" value="https://${ifx.web.alias}:9443" />

			<!-- Image Review Service Host Config - https or http set for SSL or not... PORT 9443 for https ? IFX WEB API -->
			<property name="PostOcrConfidenceUrl" value="https://${ifx.web.alias}:9443/api/ManualImageReview/UpdateOcrThreshold/" />

			<!-- Transportal related values HTTP/HTTPS check -->
			<!-- HTTP Port is 7006, HTTPS port is 4436-->
			<property name="web.Transportal.website" value="https://${transportal.web.alias}" />
			<property name="web.Transportal.webapi" value="https://${transportal.web.alias}:4436/" />

		<!-- Image Client Service Task config -->
		<property name="SleepTimeBetweenImageDownloads" value="5000" />

		<!-- IRR WebAPI Config -->
		<property name="ImageReview.irr.endpoint" value="net.tcp://${imagereview.irr.cluster}:8970/IRRService" />

		<!-- MIR Scheduled Task Config -->
		<property name="mir.task.ImageReviewResultsApiUrlExtn" value="/api/TransactionResults/AddTransactionResult" />


		<!--  FPM WebAPI Config -->
		<property name="ImageReview.fpm.endpoint" value="net.tcp://${imagereview.fingerprint.cluster}:8900/FPMService" />

		<!--FPM ServiceHost Config settings -->
		<property name="fpm.nettcp.base.address" value="net.tcp://${imagereview.fingerprint.server}:8900" />
		<property name="fpm.http.base.address" value="http://${imagereview.fingerprint.server}:8901" />
		<property name="fpm.DefaultDBTimeout" value="120" />

		<!-- FPP Client Service -->
		<property name="fpp.IntradaCompanyName" value="n.a" />
		<property name="fpp.IntradaLicenseKey" value="n.a" />

		<!-- HOCP WebAPI Config -->
		<property name="ImageReview.hocp.endpoint" value="net.tcp://${imagereview.hocp.cluster}:8950/HOCPService" />

		<!--HOCP ServiceHost Config settings -->
		<property name="hocp.nettcp.base.address" value="net.tcp://${imagereview.hocp01.server}:8950" />
		<property name="hocp.http.base.address" value="http://${imagereview.hocp01.server}:8951" />
		<property name="hocp.DefaultDBTimeout" value="120" />

		<!-- PreHOCP WebAPI Config -->
		<property name="ImageReview.prehocp.endpoint" value="net.tcp://${imagereview.prehocp.cluster}:8960/HOCPService" />

		<!--PreHOCP ServiceHost Config settings -->
		<property name="prehocp.nettcp.base.address" value="net.tcp://${imagereview.prehocp01.server}:8960" />
		<property name="prehocp.http.base.address" value="http://${imagereview.prehocp01.server}:8961" />
		<property name="prehocp.DefaultDBTimeout" value="120" />

		<!-- Image Review Web API asnd Anguler WEBUI config. -->
		<property name="ServiceAddress" value="net.tcp://${imagereview.app.cluster}:9400/ImageReviewService" />
		<property name="WebSiteUrl" value="${imagereview.web.alias}" />

		<!-- VOTT QUEUE Scheduled Task config -->
		<property name="VOTTImageUrl" value="https://${vott.app.alias}:5443/api/ImageReview/AddImageToStagingQueue" />

		<!--MIREmailNotificationTask config -->
		<property name="SMTPServer" value="smtp.cbdtp.net" />
		<property name="useEmailDefaultCredentials" value="true" />
		<property name="emailUserName" value="n.a" />
		<property name="emailPassword" value="n.a" />
		<property name="emailDomainName" value="cbdtp.net" />
		<property name="SMTPPort" value="25" />

		<!--  environment.ts -->
		<property name="locals.project" value="DEFAULT" />

		<!-- ImageClientService Tasks -->
		<property name="PluginUserName" value="sakshi.gupta" />
		<property name="PluginPassword" value="TampPjDP2gZjjKg7BSwCuz6z2Kw" />

		<!-- These are the active directory settings that are custom for each env/project -->
		<property name="SYSADMIN.securitygroup" value="TcoreSysAdmin" />
		<property name="DefaultDomain" value="int.cbdtp.net" />
		<property name="GroupDefaultOU" value="OU=Domain SecurityGroups,DC=int,DC=cbdtp,DC=net" />
		<property name="UserDefaultOU" value="DC=int,DC=cbdtp,DC=net" />
		<property name="PlazaDefaultOU" value="DC=int,DC=cbdtp,DC=net" />
		<property name="DefaultRootOU" value="DC=int,DC=cbdtp,DC=net" />
		<property name="ServiceUser" value="int\SRV_ADSystemUser" />
		<property name="ServicePassword" value="$iNCHES^iDEA^pICKED^wERE^aCTUALLY^32$" />

	<!-- Properties for mapping drives during deployment.  New practice is that these are provided in Step 4 via command line -->
	<property name="deployment.user" value="" />
	<property name="deployment.password" value="" />

	<!-- Default is always D unless the project has an E drive, which is rare -->
	<property name="deployment.drive" value="D" />
	<!-- Destination server drive -->
	<property name="jump.drive" value="D" />

	<!-- Email Configuration
	<property name="useEmailDefaultCredentials" value="true" />
	<property name="emailUserName" value="<EMAIL>" />
	<property name="emailPassword" value="S@ndieg0" />
	<property name="emailDomainName" value="tcsdhosting.com" />
	<property name="UserName" value="Insight.dev01"/>
	<property name="Password" value="S@ndieg0"/>
	<property name="ClientSettingsProvider.ServiceUri" value=""/>
	<property name="saveSentEmail" value="true"/>-->

	<!-- Web Config App Setting parameters -->
	<!-- Value does not change -->
	<!--<property name="reportPath" value="/ImageReviewReports" /> -->


	<!-- Reports deployer settings, values do not change unless we had to customize our installation  -->
	<property name="report.server.URL" value="http://${imagereview.report.server}:8080/reportserver" />
	<property name="report.service" value="${report.server.URL}/ReportService2010.asmx" />
	<property name="report.datasource.pass" value="n.a" />
	<property name="report.datasource.userid" value="n.a" />

	<property name="reportuser" value="SRV_SSPIInternalUser" />
	<property name="reportpassword" value="-fIRST+rACE+mONDAY+tODAY+tERMS+39-" />

	<!-- Include ActiveDirectory-->
	<property name="SSLFeature"  value="true"/>

	<property name="IntegratedSecurity"  value="true"/>

	<property name="devops" value="true" />

</project>