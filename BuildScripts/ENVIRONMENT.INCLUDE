<?xml version="1.0" encoding="UTF-8"?>
	<project name="CPS" default="build" basedir=".">
	<!--<property name="nant.settings.currentframework" value="net-4.5" />-->

	<!-- Define the Deployment Project Environment-->
	<property name="deployment.environment" value="CBDTP" />
	<property name="deployment.type" value="STAGE" />
	<!-- used by the build, do not modify the below lines -->
	<property name="dated.deployment.dir" value="2025-04-29" />
	<property name="release.version" value="4.20.0-rc2" />
    <property name="new.symmetric.key" value="true" />
	<property name="build_id" value="CBDTP/STAGE/4.20.0-rc2" />
	<property name="assembly.version" value="4.20.0-rc2" />


	<!--  Server definitions  -->
	<property name="deployment.server" value="prdjump.int.cbdtp.net" />
	<property name="app.server.cluster" value="stgcpsapp.cbdtp.net" />
	<property name="app01.server" value="stgcpsapp01.int.cbdtp.net" />
	<property name="app02.server" value="stgcpsapp02.int.cbdtp.net" />
	<property name="app03.server" value="n.a" />
	<property name="web.cluster.alias" value="stgcps.cbdtp.net" />
	<property name="web01.server" value="stgcpsweb01.int.cbdtp.net" />
	<property name="web02.server" value="stgcpsweb02.int.cbdtp.net" />
	<property name="db.listener" value="STGCPSAG" />
	<property name="db01.server" value="STGCPSAG.int.cbdtp.net" />
	<property name="db02.server" value="STGCPSAG.int.cbdtp.net" />
	<property name="rpt01.server" value="STGRPT11.int.cbdtp.net" />
	<property name="rpt.listener" value="STGRPT11" />
	<property name="ims.server" value="stgimsapp01.int.cbdtp.net" />
	<property name="ims02.server" value="stgimsapp02.int.cbdtp.net" />
	<property name="ims.cluster.alias" value="stgimsapp.cbdtp.net" />
	<property name="transportal.web.alias" value="stgtxp.cbdtp.net" />
	<property name="transportal.db.server" value="stgtxp01.int.cbdtp.net" />
	<property name="transportal.db.listener" value="stgtxp01.int.cbdtp.net" />
	<property name="dvas.api.alias" value="stgdvas.cbdtp.net" />
	<property name="insight.web.alias" value="stginsight.cbdtp.net" />
	<property name="insight.server" value="stginsight.cbdtp.net" />
	<property name="insight.db.server" value="stginstdb01.int.cbdtp.net" />
	<property name="ext01.server" value="stgcpsext01.int.cbdtp.net" />
	<property name="ext.alias" value="stgextint.cbdtp.net" />
	<property name="tableau.alias.url" value="https://stg-tableau.cbdtp.net/" />
	<property name="tableau.advanced.url" value="https://stg-tableau.cbdtp.net/" />
	<property name="rabbitmq.cluster" value="stgrmqcluster.cbdtp.net" />
	<!-- For BOS External CPS Website instance -->
	<property name="ext.cps.web.alias" value="stgextint.cbdtp.net:9000" />
	<property name="ext.cps.api.alias" value="stgextint.cbdtp.net:9001" />
	<property name="ext.dvas.api.alias" value="stgextint.cbdtp.net:9002" />
	<property name="ext.ims.api.alias" value="stgextint.cbdtp.net:9003" />
	<property name="ext.txp.api.alias" value="stgextint.cbdtp.net:9004" />

	<property name="ims.api.workflow" value="n/a" />
	<property name="insight.server.workflow" value="n/a" />
	<property name="ims.base.url" value="https://${ims.cluster.alias}:5443" />

	<!-- Tableau Deployment Tool Values -->
	<property name="tableau.tool.apiUrl" value="https://stg-tableau.cbdtp.net/api/3.8/" />
	<property name="tableau.tool.userName" value="SRV_TableauAppUser" />
	<property name="tableau.tool.secret" value="hALL$jULY$hOURS$fINGERS$cOUNTRY$28$" />
	<property name="tableau.tool.connectionServer" value="STGRPT11.int.cbdtp.net" />
	<property name="tableau.tool.connectionUser" value="TableauAppUser" />
	<property name="tableau.tool.connectionPassword" value="@tj4ja0Hy5fr0bOorEibV" />
	<property name="tableau.stored.proc" value="[cfg].usp_AppParameterByName_Get" />
	<property name="tableau.content.url" value=""/>
	<property name="tableau.tool.parentfolder" value="none"/>

	<property name="tableau.server" value="stg-tableau.cbdtp.net" />
	<property name="tableau.server.url" value="https://${tableau.server}:8000/" />

	<!-- Tableau DW -->
	<property name="TableauDWFeature"  value="true"/>
    <property name="tableau.DW.servername" value="stgdw11.int.cbdtp.net" />
    <property name="tableau.DW.port" value="1433" />
    <property name="tableau.DW.username" value="TableauAppUser" />
    <property name="tableau.DW.password" value="@tj4ja0Hy5fr0bOorEibV" />

	<property name="inference.cluster.alias" value="stgdss8440.int.cbdtp.net" />
	<property name="inference.url" value="http://${inference.cluster.alias}:8080" />
	<property name="mir.app.alias" value="stgmirapp.cbdtp.net" />
	<property name="mir.trans.cluster" value="stgmirapp.cbdtp.net"/>

	<!-- domain / web alias domain for CORS -->
	<property name="domain" value="cbdtp.net" />

	<!-- API configuration replacements -->
	<property name="cps.isilon.baseurl" value="https://data2-smb.prd-isilon.int.cbdtp.net:8080" />
	<property name="cps.isilon.username" value="int\\SRV_Isilon" />
	<property name="cps.isilon.password" value="xEGuB4kEpvIyKF4atbSx" />
	<property name="cps.dmdb.sharedfolder" value="\\\\data2-smb.prd-isilon.int.cbdtp.net\\STGDB_SharedFiles" />
	<property name="cps.isilon.dm.namespace" value="stg-detection" />
	<property name="cps.isilon.ims.namespace" value="ifs/prd-isilon/data2/data/vol1/stg/images" />
	<property name="cps-s3-clientid" value="1_root_accid" />
	<property name="cps-s3-clientsecret" value="QxNxVl08ZASXwXxBdMSTguagDpg2" />
	<property name="cps-s3-primary-server" value="http://10.64.42.102:9020" />
	<property name="cps-s3-primary-bucket" value="s3-ims-storage" />
	<property name="cps.storage.base" value="D:\data2-smb.prd-isilon.int.cbdtp.net" />
	<property name="cps.raasURL" value="" />
	<property name="cps.ims.keyexpiration" value="90" />

	<property name="tableau.PersonalAccessTokenName" value="TableauPAT" />
	<property name="tableau.key" value="." />
	<property name="tableau.domain" value="int" />
	<property name="tableau.TableauUserName" value="SRV_TableauAppUser" />
	<property name="tableau.TableauPassword" value="hALL$jULY$hOURS$fINGERS$cOUNTRY$28$" />
	<property name="tableau.ProjectName" value="CPS" />

	<property name="timeout.seconds" value="43200" />
	<property name="webLog-User" value="WebLogUser" />
	<property name="webLog-pwd" value="W3bL0g2020" />
	<property name="cps-isilon-type" value="NFS2HttpIsilon" />
	<property name="service-base-path-cps-hostname" value="https://stgimsapp.cbdtp.net:5443" />
	<!-- IMS Appsettings.json -->
	<!-- C:test or - D:NFS -->
	<property name="StorageBasePath" value="\\\\data2-smb.prd-isilon.int.cbdtp.net\\Staging\\images2" />
	<property name="SendPlatesToVRG" value="false" />
	<property name="DNAMatchingMessageTimeToLive" value="2147483647" />
	<property name="VRGMessageTimeToLive" value="2147483647" />
	<!--DM API-->
	<property name="ContraFlowMaxCount" value="6" />
	<property name="DetectionMessageTimeToLive" value="2147483647" />
	<!-- DM Appsettings.json -->
	<property name="DMLogDirectory" value="D:\\Logs\\DM" />
	<property name="DMNFSStorageBasePath" value="\\\\data2-smb.prd-isilon.int.cbdtp.net\\STGDB_SharedFiles\\stgcpsapp01\\DM_Archive" />
	<property name="DMDetectionMessagesLogPath" value="\\\\data2-smb.prd-isilon.int.cbdtp.net\\STGDB_SharedFiles\\stgcpsapp01\\DM_Archive" />
	<property name="DMInvalidDetectionMsgLogPath" value="\\\\data2-smb.prd-isilon.int.cbdtp.net\\STGDB_SharedFiles\\stgcpsapp01\\Invalid_CSV" />
	<property name="DMInvalidDetectionCSVLogPath" value="\\\\data2-smb.prd-isilon.int.cbdtp.net\\STGDB_SharedFiles\\stgcpsapp01\\Invalid_CSV" />
	<property name="DMDetectionFilesMaximumSize" value="230400" />
	<property name="DMDetectionCSVFilesLocation" value="\\\\data2-smb.prd-isilon.int.cbdtp.net\\STGDB_SharedFiles\\stgcpsapp01\\Good_CSVs" />
	<property name="DMCSVFileNameSuffix" value="01" />
	<property name="DMFailedCsvStorageDirectory" value="\\\\data2-smb.prd-isilon.int.cbdtp.net\\STGDB_SharedFiles\\stgcpsapp01\\Unprocessed_CSVs" />
	<!-- Information or - Verbose -->
	<property name="DMSerilogLoggingLevel" value="Information" />
	<!-- 7 or - 10 -->
	<property name="retainedFileCountLimit" value="500" />
	<!-- true or - false -->
	<property name="IncludeDeviceDataEventsInCsv" value="true" />
	<property name="cps.ignore.moms.messages" value="false" />
	<property name="suppress-insight-messages" value="false" />
	<!-- IMS -->
	<property name="IMSDetectionMessagesLogPath" value="\\\\data2-smb.prd-isilon.int.cbdtp.net\\STGDB_SharedFiles\\placeholder\\IMS_Archive" />

	<!-- ZPI Task settings -->
	<property name="zpi-User" value="TcoreCli" />
	<property name="zpi-pwd" value="uKCmq8zN" />
	<property name="zpi-thirdparty-serviceurl" value="http://************:8099/SOAP" />
	<!--sftp settings for Workflow-->
	<property name="ftp-user" value="sftp" />
	<property name="ftp-pwd" value="Transcore1!" />
	<property name="ftp-host" value="sftp://?????" />
	<!-- DataAnalytic Scheduled Tasks python -->
	<property name="smtp-server" value="smtp.cbdtp.net" />
	<property name="sender-email" value="<EMAIL>" />
	<property name="smtp-port" value="25" />

	<property name="log.dir" value="D:\\CPSLogs" />
	<property name="temp.dir" value="D:\\Temp" />
	<property name="dm.log.dir" value="D:\\DMLogs" />
	<property name="vrg.log.dir" value="D:\\VRGLogs" />
	<property name="dnamatch.log.dir" value="D:\\DNAMatchLogs" />
	<property name="ctfs.log.dir" value="D:\\Logs\\CFTS" />
	<property name="dm.archive.dir" value="D:\\DMArchive" />
	<property name="cfts.archive.dir" value="\\\\data2-smb.prd-isilon.int.cbdtp.net\\Staging\\cfts" />
	<property name="transcom.log.dir" value="D:\\TransComLogs" />

	<!-- Website environment ts settings -->
	<property name="appTitle" value="CONGESTION PRICING SYSTEM" />
	<property name="appTabLabel" value="CPS" />
	<property name="showLandingPage" value="true" />

	<!-- SSO Hub WebUI environment ts settings -->
	<property name="enableSSOHub" value="true" />

	<!-- SSO Validation WebAPI Appsettings.json -->
	<property name="TXPValidatorEnabled" value="false" />

	<!-- Logging settings -->
	<property name="log.error.level" value="ALL"/>

	<!-- Include ActiveDirectory-->
	<property name="ActiveDirectoryFeature"  value="true"/>
	<property name="DefaultDomain" value="int.cbdtp.net" />
	<property name="GroupDefaultOU" value="OU=Domain SecurityGroups,DC=int,DC=cbdtp,DC=net" />
	<property name="UserDefaultOU" value="DC=int,DC=cbdtp,DC=net" />
	<property name="DefaultRootOU" value="DC=int,DC=cbdtp,DC=net" />
	<property name="ServiceUser" value="int\srv_adsystemuser" />
	<property name="ServicePassword" value="$iNCHES^iDEA^pICKED^wERE^aCTUALLY^32$" />

	<!-- Does the project use HTTPS/SSL?-->
	<property name="SSLFeature" value="true" />
	<!-- Load Balancing/Cluster -->
	<property name="ClusterFeature" value="true" />
	<!-- Build the simluators? (DEV/QA only) -->
	<property name="SimulatorFeature" value="false"/>
	<!-- Is Project using TransPOrtal? -->
	<property name="TransPortalFeature"  value="true"/>
	ext01.server
	<!-- Is Project using ZPIProcess -->
	<property name="ZPIProcessTask"  value="true"/>
	<!-- BOS EXT WEB -->
	<property name="BOSExternalWebFeature"  value="true"/>
	<!-- Multiple RabbitMQ Servers? -->
	<property name="RabbitMQCluster"  value="true"/>

	<property name="DATAANALYTICFeature"  value="true"/>

	<!-- Set the following features to true for CBDTP -->
	<property name="TableauFeature"  value="true"/>
	<property name="TripBuildingFeature"  value="true"/>
	<property name="AARFeature"  value="false"/>
	<property name="DMVFeature"  value="true"/>
	<property name="DMVWorkerFeature"  value="true"/>
	<property name="DNAMatchFeature"  value="true"/>
	<property name="IASFeature"  value="false"/>
	<property name="NYCDOTFeature"  value="true"/>
	<property name="RaasAcknowledgementFeature"  value="true"/>
	<property name="TPIFeature"  value="true"/>
	<property name="WorkflowFeature"  value="true"/>
	<property name="VRGFeature"  value="true"/>

	<property name="TransComServiceFeature"  value="false"/>

	<!-- If TransPortal Feature = true, need the following DB information to update TransPortal DB -->
	<property name="db.user"  value="SSOAppUser"/>
	<property name="db.password"  value="B3st@pp"/>

	<!--  ELK Section -->
	<!-- ELK Logging Feature - only DEV and PROD in CBDTP have ELK -->
	<property name="ELKFeature"  value="false"/>
	<!-- If ELKFeature is true, points to the ELK server -->
	<property name="RequestUri"  value="http://na:5000"/>
	<!-- If ELKFeature is true, set to Informaton, otherwise set to 10 -->
	<property name="RestrictedToMinimumLevel"  value="10"/>
	<!-- ELK Log Buffer Path for DM, IMS, and CFTS -->
	<property name="DMBufferedPathFormat"  value="D:\\Logs\\Buffer\\DM-Buffer-{HalfHour}.json"/>
	<property name="IMSBufferedPathFormat"  value="D:\\Logs\\Buffer\\IMS-Buffer-{HalfHour}.json"/>
	<property name="CFTSBufferedPathFormat"  value="D:\\Logs\\Buffer\\CFTS-Buffer-{HalfHour}.json"/>

    <!-- DNAMatch Version = Common or CPS -->
	<property name="DNAMatchCommonVersion"  value="true"/>
	<property name="DNAMatchCPSVersion"  value="false"/>

	<property name="dmv.working.directory" value="\\\\data2-smb.prd-isilon.int.cbdtp.net\\DMVDataExchange" />
	<property name="dmv.archive.directory" value="\\\\data2-smb.prd-isilon.int.cbdtp.net\\DMVDataExchange" />

	<!--VRG MIR settings -->
	<property name="MIRPollBatchSize" value="50" />
	<property name="MirTablePendingPollWaitDuration" value="5000" />
	<property name="MirTablePendingNoDataPollWaitDuration" value="60000" />


	<!-- Properties for mapping drives during deployment -->
	<property name="deployment.user" value="." />
	<property name="deployment.password" value="." />
	<property name="deployment.drive" value="D" />

	<!-- Destination server drive -->
	<property name="jump.drive" value="D" />

	<!-- Moving to IntegratedSecurity (AD User for DB connection) -->
	<property name="IntegratedSecurity" value="true"/>


	<property name="devops" value="true" />

</project>