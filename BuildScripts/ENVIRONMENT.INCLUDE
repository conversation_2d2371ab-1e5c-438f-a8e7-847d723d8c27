<?xml version="1.0" encoding="UTF-8"?>
<project name="Insight" default="build" basedir=".">
	<property name="nant.settings.currentframework" value="net-4.0" />
	
	<!-- Define the Deployment Project Environment-->
	<!-- Project Acronym ex. VTA2/NCTA/ etc. - matches acronym in SVN and in Build Scripts \ Projects \ directory -->
	<property name="deployment.environment" value="CBDTP" />
	<!-- Build Type:  DEV, QA, PROD... should match the name of this file ENVIRONMENT_DEV.include or ENVIRONMENT_QA.include etc -->
	<property name="deployment.type" value="STAGE" />
	<!-- used by the build, do not modify the below lines -->
	<property name="dated.deployment.dir" value="2024-11-06" />
	<property name="release.version" value="20230217-INS-REL0004-P8-final" />
    <property name="new.symmetric.key" value="true" />
	<property name="build_id" value="CBDTP/STAGE/20230217-INS-REL0004-P8-final" />
	<property name="create_db" value="N" />
	
	<!-- Define the Directory where the build puts the service layer executables.   In December 2014, we modified the Product to put the Service Layer executables in C:\MOMS\ 
	The subdirectory property is to allow for older projects to append "Release\bin" to the directory path-->
	<property name="moms.release.directory" value="C:\MOMS" />
	<property name="moms.release.subdirectory" value="" />
	<property name="moms.release.AppLayer.dir" value="${moms.release.directory}\AppLayer" />
	<property name="moms.release.CommonLayer.dir" value="${moms.release.directory}\CommonLayer" />
	<property name="moms.release.ServiceClient.dir" value="${moms.release.directory}\ServiceClient" />
	<property name="moms.release.ServiceInterface.dir" value="${moms.release.directory}\ServiceInterface" />
	<property name="moms.release.ThirdParty.dir" value="${moms.release.directory}\ThirdParty" />
	
	<!-- Define the Directory where the build puts the service layer executables.   In February 2015, we modified the Product to put the Service Layer executables in C:\MOMSMobile\ 
	The subdirectory property is to allow for older projects to append "Release\bin" to the directory path-->
	<property name="momsmobile.release.directory" value="C:\MOMSMobile" />
	<property name="momsmobile.release.subdirectory" value="" />
	<!--<property name="momsmobile.release.directory" value="D:\AppBuild\Source\MOMSMobile" />
	<property name="momsmobile.release.subdirectory" value="bin\Release" /> -->
		
	<!-- SVN Settings DO NOT MODIFY-->
	<property name="svn.url-dev-branch" value="https://sd-vcmsvn02.tcore.com/svn/MOMS/Products/MOMSInsight/${build_id}/MOMS" />
	<property name="mobile.svn.url-dev-branch" value="https://sd-vcmsvn02.tcore.com/svn/MOMS/Products/MOMSInsight/${build_id}/MOMSMobile" />
	<property name="infinity.svn.url-dev-branch" value="https://sd-vcmsvn02.tcore.com/svn/MOMS/Products/MOMSInsight/${build_id}/MOMSInterfaces/InfinityMOMSService" />
	<property name="transsuite.svn.url-dev-branch" value="https://sd-vcmsvn02.tcore.com/svn/MOMS/Products/MOMSInsight/${build_id}/MOMSInterfaces/TransSuiteMOMSService" />
	<property name="insight.svn.url-dev-branch" value="https://sd-vcmsvn02.tcore.com/svn/MOMS/Products/MOMSInsight/${build_id}/Insight" />
	<property name="integrity.svn.url-dev-branch" value="https://sd-vcmsvn02.tcore.com/svn/MOMS/Products/MOMSInsight/${build_id}/MOMSInterfaces/IntegrityMOMSService" />
	<!--  Deployment Server is always the IP  --> 
	
	<property name="deployment.server" value="prdjump.int.cbdtp.net" />
	<!-- These will rarely change - unless you had to install a different version of SQL or have an E drive instead of a D drive SQL Server 2016 values are below -->
	<property name="sqlserver.data.drive" value="D" />
	<property name="sqlserver.log.drive" value="F" />
	<property name="sqlserver.reports.drive" value="D" />
	<property name="sqlserver.version" value="MSSQL15" />
	<property name="sqlserver.rpt.version" value="MSRS15" />
	<property name="sqlserver.data.path" value="${sqlserver.data.drive}:\Program Files\Microsoft SQL Server\${sqlserver.version}.MSSQLSERVER\MSSQL\Data" />
	<property name="sqlserver.log.path" value="${sqlserver.log.drive}:\Program Files\Microsoft SQL Server\${sqlserver.version}.MSSQLSERVER\MSSQL\Log" />
	<!--  Server definitions, use Fully Qualified Domain Name -->
	<property name="moms.app.server" value="stginstapp01.int.cbdtp.net" />
	<property name="moms.db.server" value="stginstdb01.int.cbdtp.net" />
	<property name="moms.report.server" value="stginstapp01.int.cbdtp.net" />	
	<property name="insight.mobile.server" value="stgcpsext01.int.cbdtp.net" />
	<property name="ifx.web.alias" value="n/a" />
	<property name="document.server.location" value="\\${moms.app.server}\" />
		
	<!-- If has DMS database -->
	<property name="HasDMS" value="false" />
	<property name="dms.db.server" value="" />
	<property name="dms.db.user" value="" />
	<property name="dms.db.password" value="" />			
		
	<!-- Log file location on the project server - only change drive letter if needed -->
	<property name="log.dir" value="D:/MOMSLogs" />
	<property name="mobile.log.dir" value="D:/MOMSLogs" />
	<property name="sso.log.dir" value="D:/MOMSLogs" />
	
	<!-- Logging settings -->
	<property name="log.error.level" value="ALL"/>
	
	<!-- Email Configuration -->
	<property name="useEmailDefaultCredentials" value="true" />
	<property name="emailUserName" value="<EMAIL>" />
	<property name="emailPassword" value="welcome@winter2020" />
	<property name="emailDomainName" value="cbdtp.net" />
	<property name="UserName" value="Stg-Insight"/>
	<property name="Password" value="welcome@winter2020"/>
	<property name="ClientSettingsProvider.ServiceUri" value=""/>
	<property name="saveSentEmail" value="true"/>
	<property name="imapPort" value="143" />
	<property name="imapUseSsl" value="false" />
	<property name="imapValidateCertificate" value="false" />
	<property name="SMTPServer" value="smtp.cbdtp.net" />
	<property name="IMAPMailServer" value="imap.cbdtp.net"/>

	<!-- MOMS Services endpoints, these never change -->
	<property name="wcf.internal.tcp.client.endpoint" value="net.tcp://${moms.app.server}:5000/MOMSWCFService"/>
	<property name="wcf.external.http.client.endpoint" value="http://${moms.app.server}:5002/MOMSWCFExternalService"/>

	<!-- Host App Settings never change -->
	<!-- Internal MOMS Service Host App Settings -->
	<property name="wcf.moms.tcp.base.address" value="net.tcp://${moms.app.server}:5000" />
	<property name="wcf.moms.http.base.address" value="http://${moms.app.server}:5001" />	
	<!-- External MOMS Service Host App Settings -->
	<property name="AuthenticateUser" value="false"/>	
	<property name="external.wcf.moms.http.base.address" value="http://${moms.app.server}:5002" />	
			
	
	<!-- Reports deployer settings --> 
	<property name="report.server.URL" value="http://${moms.report.server}:8080/reportserver" />
	<property name="report.service" value="${report.server.URL}/ReportService2010.asmx" />			
	<property name="report.datasource.pass" value="n.a" />	
	<property name="report.datasource.userid" value="n.a" />
	
	
	<!-- The below section is used to define which features should be included in the project's build -->
	<!-- Scheduled Tasks -->
	<!-- NotificationEscalation Task -->
	<property name="notificationUserID" value="99999" />
	<property name="workOrderEscalationMinutesAssigned" value="-1"/>

	
	<!-- Include Traffic Loader Task -->
	<property name="TrafficLoaderTask" value="false"/>

	
	<!-- Include ExternalNotifier  used in projects that have Infinity Lanes.  Ask Sathya to help with finding the correct values-->
	<property name="MOMSExternalNotifier" value="false"/>
	<property name="NotifierTaskServiceConnectionServer" value="localhost"/>
	<property name="NotifierTaskServiceConnectionAddress" value="net.tcp://${NotifierTaskServiceConnectionServer}:9034/MOMSExternalService"/>

	
	<!-- Include GPS WCF Service -->
	<property name="GPSFeature" value="false"/>
		<!-- Additional GPS Settings API-->
		<property name="GPSLocatorServiceHost" value="${moms.app.server}"/>
		<property name="GPSLocatorServicePort" value="80"/>
		<property name="moms.gps.service.host.base.address" value="http://${moms.app.server}:5005"/>
		<property name="wcf.gps.http.client.endpoint" value="http://${moms.app.server}:5005/MOMSWCFGPSLocatorService"/>
		
	
	<!-- Include Mobile (OLD MOBILE!!!) -->
	<property name="MobileFeature" value="false"/>
		<!-- MOMS Mobile Server definitions -->
		<property name="momsmobile.app.server" value="prdcpsext01.int.cbdtp.net" />
		<property name="momsmobile.db.server" value="prdcpsext01" />
		<!-- Internal MOMS Mobile Service Host App Settings -->
		<property name="wcf.momsmobile.tcp.base.address" value="net.tcp://${momsmobile.app.server}:988" />
		<property name="wcf.momsmobile.http.base.address" value="http://${momsmobile.app.server}:989" />		
		<property name="momsmobile.servicePrincipalName" value="prdcpsext01.int.cbdtp.net" />
		<property name="moms.servicePrincipalName" value="stginstapp01.int.cbdtp.net" />
		<property name="endpoint.servicePrincipalName" value="stginstapp01.int.cbdtp.net" />
		<!-- MOMS Mobile Web Config Settings -->
		<property name="wcf.momsmobile.internal.tcp.client.endpoint" value="net.tcp://${momsmobile.app.server}:988/MomsMobileWcfService"/>
		<property name="wcf.moms.internal.tcp.client.endpoint" value="net.tcp://${moms.app.server}:5000/MOMSWCFService"/>
		<!-- MOMS Mobile Web JS settings - check to see if this is https or http. Usually it is the moms server with port 5080, if https use port 5443 -->
		<property name="momsmobile.js.url" value="n/a" />
		<!-- MOMS Mobile Service Principal Names -->

	
	<!-- Include Infinity WCF Service -->
	<property name="InfinityFeature" value="true"/>

		<!-- Infinity Service Host Settings -->
		<property name="wcf.Infinity.http.base.address" value="http://${moms.app.server}:5006" />
		<property name="wcf.Infinity.http.client.endpoint" value="http://${moms.app.server}:5002/MOMSWCFExternalService"/>
	
	<!-- Include Infinity Service -->
	<property name="IntegrityFeature" value="false"/>

		<!-- Integrity Service Host Settings -->
		<property name="wcf.Integrity.http.base.address" value="http://${moms.app.server}:5009" />
		<property name="wcf.Integrity.http.client.endpoint" value="http://${moms.app.server}:5002/MOMSWCFExternalService"/>

	
	<!-- Include TransSuite Service -->
	<property name="TransSuiteFeature" value="false"/>

		<!-- TransSuite Service Host Settings -->
		<property name="wcf.TransSuite.http.base.address" value="http://${moms.app.server}:5007" />
		<property name="wcf.TransSuite.http.client.endpoint" value="http://${moms.app.server}:5002/MOMSWCFExternalService"/>
	
	
	<!-- Include ARCS data inserts, for Projects that use ARCS lanes (VTA2 only so far) -->
	<property name="ARCSFeature" value="false"/>
	
	<!-- Include IFX data inserts -->
	<property name="IFXFeature" value="false"/>
		<!-- IFX (Express) DB server -check if used by project (IFXFeature will be set to True) -->
		<property name="ifx.db.server" value="n/a" />	
		<!-- Moving to IntegratedSecurity (AD User for DB connection) this is in relation to IFX so only used for projects with IFX (VTA2 only so far)-->
		<property name="IntegratedSecurity" value="false"/>
		<property name="ifx.secure.connection.string" value="Database=TCS;Server=????;Integrated Security=SSPI;Min Pool Size=1;Max Pool Size=100;Connect Timeout=120;"/>
	
		
	<!-- Include ActiveDirectory-->
	<property name="ActiveDirectoryFeature"  value="true"/>
	<!-- Is Project using ADPollingTask -->
	<property name="ADPollingTask"  value="false"/>
		<!-- Active Directory Settings -->
		<property name="SYSADMIN.securitygroup" value="TcoreSysAdmin" />
		<property name="DefaultDomain" value="int.cbdtp.net" />
		<property name="GroupDefaultOU" value="OU=Domain SecurityGroups,DC=int,DC=cbdtp,DC=net" />
		<property name="UserDefaultOU" value="DC=int,DC=cbdtp,DC=net" />
		<property name="PlazaDefaultOU" value="DC=int,DC=cbdtp,DC=net" />
		<property name="DefaultRootOU" value="DC=int,DC=cbdtp,DC=net" />
		<property name="ServiceUser" value="int\srv_adsystemuser" />
		<property name="ServicePassword" value="$iNCHES^iDEA^pICKED^wERE^aCTUALLY^32$" />
	
	<!-- If TransPortal is used with this Environment set the value to true-->
	<property name="TransPortalFeature"  value="true"/>
			<!-- TransPortal Server Definitions-->
		<property name="transportal.db.server" value="stgtxp01.int.cbdtp.net" />	
		<property name="transportal.db.listener" value="stgtxp01.int.cbdtp.net" />	
		<property name="transportal.web.alias" value="stgtxp.cbdtp.net" />

		
	<!-- NonTransPortalSSOFeature -->
	<property name="NonTransPortalSSOFeature"  value="false"/>	
	
	<!-- WorkOrderMessagingFeature -->
	<property name="WorkOrderMessagingFeature"  value="true"/>		
	
	<!-- Does the project use HTTPS/SSL? -->
	<property name="SSLFeature" value="true" />
	<property name="SSLTermination" value="true" />
	
	<!-- Event Loader connection details -->
	<!-- For Event Loaders, you only need to fill out the ones we are using for the project.  You need to find out which ones the project will use -->
	
	<!-- Include MOMSEventLoader -->
	<property name="MOMSEventLoader" value="false"/>
		<!-- MOMS Event Loader connection details -->
		<property name="eventloader.database" value="TCS" />
		<property name="eventloader.user" value="amsInternalAppUser" />
		<property name="eventloader.password" value="B3st@pp" />
		<property name="eventloader.db.server" value="n/a" />
		<!-- Integrated Security for Generic EventLoader - set to True if using Integrated Security for the event loader -->
		<property name="IntegratedSecurityMOMSEventLoader" value="true"/>
		
	<!-- LANE Event Loader connection details -->
	<property name="lane.eventloader.database" value="AMS" />
	<property name="lane.eventloader.user" value="amsInternalAppUser" />
	<property name="lane.eventloader.password" value="B3st@pp" />
	<property name="lane.eventloader.db.server" value="n/a" />
	
	<!-- CAMS Event Loader connection details -->
	<property name="cams.eventloader.db.server" value="n/a" />
	<property name="cams.eventloader.database" value="AMS" />
	<property name="cams.eventloader.user" value="amsInternalAppUser" />
	<property name="cams.eventloader.password" value="B3st@pp" />

	<!-- Include SolarWinds Event Loader/Feature-->
	<property name="SolarWindsFeature" value="false"/>
		<!--SolarWinds Event Loader connection details -->
		<property name="solarwinds.eventloader.database" value="SolarWindsOrion" />
		<property name="solarwinds.eventloader.user" value="SRV_SSPIInternalUser" />
		<property name="solarwinds.eventloader.password" value="notavailable" />
		<property name="solarwinds.db.server" value="PRDNPMSQL" />

	<!-- Include WhatsUp Event Loader/Feature-->
	<property name="WhatsUpFeature" value="false"/>	
		<!--WhatsUp Event Loader connection details -->
		<property name="whatsup.eventloader.database" value="WhatsUp" />
		<property name="whatsup.eventloader.user" value="MOMSAppUser" />
		<property name="whatsup.eventloader.password" value="B3st@pp" />
		<property name="whatsup.db.server" value="n/a" />
	
	<!-- Include ImageReviewEventLoader -->
	<property name="ImageReviewEventLoader" value="true"/>
		<!-- ImageReview Event Loader connection details -->
		<property name="imagereview.eventloader.database" value="IPS" />
		<property name="imagereview.eventloader.user" value="amsInternalAppUser" />
		<property name="imagereview.eventloader.password" value="B3st@pp" />
		<property name="imagereview.eventloader.db.server" value="STGMIRDB01.int.cbdtp.net" />
	
	<!-- Include IntegrityEventLoader -->
	<property name="IntegrityEventLoader" value="false"/>
		<!-- Integrity Event Loader connection details -->
		<property name="integrity.eventloader.database" value="IPS" />
		<property name="integrity.eventloader.user" value="amsInternalAppUser" />
		<property name="integrity.eventloader.password" value="B3st@pp" />
		<property name="integrity.eventloader.db.server" value="stginstapp01.int.cbdtp.net" />
		<property name="integrity.eventloader.exe.server" value="stginstapp01.int.cbdtp.net" />
		<!-- drive may change if event loader resides on a different server!!! -->
		<property name="integrity.eventloader.log.dir" value="D:\MOMSLogs" />
		<property name="MDIntegrityFeature" value="false"/>
		
		<!--Include TransPortalEventLoader-->
	<property name="TransPortalEventLoader" value="true"/>
		<!--TransPortal Event Loader connection details -->
		<property name="transportal.eventloader.database" value="SSO" />
		<property name="transportal.eventloader.user" value="SSOAppUser" />
		<property name="transportal.eventloader.password" value="B3st@pp" />
		
	<!--Include CPSEventLoader-->
	<property name="CPSEventLoader" value="true"/>
		<!--CPS Event Loader connection details -->
		<property name="cps.eventloader.database" value="DET" />
		<property name="cps.eventloader.user" value="CPSAppUser" />
		<property name="cps.eventloader.password" value="B3st@pp" />
		<property name="cps.eventloader.db.server" value="stginstdb01" />
		<!-- Integrated Security for CPS EventLoader - set to True if using Integrated Security for the TransPortal loader -->
		<property name="IntegratedSecurityCPSLoader" value="true"/>
		
	<!--Include VOTTEventLoader-->
	<property name="VOTTEventLoader" value="true"/>
		<!--VOTT Event Loader connection details -->
		<property name="vott.eventloader.database" value="VOTT" />
		<property name="vott.eventloader.user" value="VOTTAppUser" />
		<property name="vott.eventloader.password" value="B3st@pp" />
		<property name="vott.eventloader.db.server" value="stgvottdb01" />
		<!-- Integrated Security for VOTT EventLoader - set to True if using Integrated Security for the  VOTT loader -->
		<property name="IntegratedSecurityVOTTLoader" value="true"/>

	<!-- if the Map feature is set to true, the Map becomes the landing page.  If false then WO is landing page and the map reference is removed from Index.html -->
	<property name="MapFeature" value="true"/>	
	
	<!--Insight -->
	<property name="InsightFeature" value=""/>
		<!-- Insight Definitions -->
		<!-- Insight API server value is used for the API URL so you have to change it to a cert name if an https cert is used.  i.e. nctartcs.net or whatnot.  Otherwise, use the FQDN of the Insight server -->
		<property name="insight.api.server" value="stginsight.cbdtp.net" />
		<!-- these are used for the Map and you should ask the MOMS Team what to use...if anything -->
		<property name="transsuite.map.url.server" value="n/a" />
		<property name="infinity.map.url.server" value="https://stgcps.cbdtp.net" />
		<!-- change for http/https -->
		<property name="express.map.url.server" value="http://?????????????" />
		<!-- http 9090, https 9443 -->
		<property name="express.map.api.url.server" value="http://?????????????:9090" />
		<!-- Needed for Insight feature -->
	
		<!-- Insight web url is needed so we can use a port number if necessary this is for the Insight Web Site itself (not the API url)-->
		<property name="insight.web.url" value="${insight.api.server}" />		
		<property name="insight.AuthorizeAttributeFilter" value="false" />		
		<property name="map.showInfinityOption" value="false" />
		<property name="map.rtmMap" value="false" />
		<property name="map.laneCommands" value="false" />			
		
		<property name="showRtmLayers.speed" value="true" />
		<property name="showRtmLayers.cctv" value="true" />	
		<property name="showRtmLayers.tollZone" value="true" />	
		<property name="showRtmLayers.transactions" value="true" />	
		<property name="showRtmLayers.vtms" value="true" />	
		<property name="showRtmLayers.laneMode" value="false" />	
		<property name="showRtmLayers.laneOperations" value="false" />	
		<property name="showRtmLayers.laneCommand" value="false" />	
		<property name="showRtmLayers.pricingMode" value="false" />	
		<property name="showRtmLayers.travelTimes" value="false" />	
		
		<property name="showMapLayers.speed" value="false" />
		<property name="showMapLayers.cctv" value="false" />	
		<property name="showMapLayers.tollZone" value="false" />	
		<property name="showMapLayers.transactions" value="false" />	
		<property name="showMapLayers.vtms" value="false" />	
		<property name="showMapLayers.laneMode" value="false" />	
		<property name="showMapLayers.pricingMode" value="false" />	
		
		<property name="vtmsDisplayValue" value="VTMS" />	
		<property name="showContextMenu" value="false" />	
		<property name="mapSetting.iconHeightPx" value="30" />
		<property name="mapSetting.iconWidthPx" value="30" />
		
		<property name="enableSingleSignOn" value="true" />			

		<property name="IFXWebAPIService" value="http://?????:9090" />
		<property name="cctv.servername" value="http://?????" />
		<property name="enable.external.equipment.events" value="false" />			
		<property name="liveVideoShowIFrame" value="true" />
		<property name="liveTrafficForDetectionPoints" value="true" />	
		<property name="online.maps" value="true" />
		<property name="offline.maps" value="false" />			
		<property name="offline.maps.tile.url" value="????" /> 
		<property name="offline.maps.tilelocation.php" value="C:/MapData" /> 
			
		<!-- Adrian will ask the project for a new logo - contact me and we will review this.  default is mrachina.jpg -->
		<property name="insight.project.logo.name" value="mrachina.jpg" />
		<!-- User for TransPortal api back-end access, just here for record keeping only needed for TransPortal installs-->
		<property name="API.backend.user" value="TransPortalBackend" />

		
		<!-- Transaction Columns Properties in ConfigConstants.js file -->
		<property name="Trans.TransDateTime" value="true" />
		<property name="Trans.Speed" value="true" />
		<property name="Trans.NumIndicatedAxle" value="false" />
		<property name="Trans.NumActFwdAxle" value="false" />
		<property name="Trans.RevenueExpected" value="false" />
		<property name="Trans.RevenueCollected" value="false" />
		<property name="Trans.PaymentTypeLabel" value="true" />
		<property name="Trans.TagNumber" value="true" />
		<property name="Trans.TagStatusDescription" value="true" />
		<property name="Trans.UODescription" value="false" />		
		<property name="Trans.LocationName" value="false" />
		
		<property name="useDeviceStatusForSiteMarker" value="false" />

		<property name="rtm.exceptions.url" value="https://stgcpsapp.cbdtp.net:4443" />
		
		<!-- Filter Insight colors.scss file -->
		<property name="tcore-site-title-default-color" value="#0c7cd5" />
		<property name="tcore-site-title-high-color" value="#FF0000" />
		<property name="tcore-site-title-medium-color" value="#FF931F" />
		<property name="tcore-site-title-low-color" value="#FFF200" />
		<property name="tcore-site-title-inactive-color" value="gray" />
		
	<!-- Properties for mapping drives during deployment.  New practice is that these are provided in Step 4 via command line -->
	<property name="deployment.user" value="" />
	<property name="deployment.password" value="" />
	
	<!-- Default is always D unless the project has an E drive, which is rare -->
	<property name="deployment.drive" value="D" />
	<property name="jump.drive" value="D" />
	
	<!-- Moving to IntegratedSecurity (AD User for DB connections for MOMS and TransPortal) -->
	<property name="IntegratedSecurity" value="true"/>
	
	<!-- Integrated Security for Integrity EventLoader - set to True if using Integrated Security for the IFX MOMS loader -->
	<property name="IntegratedSecurityIFXLoader" value="false"/>

	
	<!-- Integrated Security for Integrity EventLoader - set to True if using Integrated Security for the Integrity loader -->
	<property name="IntegratedSecurityIntegrityLoader" value="false"/>
	
	<!-- Integrated Security for Image Review EventLoader - set to True if using Integrated Security for the Image Review loader -->
	<property name="IntegratedSecurityImageReviewLoader" value="true"/>
	
	<!-- Integrated Security for TransPortal EventLoader - set to True if using Integrated Security for the Image Review loader -->
	<property name="IntegratedSecurityTransPortalLoader" value="true"/>

	<!-- Integrated Security for WhatsUp EventLoader - set to True if using Integrated Security for the WhatsUp loader -->
	<property name="IntegratedSecurityWhatsUpLoader" value="false"/>
	
	<!-- Integrated Security for SolarWinds EventLoader - set to True if using Integrated Security for the SolarWinds loader -->
	<property name="IntegratedSecuritySolarWindsLoader" value="true"/>
	
	<!-- INSIGHT MOBILE -->
	<property name="InsightMobileFeature" value="true"/>
		<!-- The port used for the Mobile Site -->
		<property name="SelfSignedCertificate" value="false"/>
		<property name="insight.mobile.urlname" value="stginsightmobile.cbdtp.net" />
		<property name="insight.mobile.ssl" value="true" />
	
	<!-- If TransPortal Feature = true, need the following DB information to update TransPortal DB -->
	<property name="db.user"  value="SSOAppUser"/>
	<property name="db.password"  value="B3st@pp"/>
	
	<!-- OfflineMap Feature -->
	<property name="OfflineMapFeature" value="false"/>
	
	<!-- Database AlwaysOn Feature -->
	<property name="DBAlwaysOnFeature" value="false"/>
	
	<property name="devops" value="true" />
	
</project>