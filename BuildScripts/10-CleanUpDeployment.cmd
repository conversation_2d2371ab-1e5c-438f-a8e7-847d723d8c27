@ECHO OFF
rem SET /P userInput=This action will Package MOMS for Distribution, do you wish to continue? (Y/N): 
set NOW=%date:~10,4%-%date:~4,2%-%date:~7,2%
set /P user=[Enter Project Deployment User i.e. ncta\kelleym]:=
set "psCommand=powershell -Command "$pword = read-host 'Enter Password' -AsSecureString ; ^
    $BSTR=[System.Runtime.InteropServices.Marshal]::SecureStringToBSTR($pword); ^
        [System.Runtime.InteropServices.Marshal]::PtrToStringAuto($BSTR)""
for /f "usebackq delims=" %%p in (`%psCommand%`) do set pw=%%p
set scripts-dir=%CD%
if "%1"=="batch" goto :BATCHIT

:DEPLOY
NAnt.exe -buildfile:ImageReview_build.build -logger:NAnt.Core.MailLogger -logfile:AppLogs\10-Cleanup.log cleanup-deployment  -D:deployment.user=%user% -D:deployment.password=%pw%

GOTO END

:BATCHIT
NAnt.exe -buildfile:ImageReview_build.build -logger:NAnt.Core.MailLogger -logfile:AppLogs\10-Cleanup.log cleanup-deployment  -D:deployment.user=%user% -D:deployment.password=%pw%

set e_result=%ERRORLEVEL%
rem -- Report error code if not zero.
if NOT "%e_result%"=="0" @echo 1:-e-:Error result from 10-CleanUpDeployment for IFX is %e_result%
rem -- Return error level
exit /B %e_result%

:END

pause 


