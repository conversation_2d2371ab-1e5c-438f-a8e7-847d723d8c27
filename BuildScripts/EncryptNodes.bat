@echo off
rem set variables
Set CURRENTDIR=%CD%

echo ------- Your current directory is:  %CURRENTDIR% ---------

set NOW=%date:~10,4%-%date:~4,2%-%date:~7,2%
echo Deployment beginning %NOW%
echo -----------------------------------
echo Connect to Server
echo -----------------------------------
verify > nul
SET SERVER=%1
SET DEPLOYMENTDRIVE=%2
SET DEPLOYMENTDIR=%3
SET GENTASK=%4
SET road=%5

ECHO deployment dir %DEPLOYMENTDIR%
ECHO road is %road%
If NOT "%road%" == "none" SET road=-%road%
If "%road%" == "none" SET road=
echo road = %road%

SET BASEDIR=%JUMPDRIVE%:\Staging-ImageReview\%DEPLOYMENTDIR%
SET TASKROADCOMBO=%GENTASK%%ROAD%


SET STEP="Encrypt %GENTASK%%ROAD%"
call Autobuild\do_invoke_any_locrem1.cmd %SERVER% %DEPLOYMENTDRIVE%:\Deployment-ImageReview\%DEPLOYMENTDIR%\AES "EncryptNodeConfigs.cmd %GENTASK% %TASKROADCOMBO%" %DEPLOYMENTDRIVE%:\Deployment-ImageReview\%DEPLOYMENTDIR%\AES\EncryptNodeConfigs.cmd unused unused unused unused
if %ERRORLEVEL% NEQ 0 GOTO :error

GOTO END



:error
echo ------- AN ERROR OCCURED DURING THIS STEP: Create task %SERVER% %GENTASK% %road%------
exit /b 1

:END
echo -- Create task %SERVER% %GENTASK% %road% is complete