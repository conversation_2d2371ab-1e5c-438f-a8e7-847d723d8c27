@ECHO OFF

set NOW=%date:~10,4%-%date:~4,2%-%date:~7,2%
Set CURRENTDIR=%CD%
SET DEPLOYSERVER=%1
SET ENVTYPE=%2
SET DEPLOYMENTDRIVE=%3
SET DEPLOYMENTDIR=%4
SET TABLEAUFEATURE=%5
SET PROJECT=%6
SET DWServerName=%7
SET DWPort=%8
SET DWUserName=%9
SHIFT
SHIFT
SHIFT
SHIFT
SHIFT
SHIFT
SHIFT
SHIFT
SHIFT
SET DWPassword=%1
SET DWFeature=%2
SET TABServerName=%3
SET TABUserName=%4
SET TABPassword=%5
SET connectionPW=%6



set fullstamp=%YYYY%-%MM%-%DD%_%HH%-%Min%-%Sec%


echo Deploy Tableau from  %DEPLOYSERVER% for  %ENVTYPE% 


echo CPS Tableau Deployment is starting at %fullstamp%
echo ---------------------------------- 

VERIFY > nul


if "%TABLEAUFEATURE%"=="false" GOTO :NoTableau
:DeployReports
SET STEP="Deploy Tableau"
echo Step %STEP% commencing...  --------- 
echo Start %STEP% 
CD %CURRENTDIR%
cd ..\TableauDeployTool
call TableauDeploy.exe %DEPLOYMENTDRIVE%:\Staging-CPS\%DEPLOYMENTDIR%\TABLEAU\Tableau\Workbooks %TABServerName% 1433 %TABUserName% %TABPassword% %connectionPW%
if %ERRORLEVEL% NEQ 0 GOTO :error
echo Step %STEP% completed...  --------- 

if not "%DWFeature%"=="true" GOTO END
rem if not "%ENVTYPE%"=="PROD" GOTO END
rem if not "%PROJECT%"=="CBDTP" GOTO END
:DeployDWReports
SET STEP="Deploy DW Tableau Reports"
echo Step %STEP% commencing...  --------- 
call TableauDeploy.exe %DEPLOYMENTDRIVE%:\Staging-CPS\%DEPLOYMENTDIR%\TABLEAU\Tableau\Workbooks_DW %DWServerName% %DWPort% %DWUserName% %DWPassword% %connectionPW%
if %ERRORLEVEL% NEQ 0 GOTO :error
echo Step %STEP% completed...  --------- 


GOTO END


:error
cd %CURRENTDIR%
echo ------- AN ERROR OCCURED DURING THIS STEP: %STEP% ------
echo ------- AN ERROR OCCURED DURING THIS STEP: %STEP% ------ 
rem NAnt.exe -buildfile:ImageReview_build.build -logger:NAnt.Core.MailLogger -logfile:AppLogs\build.log autobuild-error
exit /b 1

:END
echo current directory is %CD%
echo current dir to navigate to:  %CURRENTDIR%
cd %CURRENTDIR%
echo -- Install of CPS Tableau is complete, wait for DBA to run last step
echo -- Install of CPS Tableau   complete 
rem NAnt.exe -buildfile:ImageReview_build.build -logger:NAnt.Core.MailLogger -logfile:AppLogs\build.log autobuild-complete

GOTO :FinalEnd

:NoTableau
echo Tableau feature is set to false... ---------

:FinalEnd

exit /b 0


