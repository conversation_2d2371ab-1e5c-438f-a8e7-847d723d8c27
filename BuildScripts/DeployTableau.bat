@ECHO OFF

set NOW=%date:~10,4%-%date:~4,2%-%date:~7,2%
Set CURRENTDIR=%CD%
SET DEPLOYSERVER=%1
SET ENVTYPE=%2
SET DEPLOYMENTDRIVE=%3
SET DEPLOYMENTDIR=%4
SET PROJECT=%5
set fullstamp=%YYYY%-%MM%-%DD%_%HH%-%Min%-%Sec%


echo Deploy Tableau from  %DEPLOYSERVER% for  %ENVTYPE% 



echo MIR Tableau Deployment is starting at %fullstamp%
echo ---------------------------------- 

VERIFY > nul

ECHO %PROJECT%

:DeployReports
SET STEP="Deploy Tableau"
echo Step %STEP% commencing...  --------- 
echo Start %STEP% 
CD %CURRENTDIR%
cd ..\TableauDeployTool
call TableauDeploy.exe %DEPLOYMENTDRIVE%:\Staging-ImageReview\%DEPLOYMENTDIR%\TABLEAU\Tableau\Workbooks
if %ERRORLEVEL% NEQ 0 GOTO :error
echo Step %STEP% completed...  --------- 
GOTO END


:error
cd %CURRENTDIR%
echo ------- AN ERROR OCCURED DURING THIS STEP: %STEP% ------
echo ------- AN ERROR OCCURED DURING THIS STEP: %STEP% ------ 
rem NAnt.exe -buildfile:ImageReview_build.build -logger:NAnt.Core.MailLogger -logfile:AppLogs\build.log autobuild-error
exit /b 1

:END
echo current directory is %CD%
echo current dir to navigate to:  %CURRENTDIR%
cd %CURRENTDIR%
echo -- Install of MIR Tableau is complete, wait for DBA to run last step
echo -- Install of MIR Tableau complete 
rem NAnt.exe -buildfile:ImageReview_build.build -logger:NAnt.Core.MailLogger -logfile:AppLogs\build.log autobuild-complete
exit /b 0


