@echo off
rem set variables
Set CURRENTDIR=%CD%

%CURRENTDIR%\fnr.exe --cl --dir "D:\AppBuild-MOMS\Distribution\Insight\webLayer\InsightWeb" --fileMask "Index.html" --excludeFileMask "*.dll, *.exe" --includeSubDirectories --find "<script src=""https://maps.googleapis.com/maps/api/js?v=3.33&key=AIzaSyDITnMBiyA0TkIOXTBJuiuDMBJu6qVUPZ0&libraries=geometry""></script>\n" --replace ""
%CURRENTDIR%\fnr.exe --cl --dir "D:\AppBuild-MOMS\Distribution\Insight\webLayer\InsightWeb" --fileMask "Index.html" --excludeFileMask "*.dll, *.exe" --includeSubDirectories --find "<script src=""https://maps.googleapis.com/maps/api/js?client=gme-transcorelp&amp;sensor=false&amp;v=3.33""></script>" --replace ""